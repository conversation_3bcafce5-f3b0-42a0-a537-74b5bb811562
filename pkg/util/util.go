/**
* <AUTHOR>
* @date 2022-11-22 15:21
* @description
 */

package util

import (
	"github.com/gofiber/fiber/v2"
	"log"
	v1 "meta/api/v1"
	"meta/app"
	"meta/app/entity/config"
	"meta/pkg/middleware"
	"meta/pkg/register"
	"os"
	"os/signal"
)

func RegisterRouter(router *v1.Router, fiberApp *fiber.App) {
	//Swagger api
	if config.CFG.Swagger.Enable {
		router.Swagger(fiberApp)
	}
	//中间件
	middleware.Middleware(fiberApp)
	//公共路由
	router.Public(fiberApp)
	//对外提供服务的路由
	router.External(fiberApp)
	//登录后公用路由
	router.UserCommon(fiberApp)
	//私有路由
	router.Private(fiberApp)
	//404
	router.NotFound(fiberApp)

}

func InjectApp() (*fiber.App, *v1.Router, *register.Runner, func()) {
	// Define Fiber config.
	// Define a new Fiber app with config.
	fiberApp := fiber.New(config.CFG.Fiber.NewConfig())

	//依赖注入
	injector, f, err := app.Init()
	if err != nil {
		log.Fatal(err)
	}
	// Routes.
	router := injector.Router
	registerInjector := injector.RegisterInjector
	return fiberApp, router, registerInjector, f
}

func LoadConfig(path string) error {
	err := config.LoadConfig(path)
	//根据环境切换
	//集团soc非正式环境，使用 Test api
	if config.CFG.Stage.Status != "prod" {
		config.CFG.External.SocGroup.Api = config.CFG.External.SocGroup.ApiTest
	}
	return err
}

// StartServerWithGracefulShutdown function for starting server with a graceful shutdown.
func StartServerWithGracefulShutdown(a *fiber.App) {
	// Create channel for idle connections.
	idleConnsClosed := make(chan struct{})

	go func() {
		sigint := make(chan os.Signal, 1)
		signal.Notify(sigint, os.Interrupt) // Catch OS signals.
		<-sigint

		// Received an interrupt signal, shutdown.
		if err := a.Shutdown(); err != nil {
			// Error from closing listeners, or context timeout:
			log.Printf("Oops... Server is not shutting down! Reason: %v", err)
		}

		close(idleConnsClosed)
	}()
	// Run server.
	if err := a.Listen(config.CFG.Fiber.Url()); err != nil {
		log.Printf("Oops... Server is not running! Reason: %v", err)
	}

	<-idleConnsClosed
}

// StartServer func for starting a simple server.
func StartServer(a *fiber.App) {
	// Run server.
	if err := a.Listen(config.CFG.Fiber.Url()); err != nil {
		log.Printf("Oops... Server is not running! Reason: %v", err)
	}
}
