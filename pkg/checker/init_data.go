/**
* <AUTHOR>
* @date 2023-03-09 20:02
* @description
 */

package checker

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/notify"
	"meta/app/ent/tenant"
	"meta/app/entity/config"
	"meta/app/service"
)

func CheckAndInitUser(r *service.UserService, ctx context.Context, userName string, pwd string, saUser bool) (*ent.User, bool) {
	queryUser, _ := r.QueryByUserName(ctx, userName)
	if queryUser == nil {
		u := &ent.User{
			Valid:      true,
			Name:       userName,
			Password:   pwd,
			SuperAdmin: saUser,
		}
		r.Create(ctx, u)
		return u, true
	}
	return queryUser, false
}

func CheckAndInitPermission(r *service.CasbinRuleService, role, project, recourse, httpMethod string, ctx context.Context) {
	rule := &ent.CasbinRule{
		Type: "p",
		Sub:  role,
		Dom:  project,
		Obj:  recourse,
		Act:  httpMethod,
	}
	r.Create(ctx, rule)
}

func CheckAndInitRole(r *service.CasbinRuleService, userName string, role, project string, ctx context.Context) {
	rule := &ent.CasbinRule{
		Type: "g",
		Sub:  userName,
		Dom:  role,
		Obj:  project,
	}
	r.Create(ctx, rule)
}

func CheckAndAddProject(dao *service.Dao, ctx context.Context, code, name string, offline ...bool) *ent.Tenant {
	existTenant, _ := dao.Tenant.Query().Where(tenant.Code(code)).Only(ctx)
	if existTenant == nil {
		t := &ent.Tenant{Name: name, Code: code}
		if len(offline) != 0 && offline[0] {
			t.Offline = true
		}
		existTenant, _ = dao.Tenant.Create().SetItemTenant(t).Save(ctx)
	}
	CheckAndAddNotify(dao, ctx, existTenant)
	return existTenant
}

func CheckAndAddNotify(dao *service.Dao, ctx context.Context, project *ent.Tenant) *ent.Notify {
	existNotify, _ := dao.Notify.Query().Where(notify.TenantID(project.ID)).Only(ctx)
	if existNotify == nil {
		t := &ent.Notify{Name: project.Code + "通知策略", TenantID: &project.ID, SaNotifyEmail: true, SaNotifyPopo: true}
		if config.CFG.Stage.Status == "prod" {
			t.Enabled = true
		}
		existNotify, _ = dao.Notify.Create().SetItemNotify(t).Save(ctx)
	} else {
	}
	return existNotify
}
