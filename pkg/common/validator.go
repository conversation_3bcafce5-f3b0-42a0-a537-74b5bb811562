package common

import (
	"errors"
	"fmt"
	"meta/app/ent"
	"meta/app/entity/netease"
	"net"
	"strings"
)

func ValidateIP(ip string) error {
	result := net.ParseIP(ip)
	if result == nil {
		return errors.New("invalid IP address: " + ip)
	}
	return nil
}

func CheckIp(ip string) error {
	switch {
	case strings.Contains(ip, "/"):
		_, _, err := net.ParseCIDR(ip)
		if err != nil {
			return err
		}
	case strings.Contains(ip, "-") && !strings.Contains(ip, ":"):
		// ipv4 范围
		split := strings.Split(ip, "-")
		if len(split) == 2 {
			start := split[0]
			end := split[1]
			startSplit := strings.Split(start, ".")
			// 结尾简写
			if len(startSplit) == 4 && !strings.Contains(end, ".") {
				end = fmt.Sprintf("%s.%s.%s.%s", startSplit[0], startSplit[1], startSplit[2], end)
			}
			startIp := net.ParseIP(start)
			endIp := net.ParseIP(end)
			if startIp == nil || endIp == nil {
				return errors.New("invalid IP address: " + ip)
			}
		} else {
			return errors.New("invalid IP address: " + ip)
		}
	case strings.Contains(ip, "-") && strings.Contains(ip, ":"):
		// ipv6 范围
		split := strings.Split(ip, "-")
		if len(split) == 2 {
			start := split[0]
			end := split[1]
			if strings.HasSuffix(start, "::") || strings.HasSuffix(end, "::") {
				return fmt.Errorf("invalid IPv6 address: %s，IPv6范围不能以::结尾", ip)
			}
			startSplit := strings.Split(start, ":")
			// 结尾简写
			if len(startSplit) >= 3 && len(startSplit) <= 8 && !strings.Contains(end, ":") {
				startSlice := startSplit[:][:len(startSplit)-1]
				startJoin := strings.Join(startSlice, ":")
				end = fmt.Sprintf("%s:%s", startJoin, end)
			}
			startIp := net.ParseIP(start)
			endIp := net.ParseIP(end)
			if startIp == nil || endIp == nil {
				return errors.New("invalid IP address: " + ip)
			}
		} else {
			return errors.New("invalid IPv6 address: " + ip)
		}
	default:
		tempIp := net.ParseIP(ip)
		if tempIp == nil {
			return errors.New("invalid IP address: " + ip)
		}
	}
	return nil
}

func CheckCasbinRule(rule *ent.CasbinRule) error {
	if rule == nil || (rule.Type != "g" && rule.Type != "p") {
		return errors.New("unknown casbin rule type")
	}
	return nil
}

func CheckEventDetail(detail *netease.GroupEventDetail) error {
	if detail.IP == "" {
		return errors.New("ip不能未空")
	}
	if detail.StartTime.IsZero() {
		return errors.New("开始时间不能为空")
	}
	if detail.EndTime.IsZero() {
		return errors.New("结束时间不能为空")
	}
	return nil
}

func CheckEventDetail2(detail *netease.GroupEventDetail) error {
	if detail.StartTime.IsZero() {
		return errors.New("开始时间不能为空")
	}
	if detail.EndTime.IsZero() {
		return errors.New("结束时间不能为空")
	}
	return nil
}

func CheckWoFang(dragType, ip string) error {
	if dragType != "hd" && dragType != "qy" {
		return errors.New("牵引类型错误，可选值：qy | hd")
	}
	address := net.ParseIP(ip)
	if address == nil {
		return errors.New(ip + ": 格式错误")
	}
	return nil
}
