/**
* <AUTHOR>
* @date 2023-04-11 11:39
* @description
 */

package common

var (
	Prefix       = "DDoS调度平台"
	MailSignData = `
<hr>
<b>游戏安全值班POPO：<a href="https://popo.netease.com/static/html/open_popo.html?ssid=<EMAIL>&sstp=0"><EMAIL></a></br>
<font size="1">此邮件由<a href="https://ddos.nie.netease.com"> DDoS调度平台 </a>自动发出，请勿直接回复</font>
`
	ProcessSuggestion = `
1.检测业务，如无明显影响，无需处理
2.如影响业务，请联系值班 <EMAIL>`

	// nds攻击开始、结束；是否在nds防护群组；攻击开始沃防牵引状态
	NdsPopoTemplate = `{{.Title}}
IP：{{.IP}}
{{- if $.NdsGroup}}
NDS防护：{{- .NdsGroup}}{{.NdsProtect -}}
{{else}}
NDS防护：【未在NDS防护群组中】
{{- end}}
{{- if $.WoFangProtect}}
联通沃防：{{.WoFangProtect}}
{{- end}}
{{- if $.Project}}
项目：{{- $.Project -}}
{{else}}
项目：【未在CMDB中匹配到项目】
{{- end}}
开始时间：{{.StartTime}}
{{- if ne $.EndTime ""}}
结束时间：{{- .EndTime -}}
{{end}}
攻击类型：{{.AttackType}}
告警值：bps {{.AlertBps}} | pps {{.AlertPps}}
{{- if $.MaxAttackBps}}
攻击峰值：bps {{.MaxAttackBps}} | pps {{.MaxAttackPps -}}
{{end}}
{{- if $.MaxCleanBps}}
清洗峰值：bps {{.MaxCleanBps}} | pps {{.MaxCleanPps -}}
{{end}}
处置建议：{{.ProcessSuggestion}}
查看详情：{{.Schema}}://{{.Domain}}/attack/alert/detail/{{.AlertID}}`
	// 沃防牵引成功和失败
	WofangPopoTemplate = `{{.Title}}
IP：{{.IP}}
{{- if $.Project}}
项目：{{- $.Project -}}
{{else}}
项目：【未在CMDB中匹配到项目】
{{- end}}
状态：{{.Status}}
额外信息：{{.Message}}
{{- if $.StartTime}}
开始时间：{{.StartTime}}
{{- end}}
处置建议：{{.ProcessSuggestion}}
查看详情：{{.Schema}}://{{.Domain}}/process/wofang/detail/{{.WofangID}}
关联告警：{{.Schema}}://{{.Domain}}/attack/alert/detail/{{.AlertID}}`

	MatrixPopoTemplate = `{{.Title}}
区域：{{.Region}}
类型：{{.NetType}}
运营商：{{.Isp}}
状态：{{.Status}}
告警值：bps {{.AlertBps}}
{{- if $.MaxAttackBps}}
峰值：bps {{.MaxAttackBps -}}
{{end}}
开始时间：{{.StartTime}}
{{- if ne $.EndTime ""}}
结束时间：{{- .EndTime -}}
{{end}}
处置建议：{{.ProcessSuggestion}}
查看详情：{{.Schema}}://{{.Domain}}/attack/matrix/detail/{{.MatrixAlertID}}
关联策略：{{.Schema}}://{{.Domain}}/protect/matrix/detail/{{.MatrixStrategyID}}`

	NotifyTemplate = `{{.Title}}
状态：{{.Status}}
更多信息：{{.Message}}`
)
