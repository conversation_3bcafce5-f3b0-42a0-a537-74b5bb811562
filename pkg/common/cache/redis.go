/**
* <AUTHOR>
* @date 2023-08-04 17:06
* @description
 */

package cache

import (
	"context"
	"fmt"
	"meta/app/ent"
	"meta/app/entity"
	"meta/app/service"
	"meta/pkg/common"
	"strings"

	"github.com/redis/go-redis/v9"
)

func GetSystemConfigCache(r *redis.Client, service *service.SystemConfigService, ctx context.Context) (*entity.SystemConfig, error) {
	result, _ := r.Exists(ctx, common.NotifyPrefix).Result()
	// 不存在
	if result == 0 {
		all, err := service.Dao.SystemConfig.Query().All(ctx)
		if err != nil {
			fmt.Println(err)
		}
		if len(all) == 0 || err != nil {
			sconfig := &ent.SystemConfig{
				WofangTestIP: "",
				NotifyPhones: &[]string{""},
				NotifyEmails: &[]string{""},
				NotifyScenes: &[]string{""},
				IPWhitelists: &[]string{""},
			}
			service.Create(ctx, sconfig)
		} else {
			data := all[0]
			r.HSet(ctx, common.NotifyPrefix, map[string]string{
				"wofangTestIP": data.WofangTestIP,
				"notifyScenes": strings.Join(*data.NotifyScenes, ","),
				"notifyPhones": strings.Join(*data.NotifyPhones, ","),
				"notifyEmails": strings.Join(*data.NotifyEmails, ","),
				"ipWhitelists": strings.Join(*data.NotifyEmails, ","),
			})
		}
	}

	var systemConfig entity.SystemConfig
	// redis hash => struct
	err := r.HGetAll(ctx, common.NotifyPrefix).Scan(&systemConfig)
	if err != nil {
		return nil, err
	}
	return &systemConfig, nil
}
