/**
* <AUTHOR>
* @date 2022-12-21 11:25
* @description
 */

package common

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	mapset "github.com/deckarep/golang-set"
	"math"
	"meta/app/ent"
	"net"
	"net/netip"
	"slices"
	"strconv"
	"strings"
	"text/template"
	"time"
	"unicode"
)

func ProjectInCMDB(project *ent.Tenant) bool {
	if project == nil || project.ID == 0 || project.Name == "unknown" && project.Code == "unknown" {
		return false
	}
	return true
}

func GenRedisKey(data ...string) string {
	if len(data) == 0 {
		return ""
	}
	return strings.Join(data, ":")
}
func String2LocalTime(str string) time.Time {
	startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", str, time.Local)
	return startTime
}
func IpData2SingleIp(ipCidr string) ([]string, error) {
	var ips []string
	switch {
	// ipv4 cidr
	case strings.Contains(ipCidr, "/") && !strings.Contains(ipCidr, ":"):
		hosts, err := Hosts(ipCidr)
		if err != nil {
			return nil, err
		}
		for _, v := range hosts {
			ips = append(ips, v.String())
		}

	case strings.Contains(ipCidr, "-") && !strings.Contains(ipCidr, ":"):
		// ipv4 范围
		split := strings.Split(ipCidr, "-")
		if len(split) == 2 {
			start := split[0]
			end := split[1]
			starSplit := strings.Split(start, ".")
			if len(starSplit) == 4 && !strings.Contains(end, ".") {
				end = fmt.Sprintf("%s.%s.%s.%s", starSplit[0], starSplit[1], starSplit[2], end)
			}
			startLong, err := IPString2Long(start)
			endLong, err := IPString2Long(end)
			if err != nil {
				return nil, err
			}
			for i := startLong; i <= endLong; i++ {
				ipString, err := Long2IPString(i)
				if err != nil {
					return nil, err
				}
				ips = append(ips, ipString)
			}
		}
	case strings.Contains(ipCidr, ":"):
		// ipv6
		ips = append(ips, ipCidr)
	default:
		// 单个ip
		ips = append(ips, ipCidr)
	}
	return ips, nil
}

func Hosts(cidr string) ([]netip.Addr, error) {
	prefix, err := netip.ParsePrefix(cidr)
	if err != nil {
		panic(err)
	}

	var ips []netip.Addr
	for addr := prefix.Addr(); prefix.Contains(addr); addr = addr.Next() {
		ips = append(ips, addr)
	}
	return ips, nil
}
func Str2Int(data string) int {
	if data == "" {
		return 0
	}
	atoi, err := strconv.Atoi(data)
	if err != nil {
		return 0
	}
	return atoi
}
func Str2Int64(data string) int64 {
	if data == "" {
		return 0
	}
	i, err := strconv.ParseInt(data, 10, 64)
	if err != nil {
		return 0
	}
	return i
}

func IntStr2Ip(data string) string {
	if data == "" {
		return ""
	}
	ipInt, err := strconv.Atoi(data)
	if err != nil {
		return data
	}
	ip, err := Long2IPString(uint(ipInt))
	if err != nil {
		return data
	}
	return ip
}

// IPString2Long 把ip字符串转为数值
func IPString2Long(ip string) (uint, error) {
	b := net.ParseIP(ip).To4()
	if b == nil {
		return 0, errors.New("invalid ipv4 format")
	}

	return uint(b[3]) | uint(b[2])<<8 | uint(b[1])<<16 | uint(b[0])<<24, nil
}

// Long2IPString 把数值转为ip字符串
func Long2IPString(i uint) (string, error) {
	if i > math.MaxUint32 {
		return "", errors.New("beyond the scope of ipv4")
	}

	ip := make(net.IP, net.IPv4len)
	ip[0] = byte(i >> 24)
	ip[1] = byte(i >> 16)
	ip[2] = byte(i >> 8)
	ip[3] = byte(i)

	return ip.String(), nil
}

func MD5(s string) string {
	sum := md5.Sum([]byte(s))
	return hex.EncodeToString(sum[:])
}

// Timestamp2Time 时间戳转RFC3339
func Timestamp2Time(data int64) time.Time {
	return time.Unix(data, 0)
}

var (
	kBase = math.Pow10(3)
	mBase = math.Pow10(6)
	gBase = math.Pow10(9)
	tBase = math.Pow10(12)
	pBase = math.Pow10(15)
	eBase = math.Pow10(18)
)

// Bit2Str 比特数据转换成可读的数据
func Bit2Str(i int64) string {
	input := float64(i)
	switch {
	case input <= 0:
		return ""
	case input < kBase:
		return fmt.Sprintf("%.0fB", input)
	case input >= kBase && input < mBase:
		return fmt.Sprintf("%.2fK", input/kBase)
	case input >= mBase && input < gBase:
		return fmt.Sprintf("%.2fM", input/mBase)
	case input >= gBase && input < tBase:
		return fmt.Sprintf("%.2fG", input/gBase)
	case input >= tBase && input < pBase:
		return fmt.Sprintf("%.2fT", input/tBase)
	case input >= pBase && input < eBase:
		return fmt.Sprintf("%.2fP", input/pBase)
	case input >= eBase:
		return "max attack"
	default:
		return ""
	}
}

// StringSliceEqual 比较两个字符串切片是否相同
func StringSliceEqual(x, y []string) bool {
	if len(x) != len(y) {
		return false
	}
	if (x == nil) != (y == nil) {
		return false
	}
	for i, v := range x {
		if v != y[i] {
			return false
		}
	}
	return true
}

// IntSliceEqual 比较两个int切片是否相同
func IntSliceEqual(x, y []int) bool {
	if len(x) != len(y) {
		return false
	}
	if (x == nil) != (y == nil) {
		return false
	}
	for i, v := range x {
		if v != y[i] {
			return false
		}
	}
	return true
}

// Snake converts the given struct or field name into a snake_case.
//
//	Username => username
//	FullName => full_name
//	HTTPCode => http_code
func Snake(s string) string {
	var (
		j int
		b strings.Builder
	)
	for i := 0; i < len(s); i++ {
		r := rune(s[i])
		// Put '_' if it is not a start or end of a word, current letter is uppercase,
		// and previous is lowercase (cases like: "UserInfo"), or next letter is also
		// a lowercase and previous letter is not "_".
		if i > 0 && i < len(s)-1 && unicode.IsUpper(r) {
			if unicode.IsLower(rune(s[i-1])) ||
				j != i-1 && unicode.IsLower(rune(s[i+1])) && unicode.IsLetter(rune(s[i-1])) {
				j = i
				b.WriteString("_")
			}
		}
		b.WriteRune(unicode.ToLower(r))
	}
	return b.String()
}

func ParseTemplate(name, notifyTemplate string, data any) (string, error) {
	tPopo := template.New(name)
	tPopo = template.Must(tPopo.Parse(notifyTemplate))
	var buf bytes.Buffer
	err := tPopo.Execute(&buf, data)
	if err != nil {
		return "", err
	}
	content := buf.String()
	return content, nil
}

func ConcatString(str ...string) string {
	var content strings.Builder
	for _, v := range str {
		content.WriteString(v)
	}
	return content.String()
}

func SortRemoveDuplElement(targetSlice []int) (resultSlice []int) {
	set := mapset.NewSet()
	for _, v := range targetSlice {
		set.Add(v)
	}
	for val := range set.Iterator().C {
		resultSlice = append(resultSlice, val.(int))
	}
	slices.Sort(resultSlice)
	return
}
