/**
* <AUTHOR>
* @date 2023-04-06 16:19
* @description
 */

package project

import (
	"context"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqljson"
	"github.com/redis/go-redis/v9"
	"meta/app/controller"
	"meta/app/ent"
	"meta/app/ent/systemapi"
	"meta/app/entity/netease"
	"meta/app/service"
	"meta/pkg/checker"
	"meta/pkg/common"
	"strings"
)

func GetProjectByCodeOrIP(code, ip string, ctx context.Context, ndsController *controller.NdsController) *ent.Tenant {
	// 有项目code
	if code != "" && strings.ToLower(code) != "unknown" {
		cacheProject := ndsController.ProjectCtl.GetAuthProjectCache(code, ctx)
		if common.ProjectInCMDB(cacheProject) {
			return cacheProject
		}
		return nil
	} else {
		if ip != "" {
			realProject := ndsController.ProjectCtl.GetRealProject(ctx, ip)
			if realProject != nil {
				return realProject
			}
		}
	}
	return nil
}

func UpdateProjectPermission(rdb *redis.Client, a *service.TenantService, b *service.CasbinRuleService, ctx context.Context, projects []netease.V1Project) {
	allExcludeSaApis, _ := a.Dao.SystemApi.Query().Where(systemapi.Sa(false)).All(ctx)
	for _, v := range projects {
		// 项目代号
		authProjectCode := strings.ToLower(v.Code)
		// 项目名称
		authProjectName := v.Name
		cacheKey := common.GenRedisKey("project", "auth", authProjectCode)
		var cacheProject ent.Tenant
		rdb.HGetAll(ctx, cacheKey).Scan(&cacheProject)

		//项目已下线
		if cacheProject.Offline {
			continue
		}
		// 新增项目
		checker.CheckAndAddProject(a.Dao, ctx, authProjectCode, authProjectName)
		// 增加项目所有角色
		for _, api := range allExcludeSaApis {
			if api.Roles != nil {
				roles := *api.Roles
				for _, role := range roles {
					checker.CheckAndInitPermission(b, role, authProjectCode, api.Path, api.HTTPMethod, ctx)
				}
			}
		}
	}
}

func UpdateUserPermission(rdb *redis.Client, a *service.TenantService, b *service.CasbinRuleService, ctx context.Context, groups []netease.V1Group, userName string) {
	// 其他用户
	// 普通用户，查看，查询
	// 项目sa，额外加编辑权限
	// publicApis, _ := a.SystemApiService.Dao.SystemApi.Query().Where(systemapi.Public(true)).All(ctx)
	// 查询，查看，查看详情
	queryViewApis, _ := a.Dao.SystemApi.Query().Where(systemapi.HTTPMethod("GET"), systemapi.Sa(false), func(s *sql.Selector) {
		s.Where(sqljson.StringContains(systemapi.FieldRoles, "view"))
	}).All(ctx)
	// 编辑
	editApis, _ := a.Dao.SystemApi.Query().Where(systemapi.HTTPMethod("PUT"), systemapi.Sa(false), func(s *sql.Selector) {
		s.Where(sqljson.StringContains(systemapi.FieldRoles, "edit"))
	}).All(ctx)
	for _, v := range groups {
		// 项目代号
		authProjectCode := strings.ToLower(v.Project)
		// 用户组代号，如sesa.sa
		code := v.Code
		if authProjectCode == "" {
			continue
		}
		cacheKey := common.GenRedisKey("project", "auth", authProjectCode)
		var cacheProject ent.Tenant
		rdb.HGetAll(ctx, cacheKey).Scan(&cacheProject)
		//项目已下线
		if cacheProject.Offline {
			continue
		}
		// 通用资源权限，可以根据注册的api来实现
		//for _, v := range publicApis {
		//	checker.CheckAndInitPermission(a.CasbinRuleService, "query", project, v.Path, v.HTTPMethod, ctx)
		//	// 下面给查询权限了，这里不需要再给
		//}

		// 项目sa和普通用户都需要：查询，查看，查看详情
		UpdateUserRole(queryViewApis, b, userName, authProjectCode, ctx)
		// 项目sa，增加：编辑权限
		if strings.Contains(code, ".sa") {
			UpdateUserRole(editApis, b, userName, authProjectCode, ctx)
		}
	}
}

func UpdateUserRole(queryViewApis []*ent.SystemApi, casbinRuleService *service.CasbinRuleService, userName string, project string, ctx context.Context) {
	for _, v := range queryViewApis {
		roles := *v.Roles
		for _, role := range roles {
			checker.CheckAndInitRole(casbinRuleService, userName, role, project, ctx)
		}
	}
}
