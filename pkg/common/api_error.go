/**
* <AUTHOR>
* @date 2023-06-06 16:32
* @description
 */

package common

import (
	"fmt"
	"strconv"
)

func StatusCodeNotEqual200(apiName string, data any) error {
	var msg string
	switch data.(type) {
	case string:
		msg = data.(string)
	case int:
		msg = strconv.Itoa(data.(int))
	case []byte:
		msg = string(data.([]byte))
	}
	return fmt.Errorf("%s api: status code not equals 200, error: %v", apiName, msg)
}
