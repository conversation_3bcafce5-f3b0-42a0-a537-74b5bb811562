/**
* <AUTHOR>
* @date 2022-06-30 22:56
* @description
 */

package middleware

import (
	"context"
	"github.com/redis/go-redis/v9"
	"meta/app/ent/systemapi"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/app/service"
	"meta/pkg/auth"
	"meta/pkg/checker"
	"meta/pkg/common"
	"meta/pkg/common/project"
	"meta/pkg/http"
	"meta/pkg/jwt"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/google/wire"
	"go.uber.org/zap"
)

var JWTSet = wire.NewSet(wire.Struct(new(JWT), "*"))

type JWT struct {
	Logger            *zap.Logger
	UserService       *service.UserService
	TenantService     *service.TenantService
	CasbinRuleService *service.CasbinRuleService
	SystemApiService  *service.SystemApiService
	AuthEnt           *auth.Entx
	Rdb               *redis.Client
}

func (a *JWT) AuthJWT() fiber.Handler {
	return func(c *fiber.Ctx) error {
		authorization := c.Get("Authorization")
		// fmt.Println("jwt authorization ", authorization)
		if authorization == "" {
			accessToken := c.Get("X-Access-Token")
			if accessToken == "" {
				accessToken = c.Cookies("ACCESS_TOKEN")
			}
			if accessToken == "" {
				return common.NewErrorWithStatusCode(c, "Permission denied", fiber.StatusUnauthorized)
			}
			headerMap := map[string]string{"X-Access-Token": accessToken}
			// 用户名拼音
			userName, err := http.ValidateToken(accessToken, headerMap)
			if err != nil {
				return common.NewErrorWithStatusCode(c, "Permission denied", fiber.StatusUnauthorized)
			}

			ctx := a.AuthEnt.GetSAContext()

			queryUser, _ := a.UserService.QueryByUserName(ctx, userName)
			// 增加处理权限字段
			if queryUser == nil || (queryUser.Valid && queryUser.UpdateAuth) {
				v1UserInfo, err := http.GetAuthV1UserInfo(userName, headerMap)
				if err != nil {
					return common.NewErrorWithStatusCode(c, "Permission denied", fiber.StatusUnauthorized)
				}
				// 用户和项目权限
				groups := v1UserInfo.Groups
				// 项目
				projects := v1UserInfo.Projects

				if len(groups) == 0 {
					a.Logger.Sugar().Infof("%s 未在auth找到用户群组", userName)
					return common.NewErrorWithStatusCode(c, "未在auth找到用户群组", fiber.StatusForbidden)
				}
				if len(projects) == 0 {
					a.Logger.Sugar().Infof("%s 未在auth找到项目", userName)
					return common.NewErrorWithStatusCode(c, "未在auth找到项目", fiber.StatusForbidden)
				}
				// 筛选非sa的系统api，增加crud权限
				// 增加项目所有非sa操作的角色权限
				go project.UpdateProjectPermission(a.Rdb, a.TenantService, a.CasbinRuleService, ctx, projects)

				var saUser bool
				go func() {
					if userName == "liangkan" {
						saUser = true
						// 增加角色权限
						checker.CheckAndInitRole(a.CasbinRuleService, userName, "query", "sesa", ctx)
						// 增加资源权限，sesa 增加全部权限 /*
						checker.CheckAndInitPermission(a.CasbinRuleService, "query", "sesa", "/*", "GET", ctx)
					}
				}()

				// 增加用户权限
				go project.UpdateUserPermission(a.Rdb, a.TenantService, a.CasbinRuleService, ctx, groups, userName)
				createUser, create := checker.CheckAndInitUser(a.UserService, ctx, userName, "", saUser)
				if queryUser == nil && create {
					queryUser = createUser
				}

				if queryUser != nil {
					queryUser.UpdateAuth = false
					a.UserService.UpdateByID(ctx, queryUser, queryUser.ID)
				}
			}

			// return common.NewErrorWithStatusCode(c, "Permission denied", fiber.StatusUnauthorized)
			jwtConfig := config.CFG.Auth.JWT
			authorization, _ = jwt.CreateJWT("", queryUser.Name, jwtConfig.Key, jwtConfig.TTL, jwtConfig.Hmac)
			c.Locals("Authorization", authorization)
		}
		claims, err := jwt.ParseJWT(authorization, config.CFG.Auth.JWT.Key)
		if err != nil {
			a.Logger.Sugar().Error(err)
			return common.NewErrorWithStatusCode(c, err.Error(), fiber.StatusUnauthorized)
		}
		project := c.Get("Auth-Project")
		casbinUser := &entity.CasbinUser{
			UserName: claims.Username,
			Project:  project,
		}
		c.Locals("casbinUser", casbinUser)
		return c.Next()
	}
}

func funcName(a *JWT, ctx context.Context, projects []netease.V1Project) {
	allExcludeSaApis, _ := a.SystemApiService.Dao.SystemApi.Query().Where(systemapi.Sa(false)).All(ctx)
	for _, v := range projects {
		// 项目代号
		authProjectCode := strings.ToLower(v.Code)
		// 项目名称
		authProjectName := v.Name
		// 新增项目
		checker.CheckAndAddProject(a.TenantService.Dao, ctx, authProjectCode, authProjectName)
		// 增加项目所有角色
		for _, api := range allExcludeSaApis {
			if api.Roles != nil {
				roles := *api.Roles
				for _, role := range roles {
					checker.CheckAndInitPermission(a.CasbinRuleService, role, authProjectCode, api.Path, api.HTTPMethod, ctx)
				}
			}
		}
	}
}

func (a *JWT) SaJWT() fiber.Handler {
	return func(c *fiber.Ctx) error {
		jwtConfig := config.CFG.Auth.JWT
		authorization, _ := jwt.CreateJWT("", config.CFG.Stage.User, jwtConfig.Key, jwtConfig.TTL, jwtConfig.Hmac)
		c.Locals("Authorization", authorization)
		return c.Next()
	}
}
