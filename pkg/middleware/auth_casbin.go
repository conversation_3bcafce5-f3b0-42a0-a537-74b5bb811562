package middleware

import (
	"fmt"
	"meta/app/ent/user"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/service"
	"meta/pkg/auth"
	"meta/pkg/common"
	"strings"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/casbin/casbin/v2"
	"github.com/gofiber/fiber/v2"
)

var CasbinxSet = wire.NewSet(wire.Struct(new(Casbinx), "*"))

type Casbinx struct {
	UserService *service.UserService
	Logger      *zap.Logger
	AuthEnt     *auth.Entx
	Enf         *casbin.Enforcer
}

func (b *Casbinx) AuthCasbin(enf *casbin.Enforcer) fiber.Handler {
	return func(c *fiber.Ctx) error {
		obj := string(c.Request().RequestURI())
		act := c.Method()
		casbinUser := c.Locals("casbinUser").(*entity.CasbinUser)

		project := casbinUser.Project
		username := casbinUser.UserName

		// sa用户不需要经过casbin
		adminCtx := b.AuthEnt.GetSAContext()
		u, err := b.UserService.Dao.User.Query().Where(user.Name(username)).Only(adminCtx)
		if u == nil {
			return common.NewErrorWithStatusCode(c, "User invalid", fiber.StatusForbidden)
		}
		// sa用户且有效
		if u.Valid && u.SuperAdmin {
			return c.Next()
		}

		// 未带Auth-Project头，从casbin表中获取一个项目给用户
		// 获取当前用户的域（租户/项目）
		if project == "" {
			userDomains, _ := b.Enf.GetDomainsForUser(username)
			fmt.Println("userDomains ", userDomains)
			if len(userDomains) > 0 {
				project = userDomains[0]
				casbinUser := &entity.CasbinUser{
					UserName: username,
					Project:  project,
				}
				c.Locals("casbinUser", casbinUser)
			}
		}
		//
		fmt.Println("xxx ")
		fmt.Println(username)
		fmt.Println(project)
		fmt.Println(obj)
		fmt.Println(act)

		if strings.Contains(obj, "?") {
			obj = strings.Split(obj, "?")[0]
		}

		ok, _ := enf.Enforce(username, project, obj, act)

		if err != nil || !u.Valid {
			if err == nil {
				b.Logger.Sugar().Errorf("User: %s is invalid", username)
			} else {
				b.Logger.Sugar().Error(err)
			}
			return common.NewErrorWithStatusCode(c, "User invalid", fiber.StatusForbidden)
		}

		if ok {
			return c.Next()
		} else {
			return common.NewErrorWithStatusCode(c, "Access denied", fiber.StatusForbidden)
		}
	}
}

func (b *Casbinx) SaCasbin() fiber.Handler {
	return func(c *fiber.Ctx) error {
		project := "all"
		casbinUser := &entity.CasbinUser{
			UserName: config.CFG.Stage.User,
			Project:  project,
		}
		c.Locals("casbinUser", casbinUser)
		return c.Next()
	}
}
