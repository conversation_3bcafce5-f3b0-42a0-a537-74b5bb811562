/**
* <AUTHOR>
* @date 2022-06-30 22:56
* @description
 */

package middleware

import (
	"github.com/gofiber/fiber/v2"
)

func SetSecurityHeader() fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Set some security headers:
		c.Set("X-XSS-Protection", "1; mode=block")
		c.Set("X-Content-NetType-Options", "nosniff")
		c.Set("X-Download-Options", "noopen")
		c.Set("Strict-Transport-Security", "max-age=5184000")
		c.Set("X-Frame-Options", "SAMEORIGIN")
		c.Set("X-DNS-Prefetch-Control", "off")
		return c.Next()
	}
}
