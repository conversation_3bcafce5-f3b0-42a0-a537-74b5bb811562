package generator

import (
	"errors"
	"meta/app/ent"
	"meta/pkg/auth"
	"meta/pkg/common"

	"github.com/google/wire"

	"github.com/gofiber/fiber/v2"
)

var Set = wire.NewSet(wire.Struct(new(Gen), "*"))

type Gen struct {
	AuthEnt *auth.Entx
}

func (a *Gen) IntId() fiber.Handler {
	return func(c *fiber.Ctx) error {
		id, err := c.ParamsInt("id")
		if err != nil {
			return common.NewResult(c, err)
		}
		c.Locals("id", id)
		return c.Next()
	}
}

func (a *Gen) AuthCtx() fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := a.AuthEnt.GetContext(c)
		if ctx == nil {
			return common.NewErrorWithStatusCode(c, "Access denied", fiber.StatusForbidden)
		}
		c.Locals("ctx", ctx)
		return c.Next()
	}
}

func (a *Gen) SaCtx() fiber.Handler {
	return func(c *fiber.Ctx) error {
		ctx := a.AuthEnt.GetSAContext()
		c.Locals("ctx", ctx)
		return c.Next()
	}
}

func (a *Gen) CheckExpendIP() fiber.Handler {
	return func(c *fiber.Ctx) error {
		protectGroup := &ent.ProtectGroup{}
		err := c.QueryParser(protectGroup)
		if err != nil {
			return common.NewResult(c, err)
		}
		if protectGroup.ExpandIP == "" {
			return common.NewResult(c, errors.New("IP为空"))
		}
		return c.Next()
	}
}
