package middleware

import (
	"meta/app/ent"
	"meta/app/entity"
	"meta/app/service"
	"meta/pkg/auth"
	"net/url"

	"github.com/gofiber/fiber/v2"
	"github.com/google/wire"
	"go.uber.org/zap"
)

var OPLogSet = wire.NewSet(wire.Struct(new(OPLog), "*"))

type OPLog struct {
	UserOperationLogService *service.UserOperationLogService
	Logger                  *zap.Logger
	AuthEnt                 *auth.Entx
}

func (luo *OPLog) LogUserOperation() fiber.Handler {
	return func(c *fiber.Ctx) error {
		go func() {
			user := c.Locals("casbinUser")
			casbinUser := user.(*entity.CasbinUser)
			decodedURI, err := url.QueryUnescape(string(c.Request().RequestURI()))
			if err != nil {
				luo.Logger.Sugar().Error(err)
			}
			op := &ent.UserOperationLog{
				Username:    casbinUser.UserName,
				Project:     casbinUser.Project,
				RequestID:   c.Get<PERSON>Header("X-Request-Id"),
				URI:         decodedURI,
				RequestBody: string(c.Request().Body()),
				Method:      c.Method(),
			}
			// fmt.Println("xx ", c.IP())
			ctx := luo.AuthEnt.GetSAContext()
			_, err = luo.UserOperationLogService.Create(ctx, op)
			if err != nil {
				luo.Logger.Sugar().Error(err)
			}
		}()
		return c.Next()
	}
}
