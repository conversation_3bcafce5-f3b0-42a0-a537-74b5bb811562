/**
* <AUTHOR>
* @date 2022-12-28 12:12
* @description
 */

package http

import (
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"strconv"
	"sync"

	"github.com/bytedance/sonic"
)

// GetMatrixProject 根据ip获取项目信息
func GetMatrixProject(ip string, headerMap map[string]string) (*netease.MatrixIPDbResponse, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Matrix.IPDb + "/ipdb/api/v1/ipv4/ipaddress", HttpMethod: "GET", UrlData: "address=" + ip, Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("matrix ipdb", body)
	}
	result := &netease.MatrixIPDbResponse{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetMatrixFlow 获取出口流量
func GetMatrixFlow(headerMap map[string]string, queryString ...string) (*netease.MatrixFlowResponse, error) {
	query := "nienet_interface_byte_in_sec{_source_project_=\"netmonitor\",_share_project_=\"sesa\"}"
	if len(queryString) != 0 {
		query = queryString[0]
	}
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Matrix.Flow + "/prometheus/api/v1/query", HttpMethod: "GET", UrlData: "query=" + query, Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("matrix ipdb", body)
	}
	result := &netease.MatrixFlowResponse{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetMatrixLineFlow 获取出口线路聚合流量
// queryTypes: "bps", "pps", "ratio"
// 默认获取bps数据
// func GetMatrixLineFlow(headerMap map[string]string, resultStrChan chan string, matrixLineChan chan *netease.MatrixLine, queryTypes ...string) (resultStrs []string, matrixLineResult []*netease.MatrixLine) {
func GetMatrixLineFlow(headerMap map[string]string, queryTypes ...string) (resultStrs []string, matrixLineResult []*netease.MatrixLine) {
	matrixLineQuery := netease.MatrixLineQuery{
		ProjectCode:  "netmonitor",
		ShareProject: "sesa",
		Source:       "matrix",
		Nocache:      1,
	}
	var (
		queryBps   bool
		queryPps   bool
		queryRatio bool
	)
	if len(queryTypes) == 0 {
		queryBps = true
	}
	for _, queryType := range queryTypes {
		switch queryType {
		case "bps":
			queryBps = true
		case "pps":
			queryPps = true
		case "ratio":
			queryRatio = true
		}
	}
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		if queryBps {
			matrixLineQuery.Query = "nienet_line_bit_in_sec"
			queryData, err := QueryPrometheusData(headerMap, matrixLineQuery)
			if err != nil {

			}
			marshalString, err := sonic.MarshalString(queryData)
			//resultStrChan <- marshalString
			resultStrs = append(resultStrs, marshalString)

			if queryData != nil && queryData.Status == "success" {
				results := queryData.Data.Result
				for _, result := range results {
					matrixLineData := parseMatrixLineData(result, "bps")
					//matrixLineChan <- matrixLineData

					matrixLineResult = append(matrixLineResult, matrixLineData)
				}
			}
		}
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		if queryPps {
			matrixLineQuery.Query = "nienet_line_pack_in_sec"
			queryData, err := QueryPrometheusData(headerMap, matrixLineQuery)
			if err != nil {
			}
			marshalString, err := sonic.MarshalString(queryData)
			//resultStrChan <- marshalString
			resultStrs = append(resultStrs, marshalString)

			if queryData != nil && queryData.Status == "success" {
				results := queryData.Data.Result
				for _, result := range results {
					matrixLineData := parseMatrixLineData(result, "pps")
					//matrixLineChan <- matrixLineData
					matrixLineResult = append(matrixLineResult, matrixLineData)

				}
			}
		}
	}()
	wg.Add(1)
	go func() {
		defer wg.Done()
		if queryRatio {
			matrixLineQuery.Query = "nienet_line_ratio_in"
			queryData, err := QueryPrometheusData(headerMap, matrixLineQuery)
			if err != nil {
			}
			marshalString, err := sonic.MarshalString(queryData)
			//resultStrChan <- marshalString
			resultStrs = append(resultStrs, marshalString)

			if queryData != nil && queryData.Status == "success" {
				results := queryData.Data.Result
				for _, result := range results {
					matrixLineData := parseMatrixLineData(result, "ratio")
					//matrixLineChan <- matrixLineData
					matrixLineResult = append(matrixLineResult, matrixLineData)

				}
			}
		}
	}()

	wg.Wait()
	//close(matrixLineChan)
	//close(resultStrChan)
	return
}

func QueryPrometheusData(headerMap map[string]string, matrixLineQuery netease.MatrixLineQuery) (*netease.MatrixLineQueryRawResponse, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Matrix.Flow + "/prometheus/api/v1/query", HttpMethod: "POST", Header: headerMap, BodyData: matrixLineQuery}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("matrix line query", body)
	}
	result := &netease.MatrixLineQueryRawResponse{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func parseMatrixLineData(result netease.MatrixMetricResult, dataType string) (matrixLineData *netease.MatrixLine) {
	bitOrPackInSec, err := strconv.ParseInt(result.Value[1].(string), 10, 64)
	queryTime := common.Timestamp2Time(int64(result.Value[0].(float64)))
	if err != nil {
		bitOrPackInSec = 0
	}
	matrixLineData = &netease.MatrixLine{
		Isp:     result.Metric.Isp,
		Region:  result.Metric.Region,
		NetType: result.Metric.Type,
	}
	switch dataType {
	case "bps":
		matrixLineData.DataType = "bps"
		bps := &netease.MatrixBps{
			QueryTime: queryTime,
			Bps:       bitOrPackInSec,
		}
		matrixLineData.Data = *bps
	case "pps":
		matrixLineData.DataType = "pps"
		pps := &netease.MatrixPps{
			QueryTime: queryTime,
			Pps:       bitOrPackInSec,
		}
		matrixLineData.Data = *pps
	case "ratio":
		matrixLineData.DataType = "ratio"
		queryRatio, err := strconv.ParseFloat(result.Value[1].(string), 64)
		if err != nil {
			queryRatio = 0
		}
		ratio := &netease.MatrixRatio{
			QueryTime: queryTime,
			Ratio:     queryRatio,
		}
		matrixLineData.Data = *ratio
	}
	return
}
