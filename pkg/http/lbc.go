/**
* <AUTHOR>
* @date 2022-12-28 12:12
* @description
 */

package http

import (
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"

	"github.com/bytedance/sonic"
)

// GetLbcProject 根据ip获取项目信息
func GetLbcProject(ip string, headerMap map[string]string) (*netease.LbcProjectResult, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Lbc + "/virtualip", HttpMethod: "GET", UrlData: "@" + ip + "/project", Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("lbc", body)
	}
	projectResult := &netease.LbcProjectResult{}
	err = sonic.Unmarshal(body, &projectResult)
	if err != nil {
		return nil, err
	}
	return projectResult, nil
}
