/**
* <AUTHOR>
* @date 2022-12-22 16:13
* @description
 */

package http

import (
	"fmt"
	"github.com/bytedance/sonic"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"strings"
)

func Notify(content string, receiveList []string, ccList []string, sendType string, subject ...string) error {
	if len(receiveList) == 0 && ccList == nil {
		return nil
	}
	headerMap := map[string]string{"X-Notify-AccessKey": config.CFG.XAuth.NotifyToken}
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Notify + "/messages", HttpMethod: "POST", Header: headerMap}
	if sendType == "popo" {
		popo := netease.NewPopo()
		popo.Content = content
		popo.ReceiverList = receiveList
		httpData.BodyData = popo
	}
	if sendType == "sms" {
		sms := netease.NewSms()
		sms.Content = content
		sms.ReceiverList = receiveList
		httpData.BodyData = sms
	}
	if sendType == "mail" {
		email := netease.NewEmail()
		email.ReceiverList = receiveList
		if ccList != nil && len(ccList) != 0 {
			email.Cc = ccList
		}
		if len(subject) != 0 {
			//Title
			email.Subject = subject[0]
		}
		if len(subject) == 2 {
			//sign data
			signStr := subject[1]
			all := strings.ReplaceAll(content, "\n", "</br>")
			email.Content = fmt.Sprintf("%s\n%s", all, signStr)
		}

		httpData.BodyData = email
	}
	if sendType == "phone" {
		phone := netease.NewPhone()
		phone.Content = content
		phone.ReceiverList = receiveList
		httpData.BodyData = phone
	}

	body, _, err := DoRequest(httpData)
	if err != nil {
		return err
	}
	result := &netease.ResultNotify{}
	err = sonic.Unmarshal(body, result)
	if err != nil {
		return err
	}
	if result.Code != 200 {
		return common.StatusCodeNotEqual200("notify", body)

	}
	return nil
}

func NotifySms(content string, list []string) error {
	return Notify(content, list, nil, "sms")
}
func NotifyPhone(content string, list []string) error {
	return Notify(content, list, nil, "phone")
}

// popo私信和popo群是一样的，只是收件人不一样
func NotifyPopo(content string, list []string) error {
	return Notify(content, list, nil, "popo")
}

func NotifyEmail(content string, receiveList []string, ccList []string, subject ...string) error {
	return Notify(content, receiveList, ccList, "mail", subject...)
}
