/**
* <AUTHOR>
* @date 2022-12-27 12:09
* @description
 */

package http

import (
	"errors"
	"fmt"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"time"

	"github.com/bytedance/sonic"
)

// GetProtectGroupStrategy 获取防护群组的策略
func GetProtectGroupStrategy(groupId int64) (*netease.GroupStrategyDataResponse, error) {
	timestamp, sign := LoadNdsSign()
	reqData := &entity.ReqData{Data: netease.GroupStrategy{GroupId: groupId}, Sign: sign, Timestamp: timestamp}
	httpData := &entity.HttpData{Api: config.CFG.External.Nds.Api + "/group/queryStragegy.json", HttpMethod: "POST", BodyData: reqData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	strategyResponse := &netease.GroupStrategyDataResponse{}
	if resp.StatusCode == 200 {
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &strategyResponse)
		if err != nil {
			return nil, err
		}
		return strategyResponse, nil
	}
	return nil, common.StatusCodeNotEqual200("nds", resp.StatusCode)
}

// GetProtectGroupIpList 获取防护群组的ip列表
func GetProtectGroupIpList(groupId int64, groupType int) (*netease.GroupIpDataResponse, error) {
	timestamp, sign := LoadNdsSign()
	reqData := &entity.ReqData{Data: netease.GroupQueryType{GroupId: groupId, Type: groupType}, Sign: sign, Timestamp: timestamp}
	httpData := &entity.HttpData{Api: config.CFG.External.Nds.Api + "/group/getIpList.json", HttpMethod: "POST", BodyData: reqData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	dataResponse := &netease.GroupIpDataResponse{}
	if resp.StatusCode == 200 {
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &dataResponse)
		if err != nil {
			return nil, err
		}
		return dataResponse, nil
	}
	return nil, fmt.Errorf("status code %v", resp.StatusCode)
}

func GetGroupDataAll(ip ...string) ([]netease.GroupDTO, error) {
	var groupList []netease.GroupDTO
	typeList := []int{1, 2}
	for index, tv := range typeList {
		// 获取总大小
		totalData, err := GetGroupDataByPage(tv, 0, 1)
		if err != nil {
			return nil, err
		}
		total := totalData.Total
		fmt.Printf("GKL:type: %d total is %d\n", index, total)
		// 分页获取数据
		pageSize := 100
		pageStep := total / pageSize
		for i := 0; i <= pageStep; i++ {
			var (
				datas *netease.GroupResponse
				err   error
			)
			if ip != nil {
				datas, err = GetGroupDataByPage(tv, i+1, pageSize, ip...)
			} else {
				datas, err = GetGroupDataByPage(tv, i+1, pageSize)
				fmt.Printf("GKL:type:%d, 第 %d 轮返回 %+v \n", tv, i, datas)
			}
			if err != nil {
				return nil, err
			}
			if datas != nil && len(datas.Data) != 0 {
				for _, v := range datas.Data {
					groupDTO := &netease.GroupDTO{GroupId: v.GroupId, GroupName: v.GroupName, GroupType: tv}
					groupList = append(groupList, *groupDTO)
				}
			}
		}

	}

	return groupList, nil
}

func GetGroupDataByPage(groupType, page, pageSize int, ip ...string) (*netease.GroupResponse, error) {
	timestamp, sign := LoadNdsSign()
	groupQuery := netease.GroupQuery{Type: groupType, Page: page, PageSize: pageSize}
	if ip != nil {
		groupQuery.Ip = ip[0]
	}
	reqData := &entity.ReqData{Data: groupQuery, Sign: sign, Timestamp: timestamp}
	httpData := &entity.HttpData{Api: config.CFG.External.Nds.Api + "/group/query.json", HttpMethod: "POST", BodyData: reqData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode == 200 {
		dataResponse := &netease.GroupResponse{}
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &dataResponse)
		if err != nil {
			return nil, err
		}
		if dataResponse.Code != 0 {
			return nil, errors.New(dataResponse.Msg)
		}
		return dataResponse, nil
	}
	return nil, common.StatusCodeNotEqual200("nds", resp.StatusCode)
}

func GetSpectrumAlert(startTime, endTime time.Time) ([]netease.JsonAlert, error) {
	urlData := fmt.Sprintf("startTime=%v&endTime=%v&signature=%s", startTime.Unix(), endTime.Unix(), config.CFG.External.Nds.OldApikey)
	httpData := &entity.HttpData{Api: config.CFG.External.Nds.OldApi + "/getAttackData.json", HttpMethod: "GET", UrlData: urlData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode == 200 {
		var jsonAlerts []netease.JsonAlert
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &jsonAlerts)
		if err != nil {
			return nil, err
		}
		return jsonAlerts, nil
	}
	return nil, common.StatusCodeNotEqual200("nds", resp.StatusCode)
}

func GetSpectrumData(ip string, startTime, endTime time.Time) ([]netease.GroupSpectrumData, error) {
	timestamp, sign := LoadNdsSign()
	rEventDetail := &netease.GroupRequestEventDetail{IP: ip, StartTime: startTime.Unix(), EndTime: endTime.Unix()}
	reqData := &entity.ReqData{Data: rEventDetail, Sign: sign, Timestamp: timestamp}
	httpData := &entity.HttpData{Api: config.CFG.External.Nds.Api + "/monitorData/listIpData.json", HttpMethod: "POST", BodyData: reqData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode == 200 {
		spectrumDataResponse := &netease.GroupSpectrumDataResponse{}
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &spectrumDataResponse)
		if err != nil {
			return nil, err
		}
		if spectrumDataResponse.Code != 0 {
			return nil, fmt.Errorf("code not equlal to 0: %d, message: %s", spectrumDataResponse.Code, spectrumDataResponse.Msg)
		}
		return spectrumDataResponse.Data, nil
	}
	return nil, common.StatusCodeNotEqual200("nds", resp.StatusCode)
}

func GetCleanData(ip string, startTime, endTime time.Time) ([]netease.GroupCleanData, error) {
	timestamp, sign := LoadNdsSign()
	rEventDetail := &netease.GroupRequestEventDetail{IP: ip, StartTime: startTime.Unix(), EndTime: endTime.Unix()}
	reqData := &entity.ReqData{Data: rEventDetail, Sign: sign, Timestamp: timestamp}
	httpData := &entity.HttpData{Api: config.CFG.External.Nds.Api + "/monitorData/listFilterData.json", HttpMethod: "POST", BodyData: reqData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode == 200 {
		cleanDataResponse := &netease.GroupCleanDataResponse{}
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &cleanDataResponse)
		if err != nil {
			return nil, err
		}
		if cleanDataResponse.Code != 0 {
			return nil, fmt.Errorf("code not equlal to 0: %d, message: %s", cleanDataResponse.Code, cleanDataResponse.Msg)
		}
		return cleanDataResponse.Data, nil
	}
	return nil, common.StatusCodeNotEqual200("nds", resp.StatusCode)
}

// GetRealTimeSpectrumData 获取IP指定时间范围的分光数据（实时）
func GetRealTimeSpectrumData(ip string, startTime, endTime time.Time) ([]netease.GroupSpectrumData, error) {
	urlData := fmt.Sprintf("startTime=%d&endTime=%d&signature=%s&ip=%s", startTime.Unix(), endTime.Unix(), config.CFG.External.Nds.OldApikey, ip)
	httpData := &entity.HttpData{Api: config.CFG.External.Nds.OldApi + "/getIpData.json", HttpMethod: "GET", UrlData: urlData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode == 200 {
		var spectrumDataResponse []netease.GroupSpectrumData
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &spectrumDataResponse)
		if err != nil {
			return nil, err
		}
		return spectrumDataResponse, nil
	}
	return nil, common.StatusCodeNotEqual200("nds", resp.StatusCode)
}
