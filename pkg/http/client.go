/**
* <AUTHOR>
* @date 2022-12-21 11:10
* @description
 */

package http

import (
	"crypto/tls"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"io"
	"log"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"net/http"
	"strings"
	"time"
)

func LoadNdsSign() (int64, string) {
	ndsConfig := config.CFG.External.Nds
	if ndsConfig.Api == "" || ndsConfig.Apikey == "" {
		log.Fatal("load config error")
	}
	apikey := ndsConfig.Apikey
	timestamp := time.Now().UnixMilli()
	sign := common.MD5(fmt.Sprintf("%d%s", timestamp, apikey))
	return timestamp, sign
}

func SendRequest(request *http.Request) (*http.Response, error) {
	//proxyAddr := "http://127.0.0.1:8080"
	//proxy, err := url.Parse(proxyAddr)
	//if err != nil {
	//	log.Fatal(err)
	//}
	//proxy = nil
	netTransport := &http.Transport{
		//Proxy:                 http.ProxyURL(proxy),
		TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
		MaxIdleConnsPerHost:   10,
		ResponseHeaderTimeout: time.Second * time.Duration(5),
	}

	client := http.Client{
		Timeout:   time.Second * 10,
		Transport: netTransport,
	}
	resp, err := client.Do(request)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func GenHttpRequest(c *entity.HttpData) *http.Request {
	if c.Api != "" {
		var body io.Reader
		if c.BodyData != nil {
			// fmt.Println("body data", bodyData)
			marshal, err := sonic.Marshal(c.BodyData)
			if err != nil {
				log.Fatal(err)
			}
			body = strings.NewReader(string(marshal))
		}
		var api string
		if c.UrlData != "" {
			//?拼接的参数
			if strings.Contains(c.UrlData, "=") {
				api = c.Api + "?" + c.UrlData
			} else {
				//restful 参数
				api = c.Api + "/" + c.UrlData
			}
		} else {
			api = c.Api
		}
		req, _ := http.NewRequest(c.HttpMethod, api, body)
		req.Header.Add("Content-Type", "application/json")
		if len(c.Header) != 0 {
			for k, v := range c.Header {
				req.Header.Add(k, v)
			}
		}
		return req
	}
	return nil
}

func DoNdsRequest(api, uri string, reqData *entity.ReqData) (*netease.Result, error) {
	httpData := &entity.HttpData{Api: api + uri, HttpMethod: "POST", BodyData: reqData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode == 200 {
		body := GetResponseBody(resp)
		result := &netease.Result{}
		err := sonic.Unmarshal(body, &result)
		if err != nil {
			return nil, err
		}
		if result.Code != 0 {
			return nil, errors.New(result.Msg)
		}
		return result, nil

	}
	return nil, errors.New(string(rune(resp.StatusCode)))
}

func GetResponseBody(resp *http.Response) []byte {
	body, _ := io.ReadAll(resp.Body)
	err := resp.Body.Close()
	if err != nil {
		return nil
	}
	return body
}

func DoRequest(httpData *entity.HttpData) ([]byte, int, error) {
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if resp == nil {
		if err == nil {
			err = errors.New("nil resp")
		}
		return nil, 0, err
	}
	body := GetResponseBody(resp)
	return body, resp.StatusCode, err
}
