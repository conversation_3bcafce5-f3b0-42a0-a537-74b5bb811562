/**
* <AUTHOR>
* @date 2022-12-28 12:18
* @description
 */

package http

import (
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"

	"github.com/bytedance/sonic"
)

// GetAuthProject 根据code获取项目信息
func GetAuthProject(code string, headerMap map[string]string) (*netease.ProjectResult, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Auth + "/projects", HttpMethod: "GET", UrlData: "@" + code, Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("auth", body)
	}
	projectResult := &netease.ProjectResult{}
	err = sonic.Unmarshal(body, &projectResult)
	if err != nil {
		return nil, err
	}
	return projectResult, nil
}

// GetAuthProjectSaList 根据code获取项目sa列表
func GetAuthProjectSaList(code string, headerMap map[string]string) (*netease.SaResult, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Auth + "/groups", HttpMethod: "GET", UrlData: "@" + code + ".sa/users", Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("auth", body)
	}
	result := &netease.SaResult{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetAuthOfflineProject 获取下线的项目
func GetAuthOfflineProject(headerMap map[string]string) (*netease.OfflineProject, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Auth + "/tags/@stage.offline/projects", HttpMethod: "GET", Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("auth", body)
	}
	result := &netease.OfflineProject{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func GetAuthProjectSaMailList(code string, headerMap map[string]string) ([]string, error) {
	var list []string
	saList, err := GetAuthProjectSaList(code, headerMap)
	if err != nil {
		return nil, err
	}
	items := saList.Items
	for _, v := range items {
		if v.Email != "" {
			list = append(list, v.Email)
		}
	}
	return list, nil
}
func ValidateToken(token string, headerMap map[string]string) (string, error) {
	dataMap := make(map[string]string)
	dataMap["token"] = token
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.AuthV2 + "/authorize", HttpMethod: "POST", BodyData: dataMap, Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return "", err
	}
	if statusCode != 200 {
		return "", common.StatusCodeNotEqual200("auth v2", body)
	}
	result := &netease.V2AuthResponse{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return "", err
	}
	return result.User, nil
}

func GetAuthV1UserInfo(userName string, headerMap map[string]string) (*netease.V1UserInfoResponse, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Auth + "/users/@" + userName + "?_expand=1&_type=null", HttpMethod: "GET", Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("auth", body)
	}
	result := &netease.V1UserInfoResponse{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetAuthAllProject 获取所有项目信息
func GetAuthAllProject(headerMap map[string]string) (*netease.AllProject, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.AuthV2 + "/projects", HttpMethod: "GET", Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("auth v2", body)
	}
	projectResult := &netease.AllProject{}
	err = sonic.Unmarshal(body, &projectResult)
	if err != nil {
		return nil, err
	}
	return projectResult, nil
}
