package http

import (
	"github.com/bytedance/sonic"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease/gamecloud"
	"meta/pkg/common"
)

// GetSkylineDdosAttack 获取AWS Shield 数据-DDoS攻击历史数据
func GetSkylineDdosAttack(urlData string, headerMap map[string]string) (*gamecloud.AwsResponse, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Skyline + "/api/v1/aws/shields/history_events", HttpMethod: "GET", UrlData: urlData, Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("gamecloud skyline dos ", body)
	}
	result := &gamecloud.AwsResponse{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
