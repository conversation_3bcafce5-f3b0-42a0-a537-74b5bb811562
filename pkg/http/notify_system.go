/**
* <AUTHOR>
* @date 2022-12-29 23:39
* @description
 */

package http

import (
	"fmt"
	"golang.org/x/exp/slices"
	"meta/app/ent"
)

func NotifyDefault(notifyConfig *ent.Notify, content, title, signData string) error {
	// 群列表不为空，popo群通知
	if notifyConfig.PopoGroups != nil {
		popoGroups := *notifyConfig.PopoGroups
		if notifyConfig.Popo && len(popoGroups) != 0 && popoGroups[0] != "" {
			fmt.Println("notify default popo groups ", popoGroups)
			err := NotifyPopo(content, popoGroups)
			if err != nil {
				return err
			}
		}
	}

	// 邮件列表不为空，可以进行popo或邮件通知
	if notifyConfig.Emails != nil {
		emails := *notifyConfig.Emails
		if emails[0] != "" {
			if notifyConfig.Popo {
				err := NotifyPopo(content, emails)
				if err != nil {
					return err
				}
			}
			if notifyConfig.Email {
				err := NotifyEmail(content, emails, nil, title, signData)
				if err != nil {
					return err
				}
			}
		}
	}

	// 电话列表不为空，可以进行短信或者电话通知
	if notifyConfig.Phones != nil {
		phones := *notifyConfig.Phones
		if (notifyConfig.Phone || notifyConfig.Sms) && phones[0] != "" {
			if notifyConfig.Sms {
				fmt.Println("notifyConfig default sms")
				err := NotifySms(content, phones)
				if err != nil {
					return err
				}
			}
			if notifyConfig.Phone {
				fmt.Println("notifyConfig default phone")
				err := NotifyPhone(content, phones)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func NotifyProject(notifyProject *ent.Notify, projectCode, content, title, signData string, headerMap map[string]string) error {
	// 通过邮件或popo通知sa列表
	if notifyProject.SaNotifyPopo || notifyProject.SaNotifyEmail {
		if projectCode != "" {
			saMailList, err := GetAuthProjectSaMailList(projectCode, headerMap)
			if err != nil {
				return err
			}
			if notifyProject.SaNotifyPopo {
				fmt.Println("notify popo sa list ", projectCode)
				fmt.Println("notify popo sa list ", saMailList)
				err := NotifyPopo(content, saMailList)
				if err != nil {
					return err
				}
			}
			if notifyProject.SaNotifyEmail {
				fmt.Println("notify email sa list ", projectCode)
				fmt.Println("notify email sa list ", saMailList)
				err := NotifyEmail(content, saMailList, nil, title, signData)
				if err != nil {
					return err
				}
			}
		}
	}

	// 通过popo群通知
	if notifyProject.PopoGroups != nil {
		popoGroups := *notifyProject.PopoGroups
		if notifyProject.Popo && len(popoGroups) != 0 && popoGroups[0] != "" {
			fmt.Println("notify popo groups ", popoGroups)
			err := NotifyPopo(content, popoGroups)
			if err != nil {
				return err
			}
		}
	}

	////通过邮件或popo通知配置的邮件列表
	if notifyProject.Emails != nil {
		emails := *notifyProject.Emails
		if (notifyProject.Popo || notifyProject.Email) && emails[0] != "" {
			if notifyProject.Popo {
				//fmt.Println("notify email ", emails)
				err := NotifyPopo(content, emails)
				if err != nil {
					return err
				}
			}
			if notifyProject.Email {
				err := NotifyEmail(content, emails, nil, title, signData)
				if err != nil {
					return err
				}
			}
		}
	}
	// 电话列表不为空，可以进行短信或者电话通知
	if notifyProject.Phones != nil {
		phones := *notifyProject.Phones
		if (notifyProject.Phone || notifyProject.Sms) && phones[0] != "" {
			if notifyProject.Sms {
				fmt.Println("notify project sms")
				err := NotifySms(content, phones)
				if err != nil {
					return err
				}
			}
			if notifyProject.Phone {
				fmt.Println("notify project phone")
				err := NotifyPhone(content, phones)
				if err != nil {
					return err
				}
			}
		}
	}

	return nil
}

func NotifyAll(notifySystem *ent.Notify, notifyProject *ent.Notify, projectCode, content, title, signData string, headerMap map[string]string, ip string) error {
	var (
		popoGroupList []string
		popoList      []string
		// phone sms
		phonesList []string
		smsList    []string

		receiveList       []string
		ccList            []string
		notifySystemFlag  bool
		notifyProjectFlag bool
	)

	if notifySystem.Enabled {
		if notifySystem.IPWhitelists == nil || !slices.Contains(*notifySystem.IPWhitelists, ip) {
			notifySystemFlag = true
		}
	}
	if notifyProject != nil && notifyProject.Enabled {
		if notifyProject.Enabled {
			if notifyProject.IPWhitelists == nil || !slices.Contains(*notifyProject.IPWhitelists, ip) {
				notifyProjectFlag = true
			}
		}
	}

	// 系统配置的通知列表
	if notifySystemFlag {
		// 群列表不为空，popo群通知
		if notifySystem.PopoGroups != nil {
			popoGroups := *notifySystem.PopoGroups
			if notifySystem.Popo && len(popoGroups) != 0 && popoGroups[0] != "" {
				popoGroupList = append(popoGroupList, popoGroups...)
			}
		}

		// 邮件列表不为空，可以进行popo或邮件通知
		if notifySystem.Emails != nil {
			emails := *notifySystem.Emails
			if emails[0] != "" {
				if notifySystem.Popo {
					popoList = append(popoList, emails...)
				}
				if notifySystem.Email {
					ccList = append(ccList, emails...)
				}
			}
		}

		// 电话列表不为空，可以进行短信或者电话通知
		if notifySystem.Phones != nil {
			phones := *notifySystem.Phones
			if (notifySystem.Phone || notifySystem.Sms) && phones[0] != "" {
				if notifySystem.Sms {
					smsList = append(smsList, phones...)
				}
				if notifySystem.Phone {
					phonesList = append(phonesList, phones...)
				}
			}
		}
		// 系统配置的通知列表 end
	}

	// 项目配置通知列表
	if notifySystemFlag && notifyProjectFlag {
		// 通过邮件或popo通知sa列表
		if notifyProject.SaNotifyPopo || notifyProject.SaNotifyEmail {
			if projectCode != "" {
				saMailList, err := GetAuthProjectSaMailList(projectCode, headerMap)
				if err != nil {
					fmt.Printf("fetech sa mails failed: %s", err)
				}
				if notifyProject.SaNotifyPopo {
					popoList = append(popoList, saMailList...)
				}
				if notifyProject.SaNotifyEmail {
					receiveList = append(receiveList, saMailList...)
				}
			}
		}

		// 通过popo群通知
		if notifyProject.PopoGroups != nil {
			popoGroups := *notifyProject.PopoGroups
			if notifyProject.Popo && len(popoGroups) != 0 && popoGroups[0] != "" {
				popoGroupList = append(popoGroupList, popoGroups...)
			}
		}

		//通过邮件或popo通知配置的邮件列表
		if notifyProject.Emails != nil {
			emails := *notifyProject.Emails
			if (notifyProject.Popo || notifyProject.Email) && emails[0] != "" {
				if notifyProject.Popo {
					popoList = append(popoList, emails...)
				}
				if notifyProject.Email {
					receiveList = append(receiveList, emails...)
				}
			}
		}
		// 电话列表不为空，可以进行短信或者电话通知
		if notifyProject.Phones != nil {
			phones := *notifyProject.Phones
			if (notifyProject.Phone || notifyProject.Sms) && phones[0] != "" {
				if notifyProject.Sms {
					smsList = append(smsList, phones...)
				}
				if notifyProject.Phone {
					phonesList = append(phonesList, phones...)
				}
			}
		}
	}

	fmt.Println("popoGroupList", popoGroupList)
	fmt.Println("popoList", popoList)
	fmt.Println("phonesList", phonesList)
	fmt.Println("smsList", smsList)
	fmt.Println("receiveList", receiveList)
	fmt.Println("ccList", ccList)

	//popo私信
	err := NotifyPopo(content, popoList)
	if err != nil {
		fmt.Printf("popo  err:%+v\n", err)
		//return err
	}
	//popo群消息
	err = NotifyPopo(content, popoGroupList)
	if err != nil {
		fmt.Printf("popo group err:%+v\n", err)
		//return err
	}
	//邮件
	//没有收件人，ccList就是收件人
	if len(receiveList) == 0 {
		err := NotifyEmail(content, ccList, nil, title, signData)
		if err != nil {
			fmt.Printf("email no cc err:%+v\n", err)
			//return err
		}
	} else {
		err := NotifyEmail(content, receiveList, ccList, title, signData)
		if err != nil {
			fmt.Printf("email cc err:%+v\n", err)
			//return err
		}
	}
	//电话
	err = NotifyPhone(content, phonesList)
	if err != nil {
		fmt.Printf("phone err:%+v\n", err)
		//return err
	}
	//短信
	err = NotifySms(content, smsList)
	if err != nil {
		fmt.Printf("sms err:%+v\n", err)
		//return err
	}
	return err
}
