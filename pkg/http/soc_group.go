package http

import (
	"github.com/bytedance/sonic"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease/socgroup"
	"meta/pkg/common"
)

func QueryTicket(api string, bodyData *socgroup.QueryRequestData) (*socgroup.QueryResponseData, error) {
	headerMap := loadConfig()
	httpData := &entity.HttpData{Api: api + "/api/v2/workorder/query", HttpMethod: "POST",
		BodyData: bodyData,
		Header:   headerMap,
	}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("soc group", body)
	}
	queryResponseData := &socgroup.QueryResponseData{}
	err = sonic.Unmarshal(body, &queryResponseData)
	if err != nil {
		return nil, err
	}
	return queryResponseData, nil
}

func AddTicket(api string, bodyData *socgroup.AddRequestData) (*socgroup.AddResponseData, error) {
	headerMap := loadConfig()
	httpData := &entity.HttpData{Api: api + "/api/v2/workorder/add", HttpMethod: "POST",
		BodyData: bodyData,
		Header:   headerMap,
	}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("soc group", body)
	}
	responseData := &socgroup.AddResponseData{}
	err = sonic.Unmarshal(body, &responseData)
	if err != nil {
		return nil, err
	}
	return responseData, nil
}

func loadConfig() map[string]string {
	headerMap := make(map[string]string)
	headerMap["username"] = config.CFG.External.SocGroup.Username
	headerMap["password"] = config.CFG.External.SocGroup.Password
	return headerMap
}
