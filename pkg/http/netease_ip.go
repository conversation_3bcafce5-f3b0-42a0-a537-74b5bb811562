/**
* <AUTHOR>
* @date 2023-05-16 15:12
* @description
 */

package http

import (
	"github.com/bytedance/sonic"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease/neteaseip"
	"meta/pkg/common"
)

func GetIPLocationInfo(ip string) (*neteaseip.ResponseData, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.NeteaseIP + "/getipinfo", HttpMethod: "GET", UrlData: "ip=" + ip}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("neteaseip", body)
	}
	result := &neteaseip.ResponseData{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
