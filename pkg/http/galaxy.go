/**
* <AUTHOR>
* @date 2022-12-28 12:18
* @description
 */

package http

import (
	"fmt"
	"meta/app/ent"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"

	"github.com/bytedance/sonic"
)

func GetGalaxyProject(ip string, headerMap map[string]string) (*ent.Tenant, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Galaxy + "/bll/machines/spotlight", HttpMethod: "GET", UrlData: "name=" + ip, Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("galaxy", body)
	}
	spotlightResults := make([]netease.SpotlightResult, 2)
	err = sonic.Unmarshal(body, &spotlightResults)
	if err != nil {
		return nil, err
	}
	if len(spotlightResults) >= 1 {
		// ip有多个项目归属也只取第一个项目
		return &ent.Tenant{Name: spotlightResults[0].Project.ProjectName, Code: spotlightResults[0].Project.ProjectCode}, nil
	}
	return nil, fmt.Errorf("%s 未在Galaxy查询到项目", ip)
}
