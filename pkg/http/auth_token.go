package http

import (
	"context"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"time"

	"github.com/bytedance/sonic"

	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

var AuthV2Set = wire.NewSet(wire.Struct(new(AuthV2X), "*"))

type AuthV2X struct {
	Logger *zap.Logger
	Rdb    *redis.Client
}

// GetV2Token 根据配置中user和key 获取v2 token
func (r *AuthV2X) GetV2Token() (token string, headerMap map[string]string, err error) {
	ctx := context.Background()
	v2Prefix := common.GenRedisKey("token", "v2")
	val2, err := r.Rdb.Get(ctx, v2Prefix).Result()
	if err == redis.Nil {
		r.Logger.Sugar().Info("token v2 not exist, fetching from auth v2 api")
		authV2 := config.CFG.XAuth.V2
		httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.AuthV2 + "/tokens", HttpMethod: "POST", BodyData: netease.V2{User: authV2.User, Key: authV2.Key, Ttl: authV2.Ttl}}
		body, statusCode, err := DoRequest(httpData)
		if err != nil {
			return "", nil, err
		}
		if statusCode != 200 {
			return "", nil, common.StatusCodeNotEqual200("auth v2", body)
		}
		r.Logger.Sugar().Info("fetching token success")
		v2Response := netease.V2Response{}
		err = sonic.Unmarshal(body, &v2Response)
		if err != nil {
			return "", nil, err
		}
		// 获取token 更新缓存
		r.Rdb.Set(ctx, v2Prefix, v2Response.Token, time.Second*time.Duration(authV2.Ttl))
		token = v2Response.Token
	} else if err != nil {
		return "", nil, err
	} else {
		token = val2
	}
	headerMap = map[string]string{"X-Access-Token": token, "X-AUTH-PROJECT": config.CFG.XAuth.V2.Project}
	return token, headerMap, nil
}
