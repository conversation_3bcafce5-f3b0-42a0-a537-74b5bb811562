/**
* <AUTHOR>
* @date 2022-12-28 12:12
* @description
 */

package http

import (
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"

	"github.com/bytedance/sonic"
)

// GetCldProject 根据ip获取项目信息
func GetCldProject(ip string, headerMap map[string]string) (*netease.CldProject, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Cld + "/apis/noscenter/v3/spotlight", HttpMethod: "GET", UrlData: "keyword=" + ip, Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("cld project", body)
	}
	projectResult := &netease.CldProject{}
	err = sonic.Unmarshal(body, &projectResult)
	if err != nil {
		return nil, err
	}
	return projectResult, nil
}

// GetCldCIDR 根据ip获取项目信息
func GetCldCIDR(headerMap map[string]string) (*netease.CldCIDR, error) {
	httpData := &entity.HttpData{Api: config.CFG.XAuth.Api.Cld + "/apis/noscenter/v3/external_cidr", HttpMethod: "GET", Header: headerMap}
	body, statusCode, err := DoRequest(httpData)
	if err != nil {
		return nil, err
	}
	if statusCode != 200 {
		return nil, common.StatusCodeNotEqual200("cld cidr", body)
	}
	result := &netease.CldCIDR{}
	err = sonic.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}
