package http

import (
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"errors"
	"fmt"
	"math/rand"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/wofang"
	"meta/pkg/common"
	"net/url"
	"sort"
	"strings"
	"time"

	"github.com/bytedance/sonic"
)

func WoFangGetAttackData(dataMap map[string]any) (*wofang.AttackDataResponse, error) {
	httpMethod := "GET"
	urlData := loadWoFangData(httpMethod, dataMap)
	httpData := &entity.HttpData{Api: config.CFG.External.WoFang.Api2, HttpMethod: httpMethod, UrlData: urlData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	fangResponse := &wofang.AttackDataResponse{}
	//状态码 200
	if resp.StatusCode == 200 {
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &fangResponse)
		if err != nil {
			return nil, err
		}
		//0 成功 其他 失败
		if fangResponse.Code != 0 {
			return nil, errors.New(fangResponse.Message)
		}
		return fangResponse, nil
	}
	//状态码非 200
	return nil, fmt.Errorf("status code %v", resp.StatusCode)
}

// WoFangDrag2 沃防新接口，牵引清洗，取消清洗
func WoFangDrag2(dataMap map[string]any) (*wofang.Response2, error) {
	httpMethod := "GET"
	urlData := loadWoFangData(httpMethod, dataMap)
	httpData := &entity.HttpData{Api: config.CFG.External.WoFang.Api2, HttpMethod: httpMethod, UrlData: urlData}
	request := GenHttpRequest(httpData)

	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}

	fangResponse := &wofang.Response2{}
	//状态码 200
	if resp.StatusCode == 200 {
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &fangResponse)
		if err != nil {
			return nil, err
		}
		//0 成功 其他 失败
		if fangResponse.Code != 0 {
			return fangResponse, errors.New(fangResponse.Message)
		}
		return fangResponse, nil
	}
	//状态码非 200
	return nil, common.StatusCodeNotEqual200("wofang v2", resp.StatusCode)
}

func loadWoFangData(httpMethod string, dataMap map[string]any) string {
	var strLine []string
	//通用参数
	timestamp := time.Now().Unix()
	dataMap["AccessKeyId"] = config.CFG.External.WoFang.Api2Id
	dataMap["Timestamp"] = timestamp
	dataMap["Nonce"] = rand.New(rand.NewSource(time.Now().UnixNano())).Intn(900) + 100
	dataMap["Version"] = config.CFG.External.WoFang.Version
	dataMap["SignatureMethod"] = config.CFG.External.WoFang.SignatureMethod

	for k, v := range dataMap {
		strLine = append(strLine, fmt.Sprintf("%v=%v", urlEncode(k), urlEncode(v)))
	}

	//获取签名
	sign := signData(strLine, httpMethod)

	//拼接签名到url中
	strLine = append(strLine, fmt.Sprintf("Signature=%v", urlEncode(sign)))

	return strings.Join(strLine, "&")
}
func signData(data []string, httpMethod string) string {
	sort.Strings(data)
	urlData := strings.Join(data, "&")
	signData := fmt.Sprintf("%s/?%s", httpMethod, urlData)
	key := []byte(config.CFG.External.WoFang.Api2Key)
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(signData))
	return base64.StdEncoding.EncodeToString(mac.Sum(nil))
}
func urlEncode(urlStr any) string {
	encodeStr := url.QueryEscape(fmt.Sprintf("%v", urlStr))
	encodeStr = strings.ReplaceAll(encodeStr, "+", "%20")
	return encodeStr
}

// WoFangDrag 牵引操作接口【旧接口】
// post增加和删除，get查询数据，通过action区分动作
// 有点畸形的接口，post的url带数据，请求体没有数据
// Deprecated: 旧沃防api，已失效
func WoFangDrag(httpMethod, action string, externalData map[string]any) (*wofang.Response, error) {
	dataMap := loadWoFangConfig()
	var strLine []string
	// 必填字段添加到url
	for k, v := range dataMap {
		strLine = append(strLine, fmt.Sprintf("%v=%v", k, v))
	}
	// 其他数据添加到url
	if externalData != nil {
		for k, v := range externalData {
			strLine = append(strLine, fmt.Sprintf("%v=%v", k, v))
		}
	}
	urlData := strings.Join(strLine, "&")
	httpData := &entity.HttpData{Api: config.CFG.External.WoFang.Api + "/" + action, HttpMethod: httpMethod, UrlData: urlData}
	request := GenHttpRequest(httpData)
	resp, err := SendRequest(request)
	if err != nil {
		return nil, err
	}
	fangResponse := &wofang.Response{}
	// 状态码 200
	if resp.StatusCode == 200 {
		body := GetResponseBody(resp)
		err := sonic.Unmarshal(body, &fangResponse)
		if err != nil {
			return nil, err
		}
		// 1 成功 0 失败
		if fangResponse.Status == 0 {
			return nil, errors.New(fangResponse.Msg)
		}
		return fangResponse, nil
	}
	// 状态码非 200
	return nil, common.StatusCodeNotEqual200("wofang v1", resp.StatusCode)
}

// loadWoFangConfig 牵引操作接口【旧接口】
// Deprecated: 旧沃防api，已失效
func loadWoFangConfig() map[string]any {
	conf := config.CFG.External
	dataMap := make(map[string]any)
	appId := conf.WoFang.AppId
	appKey := conf.WoFang.AppKey
	timestamp := time.Now().Unix()
	// sign = md5(appkey + (appid={appid}+&dateline={当前时间戳})+appkey)
	str := fmt.Sprintf("%sappid=%d&dateline=%d%s", appKey, appId, timestamp, appKey)
	sign := common.MD5(str)
	dataMap["idc"] = conf.WoFang.IdcCode
	dataMap["appid"] = conf.WoFang.AppId
	dataMap["dateline"] = timestamp
	dataMap["sign"] = sign
	return dataMap
}
