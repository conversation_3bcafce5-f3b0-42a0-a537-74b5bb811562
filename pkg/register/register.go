/**
* <AUTHOR>
* @date 2022-12-27 16:27
* @description
 */

package register

import (
	"meta/app/entity/config"
	"time"

	"github.com/go-co-op/gocron"
	"github.com/gofiber/fiber/v2"
)

func (r *Runner) Inject(routes []fiber.Route) {
	r.scheduleTask()
	r.RegisterSAUser()
	//if config.CFG.Stage.Status == "dev" {
	//	//	//先用生产的代替测试topic（测试topic没有数据）
	//	config.CFG.External.Kafka.NetCloud.Topics = []string{"cld_9601_ndcc_aegis"}
	//	r.FetchCloudAlert()
	//}
	r.RegisterSystemApi(routes)
	r.LoadMatrixConfig()

	// 更新需要刷新auth权限的用户权限
	r.UpdateUserPermission()

	//数据修正
	//r.FixNDSAlertISPCode()

	//r.CacheOfflineProject()
	//r.UpdateAllProjectPermission()
}
func (r *Runner) scheduleTask() {
	s := gocron.NewScheduler(time.Local)
	_, err := s.Every(5).Minute().Do(r.UpdateProtectGroup)
	_, err = s.Every(1).Minute().Do(r.FetchWoFangAlert)
	_, err = s.Every(5).Minute().Do(r.FetchAwsDos)
	_, err = s.Every(10).Minute().Do(r.FetchCldCIDR)

	_, err = s.Every(6).Hour().Do(func() {
		r.CacheOfflineProject()
		r.UpdateAllProjectPermission()
	})

	_, err = s.Every(1).Day().At("12:00").Do(r.MockWofangDrain)

	//_, err = s.Every(1).Second().Do(r.FetchMatrixFlowPrometheus)

	if config.CFG.Stage.Status == "prod" {
		_, err = s.Every(1).Second().Do(r.FetchMatrixFlowPrometheus)
		_, err = s.Every(1).Hour().Do(r.FetchNDSData)
	}

	if err != nil {
		r.Logger.Error(err.Error())
	}
	s.StartAsync()
}
