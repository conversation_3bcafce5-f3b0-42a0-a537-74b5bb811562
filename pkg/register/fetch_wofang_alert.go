/**
* <AUTHOR>
* @date 2023-04-23 17:31
* @description
 */

package register

import (
	"fmt"
	mapset "github.com/deckarep/golang-set"
	"meta/app/ent"
	"meta/app/ent/wofangalert"
	"meta/pkg/common"
	"meta/pkg/http"
	"sort"
	"strings"
	"time"
)

var wofangFirstRun = true

func (r *Runner) FetchWoFangAlert() {
	r.<PERSON>gger.Sugar().Info("获取沃防告警数据")
	dataMap := make(map[string]any)
	// 获取攻击事件：GetAttackSum
	dataMap["Action"] = "GetAttackSum"
	var passThree string
	if wofangFirstRun {
		passThree = time.Now().AddDate(0, 0, -180).Format("2006-01-02 15:04:05")
		wofangFirstRun = false
	} else {
		passThree = time.Now().Add(-time.Hour * 3).Format("2006-01-02 15:04:05")
	}
	dataMap["afterStartTime"] = passThree

	drag, err := http.WoFangGetAttackData(dataMap)
	if err != nil || drag == nil {
		r.Logger.<PERSON>().Errorf("获取沃防告警数据失败：%s", err)
		return
	}
	datas := drag.Data
	if len(datas) != 0 {
		r.Logger.Sugar().Infof("成功获取沃防告警数据：%d", len(datas))
	}

	ctx := r.AuthEnt.GetSAContext()
	for _, v := range datas {
		//攻击类型
		if v.AttackStatus == 0 {
			// 1 结束 2攻击中
			v.AttackStatus = 1
		}
		attackList := strings.Split(v.AttackType, ",")

		//结束时间
		var endTime time.Time
		if v.EndTime != "" {
			endTime = common.String2LocalTime(v.EndTime)
		}
		maxDropKbps := v.MaxDropKbps * 1000
		maxInKbps := v.MaxInKbps * 1000

		dbWofangAlert, _ := r.WofangService.Dao.WofangAlert.Query().Where(wofangalert.AttackID(v.Id), wofangalert.ZoneIP(v.ZoneIp)).Only(ctx)
		if dbWofangAlert == nil {
			realProject := r.NdsController.ProjectCtl.GetRealProject(ctx, v.ZoneIp)
			var tid *int
			if realProject != nil {
				tid = &realProject.ID
			}
			var attackTypes *[]string
			if len(attackList) != 0 {
				attackTypes = &attackList
			}
			wofangAlert := &ent.WofangAlert{
				TenantID:     tid,
				AttackStatus: v.AttackStatus,
				AttackType:   attackTypes,
				DeviceIP:     v.DeviceIp,
				ZoneIP:       v.ZoneIp,
				AttackID:     v.Id,
				StartTime:    common.String2LocalTime(v.StartTime),
				EndTime:      endTime,
				MaxDropBps:   maxDropKbps,
				MaxInBps:     maxInKbps,
			}
			r.WofangService.Dao.WofangAlert.Create().SetItemWofangAlert(wofangAlert).Save(ctx)
			r.Logger.Sugar().Infof("Save wofang alert: %s", wofangAlert)
			if wofangAlert.AttackStatus == 2 {
				r.Logger.Sugar().Infof("wofang attack: %s", wofangAlert.ZoneIP)
				if !wofangFirstRun {
					//攻击开始
					fmt.Printf("wofang attack start:%s\n", wofangAlert.ZoneIP)
				}
			}
		} else {
			if v.AttackStatus == 1 {
				if dbWofangAlert.EndTime.IsZero() {
					// 更新攻击类型
					attackTypeSet := mapset.NewSet()
					for _, v := range *dbWofangAlert.AttackType {
						attackTypeSet.Add(v)
					}
					for _, v := range attackList {
						attackTypeSet.Add(v)
					}
					var attackTypes []string
					for val := range attackTypeSet.Iterator().C {
						attackTypes = append(attackTypes, val.(string))
					}
					sort.Strings(attackTypes)

					//更新攻击峰值
					if dbWofangAlert.MaxDropBps > maxDropKbps {
						maxDropKbps = dbWofangAlert.MaxDropBps
					}
					if dbWofangAlert.MaxInBps > maxInKbps {
						maxInKbps = dbWofangAlert.MaxInBps
					}
					r.WofangService.Dao.WofangAlert.UpdateOneID(dbWofangAlert.ID).
						SetAttackStatus(v.AttackStatus).
						SetEndTime(endTime).
						SetAttackType(&attackTypes).
						SetMaxInBps(maxInKbps).
						SetMaxDropBps(maxDropKbps).
						Save(ctx)
					r.Logger.Sugar().Infof("wofang attack end: %s", dbWofangAlert.ZoneIP)
					if !wofangFirstRun {
						//攻击结束
						fmt.Printf("wofang attack end:%s\n", dbWofangAlert.ZoneIP)
					}
				}
			}
		}
	}
}
