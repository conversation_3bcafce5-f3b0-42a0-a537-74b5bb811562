/**
* <AUTHOR>
* @date 2023-03-17 16:56
* @description
 */
package register

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent"
	"meta/app/ent/cloudalert"
	"meta/app/entity/config"
	"meta/app/entity/netease/gamecloud"
	"meta/pkg/common"
	"meta/pkg/common/project"
	"strings"
	"time"

	"github.com/IBM/sarama"
	"github.com/bytedance/sonic"
)

type Handler struct {
	Runner *Runner
}

func (r *Handler) Setup(sarama.ConsumerGroupSession) error {
	return nil
}
func (r *Handler) Cleanup(sarama.ConsumerGroupSession) error { return nil }

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages().
func (r *Handler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	for msg := range claim.Messages() {
		// log.Printf("Message claimed: value = %s, timestamp = %v, topic = %s", msg.Value, msg.Timestamp, msg.Topic)
		if msg.Value != nil {
			fmt.Println(time.Now().Local())
			fmt.Println("msg.Value====> ", string(msg.Value))
			go ConsumeData(msg.Value, r.Runner)
		}
		go session.MarkMessage(msg, "")
	}
	return nil
}

func SaramaConsumerGroup(r *Runner) {
	saramaConfig := sarama.NewConfig()
	saramaConfig.Consumer.Return.Errors = false
	saramaConfig.Version = sarama.V0_10_2_0 // specify appropriate version
	// saramaConfig.Consumer.Offsets.Initial = sarama.OffsetOldest // 未找到组消费位移的时候从哪边开始消费
	saramaConfig.Consumer.Offsets.Initial = sarama.OffsetNewest // 未找到组消费位移的时候从哪边开始消费

	netCloudKafka := config.CFG.External.Kafka.NetCloud
	broker := netCloudKafka.Broker
	topics := netCloudKafka.Topics

	if netCloudKafka.SaslEnable {
		saramaConfig.Net.SASL.Enable = true
		saramaConfig.Net.SASL.User = netCloudKafka.SaslUser
		saramaConfig.Net.SASL.Password = netCloudKafka.SaslPassword
		saramaConfig.Net.SASL.Mechanism = sarama.SASLTypePlaintext
	}

	group, err := sarama.NewConsumerGroup([]string{broker}, netCloudKafka.GroupId, saramaConfig)
	if err != nil {
		fmt.Println("kafka", err)
		r.Logger.Sugar().Error(err)
	}
	defer func() {
		fmt.Println("Consumer group close ")
		_ = group.Close()
	}()

	// Track errors
	go func() {
		for err := range group.Errors() {
			fmt.Println("ERROR", err)
		}
	}()
	fmt.Println("Consumer start")
	// Iterate over consumer sessions.
	ctx := context.Background()
	for {
		handler := &Handler{Runner: r}
		// `Consume` should be called inside an infinite loop, when a
		// server-side rebalance happens, the consumer session will need to be
		// recreated to get the new claims
		err := group.Consume(ctx, topics, handler)
		if err != nil {
			fmt.Println("kafka", err)
			r.Logger.Sugar().Error(err)
		}
	}
}

func Data2Array(data []byte) ([]any, error) {
	firstData := &gamecloud.First{}
	err := sonic.Unmarshal(data, &firstData)
	if err != nil {
		return nil, err
	}
	if firstData != nil && firstData.Data != nil {
		return firstData.Data, nil
	}
	return nil, errors.New("nil data")
}

func ConsumeData(data []byte, r *Runner) {
	rawDataArrays, err := Data2Array(data)
	if err != nil {
		r.Logger.Sugar().Error(err)
	}
	for _, rawData := range rawDataArrays {
		// byteRawData := rawData.([]byte)
		dataMap := rawData.(map[string]any)
		if dataMap["metric"] == nil {
			continue
		}
		metric := dataMap["metric"].(string)
		if metric == "" {
			continue
		}
		ParseData1(rawData, metric, r)
	}
}

// FIXME：目前云网络所有项目都应用的低阈值策略，告警数据量太大，一天估计50w+条
// 等正式使用，调整策略后，量降下来了再看看怎么处理后续的数据
func ParseData1(rawData any, metric string, r *Runner) {
	data, err := sonic.Marshal(rawData)
	if err != nil {
		r.Logger.Sugar().Error(err)
	}
	ctx := r.AuthEnt.GetSAContext()
	switch metric {
	case "attack_start_data":
		// 攻击开始数据（attack_start_data)，表示某一条/某一组连接访问宿主的频率过高（超过了阈值），触发了清洗行为，简单描述即为攻击开始。
		attackStartData := &gamecloud.Start{}
		err := sonic.Unmarshal(data, &attackStartData)
		// err := mapstructure.Decode(data, attackStartData)
		if err != nil {
			r.Logger.Sugar().Error(err)
		}

		fmt.Printf("attackStartData:%+v\n", attackStartData)
		startTags := attackStartData.Tags
		startAlert := parseStartAlert(&startTags)
		code := startTags.Tenant

		if startTags.Tenant == "" || strings.ToLower(startTags.Tenant) == "unknown" {
			code = "cld"
		}
		fmt.Println("code:", code)

		fmt.Println("now", time.Now().Local())
		fmt.Println("ale", startAlert.StartTime)

		galaxyProject := project.GetProjectByCodeOrIP(code, "", ctx, r.NdsController)
		fmt.Println(galaxyProject)
		if galaxyProject != nil {
			startAlert.TenantID = &galaxyProject.ID
		}
		_, err = r.CloudAlertService.Create(ctx, startAlert)
		if err != nil {
			r.Logger.Sugar().Fatalf("CloudAlertService.Create:%+v, failed: %s", startAlert, err)
		}

	case "attack_end_data":

		// 攻击结束数据（attack_end_data），在攻击开始的基础上，上述某一条/某一组连接访问宿主的频率恢复正常（低于阈值），并且在一定时间内维持该状态，则表示为攻击结束
		attackEndData := &gamecloud.End{}
		// err := mapstructure.Decode(data, attackEndData)
		err := sonic.Unmarshal(data, &attackEndData)
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		fmt.Printf("AttackEndData:%+v\n", attackEndData)
	case "flow_data":
		// 流超限数据（flow_data），表示某一条连接的发包速率严重超出正常连接发包速率，比如正常用户每条流每秒钟最多请求100个请求，但是发现某一条连接请求每秒超过了1000次。
		flowData := &gamecloud.Flow{}
		// err := mapstructure.Decode(data, flowData)
		err := sonic.Unmarshal(data, &flowData)
		if err != nil {
			fmt.Println("flow_data error:", err)
		}
		fmt.Println("flowData: ", flowData)
	case "flow_attack_pps_data":
		// 流的攻击pps数据，即分光数据
		flowAttackPpsData := &gamecloud.FlowAttackPps{}
		err := sonic.Unmarshal(data, &flowAttackPpsData)
		// err := mapstructure.Decode(data, flowAttackPpsData)
		if err != nil {
			r.Logger.Sugar().Error(err)
			fmt.Println("flow_attack_pps_data error: ", err)
		}
		fmt.Printf("flowAttackPpsData:%+v\n", flowAttackPpsData)
	case "flow_level_data":
		// 层级pps
		flowLevelData := &gamecloud.FlowLevel{}
		// err := mapstructure.Decode(data, flowLevelData)
		err := sonic.Unmarshal(data, &flowLevelData)
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		fmt.Printf("flowLevelData:%+v\n", flowLevelData)
	default:
		r.Logger.Sugar().Errorf("unknown Metric:%+v", string(data))
	}
}

func ParseData(rawData any, metric string, r *Runner) {
	data, err := sonic.Marshal(rawData)
	if err != nil {
		r.Logger.Sugar().Error(err)
	}
	ctx := r.AuthEnt.GetSAContext()

	switch metric {
	case "attack_start_data":
		// 攻击开始数据（attack_start_data)，表示某一条/某一组连接访问宿主的频率过高（超过了阈值），触发了清洗行为，简单描述即为攻击开始。
		attackStartData := &gamecloud.Start{}
		err := sonic.Unmarshal(data, &attackStartData)
		// err := mapstructure.Decode(data, attackStartData)
		if err != nil {
			r.Logger.Sugar().Error(err)
		}

		fmt.Printf("attackStartData:%+v\n", attackStartData)
		startTags := attackStartData.Tags
		startAlert := parseStartAlert(&startTags)
		ip := startAlert.DstIP
		code := startTags.Tenant

		galaxyProject := project.GetProjectByCodeOrIP(code, ip, ctx, r.NdsController)
		if galaxyProject != nil {
			startAlert.TenantID = &galaxyProject.ID
		}
		_, err = r.CloudAlertService.Create(ctx, startAlert)
		if err != nil {
			r.Logger.Sugar().Fatalf("CloudAlertService.Create:%+v, failed: %s", startAlert, err)
		}
		// r.Logger.Sugar().Infof("create start alert: %+v\n", startAlert)

		// fmt.Println("create ", create)
		// fmt.Printf("%+v\n", startAlert)

	case "attack_end_data":
		// 攻击结束数据（attack_end_data），在攻击开始的基础上，上述某一条/某一组连接访问宿主的频率恢复正常（低于阈值），并且在一定时间内维持该状态，则表示为攻击结束
		attackEndData := &gamecloud.End{}
		err := sonic.Unmarshal(data, &attackEndData)
		// err := mapstructure.Decode(data, attackEndData)
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		endTags := attackEndData.Tags
		endAlert := parseEndAlert(&endTags)
		// 查找未攻击结束，最近一条数据
		cloudAlerts, err := r.CloudAlertService.Dao.CloudAlert.Query().Where(cloudalert.SrcIP(endAlert.SrcIP), cloudalert.SrcPort(endAlert.SrcPort), cloudalert.DstIP(endAlert.DstIP), cloudalert.DstPort(endAlert.DstPort), cloudalert.EndTimeIsNil()).Order(ent.Desc(cloudalert.FieldCreatedAt)).Limit(1).All(ctx)
		if err != nil {
			r.Logger.Sugar().Error("query latest attack_end_data failed:", err)
		}
		if len(cloudAlerts) >= 1 {
			dbAlert := cloudAlerts[0]
			// 更新时间，更新maxbps，overlimit
			_, err := r.CloudAlertService.Dao.CloudAlert.UpdateOneID(dbAlert.ID).SetMaxAttackPps(endAlert.MaxAttackPps).SetEndTime(endAlert.EndTime).SetOverlimitPktCount(endAlert.OverlimitPktCount).Save(ctx)
			if err != nil {
				r.Logger.Sugar().Fatalf("CloudAlert.UpdateOneID failed: %s", err)
			}
		}
		// r.Logger.Sugar().Infof("AttackEndData: %+v\n", attackEndData)

		// fmt.Printf("AttackEndData:%+v\n", attackEndData)
	case "flow_data":
		// 流超限数据（flow_data），表示某一条连接的发包速率严重超出正常连接发包速率，比如正常用户每条流每秒钟最多请求100个请求，但是发现某一条连接请求每秒超过了1000次。
		flowData := &gamecloud.Flow{}
		err := sonic.Unmarshal(data, &flowData)
		// err := mapstructure.Decode(data, flowData)
		if err != nil {
			fmt.Println("flow_data error:", err)
		}
		flowDataTag := &flowData.Tags
		cloudFlowData := parseFlowData(flowDataTag)
		// 查找未攻击结束，最近一条数据
		cloudAlerts, err := r.CloudAlertService.Dao.CloudAlert.Query().Where(cloudalert.SrcIP(cloudFlowData.SrcIP), cloudalert.SrcPort(cloudFlowData.SrcPort), cloudalert.DstIP(cloudFlowData.DstIP), cloudalert.DstPort(cloudFlowData.DstPort), cloudalert.EndTimeIsNil()).Order(ent.Desc(cloudalert.FieldCreatedAt)).Limit(1).All(ctx)
		if err != nil {
			r.Logger.Sugar().Error("query unfinished attack_end_data failed:", err)
		}
		if len(cloudAlerts) >= 1 {
			dbAlert := cloudAlerts[0]
			cloudFlowData.CloudAlertID = &dbAlert.ID
			if dbAlert.TenantID != nil && *dbAlert.TenantID != 0 {
				cloudFlowData.TenantID = dbAlert.TenantID
			}
		} else {
			code := flowDataTag.Tenant
			ip := cloudFlowData.DstIP
			galaxyProject := project.GetProjectByCodeOrIP(code, ip, ctx, r.NdsController)
			if galaxyProject != nil {
				cloudFlowData.TenantID = &galaxyProject.ID
			}
		}
		_, err = r.CloudFlowDataService.Create(ctx, cloudFlowData)
		if err != nil {
			r.Logger.Sugar().Fatalf("CloudFlowDataService.Create %+v failed: %s", cloudFlowData, err)
		}

		// fmt.Println("flowData: ", flowData)
	case "flow_attack_pps_data":
		// 流的攻击pps数据，即分光数据
		flowAttackPpsData := &gamecloud.FlowAttackPps{}
		err := sonic.Unmarshal(data, &flowAttackPpsData)
		// err := mapstructure.Decode(data, flowAttackPpsData)
		if err != nil {
			r.Logger.Sugar().Error(err)
			fmt.Println("flow_attack_pps_data error: ", err)

		}
		flowAttackPpsTag := &flowAttackPpsData.Tags
		attackData := parseAttackPpsTag(flowAttackPpsTag)
		ip := attackData.DstIP
		code := flowAttackPpsTag.Tenant

		galaxyProject := project.GetProjectByCodeOrIP(code, ip, ctx, r.NdsController)
		if galaxyProject != nil {
			attackData.TenantID = &galaxyProject.ID
		}
		_, err = r.CloudAttackDataService.Create(ctx, attackData)
		if err != nil {
			r.Logger.Sugar().Errorf("create flowAttackPpsData:%+v , error: %s", attackData, err)
		}
		// fmt.Printf("create flowAttackPpsData: %+v\n", attackData)

	case "flow_level_data":
		// 层级pps
		flowLevelData := &gamecloud.FlowLevel{}
		err := sonic.Unmarshal(data, &flowLevelData)
		// err := mapstructure.Decode(data, flowLevelData)
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		fmt.Printf("flowLevelData:%+v\n", flowLevelData)
	default:
		r.Logger.Sugar().Errorf("unknown Metric:%+v", data)
	}
}

func parseFlowData(flowTag *gamecloud.FlowTags) *ent.CloudFlowData {
	startTs := common.Str2Int64(flowTag.StartTs)
	finalTs := common.Str2Int64(flowTag.FinalTs)
	sport := common.Str2Int(flowTag.SrcPort)
	dport := common.Str2Int(flowTag.DstPort)
	protocol := common.Str2Int(flowTag.Proto)
	sip := common.IntStr2Ip(flowTag.SrcIp)
	dip := common.IntStr2Ip(flowTag.DstIp)
	overMaxPpsCount := common.Str2Int(flowTag.FlowOverMaxPpsCount)
	maxAttackPps := common.Str2Int64(flowTag.MaxAttackPps)
	return &ent.CloudFlowData{
		SrcIP:               sip,
		SrcPort:             sport,
		DstIP:               dip,
		DstPort:             dport,
		Protocol:            protocol,
		FlowOverMaxPpsCount: overMaxPpsCount,
		MaxAttackPps:        maxAttackPps,
		StartTime:           common.Timestamp2Time(startTs),
		EndTime:             common.Timestamp2Time(finalTs),
	}
}

func parseStartAlert(startTags *gamecloud.StartTags) *ent.CloudAlert {
	startTimestamp := common.Str2Int64(startTags.StartTs)
	sport := common.Str2Int(startTags.SrcPort)
	dport := common.Str2Int(startTags.DstPort)
	defenceMode := common.Str2Int(startTags.DefenceMode)
	flowMode := common.Str2Int(startTags.FlowMode)
	protocol := common.Str2Int(startTags.Proto)
	defenceLevel := common.Str2Int(startTags.DefenceLevel)
	sip := common.IntStr2Ip(startTags.SrcIp)
	dip := common.IntStr2Ip(startTags.DstIp)

	// max_attack_pps 从攻击开始到攻击结束，最大的pps
	// overlimit_pkt_count 超出阈值的报文总数
	return &ent.CloudAlert{
		SrcIP:        sip,
		SrcPort:      sport,
		DstIP:        dip,
		DstPort:      dport,
		DefenceMode:  defenceMode,
		FlowMode:     flowMode,
		TCPAckNum:    startTags.TcpAckNum,
		TCPSeqNum:    startTags.TcpSeqNum,
		Protocol:     protocol,
		DefenceLevel: defenceLevel,
		StartTime:    common.Timestamp2Time(startTimestamp),
	}
}

func parseEndAlert(endTags *gamecloud.EndTags) *ent.CloudAlert {
	finalTs := common.Str2Int64(endTags.FinalTs)
	tags := endTags.StartTags
	cloudAlert := parseStartAlert(&tags)
	cloudAlert.MaxAttackPps = common.Str2Int64(endTags.MaxAttackPps)
	cloudAlert.EndTime = common.Timestamp2Time(finalTs)
	cloudAlert.OverlimitPktCount = common.Str2Int(endTags.OverLimitPktCount)
	return cloudAlert
}

func parseAttackPpsTag(attackPpsTag *gamecloud.FlowAttackPpsTag) *ent.CloudAttackData {
	startTs := common.Str2Int64(attackPpsTag.StartTs)
	finalTs := common.Str2Int64(attackPpsTag.FinalTs)
	sport := common.Str2Int(attackPpsTag.SrcPort)
	dport := common.Str2Int(attackPpsTag.DstPort)
	protocol := common.Str2Int(attackPpsTag.Proto)
	sip := common.IntStr2Ip(attackPpsTag.SrcIp)
	dip := common.IntStr2Ip(attackPpsTag.DstIp)
	if finalTs == 0 {
		finalTs = startTs + 1
	}
	currentAttackPps := common.Str2Int64(attackPpsTag.CurrentAttackPps)
	return &ent.CloudAttackData{
		SrcIP:            sip,
		SrcPort:          sport,
		DstIP:            dip,
		DstPort:          dport,
		Protocol:         protocol,
		CurrentAttackPps: currentAttackPps,
		StartTime:        common.Timestamp2Time(startTs),
		EndTime:          common.Timestamp2Time(finalTs),
	}
}

func (r *Runner) FetchCloudAlert() {
	SaramaConsumerGroup(r)
}
