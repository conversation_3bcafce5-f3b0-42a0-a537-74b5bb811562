package register

import (
	"meta/app/ent"
	"meta/app/ent/datasync"
	"meta/pkg/common"
	"meta/pkg/http"
	"sort"
)

var dataSourceType = []string{"v2", "v3", "lbc"}

func (r *Runner) FetchCldCIDR() {
	r.<PERSON>gger.<PERSON>().Info("获取cld cidr 数据")
	_, headerMap, err := r.AuthV2.GetV2Token()
	if err != nil {
		r.Logger.Sugar().Error(err.Error())
	}
	resp, err := http.GetCldCIDR(headerMap)
	if err != nil {
		r.Logger.Sugar().Errorf("获取cld cidr失败:%s", err)
		return
	}

	var v2List []string
	var v3List []string
	var lbcList []string
	for _, data := range resp.CidrList {
		// v2 v3 lbc
		switch data.Source {
		case "v2":
			v2List = append(v2List, data.Cidr)
		case "v3":
			v3List = append(v3List, data.Cidr)
		case "lbc":
			lbcList = append(lbcList, data.Cidr)

		}
	}
	sort.Strings(v2List)
	sort.Strings(v3List)
	sort.Strings(lbcList)

	ctx := r.AuthEnt.GetSAContext()

	for _, sourceType := range dataSourceType {
		var dataList []string
		switch sourceType {
		case "v2":
			dataList = v2List
		case "v3":
			dataList = v3List
		case "lbc":
			dataList = lbcList
		}
		dbDataList, err := r.DataSyncService.Dao.DataSync.Query().Where(
			datasync.DataType("cld"),  //cld
			datasync.Type(sourceType), // v2 | v3 | lbc
		).Order(ent.Desc(datasync.FieldCreatedAt)).Limit(1).All(ctx)
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		if len(dbDataList) == 0 || dbDataList[0].DataList == nil || !common.StringSliceEqual(dataList, *dbDataList[0].DataList) {
			var preDataList *[]string
			if len(dbDataList) != 0 {
				preDataList = dbDataList[0].DataList
			}
			// 新增一条
			dataSync := &ent.DataSync{
				PreDataList: preDataList,
				DataList:    &dataList,
				DataType:    "cld",
				Type:        sourceType,
			}
			_, err := r.DataSyncService.Create(ctx, dataSync)
			if err != nil {
				r.Logger.Sugar().Errorf("新增 %s 网段失败 %s", sourceType, err)
			}

			r.Logger.Sugar().Infof("新增 %s 网段\n%s", sourceType, dataList)
		}
	}
}
