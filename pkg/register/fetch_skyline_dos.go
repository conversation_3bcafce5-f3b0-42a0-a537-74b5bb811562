/**
* <AUTHOR>
* @date 2023-04-20 15:04
* @description
 */

package register

import (
	"fmt"
	"meta/app/ent"
	"meta/app/ent/skylinedos"
	"meta/pkg/common/project"
	"meta/pkg/http"
	"net/url"
	"time"
)

var skylineFirstRun = true

func (r *Runner) FetchAwsDos() {
	r.<PERSON>.Sugar().Info("获取skyline aws dos告警数据")
	_, headerMap, err := r.AuthV2.GetV2Token()
	if err != nil {
		r.Logger.Sugar().Error(err.Error())
	}
	now := time.Now()
	startTime := url.QueryEscape(now.Format(time.RFC3339))
	endTime := url.QueryEscape(now.Add(-time.Minute * 6).Format(time.RFC3339))
	var urlData string
	if skylineFirstRun {
		urlData = fmt.Sprintf("_page=%d&_num=%d", 1, 500)
		skylineFirstRun = false
	} else {
		urlData = fmt.Sprintf("_page=%d&_num=%d&start_time=%s&end_time=%s", 1, 500, startTime, endTime)
	}
	attack, err := http.GetSkylineDdosAttack(urlData, headerMap)
	if err != nil {
		r.Logger.Sugar().Error(err)
	}
	if attack != nil {
		items := attack.Items
		if len(items) > 0 {
			ctx := r.AuthEnt.GetSAContext()
			for _, v := range items {
				only, _ := r.SkylineDosService.Dao.SkylineDos.Query().Where(skylinedos.Resource(v.Resource), skylinedos.StartTime(v.StartTime)).Only(ctx)
				// if err != nil {
				// 	r.Logger.Sugar().Error(err)
				// }
				if only == nil {
					code := v.Project
					skylineData := &ent.SkylineDos{
						StartTime:      v.StartTime,
						EndTime:        v.EndTime,
						Region:         v.Region,
						Resource:       v.Resource,
						ResourceType:   v.ResourceType,
						VectorTypes:    &v.VectorTypes,
						Status:         v.Status,
						AttackID:       v.AttackId,
						AttackCounters: &v.AttackCounters,
						Project:        code,
						DurationTime:   v.DurationTime,
					}
					galaxyProject := project.GetProjectByCodeOrIP(code, "", ctx, r.NdsController)
					if galaxyProject != nil {
						skylineData.TenantID = &galaxyProject.ID
					}
					r.SkylineDosService.Create(ctx, skylineData)
				}
			}

		}
	}
}
