package register

import (
	"context"
	"fmt"
	"golang.org/x/exp/slices"
	"meta/app/ent"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/spectrumalert"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"meta/pkg/common/cache"
	"meta/pkg/http"
	"net"
	"strconv"
	"strings"
	"time"
)

var (
	lowerBps   = "lower:bps_"
	netTypeMap = make(map[string]int)
)

func init() {
	netTypeMap["BGP:ChinaUnicom"] = 1
	netTypeMap["Static:ChinaUnicom"] = 10010
}
func (r *Runner) FetchMatrixFlowPrometheus() {
	_, headerMap, err := r.AuthV2.GetV2Token()
	if err != nil {
		r.Logger.Sugar().Error(err.Error())
	}

	// matrixLineChan := make(chan *netease.MatrixLine, 10)
	// resultStrChan := make(chan string, 10)

	//http.GetMatrixLineFlow(headerMap, resultStr<PERSON>han, matrixLine<PERSON>han, "bps", "pps", "ratio")
	//var resStr []string
	//go func() {
	//	for str := range resultStrChan {
	//		fmt.Printf("获取matrix数据: %v\n", str)
	//		resStr = append(resStr, str)
	//	}
	//}()
	//fmt.Println("resStr", resStr)
	//return

	// http.GetMatrixLineFlow(headerMap, resultStrChan, matrixLineChan)
	_, matrixLineResult := http.GetMatrixLineFlow(headerMap)

	//if config.CFG.Stage.Status == "prod" {
	//go func() {
	//	for _, str := range resultStrs {
	//		r.Logger.Sugar().Infof("获取matrix数据: %v", str)
	//	}
	//}()
	//}
	//if config.CFG.Stage.Status == "prod" {
	//	r.Logger.Sugar().Infof("获取matrix数据: %v", strings.Join(resultStrs, ","))
	//}
	// go func() {
	ctx := r.AuthEnt.GetSAContext()
	// for matrixLineFlow := range matrixLineChan {
	for _, matrixLineFlow := range matrixLineResult {
		if &matrixLineFlow == nil || matrixLineFlow.Data == nil {
			continue
		}
		lineData := matrixLineFlow.Data

		matrixFlowRedisKey := common.GenRedisKey("matrix", matrixLineFlow.Region, matrixLineFlow.NetType, matrixLineFlow.Isp)

		// 获取机房入向对应防护策略
		var matrixStrategyInfo netease.MatrixStrategyInfo
		if r.Rdb.HGetAll(ctx, matrixFlowRedisKey).Scan(&matrixStrategyInfo); err != nil {
			r.Logger.Sugar().Error(err.Error())
		}
		attackKey := common.KeyAttackPrefix + matrixFlowRedisKey

		// 判断当前线路出口是否在告警缓存中
		attackExist, err := r.Rdb.Exists(ctx, attackKey).Result()
		if err != nil {
			r.Logger.Sugar().Error(err)
			continue
		}
		var matrixBpsLineFlow netease.MatrixBps
		// matrixPpsLineFlow   netease.MatrixPps
		// matrixRatioLineFlow netease.MatrixRatio
		switch matrixLineFlow.DataType {
		case "bps":
			matrixBpsLineFlow = lineData.(netease.MatrixBps)
			// case "pps":
			//	matrixPpsLineFlow = lineData.(netease.MatrixPps)
			// case "ratio":
			//	matrixRatioLineFlow = lineData.(netease.MatrixRatio)
		}
		// 如果出口在告警中，记录分光数据（当前的数据）
		// 同时需要提交当前被攻击的IP到运营商防护
		if attackExist == 1 && &matrixLineFlow != nil {
			// 记录 bps
			go func() {
				s, err := r.Rdb.HGet(ctx, attackKey, "spectrumAlertID").Result()
				if err != nil {
					r.Logger.Sugar().Error(err)
				}
				i, _ := strconv.Atoi(s)
				if i != 0 {
					matrixSpectrumData := &ent.MatrixSpectrumData{
						MatrixSpectrumAlertID: &i,
						Isp:                   matrixLineFlow.Isp,
						Region:                matrixLineFlow.Region,
						NetType:               matrixLineFlow.NetType,
					}
					matrixSpectrumData.Bps = matrixBpsLineFlow.Bps
					matrixSpectrumData.Time = matrixBpsLineFlow.QueryTime

					// matrixSpectrumData.Pps = matrixPpsLineFlow.Pps
					// matrixSpectrumData.Ratio = float32(matrixRatioLineFlow.Ratio)
					r.MatrixSpectrumDataService.Create(ctx, matrixSpectrumData)
				}
			}()
			// 提交当前被攻击的IP到运营商防护
			// 失败缓存：5分钟；成功缓存20分钟
			SubmitProtect(r, ctx, &matrixStrategyInfo)
		}

		// 判断bps
		if matrixLineFlow.DataType == "bps" {
			if matrixStrategyInfo.MonitorBps != 0 && matrixBpsLineFlow.Bps > matrixStrategyInfo.MonitorBps {
				MonitorLineFlow(r, matrixLineFlow, matrixStrategyInfo)
			} else {
				UnMonitorLineFlow(r, matrixLineFlow)
			}
		}
		//if matrixLineFlow.DataType == "pps" {
		//	fmt.Printf("pps %+v\n", matrixLineFlow.Data)
		//}
		//if matrixLineFlow.DataType == "ratio" {
		//	fmt.Printf("ratio %+v\n", matrixLineFlow.Data)
		//}
	}
	//}()

	matrixLineResult = nil
	//resultStrs = nil
}

func UnMonitorLineFlow(r *Runner, matrixLineFlow *netease.MatrixLine) {
	if matrixLineFlow == nil {
		return
	}
	var matrixBpsLineFlow netease.MatrixBps
	// matrixPpsLineFlow   netease.MatrixPps
	// matrixRatioLineFlow netease.MatrixRatio
	if matrixLineFlow.DataType == "bps" {
		matrixBpsLineFlow = matrixLineFlow.Data.(netease.MatrixBps)
	}

	ctx := r.AuthEnt.GetSAContext()
	matrixFlowRedisKey := common.GenRedisKey("matrix", matrixLineFlow.Region, matrixLineFlow.NetType, matrixLineFlow.Isp)
	attackKey := common.KeyAttackPrefix + matrixFlowRedisKey
	attackExist, err := r.Rdb.Exists(ctx, attackKey).Result()
	if err != nil {
		r.Logger.Sugar().Error(err)
	}
	if attackExist == 1 {
		i, err := r.Rdb.Incr(ctx, lowerBps+matrixFlowRedisKey).Result()
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		// bps小于监控阈值次数，则攻击结束
		var lowerMonitorBpsCount int64 = 5
		if i >= lowerMonitorBpsCount {
			// 如果机房出口在告警中，更新对应告警的结束时间
			matrixSpectrumAlerts, err := r.MatrixSpectrumAlertService.Dao.MatrixSpectrumAlert.Query().Where(matrixspectrumalert.Region(matrixLineFlow.Region), matrixspectrumalert.NetType(matrixLineFlow.NetType), matrixspectrumalert.Isp(matrixLineFlow.Isp), matrixspectrumalert.EndTimeIsNil()).Order(ent.Desc(matrixspectrumalert.FieldCreatedAt)).Limit(1).All(ctx)
			if err != nil {
				r.Logger.Sugar().Error(err)
			}
			if len(matrixSpectrumAlerts) >= 1 {
				matrixSpectrumAlert := matrixSpectrumAlerts[0]
				var attackInfo netease.MatrixAttackInfo
				attackInfo.SpectrumAlertID = matrixSpectrumAlert.ID
				attackInfo.LowerMonitorBpsCount = lowerMonitorBpsCount
				// redis hash => struct
				if err := r.Rdb.HGetAll(ctx, attackKey).Scan(&attackInfo); err != nil {
					r.Logger.Sugar().Error(err.Error())
				}
				//更新IP
				netType := matrixLineFlow.NetType
				isp := matrixLineFlow.Isp
				rKey := common.KeyMatrixAttackPrefix + strconv.Itoa(netTypeMap[netType+":"+isp])
				results, err := r.Rdb.SMembers(ctx, rKey).Result()
				if err != nil {
					r.Logger.Sugar().Info(err)
				}
				alertUpdate := r.MatrixSpectrumAlertService.Dao.MatrixSpectrumAlert.UpdateOneID(matrixSpectrumAlert.ID)
				if results != nil {
					slices.Sort(results)
					alertUpdate.SetIPList(&results)
				}
				r.Rdb.Del(ctx, rKey)

				alertUpdate.SetAttackInfo(attackInfo).SetEndTime(matrixBpsLineFlow.QueryTime).Save(ctx)

				matrixAlertData := &netease.MatrixAlertData{
					Title:             fmt.Sprintf("%s【机房出口流量超出阈值告警】【结束】", common.Prefix),
					Status:            "结束",
					Prefix:            common.Prefix,
					Domain:            config.CFG.Stage.Domain,
					Schema:            config.CFG.Stage.HttpSchema,
					ProcessSuggestion: common.ProcessSuggestion,
					StartTime:         matrixSpectrumAlert.StartTime.Local().String(),
					EndTime:           time.Now().Local().String(),
					Region:            matrixLineFlow.Region,
					NetType:           matrixLineFlow.NetType,
					Isp:               matrixLineFlow.Isp,
					MatrixAlertID:     matrixSpectrumAlert.ID,
					AlertBps:          common.Bit2Str(matrixSpectrumAlert.Bps),
					MaxAttackBps:      common.Bit2Str(matrixSpectrumAlert.AttackInfo.MaxBps),
				}
				if matrixSpectrumAlert.MatrixStrategyID != nil {
					matrixAlertData.MatrixStrategyID = *matrixSpectrumAlert.MatrixStrategyID
				}

				content, err := common.ParseTemplate("matrixNotify", common.MatrixPopoTemplate, matrixAlertData)
				if err != nil {
					r.Logger.Sugar().Error(err)
				}
				r.Logger.Sugar().Infof("matrixAlert content: %s", content)
				fmt.Printf("end content\n%s", content)

				// 只需要推送到系统默认配置的通知策略
				r.NdsController.NotifyAlert(ctx, 0, "", "", matrixAlertData.Title, content)
			}

			// 攻击结束

			// 删除缓存
			r.Rdb.Del(ctx, attackKey)
			r.Rdb.Del(ctx, lowerBps+matrixFlowRedisKey)
		}
	}
}

func MonitorLineFlow(r *Runner, matrixLineFlow *netease.MatrixLine, matrixStrategyInfo netease.MatrixStrategyInfo) {
	ctx := r.AuthEnt.GetSAContext()
	matrixFlowRedisKey := common.GenRedisKey("matrix", matrixLineFlow.Region, matrixLineFlow.NetType, matrixLineFlow.Isp)
	attackKey := common.KeyAttackPrefix + matrixFlowRedisKey

	r.Rdb.Del(ctx, lowerBps+matrixFlowRedisKey)
	redisMaxBps, err := r.Rdb.HGet(ctx, attackKey, "maxBps").Result()
	if err != nil {
		r.Logger.Sugar().Error(err)
	}
	var matrixBpsLineFlow netease.MatrixBps
	// matrixPpsLineFlow   netease.MatrixPps
	// matrixRatioLineFlow netease.MatrixRatio
	if matrixLineFlow.DataType == "bps" {
		matrixBpsLineFlow = matrixLineFlow.Data.(netease.MatrixBps)
	}
	//if matrixLineFlow.DataType == "pps" {
	//	matrixPpsLineFlow = matrixLineFlow.Data.(netease.MatrixPps)
	//}
	//if matrixLineFlow.DataType == "ratio" {
	//	matrixRatioLineFlow = matrixLineFlow.Data.(netease.MatrixRatio)
	//}

	if redisMaxBps == "" {
		attackExist, err := r.Rdb.Exists(ctx, attackKey).Result()
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		// 产生告警 ->
		// 不存在key，攻击开始：同时记录，告警数据，分光数据(第一次)
		if attackExist == 0 {
			// 阈值大于设置的牵引阈值
			if matrixStrategyInfo.DragBps != 0 && matrixBpsLineFlow.Bps >= matrixStrategyInfo.DragBps {
				// 产生牵引 ->
				// 提交当前被攻击的ip到运营商防护
				go SubmitProtect(r, ctx, &matrixStrategyInfo)
			}

			// 刷新缓存
			r.Rdb.HSet(ctx, attackKey, "maxBps", matrixBpsLineFlow.Bps)
			// 告警数据
			netType := matrixLineFlow.NetType
			isp := matrixLineFlow.Isp
			msAlert := &ent.MatrixSpectrumAlert{
				AttackType: "bps",
				Bps:        matrixBpsLineFlow.Bps,
				Region:     matrixLineFlow.Region,
				NetType:    netType,
				Isp:        isp,
				StartTime:  matrixBpsLineFlow.QueryTime,
			}
			if matrixStrategyInfo.ID != "" {
				id, _ := strconv.Atoi(matrixStrategyInfo.ID)
				msAlert.MatrixStrategyID = &id
			}
			attackInfo := netease.MatrixAttackInfo{
				MaxBps: matrixBpsLineFlow.Bps,
			}
			msAlert.AttackInfo = attackInfo
			//增加被攻击IP
			rKey := common.KeyMatrixAttackPrefix + strconv.Itoa(netTypeMap[netType+":"+isp])
			results, err := r.Rdb.SMembers(ctx, rKey).Result()
			if err != nil {
				r.Logger.Sugar().Info(err)
			}
			if results != nil {
				slices.Sort(results)
				msAlert.IPList = &results
			}
			createSpectrumAlert, _ := r.MatrixSpectrumAlertService.Create(ctx, msAlert)
			// 刷新缓存
			r.Rdb.HSet(ctx, attackKey, map[string]any{"spectrumAlertID": createSpectrumAlert.ID})

			go func() {
				// 分光数据(第一次)
				matrixSpectrumData := &ent.MatrixSpectrumData{
					MatrixSpectrumAlertID: &createSpectrumAlert.ID,
					Region:                matrixLineFlow.Region,
					NetType:               matrixLineFlow.NetType,
					Isp:                   matrixLineFlow.Isp,
					Bps:                   matrixBpsLineFlow.Bps,
					Time:                  matrixBpsLineFlow.QueryTime,
				}
				r.MatrixSpectrumDataService.Create(ctx, matrixSpectrumData)
				fmt.Printf("\n攻击开始\n%+v\n", matrixSpectrumData)
			}()

			go func() {
				matrixAlertData := &netease.MatrixAlertData{
					Title:             fmt.Sprintf("%s【机房出口流量超出阈值告警】【开始】", common.Prefix),
					Status:            "开始",
					Prefix:            common.Prefix,
					Domain:            config.CFG.Stage.Domain,
					Schema:            config.CFG.Stage.HttpSchema,
					ProcessSuggestion: common.ProcessSuggestion,
					StartTime:         matrixBpsLineFlow.QueryTime.Local().String(),
					MatrixAlertID:     createSpectrumAlert.ID,
					AlertBps:          common.Bit2Str(matrixBpsLineFlow.Bps),
					Region:            matrixLineFlow.Region,
					NetType:           matrixLineFlow.NetType,
					Isp:               matrixLineFlow.Isp,
				}
				if createSpectrumAlert.MatrixStrategyID != nil {
					matrixAlertData.MatrixStrategyID = *createSpectrumAlert.MatrixStrategyID
				}

				content, err := common.ParseTemplate("matrixNotify", common.MatrixPopoTemplate, matrixAlertData)
				if err != nil {
					r.Logger.Sugar().Error(err)
				}
				r.Logger.Sugar().Infof("matrixAlert content: %s", content)

				fmt.Printf("start content\n%s", content)

				// 只需要推送到系统默认配置的通知策略
				r.NdsController.NotifyAlert(ctx, 0, "", "", matrixAlertData.Title, content)

				systemConfig, _ := cache.GetSystemConfigCache(r.Rdb, r.SystemConfigService, ctx)
				if strings.Contains(systemConfig.NotifyScenes, "机房出口告警:开始") {
					content := common.ConcatString("【告警提升】" + common.Prefix + "【机房出口流量超出阈值告警】")
					err = http.NotifyPhone(content, strings.Split(systemConfig.NotifyPhones, ","))
					err = http.NotifyPopo(content, strings.Split(systemConfig.NotifyEmails, ","))
					if err != nil {
						r.Logger.Sugar().Error(err)
					}
				}

				// 联通静态出口
				if matrixLineFlow.Isp == "ChinaUnicom" && matrixLineFlow.NetType == "Static" {
					if strings.Contains(systemConfig.NotifyScenes, "机房出口告警开始&联通静态出口超过阈值&NDS告警IP是联通静态IP") {
						// 获取当前被攻击的所有联通静态IP，加到告警里面
						_, selectedIPList := GetAttackingIP(r, ctx, 10010)
						if len(selectedIPList) != 0 {
							attackIP := strings.Join(selectedIPList, ",")
							content := common.ConcatString(common.Prefix, "【机房出口流量超出阈值告警-联通静态IP】", attackIP)
							err = http.NotifyPhone(content, strings.Split(systemConfig.NotifyPhones, ","))
							err = http.NotifyPopo(content, strings.Split(systemConfig.NotifyEmails, ","))
							if err != nil {
								r.Logger.Sugar().Error(err)
							}
						}
					}
				}
			}()
		}
	} else {
		redisBps, err := strconv.ParseInt(redisMaxBps, 10, 64)
		if err != nil {
			r.Logger.Sugar().Error(err)
		}
		// 刷新最大值
		if redisBps < matrixBpsLineFlow.Bps {
			r.Rdb.HSet(ctx, attackKey, "maxBps", matrixBpsLineFlow.Bps)
		}
	}
}

func SubmitProtect(r *Runner, ctx context.Context, matrixStrategyInfo *netease.MatrixStrategyInfo) {
	wofangFlag, _ := r.Rdb.Get(ctx, common.Key10010CleanFlagPrefix).Result()
	if wofangFlag == "1" {
		return
	}

	r.Rdb.Set(ctx, common.Key10010CleanFlagPrefix, "1", 0)

	attackingIPList, _ := GetAttackingIP(r, ctx, 0)
	// 提交当前被攻击的ip到运营商防护
	for _, attackingIP := range attackingIPList {
		_, strategyType := r.NdsController.GetISPIDAndStrategyType(attackingIP, ctx)
		// 出口防护配置，bgp使用联通沃防
		if strategyType == 1 && strategyType == matrixStrategyInfo.DragType {
			// 判断是否在沃防
			wfResult, err := r.Rdb.Get(ctx, common.Key10010CleanPrefix+attackingIP).Result()
			wfFailedResult, _ := r.Rdb.Get(ctx, common.Key10010CleanFailedPrefix+attackingIP).Result()
			// 已提交成功或失败
			if wfResult == "1" || wfFailedResult == "1" {
				continue
			}
			// ip, project id,
			var projectID int
			realProject := r.NdsController.ProjectCtl.GetRealProject(ctx, attackingIP)
			if realProject != nil {
				projectID = realProject.ID
			}

			r.Logger.Sugar().Infof("提交沃防工单：%s", attackingIP)
			fmt.Println("提交沃防工单：", attackingIP)

			woFangId, status, _, _ := r.NdsController.DragWoFangClean(ctx, attackingIP, projectID, "机房出口防护模块", 0)
			spectrumAlerts, err := r.SpectrumAlertService.Dao.SpectrumAlert.Query().Where(spectrumalert.IP(attackingIP), spectrumalert.EndTimeIsNil()).Order(ent.Desc(spectrumalert.FieldCreatedAt)).Limit(1).All(ctx)
			if err != nil {
				r.Logger.Sugar().Error(err)
			}
			if len(spectrumAlerts) >= 1 {
				spectrumAlert := spectrumAlerts[0]
				if spectrumAlert.ProtectStatus == nil {
					continue
				}
				targetStatus := *spectrumAlert.ProtectStatus
				targetStatus = append(targetStatus, status)
				if status != 0 {
					if status == common.WofangFailed {
						r.Rdb.Set(ctx, common.Key10010CleanFailedPrefix+attackingIP, "1", time.Minute*5)
					} else {
						r.Rdb.Set(ctx, common.Key10010CleanPrefix+attackingIP, "1", time.Minute*20)
					}
					resultSlice := common.SortRemoveDuplElement(targetStatus)
					updateOneID := r.SpectrumAlertService.Dao.SpectrumAlert.UpdateOneID(spectrumAlert.ID)
					if woFangId != 0 {
						updateOneID.SetWofangID(woFangId)
					}
					if !common.IntSliceEqual(*spectrumAlert.ProtectStatus, resultSlice) {
						updateOneID.SetProtectStatus(&resultSlice).Save(ctx)
					}
				}
			}
		}
	}
	r.Rdb.Del(ctx, common.Key10010CleanFlagPrefix)
}

// GetAttackingIP ispType与ISPID相同
func GetAttackingIP(r *Runner, ctx context.Context, ispType int) (attackingIPList, selectedIPList []string) {
	iter := r.Rdb.Scan(ctx, 0, common.KeyAttackPrefix+"*", 0).Iterator()
	for iter.Next(ctx) {
		key := iter.Val()
		if !strings.Contains(key, ":") {
			continue
		}
		ipTemp := strings.Split(key, ":")[1]
		parseIP := net.ParseIP(strings.TrimSpace(ipTemp))
		// 不是IP
		if parseIP == nil {
			continue
		}
		realIp := parseIP.String()
		attackingIPList = append(attackingIPList, realIp)

		if ispType != 0 {
			ispID, _ := r.NdsController.GetISPIDAndStrategyType(realIp, ctx)
			if ispType == ispID {
				selectedIPList = append(selectedIPList, realIp)
			}
		}
	}
	return
}
