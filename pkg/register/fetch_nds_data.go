/**
* <AUTHOR>
* @date 2024-01-15 10:25
* @description
 */

package register

import (
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqljson"
	"meta/app/ent"
	"meta/app/ent/spectrumalert"
	"meta/pkg/http"
	"sync"
	"time"
)

var ndsFirstRun = true

func (r *Runner) FetchNDSData() {
	r.<PERSON>.<PERSON>().Info("fetch nds spectrum data and clean data")

	ctx := r.AuthEnt.GetSAContext()
	var (
		spectrumAlerts []*ent.SpectrumAlert
		err            error
		f              = func(s *sql.Selector) {
			s.Where(sqljson.ValueEQ(spectrumalert.FieldAttackInfo, 0, sqljson.Path("maxCleanBps")))
		}
	)
	alertQuery := r.SpectrumAlertService.Dao.SpectrumAlert.Query().Where(f, spectrumalert.EndTimeNotNil())
	if ndsFirstRun {
		spectrumAlerts, err = alertQuery.All(ctx)
		if err != nil {
			r.<PERSON><PERSON>.<PERSON>().Info(err)
		}
		ndsFirstRun = false
	} else {
		now := time.Now()
		startTime := now.Add(time.Hour * 3)
		endTime := now
		spectrumAlerts, err = alertQuery.Where(spectrumalert.And(spectrumalert.StartTimeGTE(startTime), spectrumalert.StartTimeLTE(endTime))).All(ctx)
		if err != nil {
			r.Logger.Sugar().Info(err)
		}
	}

	for _, v := range spectrumAlerts {
		var maxBps, maxPps, maxCleanBps, maxCleanPps int64
		ip := v.IP
		startTime := v.StartTime
		endTime := v.EndTime
		attackInfo := v.AttackInfo

		wg := &sync.WaitGroup{}
		wg.Add(1)
		go func() {
			defer wg.Done()
			spectrumDatas, err := http.GetSpectrumData(ip, startTime, endTime)
			if err != nil {
				r.Logger.Sugar().Info(err)
			}
			if len(spectrumDatas) != 0 {
				r.Logger.Sugar().Infof("获取到 %s 分光数据：%d\n", ip, len(spectrumDatas))
				for _, v := range spectrumDatas {
					if maxBps < v.Bps {
						maxBps = v.Bps
					}
					if maxPps < v.Pps {
						maxPps = v.Pps
					}
				}
			}
		}()

		wg.Add(1)
		go func() {
			defer wg.Done()
			cleanDatas, err := http.GetCleanData(ip, startTime, endTime)
			if err != nil {
				r.Logger.Sugar().Info(err)
			}
			if len(cleanDatas) != 0 {
				r.Logger.Sugar().Infof("获取到 %s 清洗数据：%d\n", ip, len(cleanDatas))
				for _, v := range cleanDatas {
					newBps := v.InBps - v.OutBps
					if maxCleanBps < newBps {
						maxCleanBps = newBps
					}
					newPps := v.InPps - v.OutPps
					if maxCleanPps < newPps {
						maxCleanPps = newPps
					}
				}
			}
		}()
		wg.Wait()

		var updateFlag bool
		if attackInfo.MaxBps < maxBps {
			attackInfo.MaxBps = maxBps
			updateFlag = true
		}
		if attackInfo.MaxPps < maxPps {
			attackInfo.MaxPps = maxPps
			updateFlag = true
		}
		if attackInfo.MaxCleanBps < maxCleanBps {
			attackInfo.MaxCleanBps = maxCleanBps
			updateFlag = true
		}
		if attackInfo.MaxCleanPps < maxCleanPps {
			attackInfo.MaxCleanPps = maxCleanPps
			updateFlag = true
		}
		if updateFlag {
			r.SpectrumAlertService.Dao.SpectrumAlert.UpdateOneID(v.ID).SetAttackInfo(attackInfo).Save(ctx)
		}
	}
}
