package main

import (
	"log"
	_ "meta/app/ent/runtime"
	"meta/app/entity/config"
	_ "meta/docs" // load API Docs files (Swagger)
	"meta/pkg/util"
)

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as abow.

// @title API
// @version 0.1
// @description This is an auto-generated API Docs.
// @termsOfService http://swagger.io/terms/
// @contact.name API Support
// @contact.email <EMAIL>
// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html
// @securityDefinitions.apikey ApiKeyAuth
// @in header
// @name Authorization
func main() {
	err := util.LoadConfig("resource")
	if err != nil {
		log.Fatal(err)
	}

	fiberApp, router, register, f := util.InjectApp()
	defer f()

	util.RegisterRouter(router, fiberApp)
	// 获取api 路径
	routes := fiberApp.GetRoutes()
	go register.Inject(routes)

	// Start server (with or without graceful shutdown).
	if config.CFG.Stage.Status == "dev" {
		util.StartServer(fiberApp)
	} else {
		util.StartServerWithGracefulShutdown(fiberApp)
	}
}
