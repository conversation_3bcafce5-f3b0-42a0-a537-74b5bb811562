package v1

import (
	"meta/app/entity/config"

	"github.com/gofiber/fiber/v2"
)

// Private 私有路由
func (r *Router) Private(fa *fiber.App) {
	var router fiber.Router
	// dev环境未启用认证授权
	if !config.CFG.Auth.Enable && config.CFG.Stage.Status == "dev" {
		router = fa.Group("/api", r.JWT.SaJWT(), r.Gen.SaCtx(), r.Casbinx.SaCasbin())
	} else {
		// router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx(), r.OPLog.LogUserOperation())

		// router = fa.Group("/api", r.JWT.AuthJWT(), r.OPLog.LogUserOperation(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx())

		// router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx(), r.OPLog.LogUserOperation())
		router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.OPLog.LogUserOperation(), r.Gen.AuthCtx())

		// router = fa.Group("/api", r.JWT.AuthJWT(), r.Casbinx.AuthCasbin(r.Enf), r.Gen.AuthCtx())
	}
	v1 := router.Group("/v1")

	// ProtectGroup路由
	protectGroupRouter := v1.Group("protectgroup")
	protectGroupRouter.Get("", r.ProtectGroupController.Query)
	protectGroupRouter.Get(":id", r.Gen.IntId(), r.ProtectGroupController.QueryByID)
	protectGroupRouter.Post("", r.ProtectGroupController.Create)
	protectGroupRouter.Post("bulk", r.ProtectGroupController.CreateBulk)
	protectGroupRouter.Post("bulk/delete", r.ProtectGroupController.DeleteBulk)
	protectGroupRouter.Put(":id", r.Gen.IntId(), r.ProtectGroupController.UpdateByID)
	protectGroupRouter.Delete(":id", r.Gen.IntId(), r.ProtectGroupController.DeleteByID)

	// SpectrumData路由
	spectrumDataRouter := v1.Group("spectrumdata")
	spectrumDataRouter.Get("", r.SpectrumDataController.Query)
	spectrumDataRouter.Get(":id", r.Gen.IntId(), r.SpectrumDataController.QueryByID)
	spectrumDataRouter.Post("", r.SpectrumDataController.Create)
	spectrumDataRouter.Post("bulk", r.SpectrumDataController.CreateBulk)
	spectrumDataRouter.Post("bulk/delete", r.SpectrumDataController.DeleteBulk)
	spectrumDataRouter.Put(":id", r.Gen.IntId(), r.SpectrumDataController.UpdateByID)
	spectrumDataRouter.Delete(":id", r.Gen.IntId(), r.SpectrumDataController.DeleteByID)

	// Strategy路由
	strategyRouter := v1.Group("strategy")
	strategyRouter.Get("", r.StrategyController.Query)
	strategyRouter.Get(":id", r.Gen.IntId(), r.StrategyController.QueryByID)
	strategyRouter.Post("", r.StrategyController.Create)
	strategyRouter.Post("bulk", r.StrategyController.CreateBulk)
	strategyRouter.Post("bulk/delete", r.StrategyController.DeleteBulk)
	strategyRouter.Put(":id", r.Gen.IntId(), r.StrategyController.UpdateByID)
	strategyRouter.Delete(":id", r.Gen.IntId(), r.StrategyController.DeleteByID)

	// User路由
	userRouter := v1.Group("user")
	userRouter.Get("info", r.UserController.UserInfo)

	userRouter.Get("", r.UserController.Query)
	userRouter.Get(":id", r.Gen.IntId(), r.UserController.QueryByID)
	userRouter.Post("", r.UserController.Create)
	userRouter.Post("bulk", r.UserController.CreateBulk)
	userRouter.Post("bulk/delete", r.UserController.DeleteBulk)
	userRouter.Put(":id", r.Gen.IntId(), r.UserController.UpdateByID)
	userRouter.Delete(":id", r.Gen.IntId(), r.UserController.DeleteByID)

	// CasbinRule路由
	casbinRuleRouter := v1.Group("casbinrule")
	casbinRuleRouter.Get("", r.CasbinRuleController.Query)
	casbinRuleRouter.Get(":id", r.Gen.IntId(), r.CasbinRuleController.QueryByID)
	casbinRuleRouter.Post("", r.CasbinRuleController.Create)
	casbinRuleRouter.Post("bulk", r.CasbinRuleController.CreateBulk)
	casbinRuleRouter.Post("bulk/delete", r.CasbinRuleController.DeleteBulk)
	casbinRuleRouter.Put(":id", r.Gen.IntId(), r.CasbinRuleController.UpdateByID)
	casbinRuleRouter.Delete(":id", r.Gen.IntId(), r.CasbinRuleController.DeleteByID)

	// Notify路由
	notifyRouter := v1.Group("notify")
	notifyRouter.Get("", r.NotifyController.Query)
	notifyRouter.Get(":id", r.Gen.IntId(), r.NotifyController.QueryByID)
	notifyRouter.Post("", r.NotifyController.Create)
	notifyRouter.Post("bulk", r.NotifyController.CreateBulk)
	notifyRouter.Post("bulk/delete", r.NotifyController.DeleteBulk)
	notifyRouter.Put(":id", r.Gen.IntId(), r.NotifyController.UpdateByID)
	notifyRouter.Delete(":id", r.Gen.IntId(), r.NotifyController.DeleteByID)

	// SpectrumAlert路由
	spectrumAlertRouter := v1.Group("spectrumalert")
	spectrumAlertRouter.Get("", r.SpectrumAlertController.Query)
	spectrumAlertRouter.Get("attacking", r.SpectrumAlertController.GetAttackData)
	spectrumAlertRouter.Get(":id", r.Gen.IntId(), r.SpectrumAlertController.QueryByID)
	spectrumAlertRouter.Post("", r.SpectrumAlertController.Create)
	spectrumAlertRouter.Post("bulk", r.SpectrumAlertController.CreateBulk)
	spectrumAlertRouter.Post("bulk/delete", r.SpectrumAlertController.DeleteBulk)
	spectrumAlertRouter.Put(":id", r.Gen.IntId(), r.SpectrumAlertController.UpdateByID)
	spectrumAlertRouter.Delete(":id", r.Gen.IntId(), r.SpectrumAlertController.DeleteByID)

	// Tenant路由
	tenantRouter := v1.Group("tenant")
	tenantRouter.Get("", r.TenantController.Query)
	tenantRouter.Get(":id", r.Gen.IntId(), r.TenantController.QueryByID)
	tenantRouter.Post("", r.TenantController.Create)
	tenantRouter.Post("bulk", r.TenantController.CreateBulk)
	tenantRouter.Post("bulk/delete", r.TenantController.DeleteBulk)
	tenantRouter.Put(":id", r.Gen.IntId(), r.TenantController.UpdateByID)
	tenantRouter.Delete(":id", r.Gen.IntId(), r.TenantController.DeleteByID)

	// CleanData路由
	cleanDataRouter := v1.Group("cleandata")
	cleanDataRouter.Get("", r.CleanDataController.Query)
	cleanDataRouter.Get(":id", r.Gen.IntId(), r.CleanDataController.QueryByID)
	cleanDataRouter.Post("", r.CleanDataController.Create)
	cleanDataRouter.Post("bulk", r.CleanDataController.CreateBulk)
	cleanDataRouter.Post("bulk/delete", r.CleanDataController.DeleteBulk)
	cleanDataRouter.Put(":id", r.Gen.IntId(), r.CleanDataController.UpdateByID)
	cleanDataRouter.Delete(":id", r.Gen.IntId(), r.CleanDataController.DeleteByID)

	// Group路由
	groupRouter := v1.Group("group")
	groupRouter.Get("", r.GroupController.Query)
	groupRouter.Get(":id", r.Gen.IntId(), r.GroupController.QueryByID)
	groupRouter.Post("", r.GroupController.Create)
	groupRouter.Post("bulk", r.GroupController.CreateBulk)
	groupRouter.Post("bulk/delete", r.GroupController.DeleteBulk)
	groupRouter.Put(":id", r.Gen.IntId(), r.GroupController.UpdateByID)
	groupRouter.Delete(":id", r.Gen.IntId(), r.GroupController.DeleteByID)

	// 集团NDS路由
	ndsRouter := v1.Group("nds")
	ndsRouter.Post("ip", r.NdsController.AddIP2ProtectGroup)
	ndsRouter.Post("push", r.NdsController.Push)
	ndsRouter.Get("alert", r.NdsController.GetSpectrumAlert)
	ndsRouter.Get("cleandata", r.NdsController.GetCleanData)
	ndsRouter.Get("spectrumdata", r.NdsController.GetSpectrumData)

	// 集团soc路由（工单）
	socGroupRouter := v1.Group("socgroup")
	socGroupRouter.Get("ticket", r.SocGroupController.Query)
	socGroupRouter.Post("ticket", r.SocGroupController.Add)

	// Wofang路由
	wofangRouter := v1.Group("wofang")

	// 沃防外部路由
	woFangApiRouter := wofangRouter.Group("api")
	woFangApiRouter.Get("", r.WoFangApi.Query)
	woFangApiRouter.Post("", r.WoFangApi.Add)
	woFangApiRouter.Delete(":dragType/:ip", r.WoFangApi.Delete)

	woFangApiRouter2 := wofangRouter.Group("api2")
	woFangApiRouter2.Post("", r.WoFangApi2.Add)
	woFangApiRouter2.Delete(":ip", r.WoFangApi2.Delete)

	// Wofang路由
	wofangRouter.Get("", r.WofangController.Query)
	wofangRouter.Get(":id", r.Gen.IntId(), r.WofangController.QueryByID)
	wofangRouter.Post("", r.WofangController.Create)
	wofangRouter.Post("bulk", r.WofangController.CreateBulk)
	wofangRouter.Post("bulk/delete", r.WofangController.DeleteBulk)
	wofangRouter.Put(":id", r.Gen.IntId(), r.WofangController.UpdateByID)
	wofangRouter.Delete(":id", r.Gen.IntId(), r.WofangController.DeleteByID)

	// SocGroupTicket路由
	socGroupTicketRouter := v1.Group("socgroupticket")
	socGroupTicketRouter.Get("", r.SocGroupTicketController.Query)
	socGroupTicketRouter.Get(":id", r.Gen.IntId(), r.SocGroupTicketController.QueryByID)
	socGroupTicketRouter.Post("", r.SocGroupTicketController.Create)
	socGroupTicketRouter.Post("bulk", r.SocGroupTicketController.CreateBulk)
	socGroupTicketRouter.Post("bulk/delete", r.SocGroupTicketController.DeleteBulk)
	socGroupTicketRouter.Put(":id", r.Gen.IntId(), r.SocGroupTicketController.UpdateByID)
	socGroupTicketRouter.Delete(":id", r.Gen.IntId(), r.SocGroupTicketController.DeleteByID)

	// SystemApi路由
	systemApiRouter := v1.Group("systemapi")
	systemApiRouter.Get("", r.SystemApiController.Query)
	systemApiRouter.Get(":id", r.Gen.IntId(), r.SystemApiController.QueryByID)
	systemApiRouter.Post("", r.SystemApiController.Create)
	systemApiRouter.Post("bulk", r.SystemApiController.CreateBulk)
	systemApiRouter.Post("bulk/delete", r.SystemApiController.DeleteBulk)
	systemApiRouter.Put(":id", r.Gen.IntId(), r.SystemApiController.UpdateByID)
	systemApiRouter.Delete(":id", r.Gen.IntId(), r.SystemApiController.DeleteByID)

	// CloudAlert路由
	cloudAlertRouter := v1.Group("cloudalert")
	cloudAlertRouter.Get("", r.CloudAlertController.Query)
	cloudAlertRouter.Get(":id", r.Gen.IntId(), r.CloudAlertController.QueryByID)
	cloudAlertRouter.Post("", r.CloudAlertController.Create)
	cloudAlertRouter.Post("bulk", r.CloudAlertController.CreateBulk)
	cloudAlertRouter.Post("bulk/delete", r.CloudAlertController.DeleteBulk)
	cloudAlertRouter.Put(":id", r.Gen.IntId(), r.CloudAlertController.UpdateByID)
	cloudAlertRouter.Delete(":id", r.Gen.IntId(), r.CloudAlertController.DeleteByID)

	// CloudFlow路由
	cloudFlowRouter := v1.Group("cloudflowdata")
	cloudFlowRouter.Get("", r.CloudFlowDataController.Query)
	cloudFlowRouter.Get(":id", r.Gen.IntId(), r.CloudFlowDataController.QueryByID)
	cloudFlowRouter.Post("", r.CloudFlowDataController.Create)
	cloudFlowRouter.Post("bulk", r.CloudFlowDataController.CreateBulk)
	cloudFlowRouter.Post("bulk/delete", r.CloudFlowDataController.DeleteBulk)
	cloudFlowRouter.Put(":id", r.Gen.IntId(), r.CloudFlowDataController.UpdateByID)
	cloudFlowRouter.Delete(":id", r.Gen.IntId(), r.CloudFlowDataController.DeleteByID)

	// CloudAttackData 路由
	CloudAttackDataRouter := v1.Group("cloudattackdata")
	CloudAttackDataRouter.Get("", r.CloudAttackDataController.Query)
	CloudAttackDataRouter.Get(":id", r.Gen.IntId(), r.CloudAttackDataController.QueryByID)
	CloudAttackDataRouter.Post("", r.CloudAttackDataController.Create)
	CloudAttackDataRouter.Post("bulk", r.CloudAttackDataController.CreateBulk)
	CloudAttackDataRouter.Post("bulk/delete", r.CloudAttackDataController.DeleteBulk)
	CloudAttackDataRouter.Put(":id", r.Gen.IntId(), r.CloudAttackDataController.UpdateByID)
	CloudAttackDataRouter.Delete(":id", r.Gen.IntId(), r.CloudAttackDataController.DeleteByID)

	// WoFangAlert路由
	woFangAlertRouter := v1.Group("wofangalert")
	woFangAlertRouter.Get("", r.WoFangAlertController.Query)
	woFangAlertRouter.Get(":id", r.Gen.IntId(), r.WoFangAlertController.QueryByID)
	woFangAlertRouter.Post("", r.WoFangAlertController.Create)
	woFangAlertRouter.Post("bulk", r.WoFangAlertController.CreateBulk)
	woFangAlertRouter.Post("bulk/delete", r.WoFangAlertController.DeleteBulk)
	woFangAlertRouter.Put(":id", r.Gen.IntId(), r.WoFangAlertController.UpdateByID)
	woFangAlertRouter.Delete(":id", r.Gen.IntId(), r.WoFangAlertController.DeleteByID)

	// SkylineDos 路由
	SkylineDosRouter := v1.Group("skylinedos")
	SkylineDosRouter.Get("", r.SkylineDosController.Query)
	SkylineDosRouter.Get(":id", r.Gen.IntId(), r.SkylineDosController.QueryByID)
	SkylineDosRouter.Post("", r.SkylineDosController.Create)
	SkylineDosRouter.Post("bulk", r.SkylineDosController.CreateBulk)
	SkylineDosRouter.Post("bulk/delete", r.SkylineDosController.DeleteBulk)
	SkylineDosRouter.Put(":id", r.Gen.IntId(), r.SkylineDosController.UpdateByID)
	SkylineDosRouter.Delete(":id", r.Gen.IntId(), r.SkylineDosController.DeleteByID)

	// MatrixStategy 路由
	MatrixStrategyRouter := v1.Group("matrixstrategy")
	MatrixStrategyRouter.Get("", r.MatrixStrategyController.Query)
	MatrixStrategyRouter.Get(":id", r.Gen.IntId(), r.MatrixStrategyController.QueryByID)
	MatrixStrategyRouter.Post("", r.MatrixStrategyController.Create)
	MatrixStrategyRouter.Post("bulk", r.MatrixStrategyController.CreateBulk)
	MatrixStrategyRouter.Post("bulk/delete", r.MatrixStrategyController.DeleteBulk)
	MatrixStrategyRouter.Put(":id", r.Gen.IntId(), r.MatrixStrategyController.UpdateByID)
	MatrixStrategyRouter.Delete(":id", r.Gen.IntId(), r.MatrixStrategyController.DeleteByID)

	// MatrixSpectrumData 路由
	MatrixSpectrumDataRouter := v1.Group("matrixspectrumdata")
	MatrixSpectrumDataRouter.Get("", r.MatrixSpectrumDataController.Query)
	MatrixSpectrumDataRouter.Get(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.QueryByID)
	MatrixSpectrumDataRouter.Post("", r.MatrixSpectrumDataController.Create)
	MatrixSpectrumDataRouter.Post("bulk", r.MatrixSpectrumDataController.CreateBulk)
	MatrixSpectrumDataRouter.Post("bulk/delete", r.MatrixSpectrumDataController.DeleteBulk)
	MatrixSpectrumDataRouter.Put(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.UpdateByID)
	MatrixSpectrumDataRouter.Delete(":id", r.Gen.IntId(), r.MatrixSpectrumDataController.DeleteByID)

	// MatrixSpectrumAlert 路由
	MatrixSpectrumAlertRouter := v1.Group("matrixspectrumalert")
	MatrixSpectrumAlertRouter.Get("", r.MatrixSpectrumAlertController.Query)
	MatrixSpectrumAlertRouter.Get("attacking", r.MatrixSpectrumAlertController.GetAttackData)
	MatrixSpectrumAlertRouter.Get(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.QueryByID)
	MatrixSpectrumAlertRouter.Post("", r.MatrixSpectrumAlertController.Create)
	MatrixSpectrumAlertRouter.Post("bulk", r.MatrixSpectrumAlertController.CreateBulk)
	MatrixSpectrumAlertRouter.Post("bulk/delete", r.MatrixSpectrumAlertController.DeleteBulk)
	MatrixSpectrumAlertRouter.Put(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.UpdateByID)
	MatrixSpectrumAlertRouter.Delete(":id", r.Gen.IntId(), r.MatrixSpectrumAlertController.DeleteByID)

	// Chart 路由
	ChartRouter := v1.Group("chart")
	// 分光数据
	ChartRouter.Get("spectrum", r.ChartApi.Query)

	// UserOperationLog 路由
	UserOperationLogRouter := v1.Group("useroperationlog")
	UserOperationLogRouter.Get("", r.UserOperationLogController.Query)
	UserOperationLogRouter.Get(":id", r.Gen.IntId(), r.UserOperationLogController.QueryByID)
	UserOperationLogRouter.Post("", r.UserOperationLogController.Create)
	UserOperationLogRouter.Post("bulk", r.UserOperationLogController.CreateBulk)
	UserOperationLogRouter.Post("bulk/delete", r.UserOperationLogController.DeleteBulk)
	UserOperationLogRouter.Put(":id", r.Gen.IntId(), r.UserOperationLogController.UpdateByID)
	UserOperationLogRouter.Delete(":id", r.Gen.IntId(), r.UserOperationLogController.DeleteByID)

	// UserOperationLog 路由
	SystemConfigRouter := v1.Group("systemconfig")
	SystemConfigRouter.Get("", r.SystemConfigController.Query)
	SystemConfigRouter.Get(":id", r.Gen.IntId(), r.SystemConfigController.QueryByID)
	SystemConfigRouter.Post("", r.SystemConfigController.Create)
	SystemConfigRouter.Post("bulk", r.SystemConfigController.CreateBulk)
	SystemConfigRouter.Post("bulk/delete", r.SystemConfigController.DeleteBulk)
	SystemConfigRouter.Put(":id", r.Gen.IntId(), r.SystemConfigController.UpdateByID)
	SystemConfigRouter.Delete(":id", r.Gen.IntId(), r.SystemConfigController.DeleteByID)

	// DataSync 路由
	DataSyncRouter := v1.Group("datasync")
	DataSyncRouter.Get("", r.DataSyncController.Query)
	DataSyncRouter.Get(":id", r.Gen.IntId(), r.DataSyncController.QueryByID)
	DataSyncRouter.Post("", r.DataSyncController.Create)
	DataSyncRouter.Post("bulk", r.DataSyncController.CreateBulk)
	DataSyncRouter.Post("bulk/delete", r.DataSyncController.DeleteBulk)
	DataSyncRouter.Put(":id", r.Gen.IntId(), r.DataSyncController.UpdateByID)
	DataSyncRouter.Delete(":id", r.Gen.IntId(), r.DataSyncController.DeleteByID)

}
