package v1

import (
	"meta/app/entity/config"

	"github.com/gofiber/fiber/v2"
)

// UserCommon 登录用户的公共路由
// 不需要RBAC，全部登录用户都可以访问
func (r *Router) UserCommon(fa *fiber.App) {
	var router fiber.Router
	// dev环境未启用认证授权
	if !config.CFG.Auth.Enable && config.CFG.Stage.Status == "dev" {
		router = fa.Group("/api", r.JWT.SaJWT(), r.Gen.SaCtx(), r.Casbinx.SaCasbin())
	} else {
		// 登录后，手动给sa会话
		// router = fa.Group("/api", r.JWT.AuthJWT(), r.Gen.SaCtx(), r.Casbinx.SaCasbin())
		router = fa.Group("/api", r.JWT.AuthJWT(), r.Gen.SaCtx())
	}
	v1 := router.Group("/v1")

	commonRouter := v1.Group("common")

	userRouter := commonRouter.Group("user")
	userRouter.Get("info", r.UserController.UserInfo)

	tenantRouter := commonRouter.Group("tenant", r.OPLog.LogUserOperation())
	// tenantRouter := commonRouter.Group("tenant")
	tenantRouter.Get("", r.TenantController.Query)

	protectGroupRouter := commonRouter.Group("protectgroup", r.OPLog.LogUserOperation())
	// protectGroupRouter := commonRouter.Group("protectgroup")
	protectGroupRouter.Get("", r.Gen.CheckExpendIP(), r.ProtectGroupController.Query)
}
