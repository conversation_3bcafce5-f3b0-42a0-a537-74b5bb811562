package test

import (
	"meta/app/ent"
	"meta/app/ent/group"
	"meta/pkg/common"
	"strconv"
	"testing"
)

var groupApi = baseApi + "/group"
var testGroup = &ent.Group{Name: "WjKdDnuedk"}
var groupIDs = []int{}

// Create Group test case
// 创建
func TestCreateGroup(t *testing.T) {
	successExpectedResult.ResponseData = testGroup.Name
	testCase := &CaseRule{Api: groupApi, HttpMethod: "POST", BodyData: testGroup, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	groupIDs = append(groupIDs, getDataMapId(result))
}

// CreateBulk Group test case
// 批量创建
func TestCreateBulkGroup(t *testing.T) {
	bulkData1 := &ent.Group{Name: "uCXOpcSpBa"}
	bulkData2 := &ent.Group{Name: "TpajQapJgS"}
	bulkData3 := &ent.Group{Name: "hbJJBryxwR"}
	bulkData4 := &ent.Group{Name: "lEdbdkncZV"}
	bulkData5 := &ent.Group{Name: "vFUwaDPwsp"}
	bulkDatas := [...]ent.Group{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: groupApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		groupIDs = append(groupIDs, getDataMapId(v))
	}
}

// Query Group test case
// 根据指定字段、时间范围查询或搜索
func TestQueryGroup(t *testing.T) {
	successExpectedResult.ResponseData = testGroup.Name
	testCase := &CaseRule{Api: groupApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID Group test case
// 根据 ID 查询
func TestQueryByIDGroup(t *testing.T) {
	successExpectedResult.ResponseData = testGroup.Name
	testCase := &CaseRule{Api: groupApi, HttpMethod: "GET", UrlData: strconv.Itoa(groupIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID Group not exist test case
// 根据 ID 查询
func TestQueryByIDGroupNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testGroup.Name
	testCase := &CaseRule{Api: groupApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query Group by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryGroupByName(t *testing.T) {
	successExpectedResult.ResponseData = testGroup.Name
	testCase := &CaseRule{Api: groupApi, HttpMethod: "GET", UrlData: group.FieldName + "=" + testGroup.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch Group search by Name test case
// 分页搜索
func TestQuerySearchGroupName(t *testing.T) {
	successExpectedResult.ResponseData = testGroup.Name
	testCase := &CaseRule{Api: groupApi, HttpMethod: "GET", UrlData: "search=" + testGroup.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID Group test case
// 根据 ID 修改
func TestUpdateByIDGroup(t *testing.T) {
	updateData := &ent.Group{Name: "uLZwVhenJd"}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: groupApi, HttpMethod: "PUT", UrlData: strconv.Itoa(groupIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID Group test case
// 根据 ID 删除
func TestDeleteByIDGroup(t *testing.T) {
	testCase := &CaseRule{Api: groupApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(groupIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID Group not exist test case
// 根据 ID 删除
func TestDeleteByIDGroupNoExist(t *testing.T) {
	testCase := &CaseRule{Api: groupApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk Group test case
// 根据 IDs 批量删除
func TestDeleteBulkGroup(t *testing.T) {
	testCase := &CaseRule{Api: groupApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: groupIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
