package test

import (
	"meta/app/ent"
	"meta/app/ent/cleandata"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var cleanDataApi = baseApi + "/cleandata"
var cfilter14 = "DAYoiTItHr"
var host14 = "wmbsnNRlJz"
var testCleanData = &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "GUQhnGzZkN", Time: time.Now().AddDate(0, 0, -1), InBps: 366, OutBps: 393, InPps: 374, OutPps: 316, InAckPps: 344, OutAckPps: 342, InAckBps: 342, OutAckBps: 362, InSynPps: 387, OutSynPps: 390, InUDPPps: 310, OutUDPPps: 383, InUDPBps: 310, OutUDPBps: 366, InIcmpPps: 338, InIcmpBps: 358, OutIcmpBps: 312, OutIcmpPps: 370, InDNSPps: 352, OutDNSPps: 332, InDNSBps: 329, OutDNSBps: 342, AttackFlags: 396, Count: 382, IPType: 369, CFilter: &cfilter14, Host: &host14}
var cleanDataIDs = []int{}

// Create CleanData test case
// 创建
func TestCreateCleanData(t *testing.T) {
	successExpectedResult.ResponseData = testCleanData.IP
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "POST", BodyData: testCleanData, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	cleanDataIDs = append(cleanDataIDs, getDataMapId(result))
}

// CreateBulk CleanData test case
// 批量创建
func TestCreateBulkCleanData(t *testing.T) {
	cfilter1 := "CMrUwwWNpH"
	host1 := "imgAUZBcQE"
	bulkData1 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "fJhhzIHKnP", Time: time.Now().AddDate(0, 0, -1), InBps: 393, OutBps: 368, InPps: 329, OutPps: 35, InAckPps: 33, OutAckPps: 373, InAckBps: 368, OutAckBps: 385, InSynPps: 392, OutSynPps: 392, InUDPPps: 396, OutUDPPps: 383, InUDPBps: 347, OutUDPBps: 328, InIcmpPps: 376, InIcmpBps: 387, OutIcmpBps: 395, OutIcmpPps: 37, InDNSPps: 34, OutDNSPps: 387, InDNSBps: 332, OutDNSBps: 358, AttackFlags: 36, Count: 398, IPType: 327, CFilter: &cfilter1, Host: &host1}
	cfilter2 := "SoIrvLfuHW"
	host2 := "wBEJidZTze"
	bulkData2 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "oKkNMpRygy", Time: time.Now().AddDate(0, 0, -1), InBps: 374, OutBps: 386, InPps: 385, OutPps: 378, InAckPps: 323, OutAckPps: 379, InAckBps: 314, OutAckBps: 338, InSynPps: 36, OutSynPps: 374, InUDPPps: 393, OutUDPPps: 358, InUDPBps: 318, OutUDPBps: 374, InIcmpPps: 322, InIcmpBps: 366, OutIcmpBps: 385, OutIcmpPps: 357, InDNSPps: 390, OutDNSPps: 372, InDNSBps: 337, OutDNSBps: 329, AttackFlags: 353, Count: 357, IPType: 341, CFilter: &cfilter2, Host: &host2}
	cfilter3 := "CWsrhngkIT"
	host3 := "qlYBUWsfQF"
	bulkData3 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "xfsmdpfXeE", Time: time.Now().AddDate(0, 0, -1), InBps: 341, OutBps: 369, InPps: 331, OutPps: 384, InAckPps: 373, OutAckPps: 352, InAckBps: 317, OutAckBps: 341, InSynPps: 372, OutSynPps: 348, InUDPPps: 34, OutUDPPps: 34, InUDPBps: 396, OutUDPBps: 397, InIcmpPps: 347, InIcmpBps: 392, OutIcmpBps: 340, OutIcmpPps: 334, InDNSPps: 33, OutDNSPps: 332, InDNSBps: 396, OutDNSBps: 347, AttackFlags: 395, Count: 398, IPType: 390, CFilter: &cfilter3, Host: &host3}
	cfilter4 := "OCTCxWsVzV"
	host4 := "nSutPYLQrz"
	bulkData4 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "XXsGUGsBiN", Time: time.Now().AddDate(0, 0, -1), InBps: 330, OutBps: 343, InPps: 378, OutPps: 366, InAckPps: 378, OutAckPps: 329, InAckBps: 341, OutAckBps: 375, InSynPps: 375, OutSynPps: 337, InUDPPps: 388, OutUDPPps: 357, InUDPBps: 365, OutUDPBps: 339, InIcmpPps: 377, InIcmpBps: 357, OutIcmpBps: 358, OutIcmpPps: 373, InDNSPps: 340, OutDNSPps: 338, InDNSBps: 376, OutDNSBps: 320, AttackFlags: 338, Count: 314, IPType: 34, CFilter: &cfilter4, Host: &host4}
	cfilter5 := "rTROudTUgP"
	host5 := "wKGgEKGnOM"
	bulkData5 := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "MPzqQBJsMb", Time: time.Now().AddDate(0, 0, -1), InBps: 38, OutBps: 364, InPps: 337, OutPps: 316, InAckPps: 384, OutAckPps: 312, InAckBps: 38, OutAckBps: 340, InSynPps: 364, OutSynPps: 316, InUDPPps: 340, OutUDPPps: 311, InUDPBps: 340, OutUDPBps: 367, InIcmpPps: 395, InIcmpBps: 340, OutIcmpBps: 338, OutIcmpPps: 311, InDNSPps: 395, OutDNSPps: 394, InDNSBps: 326, OutDNSBps: 371, AttackFlags: 323, Count: 318, IPType: 371, CFilter: &cfilter5, Host: &host5}
	bulkDatas := [...]ent.CleanData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.IP
	testCase := &CaseRule{Api: cleanDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		cleanDataIDs = append(cleanDataIDs, getDataMapId(v))
	}
}

// Query CleanData test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCleanData(t *testing.T) {
	successExpectedResult.ResponseData = testCleanData.IP
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID CleanData test case
// 根据 ID 查询
func TestQueryByIDCleanData(t *testing.T) {
	successExpectedResult.ResponseData = testCleanData.IP
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "GET", UrlData: strconv.Itoa(cleanDataIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID CleanData not exist test case
// 根据 ID 查询
func TestQueryByIDCleanDataNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testCleanData.IP
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query CleanData by IP test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCleanDataByIP(t *testing.T) {
	successExpectedResult.ResponseData = testCleanData.IP
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "GET", UrlData: cleandata.FieldIP + "=" + testCleanData.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch CleanData search by IP test case
// 分页搜索
func TestQuerySearchCleanDataIP(t *testing.T) {
	successExpectedResult.ResponseData = testCleanData.IP
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "GET", UrlData: "search=" + testCleanData.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID CleanData test case
// 根据 ID 修改
func TestUpdateByIDCleanData(t *testing.T) {
	cfilter14 := "HautAnVxgg"
	host14 := "IfEDxnYhTi"
	updateData := &ent.CleanData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "eTIqSlRydl", Time: time.Now().AddDate(0, 0, -1), InBps: 398, OutBps: 35, InPps: 36, OutPps: 351, InAckPps: 35, OutAckPps: 333, InAckBps: 339, OutAckBps: 398, InSynPps: 369, OutSynPps: 342, InUDPPps: 366, OutUDPPps: 394, InUDPBps: 31, OutUDPBps: 334, InIcmpPps: 382, InIcmpBps: 357, OutIcmpBps: 393, OutIcmpPps: 349, InDNSPps: 369, OutDNSPps: 34, InDNSBps: 350, OutDNSBps: 372, AttackFlags: 314, Count: 320, IPType: 357, CFilter: &cfilter14, Host: &host14}
	successExpectedResult.ResponseData = updateData.IP
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cleanDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID CleanData test case
// 根据 ID 删除
func TestDeleteByIDCleanData(t *testing.T) {
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(cleanDataIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID CleanData not exist test case
// 根据 ID 删除
func TestDeleteByIDCleanDataNoExist(t *testing.T) {
	testCase := &CaseRule{Api: cleanDataApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk CleanData test case
// 根据 IDs 批量删除
func TestDeleteBulkCleanData(t *testing.T) {
	testCase := &CaseRule{Api: cleanDataApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: cleanDataIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
