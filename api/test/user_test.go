package test

import (
	"meta/app/ent"
	"meta/app/ent/user"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var userApi = baseApi + "/user"
var testUser = &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "daJlmeNIMA", Password: "kJzglYLfny"}
var userIDs = []int{}

// Create User test case
// 创建
func TestCreateUser(t *testing.T) {
	successExpectedResult.ResponseData = testUser.Name
	testCase := &CaseRule{Api: userApi, HttpMethod: "POST", BodyData: testUser, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	userIDs = append(userIDs, getDataMapId(result))
}

// CreateBulk User test case
// 批量创建
func TestCreateBulkUser(t *testing.T) {
	bulkData1 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "YpxhWaxSGl", Password: "vmqRNKQIGh"}
	bulkData2 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "gSgFBLNulp", Password: "YBdFAvMpDB"}
	bulkData3 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "FWLQcyFkDV", Password: "lBPOBmDGsw"}
	bulkData4 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "namsncbTDv", Password: "yjlhGwAAGN"}
	bulkData5 := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "UPkFTOMYrg", Password: "PlyowqqSsT"}
	bulkDatas := [...]ent.User{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: userApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		userIDs = append(userIDs, getDataMapId(v))
	}
}

// Query User test case
// 根据指定字段、时间范围查询或搜索
func TestQueryUser(t *testing.T) {
	successExpectedResult.ResponseData = testUser.Name
	testCase := &CaseRule{Api: userApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID User test case
// 根据 ID 查询
func TestQueryByIDUser(t *testing.T) {
	successExpectedResult.ResponseData = testUser.Name
	testCase := &CaseRule{Api: userApi, HttpMethod: "GET", UrlData: strconv.Itoa(userIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID User not exist test case
// 根据 ID 查询
func TestQueryByIDUserNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testUser.Name
	testCase := &CaseRule{Api: userApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query User by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryUserByName(t *testing.T) {
	successExpectedResult.ResponseData = testUser.Name
	testCase := &CaseRule{Api: userApi, HttpMethod: "GET", UrlData: user.FieldName + "=" + testUser.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch User search by Name test case
// 分页搜索
func TestQuerySearchUserName(t *testing.T) {
	successExpectedResult.ResponseData = testUser.Name
	testCase := &CaseRule{Api: userApi, HttpMethod: "GET", UrlData: "search=" + testUser.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID User test case
// 根据 ID 修改
func TestUpdateByIDUser(t *testing.T) {
	updateData := &ent.User{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Name: "llrDImGcMa", Password: "nXURMdfhCl"}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: userApi, HttpMethod: "PUT", UrlData: strconv.Itoa(userIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID User test case
// 根据 ID 删除
func TestDeleteByIDUser(t *testing.T) {
	testCase := &CaseRule{Api: userApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(userIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID User not exist test case
// 根据 ID 删除
func TestDeleteByIDUserNoExist(t *testing.T) {
	testCase := &CaseRule{Api: userApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk User test case
// 根据 IDs 批量删除
func TestDeleteBulkUser(t *testing.T) {
	testCase := &CaseRule{Api: userApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: userIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
