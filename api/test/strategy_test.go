package test

import (
	"meta/app/ent"
	"meta/app/ent/strategy"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var strategyApi = baseApi + "/strategy"
var remark25 = "WYKVLeJzfa"
var testStrategy = &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark25, Name: "fniZIVwdGl", Type: 337, Bps: 365, Pps: 372, BpsCount: 392, PpsCount: 368, IspCode: 380}
var strategyIDs = []int{}

// Create Strategy test case
// 创建
func TestCreateStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "POST", BodyData: testStrategy, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	strategyIDs = append(strategyIDs, getDataMapId(result))
}

// CreateBulk Strategy test case
// 批量创建
func TestCreateBulkStrategy(t *testing.T) {
	remark1 := "xAYbydbSCI"
	bulkData1 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "RngyegXVUS", Type: 335, Bps: 396, Pps: 350, BpsCount: 319, PpsCount: 348, IspCode: 347}
	remark2 := "YthRnBYilL"
	bulkData2 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "nKmyyuRUNY", Type: 326, Bps: 383, Pps: 335, BpsCount: 315, PpsCount: 370, IspCode: 318}
	remark3 := "xEujpuexFx"
	bulkData3 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "wFLgYymnby", Type: 342, Bps: 390, Pps: 358, BpsCount: 312, PpsCount: 375, IspCode: 363}
	remark4 := "fupdBTaJLN"
	bulkData4 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "ogKtXQaUCa", Type: 31, Bps: 366, Pps: 357, BpsCount: 369, PpsCount: 383, IspCode: 374}
	remark5 := "JvujCzSrmU"
	bulkData5 := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "dSqVcPSdWb", Type: 342, Bps: 398, Pps: 369, BpsCount: 314, PpsCount: 35, IspCode: 374}
	bulkDatas := [...]ent.Strategy{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: strategyApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		strategyIDs = append(strategyIDs, getDataMapId(v))
	}
}

// Query Strategy test case
// 根据指定字段、时间范围查询或搜索
func TestQueryStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID Strategy test case
// 根据 ID 查询
func TestQueryByIDStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: strconv.Itoa(strategyIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID Strategy not exist test case
// 根据 ID 查询
func TestQueryByIDStrategyNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query Strategy by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryStrategyByName(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: strategy.FieldName + "=" + testStrategy.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch Strategy search by Name test case
// 分页搜索
func TestQuerySearchStrategyName(t *testing.T) {
	successExpectedResult.ResponseData = testStrategy.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "GET", UrlData: "search=" + testStrategy.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID Strategy test case
// 根据 ID 修改
func TestUpdateByIDStrategy(t *testing.T) {
	remark25 := "yGziOaFxGP"
	updateData := &ent.Strategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark25, Name: "EwQFATkbhD", Type: 35, Bps: 373, Pps: 38, BpsCount: 370, PpsCount: 376, IspCode: 335}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "PUT", UrlData: strconv.Itoa(strategyIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID Strategy test case
// 根据 ID 删除
func TestDeleteByIDStrategy(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(strategyIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID Strategy not exist test case
// 根据 ID 删除
func TestDeleteByIDStrategyNoExist(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk Strategy test case
// 根据 IDs 批量删除
func TestDeleteBulkStrategy(t *testing.T) {
	testCase := &CaseRule{Api: strategyApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: strategyIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
