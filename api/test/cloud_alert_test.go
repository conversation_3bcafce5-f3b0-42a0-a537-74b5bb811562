package test

import (
	"meta/app/ent"
	"meta/app/ent/cloudalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var cloudAlertApi = baseApi + "/cloudalert"
var remark28 = "UqeGIdscen"
var testCloudAlert = &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark28, SrcIP: "RsZvqaNEUl", SrcPort: 386, DstIP: "bNAtZeskAA", DstPort: 314, DefenceMode: 333, FlowMode: 323, TCPAckNum: "QBhSNvOuqQ", TCPSeqNum: "EKjHVFTXRL", Protocol: 363, DefenceLevel: 373, MaxPps: 397, MaxAttackPps: 36, OverlimitPktCount: 319, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
var cloudAlertIDs = []int{}

// Create CloudAlert test case
// 创建
func TestCreateCloudAlert(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "POST", BodyData: testCloudAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	cloudAlertIDs = append(cloudAlertIDs, getDataMapId(result))
}

// CreateBulk CloudAlert test case
// 批量创建
func TestCreateBulkCloudAlert(t *testing.T) {
	remark1 := "FDcATBrMZi"
	bulkData1 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "SoIFFsDcaF", SrcPort: 341, DstIP: "wKkblIHbtH", DstPort: 313, DefenceMode: 325, FlowMode: 322, TCPAckNum: "URLAXVBucl", TCPSeqNum: "GtnEJvxaPJ", Protocol: 357, DefenceLevel: 393, MaxPps: 381, MaxAttackPps: 360, OverlimitPktCount: 310, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark2 := "AwgivkPYwQ"
	bulkData2 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "IVbARFUhFI", SrcPort: 35, DstIP: "qplKfwBBNC", DstPort: 385, DefenceMode: 312, FlowMode: 374, TCPAckNum: "vGGiVaHUkO", TCPSeqNum: "oJvYzmktTs", Protocol: 313, DefenceLevel: 380, MaxPps: 389, MaxAttackPps: 364, OverlimitPktCount: 372, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark3 := "tflKMlEoMa"
	bulkData3 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "uQYubANbTG", SrcPort: 360, DstIP: "UliRYBPxue", DstPort: 356, DefenceMode: 352, FlowMode: 348, TCPAckNum: "hIUKPMnHMI", TCPSeqNum: "cmlREeONaT", Protocol: 387, DefenceLevel: 388, MaxPps: 316, MaxAttackPps: 396, OverlimitPktCount: 375, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark4 := "wzwlVfzjTC"
	bulkData4 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "mGPeBBmpPb", SrcPort: 371, DstIP: "FxgJrtotdh", DstPort: 354, DefenceMode: 315, FlowMode: 375, TCPAckNum: "HCrhlqvJpq", TCPSeqNum: "ZtPkSCwVTo", Protocol: 310, DefenceLevel: 39, MaxPps: 314, MaxAttackPps: 384, OverlimitPktCount: 315, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark5 := "bnEheERYDC"
	bulkData5 := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "ulbyTNGxsy", SrcPort: 320, DstIP: "JdqyonUgaY", DstPort: 351, DefenceMode: 336, FlowMode: 327, TCPAckNum: "hhIHvVXJCd", TCPSeqNum: "ZXQerkylLL", Protocol: 390, DefenceLevel: 331, MaxPps: 358, MaxAttackPps: 390, OverlimitPktCount: 370, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	bulkDatas := [...]ent.CloudAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		cloudAlertIDs = append(cloudAlertIDs, getDataMapId(v))
	}
}

// Query CloudAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCloudAlert(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID CloudAlert test case
// 根据 ID 查询
func TestQueryByIDCloudAlert(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(cloudAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID CloudAlert not exist test case
// 根据 ID 查询
func TestQueryByIDCloudAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query CloudAlert by SrcIP test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCloudAlertBySrcIP(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: cloudalert.FieldSrcIP + "=" + testCloudAlert.SrcIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch CloudAlert search by SrcIP test case
// 分页搜索
func TestQuerySearchCloudAlertSrcIP(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAlert.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "GET", UrlData: "search=" + testCloudAlert.SrcIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID CloudAlert test case
// 根据 ID 修改
func TestUpdateByIDCloudAlert(t *testing.T) {
	remark28 := "YWVRKDSaJa"
	updateData := &ent.CloudAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark28, SrcIP: "cEldeSZIwt", SrcPort: 394, DstIP: "SzyEjHifXp", DstPort: 337, DefenceMode: 39, FlowMode: 330, TCPAckNum: "skfNNYrakF", TCPSeqNum: "pryaQstTjb", Protocol: 366, DefenceLevel: 366, MaxPps: 384, MaxAttackPps: 377, OverlimitPktCount: 369, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	successExpectedResult.ResponseData = updateData.SrcIP
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cloudAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID CloudAlert test case
// 根据 ID 删除
func TestDeleteByIDCloudAlert(t *testing.T) {
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(cloudAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID CloudAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDCloudAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: cloudAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk CloudAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkCloudAlert(t *testing.T) {
	testCase := &CaseRule{Api: cloudAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: cloudAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
