package test

import (
	"meta/app/ent"
	"meta/app/ent/notify"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var notifyApi = baseApi + "/notify"
var remark10 = "iuIDIggaGv"
var testNotify = &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark10, Name: "BypXauiJFo"}
var notifyIDs = []int{}

// Create Notify test case
// 创建
func TestCreateNotify(t *testing.T) {
	successExpectedResult.ResponseData = testNotify.Name
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "POST", BodyData: testNotify, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	notifyIDs = append(notifyIDs, getDataMapId(result))
}

// CreateBulk Notify test case
// 批量创建
func TestCreateBulkNotify(t *testing.T) {
	remark1 := "eUsDsbLvsE"
	bulkData1 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "WIMDMnngSH"}
	remark2 := "tpsDwGgXSp"
	bulkData2 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "JmNVKGmPgK"}
	remark3 := "QPdYWnVMmC"
	bulkData3 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "wshhqycoZu"}
	remark4 := "FCXlrFGviE"
	bulkData4 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "wGWPBzKgqN"}
	remark5 := "TVoqFDdIWN"
	bulkData5 := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "MHQADCqbtH"}
	bulkDatas := [...]ent.Notify{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: notifyApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		notifyIDs = append(notifyIDs, getDataMapId(v))
	}
}

// Query Notify test case
// 根据指定字段、时间范围查询或搜索
func TestQueryNotify(t *testing.T) {
	successExpectedResult.ResponseData = testNotify.Name
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID Notify test case
// 根据 ID 查询
func TestQueryByIDNotify(t *testing.T) {
	successExpectedResult.ResponseData = testNotify.Name
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "GET", UrlData: strconv.Itoa(notifyIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID Notify not exist test case
// 根据 ID 查询
func TestQueryByIDNotifyNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testNotify.Name
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query Notify by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryNotifyByName(t *testing.T) {
	successExpectedResult.ResponseData = testNotify.Name
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "GET", UrlData: notify.FieldName + "=" + testNotify.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch Notify search by Name test case
// 分页搜索
func TestQuerySearchNotifyName(t *testing.T) {
	successExpectedResult.ResponseData = testNotify.Name
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "GET", UrlData: "search=" + testNotify.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID Notify test case
// 根据 ID 修改
func TestUpdateByIDNotify(t *testing.T) {
	remark10 := "ILEKuCXTtd"
	updateData := &ent.Notify{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark10, Name: "zPrPsEUUsF"}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "PUT", UrlData: strconv.Itoa(notifyIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID Notify test case
// 根据 ID 删除
func TestDeleteByIDNotify(t *testing.T) {
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(notifyIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID Notify not exist test case
// 根据 ID 删除
func TestDeleteByIDNotifyNoExist(t *testing.T) {
	testCase := &CaseRule{Api: notifyApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk Notify test case
// 根据 IDs 批量删除
func TestDeleteBulkNotify(t *testing.T) {
	testCase := &CaseRule{Api: notifyApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: notifyIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
