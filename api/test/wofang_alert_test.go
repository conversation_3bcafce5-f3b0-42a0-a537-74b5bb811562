package test

import (
	"meta/app/ent"
	"meta/app/ent/wofangalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var wofangAlertApi = baseApi + "/wofangalert"
var remark33 = "oFikkFqZqx"
var testWofangAlert = &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark33, AttackStatus: 315, DeviceIP: "UHAmWOSYUZ", ZoneIP: "UikHwxtBMf", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 355, MaxInBps: 374}
var wofangAlertIDs = []int{}

// Create WofangAlert test case
// 创建
func TestCreateWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "POST", BodyData: testWofangAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	wofangAlertIDs = append(wofangAlertIDs, getDataMapId(result))
}

// CreateBulk WofangAlert test case
// 批量创建
func TestCreateBulkWofangAlert(t *testing.T) {
	remark1 := "SxohKbXrcF"
	bulkData1 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, AttackStatus: 318, DeviceIP: "hAVGgrmbDC", ZoneIP: "zRpuoLAQuq", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 383, MaxInBps: 323}
	remark2 := "lfrrxaZxdS"
	bulkData2 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, AttackStatus: 354, DeviceIP: "UtUwrPrCAz", ZoneIP: "benatwbjnQ", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 338, MaxInBps: 37}
	remark3 := "qASGOrLMhf"
	bulkData3 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, AttackStatus: 375, DeviceIP: "YJCaJxxsaq", ZoneIP: "GvMBvjtKse", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 354, MaxInBps: 39}
	remark4 := "CKliTvxVcv"
	bulkData4 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, AttackStatus: 386, DeviceIP: "nblhMkUpbr", ZoneIP: "VXvHRlIBbz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 37, MaxInBps: 352}
	remark5 := "bjwVqqIput"
	bulkData5 := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, AttackStatus: 358, DeviceIP: "VOVgRCurUG", ZoneIP: "cGpfDDbjYn", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 320, MaxInBps: 36}
	bulkDatas := [...]ent.WofangAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		wofangAlertIDs = append(wofangAlertIDs, getDataMapId(v))
	}
}

// Query WofangAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQueryWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID WofangAlert test case
// 根据 ID 查询
func TestQueryByIDWofangAlert(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(wofangAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID WofangAlert not exist test case
// 根据 ID 查询
func TestQueryByIDWofangAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query WofangAlert by DeviceIP test case
// 根据指定字段、时间范围查询或搜索
func TestQueryWofangAlertByDeviceIP(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: wofangalert.FieldDeviceIP + "=" + testWofangAlert.DeviceIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch WofangAlert search by DeviceIP test case
// 分页搜索
func TestQuerySearchWofangAlertDeviceIP(t *testing.T) {
	successExpectedResult.ResponseData = testWofangAlert.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "GET", UrlData: "search=" + testWofangAlert.DeviceIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID WofangAlert test case
// 根据 ID 修改
func TestUpdateByIDWofangAlert(t *testing.T) {
	remark33 := "NVJXNvvSwE"
	updateData := &ent.WofangAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark33, AttackStatus: 36, DeviceIP: "ebSMWdqhQZ", ZoneIP: "KdheEyIrtc", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), MaxDropBps: 373, MaxInBps: 337}
	successExpectedResult.ResponseData = updateData.DeviceIP
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(wofangAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID WofangAlert test case
// 根据 ID 删除
func TestDeleteByIDWofangAlert(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(wofangAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID WofangAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDWofangAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk WofangAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkWofangAlert(t *testing.T) {
	testCase := &CaseRule{Api: wofangAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: wofangAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
