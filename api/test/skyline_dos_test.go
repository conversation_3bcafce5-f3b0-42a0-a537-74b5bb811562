package test

import (
	"meta/app/ent"
	"meta/app/ent/skylinedos"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var skylineDosApi = baseApi + "/skylinedos"
var remark14 = "kyXyhKCwuc"
var testSkylineDos = &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark14, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "YJSRxflgkO", Resource: "bvEtKIykWI", ResourceType: "pvxWMklozs", Status: "huIsBMtfgF", Project: "pljYCUefuY", DurationTime: 371}
var skylineDosIDs = []int{}

// Create SkylineDos test case
// 创建
func TestCreateSkylineDos(t *testing.T) {
	successExpectedResult.ResponseData = testSkylineDos.Region
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "POST", BodyData: testSkylineDos, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	skylineDosIDs = append(skylineDosIDs, getDataMapId(result))
}

// CreateBulk SkylineDos test case
// 批量创建
func TestCreateBulkSkylineDos(t *testing.T) {
	remark1 := "PECAzpKfzR"
	bulkData1 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "PMNvFCaqer", Resource: "ouXcqTnBQd", ResourceType: "AcxNivycEK", Status: "JFXjaKAYUn", Project: "CJxgSvPdux", DurationTime: 377}
	remark2 := "rkzKUmmxVJ"
	bulkData2 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "OKkCNZVXmg", Resource: "PEuuXXIMQP", ResourceType: "eIJuciWvQR", Status: "jAYXAYVByi", Project: "BluRiHvHar", DurationTime: 382}
	remark3 := "qMiSciXlcy"
	bulkData3 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "fubuVPLnDh", Resource: "kRQNlleVsJ", ResourceType: "ITpBYqrZJL", Status: "PjLuAJAwbp", Project: "FGfoJhnagt", DurationTime: 321}
	remark4 := "DfZshfOwSD"
	bulkData4 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "tRnpnrbbUP", Resource: "OnZVsISGod", ResourceType: "NxdWNMxxej", Status: "JFydpLKlBj", Project: "DGKHlrPbli", DurationTime: 344}
	remark5 := "xSUwWKqZHz"
	bulkData5 := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "YHIZuayVHF", Resource: "JBQmQYDael", ResourceType: "jtGpJxXaXu", Status: "vduzoUyCMy", Project: "DqXRMTPSxd", DurationTime: 311}
	bulkDatas := [...]ent.SkylineDos{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Region
	testCase := &CaseRule{Api: skylineDosApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		skylineDosIDs = append(skylineDosIDs, getDataMapId(v))
	}
}

// Query SkylineDos test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySkylineDos(t *testing.T) {
	successExpectedResult.ResponseData = testSkylineDos.Region
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID SkylineDos test case
// 根据 ID 查询
func TestQueryByIDSkylineDos(t *testing.T) {
	successExpectedResult.ResponseData = testSkylineDos.Region
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "GET", UrlData: strconv.Itoa(skylineDosIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID SkylineDos not exist test case
// 根据 ID 查询
func TestQueryByIDSkylineDosNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testSkylineDos.Region
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query SkylineDos by Region test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySkylineDosByRegion(t *testing.T) {
	successExpectedResult.ResponseData = testSkylineDos.Region
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "GET", UrlData: skylinedos.FieldRegion + "=" + testSkylineDos.Region, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch SkylineDos search by Region test case
// 分页搜索
func TestQuerySearchSkylineDosRegion(t *testing.T) {
	successExpectedResult.ResponseData = testSkylineDos.Region
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "GET", UrlData: "search=" + testSkylineDos.Region, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID SkylineDos test case
// 根据 ID 修改
func TestUpdateByIDSkylineDos(t *testing.T) {
	remark14 := "sJzdjhBMTn"
	updateData := &ent.SkylineDos{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark14, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), Region: "VcSaXweZUy", Resource: "KWBIhEdmUY", ResourceType: "GMSdDLgggq", Status: "XgNZyrNnMq", Project: "oxWDexgqMk", DurationTime: 313}
	successExpectedResult.ResponseData = updateData.Region
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "PUT", UrlData: strconv.Itoa(skylineDosIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID SkylineDos test case
// 根据 ID 删除
func TestDeleteByIDSkylineDos(t *testing.T) {
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(skylineDosIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID SkylineDos not exist test case
// 根据 ID 删除
func TestDeleteByIDSkylineDosNoExist(t *testing.T) {
	testCase := &CaseRule{Api: skylineDosApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk SkylineDos test case
// 根据 IDs 批量删除
func TestDeleteBulkSkylineDos(t *testing.T) {
	testCase := &CaseRule{Api: skylineDosApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: skylineDosIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
