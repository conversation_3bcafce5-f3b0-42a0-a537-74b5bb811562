package test

import (
	"meta/app/ent"
	"meta/app/ent/cloudattackdata"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var cloudAttackDataApi = baseApi + "/cloudattackdata"
var remark26 = "GkRNPkMZGI"
var testCloudAttackData = &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark26, SrcIP: "RqXcKBoCbK", SrcPort: 359, DstIP: "QOutmFoRWE", DstPort: 382, Protocol: 355, CurrentAttackPps: 375, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
var cloudAttackDataIDs = []int{}

// Create CloudAttackData test case
// 创建
func TestCreateCloudAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAttackData.SrcIP
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "POST", BodyData: testCloudAttackData, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	cloudAttackDataIDs = append(cloudAttackDataIDs, getDataMapId(result))
}

// CreateBulk CloudAttackData test case
// 批量创建
func TestCreateBulkCloudAttackData(t *testing.T) {
	remark1 := "lcOPYAarzC"
	bulkData1 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "zFhgETuqdm", SrcPort: 347, DstIP: "cyeYpnhKed", DstPort: 319, Protocol: 386, CurrentAttackPps: 383, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark2 := "nuKSwnnQkg"
	bulkData2 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "uAuIYHRugj", SrcPort: 345, DstIP: "foHCunPRBO", DstPort: 382, Protocol: 397, CurrentAttackPps: 314, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark3 := "GlbcyTktcp"
	bulkData3 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "HzkSwVxlda", SrcPort: 391, DstIP: "iOYltnbcQP", DstPort: 398, Protocol: 321, CurrentAttackPps: 371, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark4 := "nGPwWlNdgK"
	bulkData4 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "lATojNRggy", SrcPort: 326, DstIP: "JpgHWJsxXH", DstPort: 365, Protocol: 317, CurrentAttackPps: 362, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark5 := "GKgjKoRWDq"
	bulkData5 := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "uJthPzSHcs", SrcPort: 389, DstIP: "CzyZkQnXcg", DstPort: 369, Protocol: 359, CurrentAttackPps: 349, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	bulkDatas := [...]ent.CloudAttackData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.SrcIP
	testCase := &CaseRule{Api: cloudAttackDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		cloudAttackDataIDs = append(cloudAttackDataIDs, getDataMapId(v))
	}
}

// Query CloudAttackData test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCloudAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAttackData.SrcIP
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID CloudAttackData test case
// 根据 ID 查询
func TestQueryByIDCloudAttackData(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAttackData.SrcIP
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "GET", UrlData: strconv.Itoa(cloudAttackDataIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID CloudAttackData not exist test case
// 根据 ID 查询
func TestQueryByIDCloudAttackDataNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAttackData.SrcIP
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query CloudAttackData by SrcIP test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCloudAttackDataBySrcIP(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAttackData.SrcIP
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "GET", UrlData: cloudattackdata.FieldSrcIP + "=" + testCloudAttackData.SrcIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch CloudAttackData search by SrcIP test case
// 分页搜索
func TestQuerySearchCloudAttackDataSrcIP(t *testing.T) {
	successExpectedResult.ResponseData = testCloudAttackData.SrcIP
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "GET", UrlData: "search=" + testCloudAttackData.SrcIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID CloudAttackData test case
// 根据 ID 修改
func TestUpdateByIDCloudAttackData(t *testing.T) {
	remark26 := "EzvBOlMcLs"
	updateData := &ent.CloudAttackData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark26, SrcIP: "QItOMTGiAZ", SrcPort: 345, DstIP: "soymgRtWDL", DstPort: 348, Protocol: 375, CurrentAttackPps: 38, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	successExpectedResult.ResponseData = updateData.SrcIP
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cloudAttackDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID CloudAttackData test case
// 根据 ID 删除
func TestDeleteByIDCloudAttackData(t *testing.T) {
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(cloudAttackDataIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID CloudAttackData not exist test case
// 根据 ID 删除
func TestDeleteByIDCloudAttackDataNoExist(t *testing.T) {
	testCase := &CaseRule{Api: cloudAttackDataApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk CloudAttackData test case
// 根据 IDs 批量删除
func TestDeleteBulkCloudAttackData(t *testing.T) {
	testCase := &CaseRule{Api: cloudAttackDataApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: cloudAttackDataIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
