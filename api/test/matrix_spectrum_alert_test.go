package test

import (
	"meta/app/ent"
	"meta/app/ent/matrixspectrumalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var matrixSpectrumAlertApi = baseApi + "/matrixspectrumalert"
var remark12 = "vHoKKyHBPd"
var testMatrixSpectrumAlert = &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark12, Region: "hfWCxhgVTe", NetType: "JskzSasnWx", Isp: "xzKKHeZgbS", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "HKxSoZIYUB", Bps: 371}
var matrixSpectrumAlertIDs = []int{}

// Create MatrixSpectrumAlert test case
// 创建
func TestCreateMatrixSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.Region
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "POST", BodyData: testMatrixSpectrumAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	matrixSpectrumAlertIDs = append(matrixSpectrumAlertIDs, getDataMapId(result))
}

// CreateBulk MatrixSpectrumAlert test case
// 批量创建
func TestCreateBulkMatrixSpectrumAlert(t *testing.T) {
	remark1 := "qmKkIdqsou"
	bulkData1 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Region: "UmqPotKTdU", NetType: "hMSQJLOUvP", Isp: "NyNvvoYNmK", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "UyqBnYxnFd", Bps: 36}
	remark2 := "vMCHyRVBYj"
	bulkData2 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Region: "SZgmTDxCfr", NetType: "nNwVWIhPFG", Isp: "wlpYWCymqz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "kguJDwqTjP", Bps: 36}
	remark3 := "usIVzvvtCr"
	bulkData3 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Region: "oCemjffIpD", NetType: "AAZFkeAgpo", Isp: "dqIipYVbeQ", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "GtxuCCEFRH", Bps: 347}
	remark4 := "gortdMqZkn"
	bulkData4 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Region: "plCHPmCuxU", NetType: "hcDCjiOkpU", Isp: "oxqyclcaKc", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "UEdgqwccOM", Bps: 342}
	remark5 := "MHZpcnFrps"
	bulkData5 := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Region: "PkrFuYfWSv", NetType: "CNluEwNzha", Isp: "jzrrksBhXi", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "KlIgrQZdUA", Bps: 358}
	bulkDatas := [...]ent.MatrixSpectrumAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Region
	testCase := &CaseRule{Api: matrixSpectrumAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		matrixSpectrumAlertIDs = append(matrixSpectrumAlertIDs, getDataMapId(v))
	}
}

// Query MatrixSpectrumAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQueryMatrixSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.Region
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID MatrixSpectrumAlert test case
// 根据 ID 查询
func TestQueryByIDMatrixSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.Region
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(matrixSpectrumAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID MatrixSpectrumAlert not exist test case
// 根据 ID 查询
func TestQueryByIDMatrixSpectrumAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.Region
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query MatrixSpectrumAlert by Region test case
// 根据指定字段、时间范围查询或搜索
func TestQueryMatrixSpectrumAlertByRegion(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.Region
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: matrixspectrumalert.FieldRegion + "=" + testMatrixSpectrumAlert.Region, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch MatrixSpectrumAlert search by Region test case
// 分页搜索
func TestQuerySearchMatrixSpectrumAlertRegion(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixSpectrumAlert.Region
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "GET", UrlData: "search=" + testMatrixSpectrumAlert.Region, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID MatrixSpectrumAlert test case
// 根据 ID 修改
func TestUpdateByIDMatrixSpectrumAlert(t *testing.T) {
	remark12 := "DXzbjKNNoT"
	updateData := &ent.MatrixSpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark12, Region: "lKiHbeZVCO", NetType: "SuoNydzhvu", Isp: "fXkwlcXRsS", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "GxqEFsEXgA", Bps: 351}
	successExpectedResult.ResponseData = updateData.Region
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(matrixSpectrumAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID MatrixSpectrumAlert test case
// 根据 ID 删除
func TestDeleteByIDMatrixSpectrumAlert(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(matrixSpectrumAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID MatrixSpectrumAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDMatrixSpectrumAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk MatrixSpectrumAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkMatrixSpectrumAlert(t *testing.T) {
	testCase := &CaseRule{Api: matrixSpectrumAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: matrixSpectrumAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
