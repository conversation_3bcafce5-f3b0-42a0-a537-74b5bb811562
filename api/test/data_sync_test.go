package test

import (
	"meta/app/ent"
	"meta/app/ent/datasync"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var dataSyncApi = baseApi + "/datasync"
var remark29 = "kabILiihRq"
var testDataSync = &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark29, DataType: "azdyBeuAcv", Type: "LBULAJqcua"}
var dataSyncIDs = []int{}

// Create DataSync test case
// 创建
func TestCreateDataSync(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.DataType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "POST", BodyData: testDataSync, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataSyncIDs = append(dataSyncIDs, getDataMapId(result))
}

// CreateBulk DataSync test case
// 批量创建
func TestCreateBulkDataSync(t *testing.T) {
	remark1 := "NptGKMcwFI"
	bulkData1 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, DataType: "VKxHRQNYeV", Type: "PnjEZHcjoO"}
	remark2 := "GtGaqtveWh"
	bulkData2 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, DataType: "rmzLHcHfjJ", Type: "iRDJCdVCdl"}
	remark3 := "nHbeAhIvNt"
	bulkData3 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, DataType: "CWUWaNJVAL", Type: "jAeShYWFmz"}
	remark4 := "OkzxMkzmas"
	bulkData4 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, DataType: "QrtOyLqGso", Type: "ABbkGtfsEn"}
	remark5 := "ppBrGHTKMv"
	bulkData5 := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, DataType: "nrcNBkjCUz", Type: "QyfyhgGusV"}
	bulkDatas := [...]ent.DataSync{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.DataType
	testCase := &CaseRule{Api: dataSyncApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		dataSyncIDs = append(dataSyncIDs, getDataMapId(v))
	}
}

// Query DataSync test case
// 根据指定字段、时间范围查询或搜索
func TestQueryDataSync(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.DataType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID DataSync test case
// 根据 ID 查询
func TestQueryByIDDataSync(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.DataType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: strconv.Itoa(dataSyncIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID DataSync not exist test case
// 根据 ID 查询
func TestQueryByIDDataSyncNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.DataType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query DataSync by DataType test case
// 根据指定字段、时间范围查询或搜索
func TestQueryDataSyncByDataType(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.DataType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: datasync.FieldDataType + "=" + testDataSync.DataType, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch DataSync search by DataType test case
// 分页搜索
func TestQuerySearchDataSyncDataType(t *testing.T) {
	successExpectedResult.ResponseData = testDataSync.DataType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "GET", UrlData: "search=" + testDataSync.DataType, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID DataSync test case
// 根据 ID 修改
func TestUpdateByIDDataSync(t *testing.T) {
	remark29 := "WtVNUmQJNC"
	updateData := &ent.DataSync{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark29, DataType: "kTBluTNXTP", Type: "cpTDMSNdpg"}
	successExpectedResult.ResponseData = updateData.DataType
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "PUT", UrlData: strconv.Itoa(dataSyncIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID DataSync test case
// 根据 ID 删除
func TestDeleteByIDDataSync(t *testing.T) {
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(dataSyncIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID DataSync not exist test case
// 根据 ID 删除
func TestDeleteByIDDataSyncNoExist(t *testing.T) {
	testCase := &CaseRule{Api: dataSyncApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk DataSync test case
// 根据 IDs 批量删除
func TestDeleteBulkDataSync(t *testing.T) {
	testCase := &CaseRule{Api: dataSyncApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: dataSyncIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
