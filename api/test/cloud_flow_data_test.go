package test

import (
	"meta/app/ent"
	"meta/app/ent/cloudflowdata"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var cloudFlowDataApi = baseApi + "/cloudflowdata"
var remark11 = "pNgXBneSqK"
var testCloudFlowData = &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark11, SrcIP: "eaEebLYUTb", SrcPort: 313, DstIP: "LBbTxURLpL", DstPort: 376, Protocol: 315, MaxAttackPps: 396, FlowOverMaxPpsCount: 372, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
var cloudFlowDataIDs = []int{}

// Create CloudFlowData test case
// 创建
func TestCreateCloudFlowData(t *testing.T) {
	successExpectedResult.ResponseData = testCloudFlowData.SrcIP
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "POST", BodyData: testCloudFlowData, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	cloudFlowDataIDs = append(cloudFlowDataIDs, getDataMapId(result))
}

// CreateBulk CloudFlowData test case
// 批量创建
func TestCreateBulkCloudFlowData(t *testing.T) {
	remark1 := "ufeLJoaShc"
	bulkData1 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, SrcIP: "IqRsHHOLEK", SrcPort: 331, DstIP: "XcPHATBZBe", DstPort: 383, Protocol: 35, MaxAttackPps: 314, FlowOverMaxPpsCount: 320, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark2 := "zwgvkYQkGx"
	bulkData2 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, SrcIP: "gtVCivhkmZ", SrcPort: 358, DstIP: "TgZnJTiLmH", DstPort: 360, Protocol: 322, MaxAttackPps: 386, FlowOverMaxPpsCount: 398, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark3 := "fhUirQYmMq"
	bulkData3 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, SrcIP: "SylEwUnFUN", SrcPort: 343, DstIP: "rvaXMytUsO", DstPort: 367, Protocol: 345, MaxAttackPps: 363, FlowOverMaxPpsCount: 379, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark4 := "GZxpzpswYc"
	bulkData4 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, SrcIP: "AZhGToLEMC", SrcPort: 318, DstIP: "CumGXFxmsJ", DstPort: 362, Protocol: 333, MaxAttackPps: 31, FlowOverMaxPpsCount: 320, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	remark5 := "DjINMqDsSH"
	bulkData5 := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, SrcIP: "zvUHLxYGes", SrcPort: 330, DstIP: "spqwZyATRH", DstPort: 384, Protocol: 320, MaxAttackPps: 310, FlowOverMaxPpsCount: 396, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	bulkDatas := [...]ent.CloudFlowData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.SrcIP
	testCase := &CaseRule{Api: cloudFlowDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		cloudFlowDataIDs = append(cloudFlowDataIDs, getDataMapId(v))
	}
}

// Query CloudFlowData test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCloudFlowData(t *testing.T) {
	successExpectedResult.ResponseData = testCloudFlowData.SrcIP
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID CloudFlowData test case
// 根据 ID 查询
func TestQueryByIDCloudFlowData(t *testing.T) {
	successExpectedResult.ResponseData = testCloudFlowData.SrcIP
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "GET", UrlData: strconv.Itoa(cloudFlowDataIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID CloudFlowData not exist test case
// 根据 ID 查询
func TestQueryByIDCloudFlowDataNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testCloudFlowData.SrcIP
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query CloudFlowData by SrcIP test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCloudFlowDataBySrcIP(t *testing.T) {
	successExpectedResult.ResponseData = testCloudFlowData.SrcIP
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "GET", UrlData: cloudflowdata.FieldSrcIP + "=" + testCloudFlowData.SrcIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch CloudFlowData search by SrcIP test case
// 分页搜索
func TestQuerySearchCloudFlowDataSrcIP(t *testing.T) {
	successExpectedResult.ResponseData = testCloudFlowData.SrcIP
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "GET", UrlData: "search=" + testCloudFlowData.SrcIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID CloudFlowData test case
// 根据 ID 修改
func TestUpdateByIDCloudFlowData(t *testing.T) {
	remark11 := "nPCbyRAEeT"
	updateData := &ent.CloudFlowData{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark11, SrcIP: "afAKKpNyzI", SrcPort: 364, DstIP: "plIHhsXNhF", DstPort: 337, Protocol: 332, MaxAttackPps: 377, FlowOverMaxPpsCount: 338, StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1)}
	successExpectedResult.ResponseData = updateData.SrcIP
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(cloudFlowDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID CloudFlowData test case
// 根据 ID 删除
func TestDeleteByIDCloudFlowData(t *testing.T) {
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(cloudFlowDataIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID CloudFlowData not exist test case
// 根据 ID 删除
func TestDeleteByIDCloudFlowDataNoExist(t *testing.T) {
	testCase := &CaseRule{Api: cloudFlowDataApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk CloudFlowData test case
// 根据 IDs 批量删除
func TestDeleteBulkCloudFlowData(t *testing.T) {
	testCase := &CaseRule{Api: cloudFlowDataApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: cloudFlowDataIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
