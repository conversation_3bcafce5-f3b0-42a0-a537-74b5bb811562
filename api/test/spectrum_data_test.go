package test

import (
	"meta/app/ent"
	"meta/app/ent/spectrumdata"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var spectrumDataApi = baseApi + "/spectrumdata"
var monitor19 = "zFUjkqSprQ"
var product19 = "dXMvtLjyFq"
var host19 = "jlmgQnyTDI"
var testSpectrumData = &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "zMrGPurmyV", Time: time.Now().AddDate(0, 0, -1), DataType: 318, Bps: 32, Pps: 313, SynBps: 373, SynPps: 322, AckBps: 355, AckPps: 393, SynAckBps: 347, SynAckPps: 377, IcmpBps: 326, IcmpPps: 346, SmallPps: 361, NtpPps: 31, NtpBps: 314, DNSQueryPps: 386, DNSQueryBps: 377, DNSAnswerPps: 381, DNSAnswerBps: 360, SsdpBps: 337, SsdpPps: 365, UDPPps: 313, UDPBps: 365, QPS: 397, ReceiveCount: 392, IPType: 365, Monitor: &monitor19, Product: &product19, Host: &host19}
var spectrumDataIDs = []int{}

// Create SpectrumData test case
// 创建
func TestCreateSpectrumData(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumData.IP
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "POST", BodyData: testSpectrumData, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	spectrumDataIDs = append(spectrumDataIDs, getDataMapId(result))
}

// CreateBulk SpectrumData test case
// 批量创建
func TestCreateBulkSpectrumData(t *testing.T) {
	monitor1 := "aMEQvdhudy"
	product1 := "mncVnRxvue"
	host1 := "WYbeUmWDKG"
	bulkData1 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "bkRZsIqnbb", Time: time.Now().AddDate(0, 0, -1), DataType: 376, Bps: 323, Pps: 312, SynBps: 369, SynPps: 310, AckBps: 316, AckPps: 348, SynAckBps: 321, SynAckPps: 395, IcmpBps: 316, IcmpPps: 355, SmallPps: 327, NtpPps: 355, NtpBps: 378, DNSQueryPps: 30, DNSQueryBps: 319, DNSAnswerPps: 386, DNSAnswerBps: 33, SsdpBps: 331, SsdpPps: 322, UDPPps: 343, UDPBps: 347, QPS: 372, ReceiveCount: 318, IPType: 387, Monitor: &monitor1, Product: &product1, Host: &host1}
	monitor2 := "hiLGqVpzXA"
	product2 := "gQpoWgQqaP"
	host2 := "LIEbgciYBV"
	bulkData2 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "NkrxxPIjiP", Time: time.Now().AddDate(0, 0, -1), DataType: 347, Bps: 395, Pps: 354, SynBps: 318, SynPps: 350, AckBps: 354, AckPps: 399, SynAckBps: 310, SynAckPps: 31, IcmpBps: 354, IcmpPps: 317, SmallPps: 378, NtpPps: 343, NtpBps: 315, DNSQueryPps: 322, DNSQueryBps: 339, DNSAnswerPps: 319, DNSAnswerBps: 322, SsdpBps: 363, SsdpPps: 334, UDPPps: 331, UDPBps: 339, QPS: 32, ReceiveCount: 329, IPType: 382, Monitor: &monitor2, Product: &product2, Host: &host2}
	monitor3 := "twatTEBgPb"
	product3 := "aNAIIdboAI"
	host3 := "mpGGTGQZYa"
	bulkData3 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "rjRVNWgrTu", Time: time.Now().AddDate(0, 0, -1), DataType: 354, Bps: 311, Pps: 374, SynBps: 329, SynPps: 394, AckBps: 313, AckPps: 317, SynAckBps: 37, SynAckPps: 370, IcmpBps: 39, IcmpPps: 337, SmallPps: 359, NtpPps: 368, NtpBps: 382, DNSQueryPps: 392, DNSQueryBps: 348, DNSAnswerPps: 322, DNSAnswerBps: 352, SsdpBps: 38, SsdpPps: 361, UDPPps: 362, UDPBps: 354, QPS: 312, ReceiveCount: 362, IPType: 372, Monitor: &monitor3, Product: &product3, Host: &host3}
	monitor4 := "uTAwjildLr"
	product4 := "SDZjimjtZE"
	host4 := "qMOsBtRyyL"
	bulkData4 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "wiJEDPcmJW", Time: time.Now().AddDate(0, 0, -1), DataType: 399, Bps: 336, Pps: 327, SynBps: 37, SynPps: 38, AckBps: 34, AckPps: 395, SynAckBps: 388, SynAckPps: 383, IcmpBps: 336, IcmpPps: 371, SmallPps: 395, NtpPps: 328, NtpBps: 350, DNSQueryPps: 324, DNSQueryBps: 310, DNSAnswerPps: 355, DNSAnswerBps: 344, SsdpBps: 360, SsdpPps: 387, UDPPps: 311, UDPBps: 367, QPS: 311, ReceiveCount: 398, IPType: 315, Monitor: &monitor4, Product: &product4, Host: &host4}
	monitor5 := "rNzLFMVXAq"
	product5 := "GlrXepJDwW"
	host5 := "epTGiLgaGf"
	bulkData5 := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "hxPxdEDppx", Time: time.Now().AddDate(0, 0, -1), DataType: 382, Bps: 322, Pps: 367, SynBps: 321, SynPps: 31, AckBps: 398, AckPps: 397, SynAckBps: 345, SynAckPps: 328, IcmpBps: 370, IcmpPps: 358, SmallPps: 354, NtpPps: 393, NtpBps: 389, DNSQueryPps: 36, DNSQueryBps: 317, DNSAnswerPps: 356, DNSAnswerBps: 341, SsdpBps: 385, SsdpPps: 358, UDPPps: 34, UDPBps: 345, QPS: 344, ReceiveCount: 348, IPType: 385, Monitor: &monitor5, Product: &product5, Host: &host5}
	bulkDatas := [...]ent.SpectrumData{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.IP
	testCase := &CaseRule{Api: spectrumDataApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		spectrumDataIDs = append(spectrumDataIDs, getDataMapId(v))
	}
}

// Query SpectrumData test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySpectrumData(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumData.IP
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID SpectrumData test case
// 根据 ID 查询
func TestQueryByIDSpectrumData(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumData.IP
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "GET", UrlData: strconv.Itoa(spectrumDataIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID SpectrumData not exist test case
// 根据 ID 查询
func TestQueryByIDSpectrumDataNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumData.IP
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query SpectrumData by IP test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySpectrumDataByIP(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumData.IP
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "GET", UrlData: spectrumdata.FieldIP + "=" + testSpectrumData.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch SpectrumData search by IP test case
// 分页搜索
func TestQuerySearchSpectrumDataIP(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumData.IP
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "GET", UrlData: "search=" + testSpectrumData.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID SpectrumData test case
// 根据 ID 修改
func TestUpdateByIDSpectrumData(t *testing.T) {
	monitor19 := "UknHrBeYIW"
	product19 := "zvHhacfAqv"
	host19 := "BJegUabALE"
	updateData := &ent.SpectrumData{CreatedAt: time.Now().AddDate(0, 0, -1), IP: "bPZtdYdOnc", Time: time.Now().AddDate(0, 0, -1), DataType: 364, Bps: 369, Pps: 327, SynBps: 380, SynPps: 332, AckBps: 375, AckPps: 384, SynAckBps: 372, SynAckPps: 363, IcmpBps: 389, IcmpPps: 395, SmallPps: 390, NtpPps: 38, NtpBps: 343, DNSQueryPps: 370, DNSQueryBps: 323, DNSAnswerPps: 347, DNSAnswerBps: 323, SsdpBps: 339, SsdpPps: 394, UDPPps: 359, UDPBps: 383, QPS: 314, ReceiveCount: 310, IPType: 334, Monitor: &monitor19, Product: &product19, Host: &host19}
	successExpectedResult.ResponseData = updateData.IP
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "PUT", UrlData: strconv.Itoa(spectrumDataIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID SpectrumData test case
// 根据 ID 删除
func TestDeleteByIDSpectrumData(t *testing.T) {
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(spectrumDataIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID SpectrumData not exist test case
// 根据 ID 删除
func TestDeleteByIDSpectrumDataNoExist(t *testing.T) {
	testCase := &CaseRule{Api: spectrumDataApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk SpectrumData test case
// 根据 IDs 批量删除
func TestDeleteBulkSpectrumData(t *testing.T) {
	testCase := &CaseRule{Api: spectrumDataApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: spectrumDataIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
