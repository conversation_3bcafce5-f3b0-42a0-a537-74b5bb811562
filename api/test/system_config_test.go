package test

import (
	"meta/app/ent"
	"meta/app/ent/systemconfig"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var systemConfigApi = baseApi + "/systemconfig"
var remark24 = "AShuqHdKbu"
var testSystemConfig = &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark24, WofangTestIP: "vidWdsFhno"}
var systemConfigIDs = []int{}

// Create SystemConfig test case
// 创建
func TestCreateSystemConfig(t *testing.T) {
	successExpectedResult.ResponseData = testSystemConfig.WofangTestIP
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "POST", BodyData: testSystemConfig, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	systemConfigIDs = append(systemConfigIDs, getDataMapId(result))
}

// CreateBulk SystemConfig test case
// 批量创建
func TestCreateBulkSystemConfig(t *testing.T) {
	remark1 := "cwGmFbyfyM"
	bulkData1 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, WofangTestIP: "hDmVEIbeDi"}
	remark2 := "TMBEltwDuK"
	bulkData2 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, WofangTestIP: "twPhpofLYI"}
	remark3 := "njlvhOHfyu"
	bulkData3 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, WofangTestIP: "eeHNNSDrru"}
	remark4 := "PCGCgxlHsg"
	bulkData4 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, WofangTestIP: "HJenrqQMUP"}
	remark5 := "ljvzxLdXSE"
	bulkData5 := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, WofangTestIP: "FaQZfNEPQt"}
	bulkDatas := [...]ent.SystemConfig{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.WofangTestIP
	testCase := &CaseRule{Api: systemConfigApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		systemConfigIDs = append(systemConfigIDs, getDataMapId(v))
	}
}

// Query SystemConfig test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySystemConfig(t *testing.T) {
	successExpectedResult.ResponseData = testSystemConfig.WofangTestIP
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID SystemConfig test case
// 根据 ID 查询
func TestQueryByIDSystemConfig(t *testing.T) {
	successExpectedResult.ResponseData = testSystemConfig.WofangTestIP
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "GET", UrlData: strconv.Itoa(systemConfigIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID SystemConfig not exist test case
// 根据 ID 查询
func TestQueryByIDSystemConfigNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testSystemConfig.WofangTestIP
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query SystemConfig by WofangTestIP test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySystemConfigByWofangTestIP(t *testing.T) {
	successExpectedResult.ResponseData = testSystemConfig.WofangTestIP
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "GET", UrlData: systemconfig.FieldWofangTestIP + "=" + testSystemConfig.WofangTestIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch SystemConfig search by WofangTestIP test case
// 分页搜索
func TestQuerySearchSystemConfigWofangTestIP(t *testing.T) {
	successExpectedResult.ResponseData = testSystemConfig.WofangTestIP
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "GET", UrlData: "search=" + testSystemConfig.WofangTestIP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID SystemConfig test case
// 根据 ID 修改
func TestUpdateByIDSystemConfig(t *testing.T) {
	remark24 := "KoDpivCDwP"
	updateData := &ent.SystemConfig{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark24, WofangTestIP: "mNmLCsflOf"}
	successExpectedResult.ResponseData = updateData.WofangTestIP
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "PUT", UrlData: strconv.Itoa(systemConfigIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID SystemConfig test case
// 根据 ID 删除
func TestDeleteByIDSystemConfig(t *testing.T) {
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(systemConfigIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID SystemConfig not exist test case
// 根据 ID 删除
func TestDeleteByIDSystemConfigNoExist(t *testing.T) {
	testCase := &CaseRule{Api: systemConfigApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk SystemConfig test case
// 根据 IDs 批量删除
func TestDeleteBulkSystemConfig(t *testing.T) {
	testCase := &CaseRule{Api: systemConfigApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: systemConfigIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
