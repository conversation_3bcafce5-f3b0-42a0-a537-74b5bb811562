package test

import (
	"meta/app/ent"
	"meta/app/ent/tenant"
	"meta/pkg/common"
	"strconv"
	"testing"
)

var tenantApi = baseApi + "/tenant"
var testTenant = &ent.Tenant{Name: "MNcbXxCNFF", Code: "xmrxYkNiaI"}
var tenantIDs = []int{}

// Create Tenant test case
// 创建
func TestCreateTenant(t *testing.T) {
	successExpectedResult.ResponseData = testTenant.Name
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "POST", BodyData: testTenant, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	tenantIDs = append(tenantIDs, getDataMapId(result))
}

// CreateBulk Tenant test case
// 批量创建
func TestCreateBulkTenant(t *testing.T) {
	bulkData1 := &ent.Tenant{Name: "POBVEWQSdg", Code: "RRyEMbJfcy"}
	bulkData2 := &ent.Tenant{Name: "mZQrRNmxZa", Code: "sqgJAsuenQ"}
	bulkData3 := &ent.Tenant{Name: "viGKqIXkqQ", Code: "eOrdvQyzKF"}
	bulkData4 := &ent.Tenant{Name: "AKtVHMDuJj", Code: "ORrqGlIoMM"}
	bulkData5 := &ent.Tenant{Name: "iFfYKsfTLf", Code: "xvncXuVpqR"}
	bulkDatas := [...]ent.Tenant{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: tenantApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		tenantIDs = append(tenantIDs, getDataMapId(v))
	}
}

// Query Tenant test case
// 根据指定字段、时间范围查询或搜索
func TestQueryTenant(t *testing.T) {
	successExpectedResult.ResponseData = testTenant.Name
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID Tenant test case
// 根据 ID 查询
func TestQueryByIDTenant(t *testing.T) {
	successExpectedResult.ResponseData = testTenant.Name
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "GET", UrlData: strconv.Itoa(tenantIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID Tenant not exist test case
// 根据 ID 查询
func TestQueryByIDTenantNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testTenant.Name
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query Tenant by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryTenantByName(t *testing.T) {
	successExpectedResult.ResponseData = testTenant.Name
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "GET", UrlData: tenant.FieldName + "=" + testTenant.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch Tenant search by Name test case
// 分页搜索
func TestQuerySearchTenantName(t *testing.T) {
	successExpectedResult.ResponseData = testTenant.Name
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "GET", UrlData: "search=" + testTenant.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID Tenant test case
// 根据 ID 修改
func TestUpdateByIDTenant(t *testing.T) {
	updateData := &ent.Tenant{Name: "WaXZgcTXRk", Code: "ZuOinzHQtD"}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "PUT", UrlData: strconv.Itoa(tenantIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID Tenant test case
// 根据 ID 删除
func TestDeleteByIDTenant(t *testing.T) {
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(tenantIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID Tenant not exist test case
// 根据 ID 删除
func TestDeleteByIDTenantNoExist(t *testing.T) {
	testCase := &CaseRule{Api: tenantApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk Tenant test case
// 根据 IDs 批量删除
func TestDeleteBulkTenant(t *testing.T) {
	testCase := &CaseRule{Api: tenantApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: tenantIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
