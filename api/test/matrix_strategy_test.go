package test

import (
	"meta/app/ent"
	"meta/app/ent/matrixstrategy"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var matrixStrategyApi = baseApi + "/matrixstrategy"
var remark22 = "eiXUTjSdih"
var testMatrixStrategy = &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark22, Name: "vKEVvIhqBL", Region: "yEGlPyXalt", NetType: "rmUuZPpcWG", Isp: "bAQWQqXCrD", MonitorBps: 374, DragBps: 334, DragType: 389}
var matrixStrategyIDs = []int{}

// Create MatrixStrategy test case
// 创建
func TestCreateMatrixStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixStrategy.Name
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "POST", BodyData: testMatrixStrategy, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	matrixStrategyIDs = append(matrixStrategyIDs, getDataMapId(result))
}

// CreateBulk MatrixStrategy test case
// 批量创建
func TestCreateBulkMatrixStrategy(t *testing.T) {
	remark1 := "AkTDsLgOaM"
	bulkData1 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "xSFIwDqzMK", Region: "KpKmmouUFu", NetType: "CHOyKuPcuc", Isp: "UufjiNmoVG", MonitorBps: 323, DragBps: 366, DragType: 32}
	remark2 := "DyGjADtIhb"
	bulkData2 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "YVaWPjTTBW", Region: "ITRqwOLsmt", NetType: "DYDLPsZWOZ", Isp: "nIYZigADDU", MonitorBps: 359, DragBps: 322, DragType: 377}
	remark3 := "yyeMXgOuHQ"
	bulkData3 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "LWNIRnGcJf", Region: "oibbTPskKB", NetType: "UezciamqoC", Isp: "IeYTMpIBQu", MonitorBps: 397, DragBps: 393, DragType: 373}
	remark4 := "kPYGkHZvXB"
	bulkData4 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "qKWCtlyTDI", Region: "vEvSnEiEWA", NetType: "ONJiJEZNzZ", Isp: "fkhmftHUZN", MonitorBps: 333, DragBps: 365, DragType: 373}
	remark5 := "QYpkFYnIDZ"
	bulkData5 := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "VBGTYWoKXY", Region: "TGjEdrWLnd", NetType: "svKzJtBRIP", Isp: "QIVPdNpqxV", MonitorBps: 39, DragBps: 393, DragType: 358}
	bulkDatas := [...]ent.MatrixStrategy{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: matrixStrategyApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		matrixStrategyIDs = append(matrixStrategyIDs, getDataMapId(v))
	}
}

// Query MatrixStrategy test case
// 根据指定字段、时间范围查询或搜索
func TestQueryMatrixStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixStrategy.Name
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID MatrixStrategy test case
// 根据 ID 查询
func TestQueryByIDMatrixStrategy(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixStrategy.Name
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "GET", UrlData: strconv.Itoa(matrixStrategyIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID MatrixStrategy not exist test case
// 根据 ID 查询
func TestQueryByIDMatrixStrategyNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixStrategy.Name
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query MatrixStrategy by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryMatrixStrategyByName(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixStrategy.Name
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "GET", UrlData: matrixstrategy.FieldName + "=" + testMatrixStrategy.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch MatrixStrategy search by Name test case
// 分页搜索
func TestQuerySearchMatrixStrategyName(t *testing.T) {
	successExpectedResult.ResponseData = testMatrixStrategy.Name
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "GET", UrlData: "search=" + testMatrixStrategy.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID MatrixStrategy test case
// 根据 ID 修改
func TestUpdateByIDMatrixStrategy(t *testing.T) {
	remark22 := "AdPeiVYacl"
	updateData := &ent.MatrixStrategy{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark22, Name: "ziulvOQkmD", Region: "OLnXuenLMi", NetType: "DTEnKlWvWF", Isp: "jZNHDCKcVI", MonitorBps: 373, DragBps: 376, DragType: 384}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "PUT", UrlData: strconv.Itoa(matrixStrategyIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID MatrixStrategy test case
// 根据 ID 删除
func TestDeleteByIDMatrixStrategy(t *testing.T) {
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(matrixStrategyIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID MatrixStrategy not exist test case
// 根据 ID 删除
func TestDeleteByIDMatrixStrategyNoExist(t *testing.T) {
	testCase := &CaseRule{Api: matrixStrategyApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk MatrixStrategy test case
// 根据 IDs 批量删除
func TestDeleteBulkMatrixStrategy(t *testing.T) {
	testCase := &CaseRule{Api: matrixStrategyApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: matrixStrategyIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
