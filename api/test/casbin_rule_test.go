package test

import (
	"meta/app/ent"
	"meta/app/ent/casbinrule"
	"meta/pkg/common"
	"strconv"
	"testing"
)

var casbinRuleApi = baseApi + "/casbinrule"
var testCasbinRule = &ent.CasbinRule{Type: "phCErprzrh", Sub: "YGznbbPcHb", Dom: "fkEoFQVhED", Obj: "hOINpYDMTb", Act: "jytzkVSGhD", V4: "mIkBhLVrvL", V5: "YlcYGKjqfE"}
var casbinRuleIDs = []int{}

// Create CasbinRule test case
// 创建
func TestCreateCasbinRule(t *testing.T) {
	successExpectedResult.ResponseData = testCasbinRule.Type
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "POST", BodyData: testCasbinRule, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	casbinRuleIDs = append(casbinRuleIDs, getDataMapId(result))
}

// CreateBulk CasbinRule test case
// 批量创建
func TestCreateBulkCasbinRule(t *testing.T) {
	bulkData1 := &ent.CasbinRule{Type: "RmRwQyuaNH", Sub: "tKBKqxgkdO", Dom: "mCnFQKWCki", Obj: "IDBEyJAXus", Act: "pnVOCANmHt", V4: "PYOxsUnGBx", V5: "pQZuNLybwB"}
	bulkData2 := &ent.CasbinRule{Type: "AyzVbexLzW", Sub: "eyJByHTMiG", Dom: "wDGoeHnhEG", Obj: "iyakzZVOle", Act: "ncBYVlNjow", V4: "ubJnZFbTBf", V5: "yUUpESPAOD"}
	bulkData3 := &ent.CasbinRule{Type: "kAZUZATRpD", Sub: "LNwJPNEFzg", Dom: "ZssFZrkCUD", Obj: "rjJaiCzikS", Act: "KbZBvSQuNO", V4: "YdsUCeesaG", V5: "YtuXlRZKpb"}
	bulkData4 := &ent.CasbinRule{Type: "tTYZLEuUAj", Sub: "HdclBzyeNo", Dom: "YTiLskkZQZ", Obj: "BtsXRLhRDu", Act: "kgTzXNTJCf", V4: "iqntCiKXpc", V5: "DGOiysoqgf"}
	bulkData5 := &ent.CasbinRule{Type: "ZkzujaaSNF", Sub: "sNsnblojvg", Dom: "gkFnPnIfgf", Obj: "ZovBnXOVHk", Act: "OSswBcpQIO", V4: "zzFJSNndqi", V5: "dfLzleZNTI"}
	bulkDatas := [...]ent.CasbinRule{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Type
	testCase := &CaseRule{Api: casbinRuleApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		casbinRuleIDs = append(casbinRuleIDs, getDataMapId(v))
	}
}

// Query CasbinRule test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCasbinRule(t *testing.T) {
	successExpectedResult.ResponseData = testCasbinRule.Type
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID CasbinRule test case
// 根据 ID 查询
func TestQueryByIDCasbinRule(t *testing.T) {
	successExpectedResult.ResponseData = testCasbinRule.Type
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "GET", UrlData: strconv.Itoa(casbinRuleIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID CasbinRule not exist test case
// 根据 ID 查询
func TestQueryByIDCasbinRuleNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testCasbinRule.Type
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query CasbinRule by NetType test case
// 根据指定字段、时间范围查询或搜索
func TestQueryCasbinRuleByType(t *testing.T) {
	successExpectedResult.ResponseData = testCasbinRule.Type
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "GET", UrlData: casbinrule.FieldType + "=" + testCasbinRule.Type, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch CasbinRule search by NetType test case
// 分页搜索
func TestQuerySearchCasbinRuleType(t *testing.T) {
	successExpectedResult.ResponseData = testCasbinRule.Type
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "GET", UrlData: "search=" + testCasbinRule.Type, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID CasbinRule test case
// 根据 ID 修改
func TestUpdateByIDCasbinRule(t *testing.T) {
	updateData := &ent.CasbinRule{Type: "bLMjbQWupR", Sub: "BavnxpHmXL", Dom: "yafeGMEdjx", Obj: "IDFjRDpIMK", Act: "HkXSBGTdRw", V4: "XTCjiLDGuL", V5: "KdCyuFpwTe"}
	successExpectedResult.ResponseData = updateData.Type
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "PUT", UrlData: strconv.Itoa(casbinRuleIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID CasbinRule test case
// 根据 ID 删除
func TestDeleteByIDCasbinRule(t *testing.T) {
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(casbinRuleIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID CasbinRule not exist test case
// 根据 ID 删除
func TestDeleteByIDCasbinRuleNoExist(t *testing.T) {
	testCase := &CaseRule{Api: casbinRuleApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk CasbinRule test case
// 根据 IDs 批量删除
func TestDeleteBulkCasbinRule(t *testing.T) {
	testCase := &CaseRule{Api: casbinRuleApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: casbinRuleIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
