package test

import (
	"meta/app/ent"
	"meta/app/ent/wofang"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var wofangApi = baseApi + "/wofang"
var remark21 = "IMHiXgMTWd"
var testWofang = &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark21, Name: "qlYIqOpzdm", IP: "UHWlqwuDsu", Type: "gQMtDGcntR", UnDragSecond: 344, ErrorInfo: "IZachRXFOa", Status: "OzVDuDAXuQ"}
var wofangIDs = []int{}

// Create Wofang test case
// 创建
func TestCreateWofang(t *testing.T) {
	successExpectedResult.ResponseData = testWofang.Name
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "POST", BodyData: testWofang, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	wofangIDs = append(wofangIDs, getDataMapId(result))
}

// CreateBulk Wofang test case
// 批量创建
func TestCreateBulkWofang(t *testing.T) {
	remark1 := "WlAtbiZEss"
	bulkData1 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "vYQrBonLLU", IP: "iBKhVOaJed", Type: "yPalgXmLTv", UnDragSecond: 386, ErrorInfo: "EpBzNnVQuW", Status: "RjObkMbktJ"}
	remark2 := "NTjTHeADSn"
	bulkData2 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "fVXtxgtkfM", IP: "foBPJFHBcd", Type: "YMInWZrwyb", UnDragSecond: 32, ErrorInfo: "qHIsslWtUu", Status: "UaZzbTfjIy"}
	remark3 := "WwHpzLXVkt"
	bulkData3 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "IhZnVcjBEj", IP: "vZORaEFhhk", Type: "ujUCUrJsnJ", UnDragSecond: 386, ErrorInfo: "KluztODoTW", Status: "frCyLoErGG"}
	remark4 := "OovswKrIIs"
	bulkData4 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "IrXXMfZcbv", IP: "fJTNoktSQj", Type: "vfwuTlZJKV", UnDragSecond: 390, ErrorInfo: "YIEeLwcavh", Status: "fCDqqsbYUQ"}
	remark5 := "bekKJRsrxR"
	bulkData5 := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "IaNXGydeAg", IP: "tpCCgReNdU", Type: "rXqLgNGbas", UnDragSecond: 374, ErrorInfo: "vbwEmotxzG", Status: "claZNgxzAl"}
	bulkDatas := [...]ent.Wofang{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: wofangApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		wofangIDs = append(wofangIDs, getDataMapId(v))
	}
}

// Query Wofang test case
// 根据指定字段、时间范围查询或搜索
func TestQueryWofang(t *testing.T) {
	successExpectedResult.ResponseData = testWofang.Name
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID Wofang test case
// 根据 ID 查询
func TestQueryByIDWofang(t *testing.T) {
	successExpectedResult.ResponseData = testWofang.Name
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "GET", UrlData: strconv.Itoa(wofangIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID Wofang not exist test case
// 根据 ID 查询
func TestQueryByIDWofangNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testWofang.Name
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query Wofang by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQueryWofangByName(t *testing.T) {
	successExpectedResult.ResponseData = testWofang.Name
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "GET", UrlData: wofang.FieldName + "=" + testWofang.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch Wofang search by Name test case
// 分页搜索
func TestQuerySearchWofangName(t *testing.T) {
	successExpectedResult.ResponseData = testWofang.Name
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "GET", UrlData: "search=" + testWofang.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID Wofang test case
// 根据 ID 修改
func TestUpdateByIDWofang(t *testing.T) {
	remark21 := "pryPLvwcNF"
	updateData := &ent.Wofang{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark21, Name: "PJOiWwokFC", IP: "MYtjcXAyXK", Type: "OwAaNVWIdI", UnDragSecond: 326, ErrorInfo: "TyUfavOaIY", Status: "UeUNaBERbr"}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "PUT", UrlData: strconv.Itoa(wofangIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID Wofang test case
// 根据 ID 删除
func TestDeleteByIDWofang(t *testing.T) {
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(wofangIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID Wofang not exist test case
// 根据 ID 删除
func TestDeleteByIDWofangNoExist(t *testing.T) {
	testCase := &CaseRule{Api: wofangApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk Wofang test case
// 根据 IDs 批量删除
func TestDeleteBulkWofang(t *testing.T) {
	testCase := &CaseRule{Api: wofangApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: wofangIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
