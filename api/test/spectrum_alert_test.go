package test

import (
	"meta/app/ent"
	"meta/app/ent/spectrumalert"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var spectrumAlertApi = baseApi + "/spectrumalert"
var remark15 = "ZJxzvagFpy"
var testSpectrumAlert = &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark15, IP: "JLkGkHygKk", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "bDLvwgpIzo", MaxPps: 319, MaxBps: 33, IspCode: 326}
var spectrumAlertIDs = []int{}

// Create SpectrumAlert test case
// 创建
func TestCreateSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "POST", BodyData: testSpectrumAlert, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	spectrumAlertIDs = append(spectrumAlertIDs, getDataMapId(result))
}

// CreateBulk SpectrumAlert test case
// 批量创建
func TestCreateBulkSpectrumAlert(t *testing.T) {
	remark1 := "OzJIEEnICJ"
	bulkData1 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, IP: "xbfTexiWvz", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "fVoNnDVBKY", MaxPps: 346, MaxBps: 338, IspCode: 399}
	remark2 := "OXacJAugWQ"
	bulkData2 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, IP: "VGwKnwdzZy", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "MCjepABHsk", MaxPps: 390, MaxBps: 399, IspCode: 310}
	remark3 := "PXrUHncdEO"
	bulkData3 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, IP: "ruMfErNSBL", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "anaWSPefuz", MaxPps: 331, MaxBps: 350, IspCode: 378}
	remark4 := "hAXQtsiIDA"
	bulkData4 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, IP: "TXmjeccWmA", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "vUPsfaBofn", MaxPps: 388, MaxBps: 393, IspCode: 377}
	remark5 := "tZsahruGRB"
	bulkData5 := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, IP: "PIhwLgtZVu", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "sfRqrkiFHQ", MaxPps: 373, MaxBps: 392, IspCode: 380}
	bulkDatas := [...]ent.SpectrumAlert{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.IP
	testCase := &CaseRule{Api: spectrumAlertApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		spectrumAlertIDs = append(spectrumAlertIDs, getDataMapId(v))
	}
}

// Query SpectrumAlert test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID SpectrumAlert test case
// 根据 ID 查询
func TestQueryByIDSpectrumAlert(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: strconv.Itoa(spectrumAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID SpectrumAlert not exist test case
// 根据 ID 查询
func TestQueryByIDSpectrumAlertNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query SpectrumAlert by IP test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySpectrumAlertByIP(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: spectrumalert.FieldIP + "=" + testSpectrumAlert.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch SpectrumAlert search by IP test case
// 分页搜索
func TestQuerySearchSpectrumAlertIP(t *testing.T) {
	successExpectedResult.ResponseData = testSpectrumAlert.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "GET", UrlData: "search=" + testSpectrumAlert.IP, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID SpectrumAlert test case
// 根据 ID 修改
func TestUpdateByIDSpectrumAlert(t *testing.T) {
	remark15 := "SpaboZcjAG"
	updateData := &ent.SpectrumAlert{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark15, IP: "aDMJRUnEQZ", StartTime: time.Now().AddDate(0, 0, -1), EndTime: time.Now().AddDate(0, 0, -1), AttackType: "TcPcaZCVOv", MaxPps: 311, MaxBps: 331, IspCode: 338}
	successExpectedResult.ResponseData = updateData.IP
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "PUT", UrlData: strconv.Itoa(spectrumAlertIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID SpectrumAlert test case
// 根据 ID 删除
func TestDeleteByIDSpectrumAlert(t *testing.T) {
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(spectrumAlertIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID SpectrumAlert not exist test case
// 根据 ID 删除
func TestDeleteByIDSpectrumAlertNoExist(t *testing.T) {
	testCase := &CaseRule{Api: spectrumAlertApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk SpectrumAlert test case
// 根据 IDs 批量删除
func TestDeleteBulkSpectrumAlert(t *testing.T) {
	testCase := &CaseRule{Api: spectrumAlertApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: spectrumAlertIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
