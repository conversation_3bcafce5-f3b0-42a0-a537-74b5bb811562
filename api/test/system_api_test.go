package test

import (
	"meta/app/ent"
	"meta/app/ent/systemapi"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var systemApiApi = baseApi + "/systemapi"
var remark20 = "cErpbdsbCU"
var testSystemApi = &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark20, Name: "eTLoaQhVCG", Path: "AwfLAWGgqe", HTTPMethod: "bTsUbVJqqC"}
var systemApiIDs = []int{}

// Create SystemApi test case
// 创建
func TestCreateSystemApi(t *testing.T) {
	successExpectedResult.ResponseData = testSystemApi.Name
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "POST", BodyData: testSystemApi, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	systemApiIDs = append(systemApiIDs, getDataMapId(result))
}

// CreateBulk SystemApi test case
// 批量创建
func TestCreateBulkSystemApi(t *testing.T) {
	remark1 := "RuUFrtXPVI"
	bulkData1 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark1, Name: "QgRUEdfwsc", Path: "tqNTSsKLTz", HTTPMethod: "LRMQtwAmxd"}
	remark2 := "PayhFmufSe"
	bulkData2 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark2, Name: "VyVcCGQKMC", Path: "WyiDDcpCny", HTTPMethod: "DgIuqTMLKk"}
	remark3 := "FemWBqwgJV"
	bulkData3 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark3, Name: "iuAWaryaPM", Path: "jWQssKUGMA", HTTPMethod: "TSuFguPQHc"}
	remark4 := "wSXmIeYSIF"
	bulkData4 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark4, Name: "IeTbZBQKWe", Path: "FNQszlnMdy", HTTPMethod: "DiMjzbSRuL"}
	remark5 := "kTBynNFDJB"
	bulkData5 := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark5, Name: "oyztdikNqs", Path: "ywBeAitGKH", HTTPMethod: "PbyyVCXyhq"}
	bulkDatas := [...]ent.SystemApi{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Name
	testCase := &CaseRule{Api: systemApiApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		systemApiIDs = append(systemApiIDs, getDataMapId(v))
	}
}

// Query SystemApi test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySystemApi(t *testing.T) {
	successExpectedResult.ResponseData = testSystemApi.Name
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID SystemApi test case
// 根据 ID 查询
func TestQueryByIDSystemApi(t *testing.T) {
	successExpectedResult.ResponseData = testSystemApi.Name
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "GET", UrlData: strconv.Itoa(systemApiIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID SystemApi not exist test case
// 根据 ID 查询
func TestQueryByIDSystemApiNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testSystemApi.Name
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query SystemApi by Name test case
// 根据指定字段、时间范围查询或搜索
func TestQuerySystemApiByName(t *testing.T) {
	successExpectedResult.ResponseData = testSystemApi.Name
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "GET", UrlData: systemapi.FieldName + "=" + testSystemApi.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch SystemApi search by Name test case
// 分页搜索
func TestQuerySearchSystemApiName(t *testing.T) {
	successExpectedResult.ResponseData = testSystemApi.Name
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "GET", UrlData: "search=" + testSystemApi.Name, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID SystemApi test case
// 根据 ID 修改
func TestUpdateByIDSystemApi(t *testing.T) {
	remark20 := "TqctFacAXr"
	updateData := &ent.SystemApi{CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Remark: &remark20, Name: "GhcTWKShNY", Path: "hdGYSCyUpY", HTTPMethod: "axuNoJLIzd"}
	successExpectedResult.ResponseData = updateData.Name
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "PUT", UrlData: strconv.Itoa(systemApiIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID SystemApi test case
// 根据 ID 删除
func TestDeleteByIDSystemApi(t *testing.T) {
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(systemApiIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID SystemApi not exist test case
// 根据 ID 删除
func TestDeleteByIDSystemApiNoExist(t *testing.T) {
	testCase := &CaseRule{Api: systemApiApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk SystemApi test case
// 根据 IDs 批量删除
func TestDeleteBulkSystemApi(t *testing.T) {
	testCase := &CaseRule{Api: systemApiApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: systemApiIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
