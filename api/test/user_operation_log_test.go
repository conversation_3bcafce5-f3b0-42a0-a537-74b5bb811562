package test

import (
	"meta/app/ent"
	"meta/app/ent/useroperationlog"
	"meta/pkg/common"
	"strconv"
	"testing"
	"time"
)

var userOperationLogApi = baseApi + "/useroperationlog"
var remark18 = "FoXGMRUcgF"
var testUserOperationLog = &ent.UserOperationLog{Remark: &remark18, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "YECnxhGoSV", Method: "cULTFhoJES", URI: "uhPRAxPqIR", RequestBody: "WPZecSSnTB", Project: "zrTuBOevwA"}
var userOperationLogIDs = []int{}

// Create UserOperationLog test case
// 创建
func TestCreateUserOperationLog(t *testing.T) {
	successExpectedResult.ResponseData = testUserOperationLog.Username
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "POST", BodyData: testUserOperationLog, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	userOperationLogIDs = append(userOperationLogIDs, getDataMapId(result))
}

// CreateBulk UserOperationLog test case
// 批量创建
func TestCreateBulkUserOperationLog(t *testing.T) {
	remark1 := "sWyoduqvAJ"
	bulkData1 := &ent.UserOperationLog{Remark: &remark1, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "zOAbfDKsFh", Method: "xMKYfStAlO", URI: "pXscDwahvg", RequestBody: "jbsWiQvHMN", Project: "VnnSEykGjI"}
	remark2 := "QDxJvgsPMH"
	bulkData2 := &ent.UserOperationLog{Remark: &remark2, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "XSkLnCTgxQ", Method: "fqDoLQkQFo", URI: "ctPcfjFIUc", RequestBody: "HbyJPPUpoM", Project: "BPTEWjigtT"}
	remark3 := "SkXBWYNPMz"
	bulkData3 := &ent.UserOperationLog{Remark: &remark3, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "FIVMYAihic", Method: "HNqdtrCzzN", URI: "WbPbuZOQrX", RequestBody: "sSXZvNRIay", Project: "jntKvkjbNj"}
	remark4 := "nElDiPmFwI"
	bulkData4 := &ent.UserOperationLog{Remark: &remark4, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "QjFapquOQe", Method: "VwCMfATuIq", URI: "FDZlQyTPeJ", RequestBody: "radGpLzVkD", Project: "IfdiTzjdWA"}
	remark5 := "GZJVmjBukV"
	bulkData5 := &ent.UserOperationLog{Remark: &remark5, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "oFtZJOKmgJ", Method: "lLufeKosIC", URI: "CelxPBqrwy", RequestBody: "FxlbYuWLNn", Project: "fJuLiPFkfv"}
	bulkDatas := [...]ent.UserOperationLog{*bulkData1, *bulkData2, *bulkData3, *bulkData4, *bulkData5}
	successExpectedResult.ResponseData = bulkData1.Username
	testCase := &CaseRule{Api: userOperationLogApi + "/bulk", HttpMethod: "POST", BodyData: bulkDatas, Expected: successExpectedResult, Assert: assertEqualContains}
	result := runTest(t, testCase)
	dataMap := result.([]any)
	for _, v := range dataMap {
		userOperationLogIDs = append(userOperationLogIDs, getDataMapId(v))
	}
}

// Query UserOperationLog test case
// 根据指定字段、时间范围查询或搜索
func TestQueryUserOperationLog(t *testing.T) {
	successExpectedResult.ResponseData = testUserOperationLog.Username
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "GET", UrlData: "current=1&pageSize=10", Expected: successExpectedResult, Assert: assertEqualContainsGrater}
	runTest(t, testCase)
}

// QueryByID UserOperationLog test case
// 根据 ID 查询
func TestQueryByIDUserOperationLog(t *testing.T) {
	successExpectedResult.ResponseData = testUserOperationLog.Username
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "GET", UrlData: strconv.Itoa(userOperationLogIDs[0]), Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QueryByID UserOperationLog not exist test case
// 根据 ID 查询
func TestQueryByIDUserOperationLogNotExist(t *testing.T) {
	successExpectedResult.ResponseData = testUserOperationLog.Username
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "GET", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// Query UserOperationLog by Username test case
// 根据指定字段、时间范围查询或搜索
func TestQueryUserOperationLogByUsername(t *testing.T) {
	successExpectedResult.ResponseData = testUserOperationLog.Username
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "GET", UrlData: useroperationlog.FieldUsername + "=" + testUserOperationLog.Username, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// QuerySearch UserOperationLog search by Username test case
// 分页搜索
func TestQuerySearchUserOperationLogUsername(t *testing.T) {
	successExpectedResult.ResponseData = testUserOperationLog.Username
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "GET", UrlData: "search=" + testUserOperationLog.Username, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// UpdateByID UserOperationLog test case
// 根据 ID 修改
func TestUpdateByIDUserOperationLog(t *testing.T) {
	remark18 := "CrWyEkyNTX"
	updateData := &ent.UserOperationLog{Remark: &remark18, CreatedAt: time.Now().AddDate(0, 0, -1), UpdatedAt: time.Now().AddDate(0, 0, -1), Username: "RFYFGSpRBK", Method: "FASyWolauK", URI: "BPiuVTdiCu", RequestBody: "ikChhTUtjp", Project: "EtVTlKFcDn"}
	successExpectedResult.ResponseData = updateData.Username
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "PUT", UrlData: strconv.Itoa(userOperationLogIDs[0]), BodyData: updateData, Expected: successExpectedResult, Assert: assertEqualContains}
	runTest(t, testCase)
}

// DeleteByID UserOperationLog test case
// 根据 ID 删除
func TestDeleteByIDUserOperationLog(t *testing.T) {
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "DELETE", UrlData: strconv.Itoa(userOperationLogIDs[0]), Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteByID UserOperationLog not exist test case
// 根据 ID 删除
func TestDeleteByIDUserOperationLogNoExist(t *testing.T) {
	testCase := &CaseRule{Api: userOperationLogApi, HttpMethod: "DELETE", UrlData: "0", Expected: errorFoundExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}

// DeleteBulk UserOperationLog test case
// 根据 IDs 批量删除
func TestDeleteBulkUserOperationLog(t *testing.T) {
	testCase := &CaseRule{Api: userOperationLogApi + "/bulk/delete", HttpMethod: "POST", BodyData: common.DeleteItem{Ids: userOperationLogIDs}, Expected: successExpectedResult, Assert: assertEqual}
	runTest(t, testCase)
}
