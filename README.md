## DDoS平台后端

使用[Task](https://taskfile.dev/zh-Hans/installation/)进行CI/CD

基于[Meta](https://github.com/one-meta/meta)的后端脚手架

基于[air](https://github.com/cosmtrek/air)go 程序热重启，可在开发中热重启

依赖：

1. [Task](https://taskfile.dev/zh-Hans/installation/)
2. go >= v1.21.6
3. docker 或者 podman

参考：[Meta框架联合使用说明](https://github.com/one-meta/meta/wiki/%E6%A1%86%E6%9E%B6%E8%81%94%E5%90%88%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E)

## 开发
项目根目录执行：`air`

### 运行

`task run`

### 编译linux exec

`task build` 或者 `task linux`

### 部署（本地CI/CD）

> 如果不是使用podman，需要修改Taskfile中的 `env.DOCKER_NAME`


测试环境：`task deploy-test` 或者 `task test`

生产环境：`task deploy-prod` 或者 `task prod`
