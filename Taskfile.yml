version: '3'
silent: true

env:
  DOCKER_NAME: "podman"

  TEST_IMG_URL: "ncr.nie.netease.com/sesa/ddos"
  TEST_DOCKER_FILE: "Dockerfile_local"

  PROD_IMG_URL: "ncr.nie.netease.com/sesa/ddos:production"
  PROD_DOCKER_FILE: "Dockerfile_local_prod"
tasks:
  run:
    desc: Run project.
    cmds:
      - go run main.go
  build:
    desc: Build linux exec.
    cmds:
      - meta-g && wire ./app
      - CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o meta main.go
    aliases: [ linux ]
  deploy-test:
    deps: [ build ]
    desc: Build and deploy test docker image.
    cmds:
      - ${DOCKER_NAME} build --platform linux/amd64 -t ${TEST_IMG_URL} . -f ${TEST_DOCKER_FILE}
      - ${DOCKER_NAME} push ${TEST_IMG_URL}
    aliases: [ test ]

  deploy-prod:
    deps: [ build ]
    desc: Build and deploy prod docker image.
    cmds:
      - ${DOCKER_NAME} build --platform linux/amd64 -t ${PROD_IMG_URL} . -f ${PROD_DOCKER_FILE}
      - ${DOCKER_NAME} push ${PROD_IMG_URL}
    aliases: [ prod ]