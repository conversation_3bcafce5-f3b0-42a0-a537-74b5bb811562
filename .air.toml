# See: https://github.com/cosmtrek/air/blob/master/air_example.toml
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
args_bin = []
bin = "./tmp/meta"
cmd = "meta-g && go mod tidy && mkdir -p data/logs/web && cp -rf resource ./tmp && wire ./app && swag init && CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 go build -o ./tmp/meta ."
# It's not necessary to trigger build each time file changes if it's too frequent.
delay = 3000 # ms
exclude_dir = ["assets", "tmp", "vendor", "testdata", "docs", "data", "app/ent"]
exclude_file = ["app/wire_gen.go"]
exclude_regex = ["_test.go"]
exclude_unchanged = false
follow_symlink = false
full_bin = ""
include_dir = []
include_ext = ["go", "tpl", "tmpl", "toml"]
kill_delay = 500 # ms
log = "build-errors.log"
send_interrupt = false
stop_on_error = true

[color]
app = ""
build = "yellow"
main = "magenta"
runner = "green"
watcher = "cyan"

[log]
time = false

[misc]
# Delete tmp directory on exit
clean_on_exit = false

[screen]
clear_on_rebuild = false
