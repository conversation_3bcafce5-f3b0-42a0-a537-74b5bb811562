module meta

go 1.21

require (
	entgo.io/ent v0.12.5
	github.com/IBM/sarama v1.42.1
	github.com/bytedance/sonic v1.10.2
	github.com/casbin/casbin/v2 v2.81.0
	github.com/casbin/ent-adapter v0.3.0
	github.com/deckarep/golang-set v1.8.0
	github.com/go-co-op/gocron v1.37.0
	github.com/go-sql-driver/mysql v1.7.1
	github.com/gofiber/fiber/v2 v2.52.0
	github.com/gofiber/swagger v0.1.14
	github.com/golang-jwt/jwt/v4 v4.5.0
	github.com/google/wire v0.5.0
	github.com/mattn/go-sqlite3 v1.14.19
	github.com/redis/go-redis/v9 v9.4.0
	github.com/sethvargo/go-password v0.2.0
	github.com/spf13/viper v1.18.2
	github.com/stretchr/testify v1.8.4
	github.com/swaggo/swag v1.16.2
	go.uber.org/zap v1.26.0
	golang.org/x/crypto v0.18.0
	golang.org/x/exp v0.0.0-20240112132812-db7319d0e0e3
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
)

require (
	ariga.io/atlas v0.18.0 // indirect
	github.com/agext/levenshtein v1.2.3 // indirect
	github.com/andybalholm/brotli v1.1.0 // indirect
	github.com/casbin/govaluate v1.1.1 // indirect
	github.com/chenzhuoyu/iasm v0.9.1 // indirect
	github.com/eapache/go-resiliency v1.5.0 // indirect
	github.com/go-openapi/spec v0.20.14 // indirect
	github.com/go-openapi/swag v0.22.7 // indirect
	github.com/hashicorp/hcl/v2 v2.19.1 // indirect
	github.com/jackc/pgconn v1.14.1 // indirect
	github.com/jackc/pgservicefile v0.0.0-20231201235250-de7065d80cb9 // indirect
	github.com/jackc/pgtype v1.14.1 // indirect
	github.com/klauspost/compress v1.17.4 // indirect
	github.com/klauspost/cpuid/v2 v2.2.6 // indirect
	github.com/lib/pq v1.10.9 // indirect
	github.com/mitchellh/go-wordwrap v1.0.1 // indirect
	github.com/pelletier/go-toml/v2 v2.1.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/rivo/uniseg v0.4.4 // indirect
	github.com/zclconf/go-cty v1.14.1 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/arch v0.7.0 // indirect
)
