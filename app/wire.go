//go:build wireinject
// +build wireinject

package app

import (
	"github.com/google/wire"
	router "meta/api/v1"
	"meta/app/controller"
	"meta/app/service"
	"meta/app/wireset"
	"meta/pkg"
)

func Init() (*wireset.Injector, func(), error) {
	// 调用wire.Build方法传入所有的依赖对象以及构建最终对象的函数得到目标对象
	panic(
		wire.Build(
			//resource.NewQueryParam,
			wireset.InitLog,
			wireset.CacheProvider,
			wireset.InitEnt,
			wireset.InitCasbin,
			service.Set,
			controller.Set,
			router.Set,
			pkg.Set,
			wireset.InjectorSet,
		),
	)
}
