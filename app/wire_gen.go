// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package app

import (
	"meta/api/v1"
	"meta/app/controller"
	"meta/app/service"
	"meta/app/wireset"
	"meta/pkg/auth"
	"meta/pkg/http"
	"meta/pkg/middleware"
	"meta/pkg/middleware/generator"
	"meta/pkg/register"
)

// Injectors from wire.go:

func Init() (*wireset.Injector, func(), error) {
	enforcer, cleanup, err := wireset.InitCasbin()
	if err != nil {
		return nil, nil, err
	}
	client, cleanup2, err := wireset.InitEnt()
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	userService := &service.UserService{
		Dao: client,
	}
	logger, cleanup3, err := wireset.InitLog()
	if err != nil {
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	tenantService := &service.TenantService{
		Dao: client,
	}
	entx := &auth.Entx{
		UserService:   userService,
		TenantService: tenantService,
		Logger:        logger,
	}
	casbinx := &middleware.Casbinx{
		UserService: userService,
		Logger:      logger,
		AuthEnt:     entx,
		Enf:         enforcer,
	}
	casbinRuleService := &service.CasbinRuleService{
		Dao: client,
		Enf: enforcer,
	}
	systemApiService := &service.SystemApiService{
		Dao: client,
	}
	redisClient, cleanup4, err := wireset.NewRedis()
	if err != nil {
		cleanup3()
		cleanup2()
		cleanup()
		return nil, nil, err
	}
	jwt := &middleware.JWT{
		Logger:            logger,
		UserService:       userService,
		TenantService:     tenantService,
		CasbinRuleService: casbinRuleService,
		SystemApiService:  systemApiService,
		AuthEnt:           entx,
		Rdb:               redisClient,
	}
	apikey := &middleware.Apikey{}
	userOperationLogService := &service.UserOperationLogService{
		Dao: client,
	}
	opLog := &middleware.OPLog{
		UserOperationLogService: userOperationLogService,
		Logger:                  logger,
		AuthEnt:                 entx,
	}
	gen := &generator.Gen{
		AuthEnt: entx,
	}
	casbinRuleController := &controller.CasbinRuleController{
		CasbinRuleService: casbinRuleService,
		Logger:            logger,
	}
	cleanDataService := &service.CleanDataService{
		Dao: client,
	}
	cleanDataController := &controller.CleanDataController{
		CleanDataService: cleanDataService,
		Logger:           logger,
	}
	groupService := &service.GroupService{
		Dao: client,
	}
	groupController := &controller.GroupController{
		GroupService: groupService,
		Logger:       logger,
	}
	protectGroupService := &service.ProtectGroupService{
		Dao: client,
	}
	protectGroupController := &controller.ProtectGroupController{
		ProtectGroupService: protectGroupService,
		Logger:              logger,
	}
	spectrumAlertService := &service.SpectrumAlertService{
		Dao: client,
	}
	spectrumDataService := &service.SpectrumDataService{
		Dao: client,
	}
	notifyService := &service.NotifyService{
		Dao: client,
	}
	strategyService := &service.StrategyService{
		Dao: client,
	}
	authV2X := &http.AuthV2X{
		Logger: logger,
		Rdb:    redisClient,
	}
	wofangService := &service.WofangService{
		Dao: client,
	}
	projectController := &controller.ProjectController{
		TenantService: tenantService,
		Logger:        logger,
		Rdb:           redisClient,
		AuthV2:        authV2X,
	}
	systemConfigService := &service.SystemConfigService{
		Dao: client,
	}
	ndsController := &controller.NdsController{
		ProtectGroupService:  protectGroupService,
		SpectrumAlertService: spectrumAlertService,
		SpectrumDataService:  spectrumDataService,
		CleanDataService:     cleanDataService,
		TenantService:        tenantService,
		NotifyService:        notifyService,
		StrategyService:      strategyService,
		Logger:               logger,
		Rdb:                  redisClient,
		AuthEnt:              entx,
		AuthV2:               authV2X,
		WofangService:        wofangService,
		ProjectCtl:           projectController,
		SystemConfigService:  systemConfigService,
	}
	spectrumAlertController := &controller.SpectrumAlertController{
		SpectrumAlertService: spectrumAlertService,
		Logger:               logger,
	}
	spectrumDataController := &controller.SpectrumDataController{
		SpectrumDataService: spectrumDataService,
		Logger:              logger,
	}
	tenantController := &controller.TenantController{
		TenantService: tenantService,
		Logger:        logger,
	}
	userController := &controller.UserController{
		UserService: userService,
		Logger:      logger,
		Enf:         enforcer,
		AuthEnt:     entx,
	}
	strategyController := &controller.StrategyController{
		StrategyService: strategyService,
		Logger:          logger,
	}
	notifyController := &controller.NotifyController{
		NotifyService: notifyService,
		Logger:        logger,
	}
	socGroupController := &controller.SocGroupController{}
	wofangController := &controller.WofangController{
		WofangService: wofangService,
		Logger:        logger,
		Rdb:           redisClient,
	}
	socGroupTicketService := &service.SocGroupTicketService{
		Dao: client,
	}
	socGroupTicketController := &controller.SocGroupTicketController{
		SocGroupTicketService: socGroupTicketService,
		Logger:                logger,
		Rdb:                   redisClient,
	}
	systemApiController := &controller.SystemApiController{
		SystemApiService: systemApiService,
		Logger:           logger,
		Rdb:              redisClient,
	}
	cloudAlertService := &service.CloudAlertService{
		Dao: client,
	}
	cloudAlertController := &controller.CloudAlertController{
		CloudAlertService: cloudAlertService,
		Logger:            logger,
	}
	cloudFlowDataService := &service.CloudFlowDataService{
		Dao: client,
	}
	cloudFlowDataController := &controller.CloudFlowDataController{
		CloudFlowDataService: cloudFlowDataService,
		Logger:               logger,
	}
	cloudAttackDataService := &service.CloudAttackDataService{
		Dao: client,
	}
	cloudAttackDataController := &controller.CloudAttackDataController{
		CloudAttackDataService: cloudAttackDataService,
		Logger:                 logger,
	}
	wofangAlertService := &service.WofangAlertService{
		Dao: client,
	}
	wofangAlertController := &controller.WofangAlertController{
		WofangAlertService: wofangAlertService,
		Logger:             logger,
	}
	skylineDosService := &service.SkylineDosService{
		Dao: client,
	}
	skylineDosController := &controller.SkylineDosController{
		SkylineDosService: skylineDosService,
		Logger:            logger,
	}
	matrixStrategyService := &service.MatrixStrategyService{
		Dao: client,
	}
	matrixStrategyController := &controller.MatrixStrategyController{
		MatrixStrategyService: matrixStrategyService,
		Logger:                logger,
		Rdb:                   redisClient,
	}
	matrixSpectrumDataService := &service.MatrixSpectrumDataService{
		Dao: client,
	}
	matrixSpectrumDataController := &controller.MatrixSpectrumDataController{
		MatrixSpectrumDataService: matrixSpectrumDataService,
		Logger:                    logger,
	}
	matrixSpectrumAlertService := &service.MatrixSpectrumAlertService{
		Dao: client,
	}
	matrixSpectrumAlertController := &controller.MatrixSpectrumAlertController{
		MatrixSpectrumAlertService: matrixSpectrumAlertService,
		Logger:                     logger,
		Rdb:                        redisClient,
		NdsController:              ndsController,
	}
	userOperationLogController := &controller.UserOperationLogController{
		UserOperationLogService: userOperationLogService,
		Logger:                  logger,
	}
	systemConfigController := &controller.SystemConfigController{
		SystemConfigService: systemConfigService,
		Logger:              logger,
		Rdb:                 redisClient,
	}
	dataSyncService := &service.DataSyncService{
		Dao: client,
	}
	dataSyncController := &controller.DataSyncController{
		DataSyncService: dataSyncService,
		Logger:          logger,
	}
	woFangApi := &controller.WoFangApi{}
	woFangApi2 := &controller.WoFangApi2{
		Logger: logger,
	}
	chartController := &controller.ChartController{}
	router := &v1.Router{
		Enf:                           enforcer,
		Casbinx:                       casbinx,
		JWT:                           jwt,
		AuthApiKey:                    apikey,
		OPLog:                         opLog,
		Gen:                           gen,
		CasbinRuleController:          casbinRuleController,
		CleanDataController:           cleanDataController,
		GroupController:               groupController,
		ProtectGroupController:        protectGroupController,
		NdsController:                 ndsController,
		SpectrumAlertController:       spectrumAlertController,
		SpectrumDataController:        spectrumDataController,
		TenantController:              tenantController,
		UserController:                userController,
		StrategyController:            strategyController,
		NotifyController:              notifyController,
		SocGroupController:            socGroupController,
		WofangController:              wofangController,
		SocGroupTicketController:      socGroupTicketController,
		SystemApiController:           systemApiController,
		CloudAlertController:          cloudAlertController,
		CloudFlowDataController:       cloudFlowDataController,
		CloudAttackDataController:     cloudAttackDataController,
		WoFangAlertController:         wofangAlertController,
		SkylineDosController:          skylineDosController,
		MatrixStrategyController:      matrixStrategyController,
		MatrixSpectrumDataController:  matrixSpectrumDataController,
		MatrixSpectrumAlertController: matrixSpectrumAlertController,
		UserOperationLogController:    userOperationLogController,
		SystemConfigController:        systemConfigController,
		DataSyncController:            dataSyncController,
		WoFangApi:                     woFangApi,
		WoFangApi2:                    woFangApi2,
		ChartApi:                      chartController,
	}
	runner := &register.Runner{
		ProtectGroupService:        protectGroupService,
		Logger:                     logger,
		AuthEnt:                    entx,
		AuthV2:                     authV2X,
		Rdb:                        redisClient,
		UserService:                userService,
		TenantService:              tenantService,
		CasbinRuleService:          casbinRuleService,
		SystemApiService:           systemApiService,
		CloudAlertService:          cloudAlertService,
		CloudAttackDataService:     cloudAttackDataService,
		CloudFlowDataService:       cloudFlowDataService,
		WofangService:              wofangService,
		SkylineDosService:          skylineDosService,
		NdsController:              ndsController,
		MatrixStrategyService:      matrixStrategyService,
		MatrixSpectrumDataService:  matrixSpectrumDataService,
		MatrixSpectrumAlertService: matrixSpectrumAlertService,
		SystemConfigService:        systemConfigService,
		SpectrumAlertService:       spectrumAlertService,
		DataSyncService:            dataSyncService,
	}
	injector := &wireset.Injector{
		Router:           router,
		RegisterInjector: runner,
	}
	return injector, func() {
		cleanup4()
		cleanup3()
		cleanup2()
		cleanup()
	}, nil
}
