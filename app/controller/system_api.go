package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type SystemApiController struct {
	SystemApiService *service.SystemApiService
	Logger           *zap.Logger
	Rdb              *redis.Client
}

// Query 根据指定字段、时间范围查询或搜索 SystemApi
//
//	@Description Query 根据指定字段、时间范围查询或搜索 SystemApi
//	@Summary Query 根据指定字段、时间范围查询或搜索 SystemApi
//	@Tags SystemApi
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param name query string false "name"
// @Param path query string false "path"
// @Param http_method query string false "http_method"
// @Param public query bool false "public"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.SystemApi}
//	@Router /api/v1/systemapi [get]
func (sac *SystemApiController) Query(c *fiber.Ctx) error {
	sa := &ent.SystemApi{}
	qp, err := common.QueryParser(c, sa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := sac.SystemApiService.Query(ctx, sa, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 SystemApi
//
//	@Description QueryByID 根据 ID 查询 SystemApi
//	@Summary QueryByID 根据 ID 查询 SystemApi
//	@Tags SystemApi
//	@Accept json
//	@Produce json
//	@Param id path int true "SystemApi ID"
//	@Success 200 {object} common.Result{data=ent.SystemApi}
//	@Router /api/v1/systemapi/{id} [get]
func (sac *SystemApiController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := sac.SystemApiService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 SystemApi
//
//	@Description Create 创建 SystemApi
//	@Summary Create 创建 SystemApi
//	@Tags SystemApi
//	@Accept json
//	@Produce json
//	@Param systemapi body ent.SystemApi true "SystemApi"
//	@Success 200 {object} common.Result{data=ent.SystemApi}
//	@Router /api/v1/systemapi [post]
func (sac *SystemApiController) Create(c *fiber.Ctx) error {
	sa := &ent.SystemApi{}
	err := common.BodyParser(c, sa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := sac.SystemApiService.Create(ctx, sa)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 SystemApi
//
//	@Description CreateBulk 批量创建 SystemApi
//	@Summary CreateBulk 批量创建 SystemApi
//	@Tags SystemApi
//	@Accept json
//	@Produce json
//	@Param systemapi body []ent.SystemApi true "SystemApi"
//	@Success 200 {object} common.Result{data=[]ent.SystemApi}
//	@Router /api/v1/systemapi/bulk [post]
func (sac *SystemApiController) CreateBulk(c *fiber.Ctx) error {
	sa := make([]*ent.SystemApi, 10)
	err := common.RequestBodyParser(c, &sa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := sac.SystemApiService.CreateBulk(ctx, sa)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 SystemApi
//
//	@Description UpdateByID 根据 ID 修改 SystemApi
//	@Summary UpdateByID 根据 ID 修改 SystemApi
//	@Tags SystemApi
//	@Accept json
//	@Produce json
//	@Param id path int true "SystemApi ID"
//
// @Param systemapi body ent.SystemApi true "SystemApi"
//
//	@Success 200 {object} common.Result{data=ent.SystemApi}
//	@Router /api/v1/systemapi/{id} [put]
func (sac *SystemApiController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	sa, err := sac.SystemApiService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, sa)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := sac.SystemApiService.UpdateByID(ctx, sa, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 SystemApi
//
//	@Description DeleteByID 根据 ID 删除 SystemApi
//	@Summary DeleteByID 根据 ID 删除 SystemApi
//	@Tags SystemApi
//	@Accept json
//	@Produce json
//	@Param id path int true "SystemApi ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/systemapi/{id} [delete]
func (sac *SystemApiController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := sac.SystemApiService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 SystemApi
//
//	@Description DeleteBulk 根据 IDs 批量删除 SystemApi
//	@Summary DeleteBulk 根据 IDs 批量删除 SystemApi
//	@Tags SystemApi
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/systemapi/bulk/delete [post]
func (sac *SystemApiController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = sac.SystemApiService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
