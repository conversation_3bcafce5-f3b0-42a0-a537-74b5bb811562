package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/entity"
	"meta/app/service"
	"meta/pkg/common"
)

type MatrixSpectrumAlertController struct {
	MatrixSpectrumAlertService *service.MatrixSpectrumAlertService
	Logger                     *zap.Logger
	Rdb                        *redis.Client
	NdsController              *NdsController
}

// Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert
//
//	@Description Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert
//	@Summary Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert
//	@Tags MatrixSpectrumAlert
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param ip query string false "ip" Format(ipv4)
// @Param device_name query string false "device_name"
// @Param interface query string false "interface"
// @Param protect_type query string false "protect_type"
// @Param start_time query string false "start_time" Format(date-time)
// @Param end_time query string false "end_time" Format(date-time)
// @Param attack_type query string false "attack_type"
// @Param bps query integer false "bps"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.MatrixSpectrumAlert}
//	@Router /api/v1/matrixspectrumalert [get]
func (msac *MatrixSpectrumAlertController) Query(c *fiber.Ctx) error {
	msa := &ent.MatrixSpectrumAlert{}
	qp, err := common.QueryParser(c, msa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := msac.MatrixSpectrumAlertService.Query(ctx, msa, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 MatrixSpectrumAlert
//
//	@Description QueryByID 根据 ID 查询 MatrixSpectrumAlert
//	@Summary QueryByID 根据 ID 查询 MatrixSpectrumAlert
//	@Tags MatrixSpectrumAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixSpectrumAlert ID"
//	@Success 200 {object} common.Result{data=ent.MatrixSpectrumAlert}
//	@Router /api/v1/matrixspectrumalert/{id} [get]
func (msac *MatrixSpectrumAlertController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := msac.MatrixSpectrumAlertService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 MatrixSpectrumAlert
//
//	@Description Create 创建 MatrixSpectrumAlert
//	@Summary Create 创建 MatrixSpectrumAlert
//	@Tags MatrixSpectrumAlert
//	@Accept json
//	@Produce json
//	@Param matrixspectrumalert body ent.MatrixSpectrumAlert true "MatrixSpectrumAlert"
//	@Success 200 {object} common.Result{data=ent.MatrixSpectrumAlert}
//	@Router /api/v1/matrixspectrumalert [post]
func (msac *MatrixSpectrumAlertController) Create(c *fiber.Ctx) error {
	msa := &ent.MatrixSpectrumAlert{}
	err := common.BodyParser(c, msa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := msac.MatrixSpectrumAlertService.Create(ctx, msa)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 MatrixSpectrumAlert
//
//	@Description CreateBulk 批量创建 MatrixSpectrumAlert
//	@Summary CreateBulk 批量创建 MatrixSpectrumAlert
//	@Tags MatrixSpectrumAlert
//	@Accept json
//	@Produce json
//	@Param matrixspectrumalert body []ent.MatrixSpectrumAlert true "MatrixSpectrumAlert"
//	@Success 200 {object} common.Result{data=[]ent.MatrixSpectrumAlert}
//	@Router /api/v1/matrixspectrumalert/bulk [post]
func (msac *MatrixSpectrumAlertController) CreateBulk(c *fiber.Ctx) error {
	msa := make([]*ent.MatrixSpectrumAlert, 10)
	err := common.RequestBodyParser(c, &msa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := msac.MatrixSpectrumAlertService.CreateBulk(ctx, msa)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 MatrixSpectrumAlert
//
//	@Description UpdateByID 根据 ID 修改 MatrixSpectrumAlert
//	@Summary UpdateByID 根据 ID 修改 MatrixSpectrumAlert
//	@Tags MatrixSpectrumAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixSpectrumAlert ID"
//
// @Param matrixspectrumalert body ent.MatrixSpectrumAlert true "MatrixSpectrumAlert"
//
//	@Success 200 {object} common.Result{data=ent.MatrixSpectrumAlert}
//	@Router /api/v1/matrixspectrumalert/{id} [put]
func (msac *MatrixSpectrumAlertController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	msa, err := msac.MatrixSpectrumAlertService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, msa)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := msac.MatrixSpectrumAlertService.UpdateByID(ctx, msa, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 MatrixSpectrumAlert
//
//	@Description DeleteByID 根据 ID 删除 MatrixSpectrumAlert
//	@Summary DeleteByID 根据 ID 删除 MatrixSpectrumAlert
//	@Tags MatrixSpectrumAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixSpectrumAlert ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/matrixspectrumalert/{id} [delete]
func (msac *MatrixSpectrumAlertController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := msac.MatrixSpectrumAlertService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 MatrixSpectrumAlert
//
//	@Description DeleteBulk 根据 IDs 批量删除 MatrixSpectrumAlert
//	@Summary DeleteBulk 根据 IDs 批量删除 MatrixSpectrumAlert
//	@Tags MatrixSpectrumAlert
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/matrixspectrumalert/bulk/delete [post]
func (msac *MatrixSpectrumAlertController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = msac.MatrixSpectrumAlertService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}

// GetAttackData 获取攻击数据 MatrixSpectrumAlert
//
//	@Description Query 获取攻击数据 MatrixSpectrumAlert
//	@Summary Query 获取攻击数据 MatrixSpectrumAlert
//	@Tags MatrixSpectrumAlert
//	@Accept json
//	@Produce json
//
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.MatrixSpectrumAlert}
//	@Router /api/v1/matrixspectrumalert/attacking [get]
func (msac *MatrixSpectrumAlertController) GetAttackData(c *fiber.Ctx) error {
	msa := &ent.MatrixSpectrumAlert{}
	attack := &entity.AttackQuery{}
	qp, err := common.QueryParser(c, attack)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	var count int
	var result []*ent.MatrixSpectrumAlert
	if !attack.Attacking {
		count, result, err = msac.MatrixSpectrumAlertService.GetEndTimeNil(ctx, msa, qp)
	} else {
		count, result, err = msac.MatrixSpectrumAlertService.Query(ctx, msa, qp)
	}
	return common.NewPageResult(c, err, count, result)
}
