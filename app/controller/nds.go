package controller

import (
	"context"
	"fmt"
	"meta/app/ent/protectgroup"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/app/service"
	"meta/pkg/auth"
	"meta/pkg/common"
	"meta/pkg/http"
	"time"

	"github.com/go-co-op/gocron"

	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

type NdsController struct {
	ProtectGroupService  *service.ProtectGroupService
	SpectrumAlertService *service.SpectrumAlertService
	SpectrumDataService  *service.SpectrumDataService
	CleanDataService     *service.CleanDataService
	TenantService        *service.TenantService
	NotifyService        *service.NotifyService
	StrategyService      *service.StrategyService
	Logger               *zap.Logger
	Rdb                  *redis.Client
	AuthEnt              *auth.Entx
	AuthV2               *http.AuthV2X
	WofangService        *service.WofangService
	ProjectCtl           *ProjectController
	SystemConfigService  *service.SystemConfigService
}

var (
	GScheduler = gocron.NewScheduler(time.Local)
	TaskMap    = map[string]*gocron.Job{}
)

// GetSpectrumAlert 获取NDS时间范围内的告警数据
// @Description 获取NDS时间范围内的告警数据
// @Summary 获取NDS时间范围内的告警数据
// @Tags NDS
// @Accept json
// @Produce json
// @Param startTime query string true "开始时间" Format(date-time)
// @Param endTime query string true "结束时间" Format(date-time)
// @Success 200 {object} common.Result{data=[]netease.AlertVO}
// @Router /api/v1/nds/alert [get]
func (nc *NdsController) GetSpectrumAlert(c *fiber.Ctx) error {
	eventDetail := &netease.GroupEventDetail{}
	_, err := common.QueryParser(c, eventDetail)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.CheckEventDetail2(eventDetail)
	if err != nil {
		return common.NewResult(c, err)
	}
	alertDatas, err := http.GetSpectrumAlert(eventDetail.StartTime, eventDetail.EndTime)
	if err != nil {
		return common.NewResult(c, err)
	}
	var alertVOs []netease.AlertVO
	for _, v := range alertDatas {
		alertVO := &netease.AlertVO{Ip: v.Ip, Product: v.Product, MaxBps: v.MaxBps, MaxPps: v.MaxPps, StartTime: common.Timestamp2Time(v.StartTime), EndTime: common.Timestamp2Time(v.EndTime), AttackTypes: v.AttackTypes}
		alertVOs = append(alertVOs, *alertVO)
	}
	return common.NewPageResult(c, err, len(alertDatas), alertVOs)
}

// GetSpectrumData 获取NDS告警时间范围内的分光数据
// @Description 获取NDS告警时间范围内的分光数据
// @Summary 获取NDS告警时间范围内的分光数据
// @Tags NDS
// @Accept json
// @Produce json
// @Param ip query string true "ip"
// @Param startTime query string true "开始时间" Format(date-time)
// @Param endTime query string true "结束时间" Format(date-time)
// @Success 200 {object} common.Result{data=[]netease.GroupSpectrumDataResponse}
// @Router /api/v1/nds/spectrumdata [get]
func (nc *NdsController) GetSpectrumData(c *fiber.Ctx) error {
	eventDetail := &netease.GroupEventDetail{}
	_, err := common.QueryParser(c, eventDetail)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.CheckEventDetail(eventDetail)
	if err != nil {
		return common.NewResult(c, err)
	}
	spectrumDatas, err := http.GetSpectrumData(eventDetail.IP, eventDetail.StartTime, eventDetail.EndTime)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewPageResult(c, err, len(spectrumDatas), spectrumDatas)
}

// GetCleanData 获取NDS告警时间范围内的清洗数据
// @Description 获取NDS告警时间范围内的清洗数据
// @Summary 获取NDS告警时间范围内的清洗数据
// @Tags NDS
// @Accept json
// @Produce json
// @Param ip query string true "ip"
// @Param startTime query string true "开始时间" Format(date-time)
// @Param endTime query string true "结束时间" Format(date-time)
// @Success 200 {object} common.Result{data=[]netease.GroupCleanDataResponse}
// @Router /api/v1/nds/cleandata [get]
func (nc *NdsController) GetCleanData(c *fiber.Ctx) error {
	eventDetail := &netease.GroupEventDetail{}
	_, err := common.QueryParser(c, eventDetail)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.CheckEventDetail(eventDetail)
	if err != nil {
		return common.NewResult(c, err)
	}
	cleanDatas, err := http.GetCleanData(eventDetail.IP, eventDetail.StartTime, eventDetail.EndTime)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewPageResult(c, err, len(cleanDatas), cleanDatas)
}

// AddIP2ProtectGroup 添加ip到NDS防护群组
// @Description 添加ip到NDS防护群组
// @Summary 添加ip到NDS防护群组
// @Tags NDS
// @Accept json
// @Produce json
// @Param GroupUpdate body netease.GroupUpdate true "GroupUpdate"
// @Success 200 {object} common.Message
// @Router /api/v1/nds/ip [post]
func (nc *NdsController) AddIP2ProtectGroup(c *fiber.Ctx) error {
	groupUpdate := &netease.GroupUpdate{}
	err := common.BodyParser(c, groupUpdate)
	if err != nil {
		return common.NewResult(c, err)
	}
	timestamp, sign := http.LoadNdsSign()
	reqData := &entity.ReqData{Data: groupUpdate, Sign: sign, Timestamp: timestamp}
	// 添加到防护群组
	if config.CFG.Stage.Status == "prod" {
		result, err := http.DoNdsRequest(config.CFG.External.Nds.Api, "/group/updateIpList.json", reqData)
		if err != nil {
			nc.Logger.Sugar().Error(err.Error())
		}
		fmt.Printf("nds update result: %+v\n", result)
	}

	fmt.Printf("\n%+v\n", groupUpdate)
	fmt.Println(reqData)

	ctx := c.Locals("ctx").(context.Context)
	// 从分光告警提交的防护IP，更新告警对应的防护群组ID
	if groupUpdate.AlertId != 0 {
		dbProtectGroup, err := nc.ProtectGroupService.Dao.ProtectGroup.Query().Where(protectgroup.GroupID(groupUpdate.GroupId)).Only(ctx)
		if err != nil {
			nc.Logger.Sugar().Error(err.Error())
		}
		_, err = nc.ProtectGroupService.Dao.SpectrumAlert.UpdateOneID(groupUpdate.AlertId).SetProtectGroupID(int(dbProtectGroup.GroupID)).Save(ctx)
		if err != nil {
			nc.Logger.Sugar().Error(err.Error())
		}
	}

	return common.NewResult(c, err)
}
