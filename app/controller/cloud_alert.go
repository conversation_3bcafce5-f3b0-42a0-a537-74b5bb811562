package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type CloudAlertController struct {
	CloudAlertService *service.CloudAlertService
	Logger            *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 CloudAlert
//
//	@Description Query 根据指定字段、时间范围查询或搜索 CloudAlert
//	@Summary Query 根据指定字段、时间范围查询或搜索 CloudAlert
//	@Tags CloudAlert
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param src_ip query string false "src_ip"
// @Param src_port query integer false "src_port"
// @Param dst_ip query string false "dst_ip"
// @Param dst_port query integer false "dst_port"
// @Param defence_mode query integer false "defence_mode"
// @Param flow_mode query integer false "flow_mode"
// @Param tcp_ack_num query string false "tcp_ack_num"
// @Param tcp_seq_num query string false "tcp_seq_num"
// @Param protocol query integer false "protocol"
// @Param defence_level query string false "defence_level"
// @Param start_time query string false "start_time" Format(date-time)
// @Param end_time query string false "end_time" Format(date-time)
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.CloudAlert}
//	@Router /api/v1/cloudalert [get]
func (cac *CloudAlertController) Query(c *fiber.Ctx) error {
	ca := &ent.CloudAlert{}
	qp, err := common.QueryParser(c, ca)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := cac.CloudAlertService.Query(ctx, ca, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 CloudAlert
//
//	@Description QueryByID 根据 ID 查询 CloudAlert
//	@Summary QueryByID 根据 ID 查询 CloudAlert
//	@Tags CloudAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudAlert ID"
//	@Success 200 {object} common.Result{data=ent.CloudAlert}
//	@Router /api/v1/cloudalert/{id} [get]
func (cac *CloudAlertController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := cac.CloudAlertService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 CloudAlert
//
//	@Description Create 创建 CloudAlert
//	@Summary Create 创建 CloudAlert
//	@Tags CloudAlert
//	@Accept json
//	@Produce json
//	@Param cloudalert body ent.CloudAlert true "CloudAlert"
//	@Success 200 {object} common.Result{data=ent.CloudAlert}
//	@Router /api/v1/cloudalert [post]
func (cac *CloudAlertController) Create(c *fiber.Ctx) error {
	ca := &ent.CloudAlert{}
	err := common.BodyParser(c, ca)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := cac.CloudAlertService.Create(ctx, ca)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 CloudAlert
//
//	@Description CreateBulk 批量创建 CloudAlert
//	@Summary CreateBulk 批量创建 CloudAlert
//	@Tags CloudAlert
//	@Accept json
//	@Produce json
//	@Param cloudalert body []ent.CloudAlert true "CloudAlert"
//	@Success 200 {object} common.Result{data=[]ent.CloudAlert}
//	@Router /api/v1/cloudalert/bulk [post]
func (cac *CloudAlertController) CreateBulk(c *fiber.Ctx) error {
	ca := make([]*ent.CloudAlert, 10)
	err := common.RequestBodyParser(c, &ca)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := cac.CloudAlertService.CreateBulk(ctx, ca)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 CloudAlert
//
//	@Description UpdateByID 根据 ID 修改 CloudAlert
//	@Summary UpdateByID 根据 ID 修改 CloudAlert
//	@Tags CloudAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudAlert ID"
//
// @Param cloudalert body ent.CloudAlert true "CloudAlert"
//
//	@Success 200 {object} common.Result{data=ent.CloudAlert}
//	@Router /api/v1/cloudalert/{id} [put]
func (cac *CloudAlertController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	ca, err := cac.CloudAlertService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, ca)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := cac.CloudAlertService.UpdateByID(ctx, ca, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 CloudAlert
//
//	@Description DeleteByID 根据 ID 删除 CloudAlert
//	@Summary DeleteByID 根据 ID 删除 CloudAlert
//	@Tags CloudAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudAlert ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/cloudalert/{id} [delete]
func (cac *CloudAlertController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := cac.CloudAlertService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 CloudAlert
//
//	@Description DeleteBulk 根据 IDs 批量删除 CloudAlert
//	@Summary DeleteBulk 根据 IDs 批量删除 CloudAlert
//	@Tags CloudAlert
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/cloudalert/bulk/delete [post]
func (cac *CloudAlertController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = cac.CloudAlertService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
