package controller

import (
	"context"
	"errors"
	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/entity/wofang"
	"meta/app/service"
	"meta/pkg/common"
	"meta/pkg/http"
	"net"
	"strings"
	"time"
)

type WofangController struct {
	WofangService *service.WofangService
	Logger        *zap.Logger
	Rdb           *redis.Client
}

// Query 根据指定字段、时间范围查询或搜索 Wofang
//
//	@Description Query 根据指定字段、时间范围查询或搜索 Wofang
//	@Summary Query 根据指定字段、时间范围查询或搜索 Wofang
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param name query string false "name"
// @Param ip query string false "ip" Format(ipv4)
// @Param type query string false "type"
// @Param un_drag_second query integer false "un_drag_second"
// @Param api_response query string false "api_response"
// @Param status query string false "status"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.Wofang}
//	@Router /api/v1/wofang [get]
func (wc *WofangController) Query(c *fiber.Ctx) error {
	w := &ent.Wofang{}
	qp, err := common.QueryParser(c, w)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := wc.WofangService.Query(ctx, w, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 Wofang
//
//	@Description QueryByID 根据 ID 查询 Wofang
//	@Summary QueryByID 根据 ID 查询 Wofang
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param id path int true "Wofang ID"
//	@Success 200 {object} common.Result{data=ent.Wofang}
//	@Router /api/v1/wofang/{id} [get]
func (wc *WofangController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := wc.WofangService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 Wofang
//
//	@Description Create 创建 Wofang
//	@Summary Create 创建 Wofang
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param wofang body ent.Wofang true "Wofang"
//	@Success 200 {object} common.Result{data=ent.Wofang}
//	@Router /api/v1/wofang [post]
func (wc *WofangController) Create(c *fiber.Ctx) error {
	w := &ent.Wofang{}
	err := common.BodyParser(c, w)
	if err != nil {
		return common.NewResult(c, err)
	}
	parseIP := net.ParseIP(strings.TrimSpace(w.IP))
	if parseIP == nil {
		return common.NewResult(c, errors.New("ip错误"))
	}
	dataMap := wofang.NewDrag2("Drain", w.IP, w.UnDragSecond, w.StartTime)
	drag, err := http.WoFangDrag2(dataMap)
	if drag == nil || err != nil {
		if drag != nil {
			w.ErrorInfo = drag.Message
		} else {
			w.ErrorInfo = err.Error()
		}
		w.Status = "failed"
		wc.Logger.Sugar().Error(err)
	}

	if drag != nil && drag.Code == 0 {
		w.Status = "success"
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := wc.WofangService.Create(ctx, w)
	wc.Rdb.Set(ctx, common.Key10010CleanPrefix+w.IP, "1", time.Second*time.Duration(w.UnDragSecond))

	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 Wofang
//
//	@Description CreateBulk 批量创建 Wofang
//	@Summary CreateBulk 批量创建 Wofang
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param wofang body []ent.Wofang true "Wofang"
//	@Success 200 {object} common.Result{data=[]ent.Wofang}
//	@Router /api/v1/wofang/bulk [post]
func (wc *WofangController) CreateBulk(c *fiber.Ctx) error {
	w := make([]*ent.Wofang, 10)
	err := common.RequestBodyParser(c, &w)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := wc.WofangService.CreateBulk(ctx, w)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 Wofang
//
//	@Description UpdateByID 根据 ID 修改 Wofang
//	@Summary UpdateByID 根据 ID 修改 Wofang
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param id path int true "Wofang ID"
//
// @Param wofang body ent.Wofang true "Wofang"
//
//	@Success 200 {object} common.Result{data=ent.Wofang}
//	@Router /api/v1/wofang/{id} [put]
func (wc *WofangController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	w, err := wc.WofangService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, w)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := wc.WofangService.UpdateByID(ctx, w, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 Wofang
//
//	@Description DeleteByID 根据 ID 删除 Wofang
//	@Summary DeleteByID 根据 ID 删除 Wofang
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param id path int true "Wofang ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/wofang/{id} [delete]
func (wc *WofangController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := wc.WofangService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 Wofang
//
//	@Description DeleteBulk 根据 IDs 批量删除 Wofang
//	@Summary DeleteBulk 根据 IDs 批量删除 Wofang
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/wofang/bulk/delete [post]
func (wc *WofangController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = wc.WofangService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
