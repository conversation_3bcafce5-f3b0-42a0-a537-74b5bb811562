package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/entity/config"
	"meta/app/entity/netease/socgroup"
	"meta/app/service"
	"meta/pkg/common"
	"meta/pkg/http"
	"strings"
)

type SocGroupTicketController struct {
	SocGroupTicketService *service.SocGroupTicketService
	Logger                *zap.Logger
	Rdb                   *redis.Client
}

// Query 根据指定字段、时间范围查询或搜索 SocGroupTicket
//
//	@Description Query 根据指定字段、时间范围查询或搜索 SocGroupTicket
//	@Summary Query 根据指定字段、时间范围查询或搜索 SocGroupTicket
//	@Tags SocGroupTicket
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param name query string false "name"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.SocGroupTicket}
//	@Router /api/v1/socgroupticket [get]
func (sgtc *SocGroupTicketController) Query(c *fiber.Ctx) error {
	sgt := &ent.SocGroupTicket{}
	qp, err := common.QueryParser(c, sgt)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := sgtc.SocGroupTicketService.Query(ctx, sgt, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 SocGroupTicket
//
//	@Description QueryByID 根据 ID 查询 SocGroupTicket
//	@Summary QueryByID 根据 ID 查询 SocGroupTicket
//	@Tags SocGroupTicket
//	@Accept json
//	@Produce json
//	@Param id path int true "SocGroupTicket ID"
//	@Success 200 {object} common.Result{data=ent.SocGroupTicket}
//	@Router /api/v1/socgroupticket/{id} [get]
func (sgtc *SocGroupTicketController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := sgtc.SocGroupTicketService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 SocGroupTicket
//
//	@Description Create 创建 SocGroupTicket
//	@Summary Create 创建 SocGroupTicket
//	@Tags SocGroupTicket
//	@Accept json
//	@Produce json
//	@Param socgroupticket body ent.SocGroupTicket true "SocGroupTicket"
//	@Success 200 {object} common.Result{data=ent.SocGroupTicket}
//	@Router /api/v1/socgroupticket [post]
func (sgtc *SocGroupTicketController) Create(c *fiber.Ctx) error {
	sgt := &ent.SocGroupTicket{}
	err := common.BodyParser(c, sgt)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	var followIdList []int
	if sgt.FollowList == nil || len(*sgt.FollowList) == 0 {
		followIdList = []int{5314}
	} else {
		followIdList = *sgt.FollowList
	}
	var configType int
	if sgt.ConfigType != 0 {
		configType = sgt.ConfigType - 1
	}
	requestData := &socgroup.AddRequestData{
		WorkOrderType:          "DOSDIVERT",
		Name:                   sgt.Name,
		Description:            sgt.Description,
		FollowUserIdList:       followIdList,
		DepartmentId:           sgt.DepartmentID,
		IpList:                 strings.Join(*sgt.IPList, "\n"),
		Bandwidth:              sgt.MinBandwidth,
		DivertType:             sgt.DivertType - 1,
		OpType:                 sgt.OpType - 1,
		OpTime:                 sgt.OpTime.UnixMilli(),
		ConfigType:             configType,
		ConfigArgs:             sgt.ConfigArgs,
		ProductName:            sgt.ProductName,
		ProductAlias:           sgt.ProductCode,
		EmergencyContacterList: *sgt.ContactList,
	}
	ticket, err := http.AddTicket(config.CFG.External.SocGroup.Api, requestData)
	if ticket == nil || err != nil {
		if ticket != nil {
			sgt.ErrorInfo = ticket.Msg
		} else {
			sgt.ErrorInfo = err.Error()
		}
		sgtc.Logger.Sugar().Error(err)
	} else {
		sgt.GroupTicketID = ticket.DataItems.Id
	}
	create, err := sgtc.SocGroupTicketService.Create(ctx, sgt)

	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 SocGroupTicket
//
//	@Description CreateBulk 批量创建 SocGroupTicket
//	@Summary CreateBulk 批量创建 SocGroupTicket
//	@Tags SocGroupTicket
//	@Accept json
//	@Produce json
//	@Param socgroupticket body []ent.SocGroupTicket true "SocGroupTicket"
//	@Success 200 {object} common.Result{data=[]ent.SocGroupTicket}
//	@Router /api/v1/socgroupticket/bulk [post]
func (sgtc *SocGroupTicketController) CreateBulk(c *fiber.Ctx) error {
	sgt := make([]*ent.SocGroupTicket, 10)
	err := common.RequestBodyParser(c, &sgt)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := sgtc.SocGroupTicketService.CreateBulk(ctx, sgt)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 SocGroupTicket
//
//	@Description UpdateByID 根据 ID 修改 SocGroupTicket
//	@Summary UpdateByID 根据 ID 修改 SocGroupTicket
//	@Tags SocGroupTicket
//	@Accept json
//	@Produce json
//	@Param id path int true "SocGroupTicket ID"
//
// @Param socgroupticket body ent.SocGroupTicket true "SocGroupTicket"
//
//	@Success 200 {object} common.Result{data=ent.SocGroupTicket}
//	@Router /api/v1/socgroupticket/{id} [put]
func (sgtc *SocGroupTicketController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	sgt, err := sgtc.SocGroupTicketService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, sgt)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := sgtc.SocGroupTicketService.UpdateByID(ctx, sgt, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 SocGroupTicket
//
//	@Description DeleteByID 根据 ID 删除 SocGroupTicket
//	@Summary DeleteByID 根据 ID 删除 SocGroupTicket
//	@Tags SocGroupTicket
//	@Accept json
//	@Produce json
//	@Param id path int true "SocGroupTicket ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/socgroupticket/{id} [delete]
func (sgtc *SocGroupTicketController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := sgtc.SocGroupTicketService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 SocGroupTicket
//
//	@Description DeleteBulk 根据 IDs 批量删除 SocGroupTicket
//	@Summary DeleteBulk 根据 IDs 批量删除 SocGroupTicket
//	@Tags SocGroupTicket
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/socgroupticket/bulk/delete [post]
func (sgtc *SocGroupTicketController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = sgtc.SocGroupTicketService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
