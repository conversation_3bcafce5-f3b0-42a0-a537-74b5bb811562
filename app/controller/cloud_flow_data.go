package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type CloudFlowDataController struct {
	CloudFlowDataService *service.CloudFlowDataService
	Logger               *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 CloudFlowData
//
//	@Description Query 根据指定字段、时间范围查询或搜索 CloudFlowData
//	@Summary Query 根据指定字段、时间范围查询或搜索 CloudFlowData
//	@Tags CloudFlowData
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param src_ip query string false "src_ip"
// @Param src_port query integer false "src_port"
// @Param dst_ip query string false "dst_ip"
// @Param dst_port query integer false "dst_port"
// @Param protocol query integer false "protocol"
// @Param count query integer false "count"
// @Param start_time query string false "start_time" Format(date-time)
// @Param end_time query string false "end_time" Format(date-time)
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.CloudFlowData}
//	@Router /api/v1/cloudflowdata [get]
func (cfdc *CloudFlowDataController) Query(c *fiber.Ctx) error {
	cfd := &ent.CloudFlowData{}
	qp, err := common.QueryParser(c, cfd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := cfdc.CloudFlowDataService.Query(ctx, cfd, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 CloudFlowData
//
//	@Description QueryByID 根据 ID 查询 CloudFlowData
//	@Summary QueryByID 根据 ID 查询 CloudFlowData
//	@Tags CloudFlowData
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudFlowData ID"
//	@Success 200 {object} common.Result{data=ent.CloudFlowData}
//	@Router /api/v1/cloudflowdata/{id} [get]
func (cfdc *CloudFlowDataController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := cfdc.CloudFlowDataService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 CloudFlowData
//
//	@Description Create 创建 CloudFlowData
//	@Summary Create 创建 CloudFlowData
//	@Tags CloudFlowData
//	@Accept json
//	@Produce json
//	@Param cloudflowdata body ent.CloudFlowData true "CloudFlowData"
//	@Success 200 {object} common.Result{data=ent.CloudFlowData}
//	@Router /api/v1/cloudflowdata [post]
func (cfdc *CloudFlowDataController) Create(c *fiber.Ctx) error {
	cfd := &ent.CloudFlowData{}
	err := common.BodyParser(c, cfd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := cfdc.CloudFlowDataService.Create(ctx, cfd)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 CloudFlowData
//
//	@Description CreateBulk 批量创建 CloudFlowData
//	@Summary CreateBulk 批量创建 CloudFlowData
//	@Tags CloudFlowData
//	@Accept json
//	@Produce json
//	@Param cloudflowdata body []ent.CloudFlowData true "CloudFlowData"
//	@Success 200 {object} common.Result{data=[]ent.CloudFlowData}
//	@Router /api/v1/cloudflowdata/bulk [post]
func (cfdc *CloudFlowDataController) CreateBulk(c *fiber.Ctx) error {
	cfd := make([]*ent.CloudFlowData, 10)
	err := common.RequestBodyParser(c, &cfd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := cfdc.CloudFlowDataService.CreateBulk(ctx, cfd)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 CloudFlowData
//
//	@Description UpdateByID 根据 ID 修改 CloudFlowData
//	@Summary UpdateByID 根据 ID 修改 CloudFlowData
//	@Tags CloudFlowData
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudFlowData ID"
//
// @Param cloudflowdata body ent.CloudFlowData true "CloudFlowData"
//
//	@Success 200 {object} common.Result{data=ent.CloudFlowData}
//	@Router /api/v1/cloudflowdata/{id} [put]
func (cfdc *CloudFlowDataController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	cfd, err := cfdc.CloudFlowDataService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, cfd)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := cfdc.CloudFlowDataService.UpdateByID(ctx, cfd, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 CloudFlowData
//
//	@Description DeleteByID 根据 ID 删除 CloudFlowData
//	@Summary DeleteByID 根据 ID 删除 CloudFlowData
//	@Tags CloudFlowData
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudFlowData ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/cloudflowdata/{id} [delete]
func (cfdc *CloudFlowDataController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := cfdc.CloudFlowDataService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 CloudFlowData
//
//	@Description DeleteBulk 根据 IDs 批量删除 CloudFlowData
//	@Summary DeleteBulk 根据 IDs 批量删除 CloudFlowData
//	@Tags CloudFlowData
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/cloudflowdata/bulk/delete [post]
func (cfdc *CloudFlowDataController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = cfdc.CloudFlowDataService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
