/**
* <AUTHOR>
* @date 2023-04-10 16:59
* @description
 */

package controller

import (
	"context"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/checker"
	"meta/pkg/common"
	"meta/pkg/http"
	"net"
	"time"
)

type ProjectController struct {
	TenantService *service.TenantService
	Logger        *zap.Logger
	Rdb           *redis.Client
	AuthV2        *http.AuthV2X
}

// GetRealProject 获取ip 的真实项目(一个)
func (pc *ProjectController) GetRealProject( ctx context.Context,ip string) *ent.Tenant {
	// Galaxy -> ( lbc | cld ) -> matrix
	//TODO：增加 cld v1 | galaxy cld eip接口
	//先查Galaxy
	realProject := pc.GetGalaxyProjectCache(ip, ctx)

	//其它接口只支持完整的ip查询
	parseIP := net.ParseIP(ip)
	if parseIP == nil && !common.ProjectInCMDB(realProject) {
		return nil
	}

	if realProject.Code == "lbc" {
		// 如果是ip属于lbc，则根据lbc接口查询实际的租户
		lbcProject := pc.GetLbcProjectCache(ip, ctx)
		if common.ProjectInCMDB(lbcProject) {
			realProject = lbcProject
		}
	}
	if realProject.Code == "cld" {
		// 如果是ip属于cld，则根据cld接口查询实际的租户
		cldProject := pc.GetCldProjectCache(ip, ctx)
		if common.ProjectInCMDB(cldProject) {
			realProject = cldProject
		}
	}

	//由matrix的接口兜底
	if !common.ProjectInCMDB(realProject) && realProject.Code != "cld" && realProject.Code != "lbc" {
		matrixProject := pc.GetMatrixProjectCache(ip, ctx)
		if common.ProjectInCMDB(matrixProject) {
			realProject = matrixProject
		}
	}
	//非未知项目
	if common.ProjectInCMDB(realProject) {
		return realProject
	}
	return nil
}

// GetGalaxyProjectCache 获取 galaxy ip项目
func (pc *ProjectController) GetGalaxyProjectCache(ip string, ctx context.Context) *ent.Tenant {
	galaxyRedisKey := common.GenRedisKey(common.ProjectPrefix, ip, common.GalaxyPrefix)
	cacheProject := getProjectCache(ip, ctx, pc, common.GalaxyPrefix, common.GalaxyPrefix)

	if cacheProject != nil {
		return cacheProject
	}
	_, headerMap, err := pc.AuthV2.GetV2Token()
	if err != nil {
		pc.Logger.Sugar().Error(err.Error())
	}
	cacheProject, _ = http.GetGalaxyProject(ip, headerMap)
	//if err != nil {
	//	pc.Logger.Sugar().Error(err.Error())
	//}
	// 没有项目的也缓存
	if cacheProject == nil {
		return setUnknownProjectCache(ip, ctx, pc, galaxyRedisKey)
	}
	setProjectCache(ctx, pc.Rdb, galaxyRedisKey, cacheProject)
	return checker.CheckAndAddProject(pc.TenantService.Dao, ctx, cacheProject.Code, cacheProject.Name)
}

// GetLbcProjectCache 获取 lbc ip项目
func (pc *ProjectController) GetLbcProjectCache(ip string, ctx context.Context) *ent.Tenant {
	lbcRedisKey := common.GenRedisKey(common.ProjectPrefix, ip, common.LbcPrefix)
	cacheProject := getProjectCache(ip, ctx, pc, lbcRedisKey, common.LbcPrefix)
	if cacheProject != nil {
		return cacheProject
	}

	_, headerMap, err := pc.AuthV2.GetV2Token()
	if err != nil {
		pc.Logger.Sugar().Error(err.Error())
		return nil
	}
	lbcProject, err := http.GetLbcProject(ip, headerMap)
	// 没有项目的也缓存
	if lbcProject == nil || lbcProject.Tenantname == "" {
		return setUnknownProjectCache(ip, ctx, pc, lbcRedisKey)
	}
	tenantCode := lbcProject.Tenantname
	cacheProject = pc.GetAuthProjectCache(tenantCode, ctx)
	if cacheProject == nil {
		return setUnknownProjectCache(ip, ctx, pc, lbcRedisKey)
	}
	setProjectCache(ctx, pc.Rdb, lbcRedisKey, cacheProject)

	return checker.CheckAndAddProject(pc.TenantService.Dao, ctx, cacheProject.Code, cacheProject.Name)
}

// GetCldProjectCache 获取 cld ip 项目
func (pc *ProjectController) GetCldProjectCache(ip string, ctx context.Context) *ent.Tenant {
	cldRedisKey := common.GenRedisKey(common.ProjectPrefix, ip, common.CldPrefix)
	cacheProject := getProjectCache(ip, ctx, pc, cldRedisKey, common.CldPrefix)
	if cacheProject != nil {
		return cacheProject
	}
	_, headerMap, err := pc.AuthV2.GetV2Token()
	if err != nil {
		pc.Logger.Sugar().Error(err.Error())
		return nil
	}
	cldProject, err := http.GetCldProject(ip, headerMap)
	// 没有项目的也缓存
	if cldProject == nil || cldProject.Tenant == "" || cldProject.Tenant == "unknown_tenant" {
		return setUnknownProjectCache(ip, ctx, pc, cldRedisKey)
	}

	tenantCode := cldProject.Tenant
	cacheProject = pc.GetAuthProjectCache(tenantCode, ctx)
	if cacheProject == nil {
		return setUnknownProjectCache(ip, ctx, pc, cldRedisKey)
	}
	setProjectCache(ctx, pc.Rdb, cldRedisKey, cacheProject)

	return checker.CheckAndAddProject(pc.TenantService.Dao, ctx, cacheProject.Code, cacheProject.Name)
}

// setProjectCache default TTL 25H
func setProjectCache(ctx context.Context, rdb *redis.Client, prefixKey string, project *ent.Tenant, ttls ...time.Duration) {
	rdb.HSet(ctx, prefixKey, map[string]string{"name": project.Name, "code": project.Code})

	var ttl time.Duration = 25
	if len(ttls) != 0 {
		ttl = 24 * ttls[0]
	}
	rdb.Expire(ctx, prefixKey, time.Hour*ttl)
}
func setUnknownProjectCache(ip string, ctx context.Context, pc *ProjectController, prefixKey string) *ent.Tenant {
	project := &ent.Tenant{Name: "unknown", Code: "unknown"}
	//pc.Logger.Sugar().Infof("缓存 %s 为【unknown】项目，TTL: 24H", ip)
	setProjectCache(ctx, pc.Rdb, prefixKey, project)
	return project
}

func getProjectCache(ip string, ctx context.Context, pc *ProjectController, prefixKey, prefix string) *ent.Tenant {
	var cacheProject ent.Tenant
	// redis hash => struct
	if err := pc.Rdb.HGetAll(ctx, prefixKey).Scan(&cacheProject); err != nil {
		pc.Logger.Sugar().Infof("redis get %s:%s project code：%s", prefix, ip, err.Error())
	}
	// 缓存的未知项目
	if cacheProject.Code == "unknown" && cacheProject.Name == "unknown" {
		//pc.Logger.Sugar().Infof("获取到 %s 未知项目缓存 %s", prefix, ip)
		return nil
	}
	if cacheProject.ID == 0 {
		return nil
	}
	pc.Logger.Sugar().Infof("get ip: %s project cache: %+v", ip, cacheProject)
	return &cacheProject
}

func (pc *ProjectController) GetAuthProjectCache(code string, ctx context.Context) *ent.Tenant {
	authRedisKey := common.GenRedisKey(common.ProjectPrefix, common.AuthPrefix, code)
	cacheProject := getProjectCache(code, ctx, pc, authRedisKey, common.AuthPrefix)
	//获取到缓存
	if cacheProject != nil {
		pc.Logger.Sugar().Infof("get auth projet cache: %+v", cacheProject)
		return cacheProject
	}
	//没有缓存
	_, headerMap, err := pc.AuthV2.GetV2Token()
	if err != nil {
		pc.Logger.Sugar().Error(err.Error())
		return nil
	}
	authProject, err := http.GetAuthProject(code, headerMap)
	if err != nil {
		pc.Logger.Sugar().Error(err.Error())
		return nil
	}
	// 没有项目的也缓存
	if authProject == nil {
		return setUnknownProjectCache(code, ctx, pc, authRedisKey)
	}
	cacheProject = &ent.Tenant{
		Name: authProject.Name,
		Code: code,
	}
	setProjectCache(ctx, pc.Rdb, authRedisKey, cacheProject, 7)

	return checker.CheckAndAddProject(pc.TenantService.Dao, ctx, cacheProject.Code, cacheProject.Name)
}

// GetMatrixProjectCache
func (pc *ProjectController) GetMatrixProjectCache(ip string, ctx context.Context) *ent.Tenant {
	matrixRedisKey := common.GenRedisKey(common.ProjectPrefix, common.MatrixPrefix, ip)
	authCacheProject := getProjectCache(ip, ctx, pc, matrixRedisKey, common.MatrixPrefix)
	//获取到缓存
	if authCacheProject != nil {
		return authCacheProject
	}
	_, headerMap, err := pc.AuthV2.GetV2Token()
	if err != nil {
		pc.Logger.Sugar().Error(err.Error())
		return nil
	}
	responseData, err := http.GetMatrixProject(ip, headerMap)
	// 没有项目的也缓存
	if err != nil || !responseData.Success || len(responseData.Data) == 0 {
		return setUnknownProjectCache(ip, ctx, pc, matrixRedisKey)
	}
	var tenantCode string
	data := responseData.Data
	for _, v := range data {
		if v.Project != "" {
			tenantCode = v.Project
			break
		}
	}
	if tenantCode == "" {
		return setUnknownProjectCache(ip, ctx, pc, matrixRedisKey)
	}
	// matrix 查询的ip项目为公用 -> nie
	if tenantCode == "公用" {
		tenantCode = "nie"
	}

	authCacheProject = pc.GetAuthProjectCache(tenantCode, ctx)
	if authCacheProject == nil {
		// matrix有自己的项目
		if tenantCode != "" {
			authCacheProject = &ent.Tenant{
				Name: tenantCode,
				Code: tenantCode,
			}
		} else {
			//matrix也没有自己的项目
			return setUnknownProjectCache(ip, ctx, pc, matrixRedisKey)
		}
	}

	setProjectCache(ctx, pc.Rdb, matrixRedisKey, authCacheProject)

	return checker.CheckAndAddProject(pc.TenantService.Dao, ctx, authCacheProject.Code, authCacheProject.Name)
}
