package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type GroupController struct {
	GroupService *service.GroupService
	Logger       *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 Group
//
//	@Description Query 根据指定字段、时间范围查询或搜索 Group
//	@Summary Query 根据指定字段、时间范围查询或搜索 Group
//	@Tags Group
//	@Accept json
//	@Produce json
//	@Param Name query string false "Name"
//
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.ProtectGroup}
//	@Router /api/v1/group [get]
func (gc *GroupController) Query(c *fiber.Ctx) error {
	g := &ent.Group{}
	qp, err := common.QueryParser(c, g)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := gc.GroupService.Query(ctx, g, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 Group
//
//	@Description QueryByID 根据 ID 查询 Group
//	@Summary QueryByID 根据 ID 查询 Group
//	@Tags Group
//	@Accept json
//	@Produce json
//	@Param id path int true "Group ID"
//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
//	@Router /api/v1/group/{id} [get]
func (gc *GroupController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := gc.GroupService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 Group
//
//	@Description Create 创建 Group
//	@Summary Create 创建 Group
//	@Tags Group
//	@Accept json
//	@Produce json
//	@Param group body ent.ProtectGroup true "Group"
//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
//	@Router /api/v1/group [post]
func (gc *GroupController) Create(c *fiber.Ctx) error {
	g := &ent.Group{}
	err := common.BodyParser(c, g)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := gc.GroupService.Create(ctx, g)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 Group
//
//	@Description CreateBulk 批量创建 Group
//	@Summary CreateBulk 批量创建 Group
//	@Tags Group
//	@Accept json
//	@Produce json
//	@Param group body []ent.ProtectGroup true "Group"
//	@Success 200 {object} common.Result{data=[]ent.ProtectGroup}
//	@Router /api/v1/group/bulk [post]
func (gc *GroupController) CreateBulk(c *fiber.Ctx) error {
	g := make([]*ent.Group, 10)
	err := common.RequestBodyParser(c, &g)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := gc.GroupService.CreateBulk(ctx, g)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 Group
//
//	@Description UpdateByID 根据 ID 修改 Group
//	@Summary UpdateByID 根据 ID 修改 Group
//	@Tags Group
//	@Accept json
//	@Produce json
//	@Param id path int true "Group ID"
//
// @Param group body ent.ProtectGroup true "Group"
//
//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
//	@Router /api/v1/group/{id} [put]
func (gc *GroupController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	g, err := gc.GroupService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, g)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := gc.GroupService.UpdateByID(ctx, g, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 Group
//
//	@Description DeleteByID 根据 ID 删除 Group
//	@Summary DeleteByID 根据 ID 删除 Group
//	@Tags Group
//	@Accept json
//	@Produce json
//	@Param id path int true "Group ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/group/{id} [delete]
func (gc *GroupController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := gc.GroupService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 Group
//
//	@Description DeleteBulk 根据 IDs 批量删除 Group
//	@Summary DeleteBulk 根据 IDs 批量删除 Group
//	@Tags Group
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/group/bulk/delete [post]
func (gc *GroupController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = gc.GroupService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
