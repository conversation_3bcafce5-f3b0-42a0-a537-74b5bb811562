package controller

import (
	"errors"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/entity/wofang"
	"meta/pkg/common"
	"meta/pkg/http"
	"net"
	"strings"
	"time"
)

type WoFangApi2 struct {
	Logger *zap.Logger
}

// Query 根据 ip 查询 沃防ip牵引状态
//
//	@Description Query 根据 ip 查询 沃防ip牵引状态
//	@Summary Query 根据 ip 查询 沃防ip牵引状态
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param ips path string true "查询ip"
//
// @Success 200 {object} common.Result{data=wofang.Response}
// @Router /api/v1/wofang/api2 [get]
func (wfc *WoFangApi2) Query(c *fiber.Ctx) error {
	wfq := &wofang.NewQuery{}
	_, err := common.QueryParser(c, wfq)
	if err != nil {
		return common.NewResult(c, err)
	}
	ip := wfq.Ip
	parseIP := net.ParseIP(strings.TrimSpace(ip))
	if parseIP == nil {
		return common.NewResult(c, errors.New("ip错误"))
	}

	dataMap := wofang.NewDrag2("GetDrainList", ip, 0, time.Time{})
	drag, err := http.WoFangDrag2(dataMap)
	if err != nil {
		wfc.Logger.Sugar().Info(err)
	}
	var resultStr string
	if drag == nil {
		err = errors.New("未知错误")
	} else {
		switch drag.Code {
		case 500:
			err = errors.New("未知错误")
			//err = errors.New(drag.Message)
		case 403:
			err = errors.New("权限不足，没有IP的操作权限")
		case 404:
			err = errors.New("IP未在引流中")
		case 0:
			return common.NewResult(c, nil, nil)
		}
	}
	return common.NewResult(c, err, resultStr)
}

// Add 将ip添加到沃防牵引
//
//	@Description Add 将ip添加到沃防牵引
//	@Summary Add 将ip添加到沃防牵引
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param add body wofang.Add true "需要操作的数据"
//
// @Success 200 {object} common.Result{data=wofang.Response}
// @Router /api/v1/wofang/api2 [post]
func (wfc *WoFangApi2) Add(c *fiber.Ctx) error {
	wfq := &wofang.Add{}
	err := common.BodyParser(c, wfq)
	if err != nil {
		return common.NewResult(c, err)
	}
	dataMap := wofang.NewDrag2("Drain", wfq.Ip, wfq.UnDragSecond, time.Time{})
	drag, err := http.WoFangDrag2(dataMap)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err, drag)
}

// Delete 将ip从沃防牵引 删除
//
//	@Description Delete 将ip从沃防牵引 删除
//	@Summary Delete 将ip从沃防牵引 删除
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param ip path string true "需要删除的ip"
//
// @Success 200 {object} common.Result{data=wofang.Response}
// @Router /api/v1/wofang/api2/{ip} [delete]
func (wfc *WoFangApi2) Delete(c *fiber.Ctx) error {
	ip := c.Params("ip")
	dataMap := wofang.NewDrag2("Undrain", ip, 0, time.Time{})
	drag, err := http.WoFangDrag2(dataMap)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err, drag)
}
