package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type SkylineDosController struct {
	SkylineDosService *service.SkylineDosService
	Logger            *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 SkylineDos
//
//	@Description Query 根据指定字段、时间范围查询或搜索 SkylineDos
//	@Summary Query 根据指定字段、时间范围查询或搜索 SkylineDos
//	@Tags SkylineDos
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param start_time query string false "start_time" Format(date-time)
// @Param end_time query string false "end_time" Format(date-time)
// @Param region query string false "region"
// @Param resource query string false "resource"
// @Param resource_type query string false "resource_type"
// @Param status query string false "status"
// @Param project query string false "project"
// @Param duration_time query integer false "duration_time"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.SkylineDos}
//	@Router /api/v1/skylinedos [get]
func (sdc *SkylineDosController) Query(c *fiber.Ctx) error {
	sd := &ent.SkylineDos{}
	qp, err := common.QueryParser(c, sd)
	if err != nil {
		return common.NewResult(c, err)
	}
	if qp.Order == "-id" {
		qp.Order = "-start_time"
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := sdc.SkylineDosService.Query(ctx, sd, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 SkylineDos
//
//	@Description QueryByID 根据 ID 查询 SkylineDos
//	@Summary QueryByID 根据 ID 查询 SkylineDos
//	@Tags SkylineDos
//	@Accept json
//	@Produce json
//	@Param id path int true "SkylineDos ID"
//	@Success 200 {object} common.Result{data=ent.SkylineDos}
//	@Router /api/v1/skylinedos/{id} [get]
func (sdc *SkylineDosController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := sdc.SkylineDosService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 SkylineDos
//
//	@Description Create 创建 SkylineDos
//	@Summary Create 创建 SkylineDos
//	@Tags SkylineDos
//	@Accept json
//	@Produce json
//	@Param skylinedos body ent.SkylineDos true "SkylineDos"
//	@Success 200 {object} common.Result{data=ent.SkylineDos}
//	@Router /api/v1/skylinedos [post]
func (sdc *SkylineDosController) Create(c *fiber.Ctx) error {
	sd := &ent.SkylineDos{}
	err := common.BodyParser(c, sd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := sdc.SkylineDosService.Create(ctx, sd)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 SkylineDos
//
//	@Description CreateBulk 批量创建 SkylineDos
//	@Summary CreateBulk 批量创建 SkylineDos
//	@Tags SkylineDos
//	@Accept json
//	@Produce json
//	@Param skylinedos body []ent.SkylineDos true "SkylineDos"
//	@Success 200 {object} common.Result{data=[]ent.SkylineDos}
//	@Router /api/v1/skylinedos/bulk [post]
func (sdc *SkylineDosController) CreateBulk(c *fiber.Ctx) error {
	sd := make([]*ent.SkylineDos, 10)
	err := common.RequestBodyParser(c, &sd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := sdc.SkylineDosService.CreateBulk(ctx, sd)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 SkylineDos
//
//	@Description UpdateByID 根据 ID 修改 SkylineDos
//	@Summary UpdateByID 根据 ID 修改 SkylineDos
//	@Tags SkylineDos
//	@Accept json
//	@Produce json
//	@Param id path int true "SkylineDos ID"
//
// @Param skylinedos body ent.SkylineDos true "SkylineDos"
//
//	@Success 200 {object} common.Result{data=ent.SkylineDos}
//	@Router /api/v1/skylinedos/{id} [put]
func (sdc *SkylineDosController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	sd, err := sdc.SkylineDosService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, sd)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := sdc.SkylineDosService.UpdateByID(ctx, sd, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 SkylineDos
//
//	@Description DeleteByID 根据 ID 删除 SkylineDos
//	@Summary DeleteByID 根据 ID 删除 SkylineDos
//	@Tags SkylineDos
//	@Accept json
//	@Produce json
//	@Param id path int true "SkylineDos ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/skylinedos/{id} [delete]
func (sdc *SkylineDosController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := sdc.SkylineDosService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 SkylineDos
//
//	@Description DeleteBulk 根据 IDs 批量删除 SkylineDos
//	@Summary DeleteBulk 根据 IDs 批量删除 SkylineDos
//	@Tags SkylineDos
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/skylinedos/bulk/delete [post]
func (sdc *SkylineDosController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = sdc.SkylineDosService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
