package controller

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent"
	"meta/app/ent/tenant"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/service"
	"meta/pkg/auth"
	"meta/pkg/common"
	"meta/pkg/jwt"

	"github.com/casbin/casbin/v2"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
)

type UserController struct {
	UserService *service.UserService
	Logger      *zap.Logger
	Enf         *casbin.Enforcer
	AuthEnt     *auth.Entx
}

// Query 根据指定字段、时间范围查询或搜索 User
//
//	@Description Query 根据指定字段、时间范围查询或搜索 User
//	@Summary Query 根据指定字段、时间范围查询或搜索 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Param Name query string false "Name"
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param Password query string false "Password" Format(password)
// @Param SuperAdmin query bool false "SuperAdmin"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.User}
//	@Router /api/v1/user [get]
func (uc *UserController) Query(c *fiber.Ctx) error {
	u := &ent.User{}
	qp, err := common.QueryParser(c, u)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := uc.UserService.Query(ctx, u, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 User
//
//	@Description QueryByID 根据 ID 查询 User
//	@Summary QueryByID 根据 ID 查询 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Param id path int true "User ID"
//	@Success 200 {object} common.Result{data=ent.User}
//	@Router /api/v1/user/{id} [get]
func (uc *UserController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := uc.UserService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 User
//
//	@Description Create 创建 User
//	@Summary Create 创建 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Param user body ent.User true "User"
//	@Success 200 {object} common.Result{data=ent.User}
//	@Router /api/v1/user [post]
func (uc *UserController) Create(c *fiber.Ctx) error {
	u := &ent.User{Valid: true, SuperAdmin: false}
	err := common.BodyParser(c, u)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := uc.UserService.Create(ctx, u)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 User
//
//	@Description CreateBulk 批量创建 User
//	@Summary CreateBulk 批量创建 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Param user body []ent.User true "User"
//	@Success 200 {object} common.Result{data=[]ent.User}
//	@Router /api/v1/user/bulk [post]
func (uc *UserController) CreateBulk(c *fiber.Ctx) error {
	u := make([]*ent.User, 10)
	err := common.RequestBodyParser(c, &u)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := uc.UserService.CreateBulk(ctx, u)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 User
//
//	@Description UpdateByID 根据 ID 修改 User
//	@Summary UpdateByID 根据 ID 修改 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Param id path int true "User ID"
//
// @Param user body ent.User true "User"
//
//	@Success 200 {object} common.Result{data=ent.User}
//	@Router /api/v1/user/{id} [put]
func (uc *UserController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	u, err := uc.UserService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, u)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := uc.UserService.UpdateByID(ctx, u, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 User
//
//	@Description DeleteByID 根据 ID 删除 User
//	@Summary DeleteByID 根据 ID 删除 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Param id path int true "User ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/user/{id} [delete]
func (uc *UserController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := uc.UserService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 User
//
//	@Description DeleteBulk 根据 IDs 批量删除 User
//	@Summary DeleteBulk 根据 IDs 批量删除 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/user/bulk/delete [post]
func (uc *UserController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = uc.UserService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}

// Login 根据用户名和密码登录 User
//
//	@Description Login 根据用户名和密码登录 User
//	@Summary Login 根据用户名和密码登录 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Param user body ent.User true "User"
//	@Success 200 {object} common.Result{data=string}
//	@Router /api/v1/user/login [post]
func (uc *UserController) Login(c *fiber.Ctx) error {
	u := &ent.User{}
	err := common.BodyParser(c, u)
	if err != nil && (u.Name == "" || u.Password == "") {
		return common.NewResult(c, err)
	}
	ctx := context.Background()
	u, token, err := uc.UserService.Login(ctx, u)
	if err != nil {
		return common.NewResult(c, err)
	}
	if !u.Valid {
		return common.NewErrorWithStatusCode(c, "User invalid", fiber.StatusForbidden)
	}
	loginInfo := &entity.LoginInfo{
		Token: token,
	}
	var projects []entity.Project
	userDomains, _ := uc.Enf.GetDomainsForUser(u.Name)
	saContext := uc.AuthEnt.GetSAContext()
	for _, v := range userDomains {
		queryProject, err := uc.UserService.Dao.Tenant.Query().Where(tenant.Code(v)).Only(saContext)
		if err != nil {
			uc.Logger.Sugar().Info(err)
		}
		project := entity.Project{
			Name: queryProject.Name,
			Code: v,
		}
		projects = append(projects, project)
	}

	if len(projects) > 0 {
		loginInfo.Projects = projects
	}
	return common.NewResult(c, err, loginInfo)
}

// Logout 根据退出登录 User
//
//	@Description Logout 根据退出登录 User
//	@Summary Logout 根据退出登录 User
//	@Tags User
//	@Accept json
//	@Produce json
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/user/logout [get]
func (uc *UserController) Logout(c *fiber.Ctx) error {
	authorization := c.Get("Authorization")
	_, err := jwt.ParseJWT(authorization, config.CFG.Auth.JWT.Key)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err)
}

// UserInfo 获取当前用户信息 User
//
//	@Description UserInfo 获取当前用户信息 User
//	@Summary UserInfo 获取当前用户信息 User
//	@Tags User
//	@Accept json
//	@Produce json
//	@Success 200 {object} common.Result{data=string}
//	@Router /api/v1/user/info [get]
func (uc *UserController) UserInfo(c *fiber.Ctx) error {
	if c.Locals("casbinUser") == nil {
		return common.NewResult(c, errors.New("nil casbin user"))
	}
	authorization := c.Get("Authorization")
	if authorization == "" {
		if c.Locals("Authorization") != nil {
			authorization = c.Locals("Authorization").(string)
		} else {
			return common.NewErrorWithStatusCode(c, "Access denied", fiber.StatusForbidden)
		}
	}
	casbinUser := c.Locals("casbinUser").(*entity.CasbinUser)
	ctx := c.Locals("ctx").(context.Context)
	userName := casbinUser.UserName
	project := casbinUser.Project
	queryUser, err := uc.UserService.QueryByUserName(ctx, userName)
	if err != nil {
		return common.NewResult(c, err)
	}
	if !queryUser.Valid {
		return common.NewErrorWithStatusCode(c, "User invalid", fiber.StatusForbidden)
	}
	//提示权限未处理完毕
	//if queryUser.UpdateAuth{
	//	return common.NewErrorWithStatusCode(c, "User invalid", fiber.StatusForbidden)
	//}

	userInfo := &entity.UserInfo{}
	if queryUser.Valid {
		admin := queryUser.SuperAdmin
		if admin {
			userInfo.Role = "admin"
			userAccess := &entity.Access{
				Query:      true,
				New:        true,
				Edit:       true,
				ViewDetail: true,
				View:       true,
				Delete:     true,
				BulkDelete: true,
			}
			userInfo.Access = *userAccess
		} else {
			userInfo.Role = "user"
			forUser, err := uc.Enf.GetRolesForUser(userName, project)
			if err != nil {
				return common.NewResult(c, err)
			}
			userAccess := &entity.Access{}
			for _, v := range forUser {
				//switch {
				////xx权限开头，赋予对应权限，适用于
				//不行
				//case strings.HasPrefix(v, "query"):
				//	userAccess.Query = true
				//case strings.HasPrefix(v, "new"):
				//	userAccess.New = true
				//case strings.HasPrefix(v, "edit"):
				//	userAccess.Edit = true
				//case strings.HasPrefix(v, "viewDetail"):
				//	userAccess.ViewDetail = true
				//case strings.HasPrefix(v, "view"):
				//	userAccess.View = true
				//case strings.HasPrefix(v, "delete"):
				//	userAccess.Delete = true
				//case strings.HasPrefix(v, "bulkDelete"):
				//	userAccess.BulkDelete = true
				//}
				switch v {
				case "query":
					userAccess.Query = true
				case "new":
					userAccess.New = true
				case "edit":
					userAccess.Edit = true
				case "viewDetail":
					userAccess.ViewDetail = true
				case "view":
					userAccess.View = true
				case "delete":
					userAccess.Delete = true
				case "bulkDelete":
					userAccess.BulkDelete = true
				}
			}
			if userAccess != nil {
				userInfo.Access = *userAccess
			}
		}
		userInfo.Name = userName
		loginInfo := &entity.LoginInfo{
			Token: authorization,
		}
		var projects []entity.Project
		userDomains, _ := uc.Enf.GetDomainsForUser(queryUser.Name)
		fmt.Printf("userDomains:%+v", userDomains)
		saContext := uc.AuthEnt.GetSAContext()
		for _, v := range userDomains {
			queryProject, err := uc.UserService.Dao.Tenant.Query().Where(tenant.Code(v)).Only(saContext)
			fmt.Printf("v:%s, queryProject:%+v", v, queryProject)

			if err != nil {
				uc.Logger.Sugar().Info(err)
			}
			if queryProject == nil {
				continue
			}
			project := entity.Project{
				Name: queryProject.Name,
				Code: v,
			}
			projects = append(projects, project)
		}
		if len(projects) > 0 {
			loginInfo.Projects = projects
		}
		userInfo.LoginInfo = *loginInfo

	}
	return common.NewResult(c, err, userInfo)
}
