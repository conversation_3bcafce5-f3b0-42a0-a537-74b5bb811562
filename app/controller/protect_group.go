package controller

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/app/service"
	"meta/pkg/common"
	"meta/pkg/http"
	"strings"

	mapset "github.com/deckarep/golang-set"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
)

type ProtectGroupController struct {
	ProtectGroupService *service.ProtectGroupService
	Logger              *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 ProtectGroup
//
//	@Description Query 根据指定字段、时间范围查询或搜索 ProtectGroup
//	@Summary Query 根据指定字段、时间范围查询或搜索 ProtectGroup
//	@Tags ProtectGroup
//	@Accept json
//	@Produce json
//	@Param Remark query string false "Remark"
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param GroupName query string false "GroupName"
// @Param NetType query integer false "NetType"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.ProtectGroup}
//	@Router /api/v1/protectgroup [get]
func (pgc *ProtectGroupController) Query(c *fiber.Ctx) error {
	pg := &ent.ProtectGroup{}
	qp, err := common.QueryParser(c, pg)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := pgc.ProtectGroupService.Query(ctx, pg, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 ProtectGroup
//
//	@Description QueryByID 根据 ID 查询 ProtectGroup
//	@Summary QueryByID 根据 ID 查询 ProtectGroup
//	@Tags ProtectGroup
//	@Accept json
//	@Produce json
//	@Param id path int true "Group ID"
//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
//	@Router /api/v1/protectgroup/{id} [get]
func (pgc *ProtectGroupController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := pgc.ProtectGroupService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 Group
//
//	@Description Create 创建 ProtectGroup
//	@Summary Create 创建 ProtectGroup
//	@Tags ProtectGroup
//	@Accept json
//	@Produce json
//	@Param protectgroup body ent.ProtectGroup true "Group"
//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
//	@Router /api/v1/protectgroup [post]
func (pgc *ProtectGroupController) Create(c *fiber.Ctx) error {
	pg := &ent.ProtectGroup{}
	err := common.BodyParser(c, pg)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := pgc.ProtectGroupService.Create(ctx, pg)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 ProtectGroup
//
//	@Description CreateBulk 批量创建 ProtectGroup
//	@Summary CreateBulk 批量创建 ProtectGroup
//	@Tags ProtectGroup
//	@Accept json
//	@Produce json
//	@Param protectgroup body []ent.ProtectGroup true "Group"
//	@Success 200 {object} common.Result{data=[]ent.ProtectGroup}
//	@Router /api/v1/protectgroup/bulk [post]
func (pgc *ProtectGroupController) CreateBulk(c *fiber.Ctx) error {
	pg := make([]*ent.ProtectGroup, 10)
	err := common.RequestBodyParser(c, &pg)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := pgc.ProtectGroupService.CreateBulk(ctx, pg)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 ProtectGroup
//
//	@Description UpdateByID 根据 ID 修改 ProtectGroup
//	@Summary UpdateByID 根据 ID 修改 ProtectGroup
//	@Tags ProtectGroup
//	@Accept json
//	@Produce json
//	@Param id path int true "Group ID"
//
// @Param protectgroup body ent.ProtectGroup true "Group"
//
//	@Success 200 {object} common.Result{data=ent.ProtectGroup}
//	@Router /api/v1/protectgroup/{id} [put]
func (pgc *ProtectGroupController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	pg, err := pgc.ProtectGroupService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, pg)
	if err != nil {
		return common.NewResult(c, err)
	}
	if pg.IPList == nil {
		return common.NewResult(c, errors.New("ip列表为空"))
	}

	var ips []string
	var expendIpList []string
	ipSegmentSet := mapset.NewSet()
	ipSet := mapset.NewSet()

	for _, ipOrCidr := range *pg.IPList {
		err := common.CheckIp(ipOrCidr)
		if err != nil {
			return common.NewResult(c, err)
		}
		if ipSegmentSet.Contains(ipOrCidr) {
			return common.NewResult(c, fmt.Errorf("数据重复:%s", ipOrCidr))
		}
		ipSegmentSet.Add(ipOrCidr)

		ips = append(ips, ipOrCidr)
		tempList, err := common.IpData2SingleIp(ipOrCidr)
		if err != nil {
			pgc.Logger.Error(err.Error())
			continue
		}

		for _, ip := range tempList {
			if ipSet.Contains(ip) {
				return common.NewResult(c, fmt.Errorf("IPv4展开数据重复:%s", ip))
			}
			ipSet.Add(ip)
		}
		expendIpList = append(expendIpList, tempList...)
	}
	if len(expendIpList) != 0 {
		pg.ExpandIP = strings.Join(expendIpList, "\n")
		pg.IPList = &ips
	}
	groupUpdate := &netease.GroupUpdate{
		GroupId: pg.GroupID,
		Type:    pg.Type,
		Ips:     ips,
	}
	timestamp, sign := http.LoadNdsSign()
	reqData := &entity.ReqData{Data: groupUpdate, Sign: sign, Timestamp: timestamp}
	// 正式环境才修改NDS防护群组ip列表
	if config.CFG.Stage.Status == "prod" {
		_, err = http.DoNdsRequest(config.CFG.External.Nds.Api, "/group/updateIpList.json", reqData)
		if err != nil {
			pgc.Logger.Sugar().Error(err.Error())
			return common.NewResult(c, err)
		}
		fmt.Printf("\n%+v\n", groupUpdate)
		fmt.Printf("%+v\n", reqData.Data)
	}

	data, err := pgc.ProtectGroupService.UpdateByID(ctx, pg, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 ProtectGroup
//
//	@Description DeleteByID 根据 ID 删除 ProtectGroup
//	@Summary DeleteByID 根据 ID 删除 ProtectGroup
//	@Tags ProtectGroup
//	@Accept json
//	@Produce json
//	@Param id path int true "Group ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/protectgroup/{id} [delete]
func (pgc *ProtectGroupController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := pgc.ProtectGroupService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 ProtectGroup
//
//	@Description DeleteBulk 根据 IDs 批量删除 ProtectGroup
//	@Summary DeleteBulk 根据 IDs 批量删除 ProtectGroup
//	@Tags ProtectGroup
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/protectgroup/bulk/delete [post]
func (pgc *ProtectGroupController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = pgc.ProtectGroupService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
