package controller

import (
	"context"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
)

type MatrixSpectrumDataController struct {
	MatrixSpectrumDataService *service.MatrixSpectrumDataService
	Logger                    *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumData
//
//	@Description Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumData
//	@Summary Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumData
//	@Tags MatrixSpectrumData
//	@Accept json
//	@Produce json
//	@Param device_ip query string false "device_ip"
//
// @Param device_name query string false "device_name"
// @Param interface query string false "interface"
// @Param bps query integer false "bps"
// @Param time query string false "time" Format(date-time)
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.MatrixSpectrumData}
//	@Router /api/v1/matrixspectrumdata [get]
func (msdc *MatrixSpectrumDataController) Query(c *fiber.Ctx) error {
	msd := &ent.MatrixSpectrumData{}
	qp, err := common.QueryParser(c, msd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := msdc.MatrixSpectrumDataService.Query(ctx, msd, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 MatrixSpectrumData
//
//	@Description QueryByID 根据 ID 查询 MatrixSpectrumData
//	@Summary QueryByID 根据 ID 查询 MatrixSpectrumData
//	@Tags MatrixSpectrumData
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixSpectrumData ID"
//	@Success 200 {object} common.Result{data=ent.MatrixSpectrumData}
//	@Router /api/v1/matrixspectrumdata/{id} [get]
func (msdc *MatrixSpectrumDataController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := msdc.MatrixSpectrumDataService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 MatrixSpectrumData
//
//	@Description Create 创建 MatrixSpectrumData
//	@Summary Create 创建 MatrixSpectrumData
//	@Tags MatrixSpectrumData
//	@Accept json
//	@Produce json
//	@Param matrixspectrumdata body ent.MatrixSpectrumData true "MatrixSpectrumData"
//	@Success 200 {object} common.Result{data=ent.MatrixSpectrumData}
//	@Router /api/v1/matrixspectrumdata [post]
func (msdc *MatrixSpectrumDataController) Create(c *fiber.Ctx) error {
	msd := &ent.MatrixSpectrumData{}
	err := common.BodyParser(c, msd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := msdc.MatrixSpectrumDataService.Create(ctx, msd)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 MatrixSpectrumData
//
//	@Description CreateBulk 批量创建 MatrixSpectrumData
//	@Summary CreateBulk 批量创建 MatrixSpectrumData
//	@Tags MatrixSpectrumData
//	@Accept json
//	@Produce json
//	@Param matrixspectrumdata body []ent.MatrixSpectrumData true "MatrixSpectrumData"
//	@Success 200 {object} common.Result{data=[]ent.MatrixSpectrumData}
//	@Router /api/v1/matrixspectrumdata/bulk [post]
func (msdc *MatrixSpectrumDataController) CreateBulk(c *fiber.Ctx) error {
	msd := make([]*ent.MatrixSpectrumData, 10)
	err := common.RequestBodyParser(c, &msd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := msdc.MatrixSpectrumDataService.CreateBulk(ctx, msd)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 MatrixSpectrumData
//
//	@Description UpdateByID 根据 ID 修改 MatrixSpectrumData
//	@Summary UpdateByID 根据 ID 修改 MatrixSpectrumData
//	@Tags MatrixSpectrumData
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixSpectrumData ID"
//
// @Param matrixspectrumdata body ent.MatrixSpectrumData true "MatrixSpectrumData"
//
//	@Success 200 {object} common.Result{data=ent.MatrixSpectrumData}
//	@Router /api/v1/matrixspectrumdata/{id} [put]
func (msdc *MatrixSpectrumDataController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	msd, err := msdc.MatrixSpectrumDataService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, msd)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := msdc.MatrixSpectrumDataService.UpdateByID(ctx, msd, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 MatrixSpectrumData
//
//	@Description DeleteByID 根据 ID 删除 MatrixSpectrumData
//	@Summary DeleteByID 根据 ID 删除 MatrixSpectrumData
//	@Tags MatrixSpectrumData
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixSpectrumData ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/matrixspectrumdata/{id} [delete]
func (msdc *MatrixSpectrumDataController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := msdc.MatrixSpectrumDataService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 MatrixSpectrumData
//
//	@Description DeleteBulk 根据 IDs 批量删除 MatrixSpectrumData
//	@Summary DeleteBulk 根据 IDs 批量删除 MatrixSpectrumData
//	@Tags MatrixSpectrumData
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/matrixspectrumdata/bulk/delete [post]
func (msdc *MatrixSpectrumDataController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = msdc.MatrixSpectrumDataService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
