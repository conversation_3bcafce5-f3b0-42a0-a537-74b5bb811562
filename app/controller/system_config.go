package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
	"strings"
)

type SystemConfigController struct {
	SystemConfigService *service.SystemConfigService
	Logger              *zap.Logger
	Rdb                 *redis.Client
}

// Query 根据指定字段、时间范围查询或搜索 SystemConfig
//
//	@Description Query 根据指定字段、时间范围查询或搜索 SystemConfig
//	@Summary Query 根据指定字段、时间范围查询或搜索 SystemConfig
//	@Tags SystemConfig
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param wofang_test_ip query string false "wofang_test_ip"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.SystemConfig}
//	@Router /api/v1/systemconfig [get]
func (scc *SystemConfigController) Query(c *fiber.Ctx) error {
	sc := &ent.SystemConfig{}
	qp, err := common.QueryParser(c, sc)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := scc.SystemConfigService.Query(ctx, sc, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 SystemConfig
//
//	@Description QueryByID 根据 ID 查询 SystemConfig
//	@Summary QueryByID 根据 ID 查询 SystemConfig
//	@Tags SystemConfig
//	@Accept json
//	@Produce json
//	@Param id path int true "SystemConfig ID"
//	@Success 200 {object} common.Result{data=ent.SystemConfig}
//	@Router /api/v1/systemconfig/{id} [get]
func (scc *SystemConfigController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := scc.SystemConfigService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 SystemConfig
//
//	@Description Create 创建 SystemConfig
//	@Summary Create 创建 SystemConfig
//	@Tags SystemConfig
//	@Accept json
//	@Produce json
//	@Param systemconfig body ent.SystemConfig true "SystemConfig"
//	@Success 200 {object} common.Result{data=ent.SystemConfig}
//	@Router /api/v1/systemconfig [post]
func (scc *SystemConfigController) Create(c *fiber.Ctx) error {
	sc := &ent.SystemConfig{}
	err := common.BodyParser(c, sc)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := scc.SystemConfigService.Create(ctx, sc)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 SystemConfig
//
//	@Description CreateBulk 批量创建 SystemConfig
//	@Summary CreateBulk 批量创建 SystemConfig
//	@Tags SystemConfig
//	@Accept json
//	@Produce json
//	@Param systemconfig body []ent.SystemConfig true "SystemConfig"
//	@Success 200 {object} common.Result{data=[]ent.SystemConfig}
//	@Router /api/v1/systemconfig/bulk [post]
func (scc *SystemConfigController) CreateBulk(c *fiber.Ctx) error {
	sc := make([]*ent.SystemConfig, 10)
	err := common.RequestBodyParser(c, &sc)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := scc.SystemConfigService.CreateBulk(ctx, sc)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 SystemConfig
//
//	@Description UpdateByID 根据 ID 修改 SystemConfig
//	@Summary UpdateByID 根据 ID 修改 SystemConfig
//	@Tags SystemConfig
//	@Accept json
//	@Produce json
//	@Param id path int true "SystemConfig ID"
//
// @Param systemconfig body ent.SystemConfig true "SystemConfig"
//
//	@Success 200 {object} common.Result{data=ent.SystemConfig}
//	@Router /api/v1/systemconfig/{id} [put]
func (scc *SystemConfigController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	sc, err := scc.SystemConfigService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, sc)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := scc.SystemConfigService.UpdateByID(ctx, sc, id)
	_, err = scc.Rdb.HSet(ctx, common.NotifyPrefix, map[string]string{
		"wofangTestIP": data.WofangTestIP,
		"notifyScenes": strings.Join(*data.NotifyScenes, ","),
		"notifyPhones": strings.Join(*data.NotifyPhones, ","),
		"notifyEmails": strings.Join(*data.NotifyEmails, ","),
		"ipWhitelists": strings.Join(*data.IPWhitelists, ","),
	}).Result()
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 SystemConfig
//
//	@Description DeleteByID 根据 ID 删除 SystemConfig
//	@Summary DeleteByID 根据 ID 删除 SystemConfig
//	@Tags SystemConfig
//	@Accept json
//	@Produce json
//	@Param id path int true "SystemConfig ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/systemconfig/{id} [delete]
func (scc *SystemConfigController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := scc.SystemConfigService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 SystemConfig
//
//	@Description DeleteBulk 根据 IDs 批量删除 SystemConfig
//	@Summary DeleteBulk 根据 IDs 批量删除 SystemConfig
//	@Tags SystemConfig
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/systemconfig/bulk/delete [post]
func (scc *SystemConfigController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = scc.SystemConfigService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
