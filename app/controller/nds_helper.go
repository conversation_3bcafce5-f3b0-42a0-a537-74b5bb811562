package controller

import (
	"bytes"
	"context"
	"fmt"
	"math"
	"meta/app/ent"
	"meta/app/ent/protectgroup"
	"meta/app/ent/strategy"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"meta/pkg/http"
	"text/template"
	"time"
)

// GetNdsCleanData 获取nds清洗数据
func (nc *NdsController) GetNdsCleanData(ip string, startTime, endTime time.Time, spectrumAlertID int, tenantID int, ctx context.Context) {
	respDatas, err := http.GetCleanData(ip, startTime, endTime)
	if err != nil {
		nc.Logger.Sugar().Errorf("%s: %s", ip, err.Error())
	}
	if len(respDatas) != 0 {
		nc.Logger.Sugar().Infof("获取到 %s 清洗数据：%d", ip, len(respDatas))
	}
	var cleanData []*ent.CleanData
	key := common.KeyAttackPrefix + ip
	for _, sv := range respDatas {
		timestamp2Time := common.Timestamp2Time(sv.Time)
		// dbData, _ := nc.SpectrumDataService.Dao.CleanData.Query().Where(cleandata.IP(ip), cleandata.TenantID(tenantID), cleandata.Time(timestamp2Time)).Only(ctx)
		// if dbData == nil {
		data := &ent.CleanData{
			IP: sv.Ip, Time: timestamp2Time,
			InBps: sv.InBps, OutBps: sv.OutBps, InPps: sv.InPps, OutPps: sv.OutPps,
			InAckPps: sv.InAckPps, OutAckPps: sv.OutAckPps, InAckBps: sv.InAckBps, OutAckBps: sv.OutAckBps,
			InSynPps: sv.InSynPps, OutSynPps: sv.OutSynPps,
			InUDPPps: sv.InUdpPps, OutUDPPps: sv.OutUdpPps, InUDPBps: sv.InUdpBps, OutUDPBps: sv.OutUdpBps,
			InIcmpBps: sv.InIcmpBps, InIcmpPps: sv.InIcmpPps, OutIcmpPps: sv.OutIcmpPps, OutIcmpBps: sv.OutIcmpBps,
			InDNSPps: sv.InDnsPps, InDNSBps: sv.InDnsBps, OutDNSPps: sv.OutDnsPps, OutDNSBps: sv.OutDnsBps,
			CFilterID: sv.FilterId, AttackFlags: sv.AttackFlags, Count: sv.Count, IPType: sv.IpType,
			CFilter: sv.Filter, Host: sv.Host,
		}
		if tenantID != 0 {
			data.TenantID = &tenantID
		}
		cleanData = append(cleanData, data)

		var attackInfo netease.AttackInfo
		// redis hash => struct
		if err := nc.Rdb.HGetAll(ctx, key).Scan(&attackInfo); err != nil {
			nc.Logger.Sugar().Error(err.Error())
		}
		newBps := sv.InBps - sv.OutBps
		if attackInfo.MaxCleanBps < newBps {
			nc.Rdb.HSet(ctx, key, "maxCleanBps", newBps)
		}
		newPps := sv.InPps - sv.OutPps
		if attackInfo.MaxCleanPps < newPps {
			nc.Rdb.HSet(ctx, key, "maxCleanPps", newPps)
		}
		//}
	}
	if cleanData != nil {
		slice := SplitCleanDataSlice(cleanData, 500)
		for _, v := range slice {
			_, err = nc.CleanDataService.CreateBulk2(ctx, v.([]*ent.CleanData), spectrumAlertID)
			if err != nil {
				nc.Logger.Sugar().Error(err.Error())
			}
		}

	}
}

func SplitCleanDataSlice(targetSlice []*ent.CleanData, splitSize int) []any {
	dataSliceLen := len(targetSlice)
	var resultSlice []any
	if splitSize >= dataSliceLen {
		resultSlice = append(resultSlice, targetSlice)
		return resultSlice
	}
	step := int(math.Ceil(float64(dataSliceLen / splitSize)))
	start := 0
	end := splitSize

	for i := 0; i <= step; i++ {
		resultSlice = append(resultSlice, targetSlice[start:end])
		start = end
		end = end + splitSize
		if end >= dataSliceLen {
			end = dataSliceLen
		}
	}
	return resultSlice
}

// GetNdsSpectrumData 获取nds分光数据
// 同时计算bps和pps超过阈值次数，并保存到redis
func (nc *NdsController) GetNdsSpectrumData(ip string, startTime, endTime time.Time, spectrumAlertID int, tenantID int, stra *ent.Strategy, ctx context.Context) {
	respDatas, err := http.GetSpectrumData(ip, startTime, endTime)
	if err != nil {
		nc.Logger.Sugar().Errorf("%s: %s", ip, err.Error())
	}
	if len(respDatas) != 0 {
		nc.Logger.Sugar().Infof("获取到 %s 分光数据：%d", ip, len(respDatas))
	}
	var spectrumDatas []*ent.SpectrumData

	for _, sv := range respDatas {
		timestamp2Time := common.Timestamp2Time(sv.Time)
		// 兜底获取分光，不需要再查询是否已有数据
		// dbData, _ := nc.SpectrumDataService.Dao.SpectrumData.Query().Where(spectrumdata.IP(ip), spectrumdata.TenantID(tenantID), spectrumdata.Time(timestamp2Time)).Only(ctx)
		// if dbData == nil {
		data := &ent.SpectrumData{
			IP: sv.Ip, Time: timestamp2Time, DataType: sv.DataType, MonitorID: sv.MonitorId,
			Bps: sv.Bps, Pps: sv.Pps,
			SynBps: sv.SynBps, SynPps: sv.SynPps, AckBps: sv.AckBps, AckPps: sv.AckPps,
			SynAckBps: sv.SynAckBps, SynAckPps: sv.SynAckPps,
			IcmpBps: sv.IcmpBps, IcmpPps: sv.IcmpPps,
			SmallPps: sv.SmallPps, NtpBps: sv.NtpBps, NtpPps: sv.NtpPps,
			DNSQueryPps: sv.DnsQueryPps, DNSQueryBps: sv.DnsQueryBps,
			DNSAnswerBps: sv.DnsAnswerBps, DNSAnswerPps: sv.DnsAnswerPps,
			SsdpBps: sv.SsdpBps, SsdpPps: sv.SsdpPps, UDPBps: sv.UdpBps, UDPPps: sv.UdpPps,
			QPS: sv.Qps, ReceiveCount: sv.ReceiveCount, IPType: sv.IpType, Monitor: sv.Monitor,
			Product: sv.Product, Host: sv.Host,
		}
		if tenantID != 0 {
			data.TenantID = &tenantID
		}
		spectrumDatas = append(spectrumDatas, data)
		attackInfo := &netease.AttackInfo{
			MaxBps: sv.Bps,
			MaxPps: sv.Pps,
		}
		CalculateCount(nc, ctx, ip, attackInfo, stra)
		//}
	}
	if spectrumDatas != nil {
		slice := SplitSpectrumDataSlice(spectrumDatas, 500)
		for _, v := range slice {
			_, err = nc.SpectrumDataService.CreateBulk2(ctx, v.([]*ent.SpectrumData), spectrumAlertID)
			if err != nil {
				nc.Logger.Sugar().Error(err.Error())
			}
		}
	}
}

func SplitSpectrumDataSlice(targetSlice []*ent.SpectrumData, splitSize int) []any {
	dataSliceLen := len(targetSlice)
	var resultSlice []any
	if splitSize >= dataSliceLen {
		resultSlice = append(resultSlice, targetSlice)
		return resultSlice
	}
	step := int(math.Ceil(float64(dataSliceLen / splitSize)))
	start := 0
	end := splitSize

	for i := 0; i <= step; i++ {
		resultSlice = append(resultSlice, targetSlice[start:end])
		start = end
		end = end + splitSize
		if end >= dataSliceLen {
			end = dataSliceLen
		}
	}
	return resultSlice
}

func CalculateCount(nc *NdsController, ctx context.Context, ip string, targetAttackInfo *netease.AttackInfo, stra *ent.Strategy) {
	keyAttack := common.KeyAttackPrefix + ip
	keyBpsCounter := common.KeyCounterBpsPrefix + ip
	keyPpsCounter := common.KeyCounterPpsPrefix + ip
	var attackInfo netease.AttackInfo
	// redis hash => struct
	if err := nc.Rdb.HGetAll(ctx, keyAttack).Scan(&attackInfo); err != nil {
		nc.Logger.Sugar().Errorf("redis err：%s", err.Error())
	}
	// 更新最大值
	if attackInfo.MaxBps < targetAttackInfo.MaxBps {
		nc.Rdb.HSet(ctx, keyAttack, "maxBps", targetAttackInfo.MaxBps)
	}
	if attackInfo.MaxPps < targetAttackInfo.MaxPps {
		nc.Rdb.HSet(ctx, keyAttack, "maxPps", targetAttackInfo.MaxPps)
	}
	// 计算是否超过策略阈值，同时增加次数
	if stra != nil {
		if targetAttackInfo.MaxBps > stra.Bps {
			nc.Rdb.Incr(ctx, keyBpsCounter)
		}
		if targetAttackInfo.MaxPps > stra.Pps {
			nc.Rdb.Incr(ctx, keyPpsCounter)
		}
	}
}

// GetNdsProtectGroup 获取ip所在的防护群组
func (nc *NdsController) GetNdsProtectGroup(ip string, ctx context.Context) *ent.ProtectGroup {
	all, err := http.GetGroupDataAll(ip)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	if len(all) >= 1 {
		g := all[0]
		existGroup, err := nc.SpectrumAlertService.Dao.ProtectGroup.Query().Where(protectgroup.GroupName(g.GroupName), protectgroup.GroupID(g.GroupId), protectgroup.Type(g.GroupType)).Only(ctx)
		if err != nil {
			nc.Logger.Sugar().Error(err.Error())
		}
		if existGroup == nil {
			pGroup := &ent.ProtectGroup{GroupID: g.GroupId, GroupName: g.GroupName, Type: g.GroupType}
			save, err := nc.SpectrumAlertService.Dao.ProtectGroup.Create().SetItemProtectGroup(pGroup).Save(ctx)
			if err != nil {
				nc.Logger.Sugar().Error(err.Error())
			}
			return save
		} else {
			return existGroup
		}
	}
	return nil
}

// GetProtectStrategy 获取防护策略
func (nc *NdsController) GetProtectStrategy(projectId, ispID, straType int, ctx context.Context) *ent.Strategy {
	var stra *ent.Strategy

	if projectId != 0 {
		all, err := nc.StrategyService.Dao.Strategy.Query().Where(strategy.TenantID(projectId), strategy.IspCode(ispID), strategy.Type(straType)).Order(ent.Desc(strategy.FieldUpdatedAt)).All(ctx)
		if err != nil {
			nc.Logger.Sugar().Errorf("get project strategy failed: %s, projectId:%d, ispID:%d, straType:%d", err, projectId, ispID, straType)
		}
		if len(all) != 0 {
			stra = all[0]
		}
	}
	if stra == nil || !stra.Enabled {
		all, err := nc.StrategyService.Dao.Strategy.Query().Where(strategy.IspCode(ispID), strategy.Type(straType), strategy.SystemEQ(true)).Order(ent.Desc(strategy.FieldUpdatedAt)).All(ctx)
		if err != nil {
			nc.Logger.Sugar().Errorf("get system strategy failed: %s,ispID:%d,straType:%d", err, ispID, straType)
		}
		if len(all) != 0 {
			for _, v := range all {
				if v.Enabled {
					stra = v
					break
				}
			}
			if stra == nil {
				stra = all[0]
			}
		}
	}
	nc.Logger.Sugar().Infof("获取到防护策略: %+v", stra)
	// 没有默认策略，也没有项目策略，新建一个默认策略
	if stra == nil {
		stra = &ent.Strategy{}
		// BGP默认防护
		if ispID == 1 {
			// 60G
			stra.Bps = int64(50 * math.Pow10(10))
			stra.Pps = int64(50 * math.Pow10(10))
		}
		// 联通
		if ispID == 10010 {
			// 60G
			stra.Bps = int64(6 * math.Pow10(10))
			stra.Pps = int64(6 * math.Pow10(10))
		}
		// 移动
		if ispID == 10086 {
			// 130G
			stra.Bps = int64(13 * math.Pow10(10))
			stra.Pps = int64(13 * math.Pow10(10))
		}
		// 电信
		if ispID == 10000 {
			// 100G
			stra.Bps = int64(10 * math.Pow10(10))
			stra.Pps = int64(10 * math.Pow10(10))
		}

		stra.BpsCount = 1
		stra.PpsCount = 1
		if ispID == 1 {
			stra.Enabled = true
		}
		stra.System = true
		stra.IspCode = ispID
		mark := "满足该策略将自动提交清洗工单"
		stra.Remark = &mark
		stra.Type = straType
		stra.Name = "系统默认防护策略"
		_, err := nc.StrategyService.Create(ctx, stra)
		if err != nil {
			nc.Logger.Sugar().Error(err.Error())
		}
		nc.Logger.Sugar().Infof("不存在 %d 默认防护策略，新建默认防护策略", ispID)
	} else {
		if !stra.Enabled {
			nc.Logger.Sugar().Info("策略未启用")
			return nil
		}
	}
	return stra
}

func (nc *NdsController) notifyNdsAlert(ctx context.Context, ndsAttack *netease.NdsAttackData) {
	tPopo := template.New("popoNotify")
	tPopo = template.Must(tPopo.Parse(common.NdsPopoTemplate))
	var buf bytes.Buffer
	err := tPopo.Execute(&buf, ndsAttack)
	if err != nil {
		nc.Logger.Sugar().Error(err)
	}
	content := buf.String()
	nc.Logger.Sugar().Infof("notifyNdsAlert content: %s", content)
	ip := ndsAttack.IP
	notifySystem, err := CheckSystemNotify(ctx, nc.NotifyService)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	// 项目id不为空，通知项目
	projectId := ndsAttack.ProjectID
	notifyProject, err := CheckNotifyProject(ctx, projectId, nc.NotifyService)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	_, headerMap, err := nc.AuthV2.GetV2Token()
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	err = http.NotifyAll(notifySystem, notifyProject, ndsAttack.ProjectCode, content, ndsAttack.Title, common.MailSignData, headerMap, ip)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
}

func (nc *NdsController) notifyWofangAlert(ctx context.Context, data *netease.WofangDrainData) {
	content, err := common.ParseTemplate("wofangNotify", common.WofangPopoTemplate, data)
	nc.Logger.Sugar().Infof("notifyNdsAlert content: %s", content)
	fmt.Println("content ", content)
	ip := data.IP
	notifySystem, err := CheckSystemNotify(ctx, nc.NotifyService)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	// 项目id不为空，通知项目
	projectId := data.ProjectID
	notifyProject, err := CheckNotifyProject(ctx, projectId, nc.NotifyService)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	_, headerMap, err := nc.AuthV2.GetV2Token()
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	err = http.NotifyAll(notifySystem, notifyProject, data.ProjectCode, content, data.Title, common.MailSignData, headerMap, ip)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
}
