package controller

import (
	"context"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
)

type CasbinRuleController struct {
	CasbinRuleService *service.CasbinRuleService
	Logger            *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 CasbinRule
//
//	@Description Query 根据指定字段、时间范围查询或搜索 CasbinRule
//	@Summary Query 根据指定字段、时间范围查询或搜索 CasbinRule
//	@Tags CasbinRule
//	@Accept json
//	@Produce json
//	@Param NetType query string false "NetType"
//
// @Param Sub query string false "Sub"
// @Param Dom query string false "Dom"
// @Param Obj query string false "Obj"
// @Param Act query string false "Act"
// @Param V4 query string false "V4"
// @Param V5 query string false "V5"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.CasbinRule}
//	@Router /api/v1/casbinrule [get]
func (crc *CasbinRuleController) Query(c *fiber.Ctx) error {
	cr := &ent.CasbinRule{}
	qp, err := common.QueryParser(c, cr)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := crc.CasbinRuleService.Query(ctx, cr, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 CasbinRule
//
//	@Description QueryByID 根据 ID 查询 CasbinRule
//	@Summary QueryByID 根据 ID 查询 CasbinRule
//	@Tags CasbinRule
//	@Accept json
//	@Produce json
//	@Param id path int true "CasbinRule ID"
//	@Success 200 {object} common.Result{data=ent.CasbinRule}
//	@Router /api/v1/casbinrule/{id} [get]
func (crc *CasbinRuleController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := crc.CasbinRuleService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 CasbinRule
//
//	@Description Create 创建 CasbinRule
//	@Summary Create 创建 CasbinRule
//	@Tags CasbinRule
//	@Accept json
//	@Produce json
//	@Param casbinrule body ent.CasbinRule true "CasbinRule"
//	@Success 200 {object} common.Result{data=ent.CasbinRule}
//	@Router /api/v1/casbinrule [post]
func (crc *CasbinRuleController) Create(c *fiber.Ctx) error {
	cr := &ent.CasbinRule{}
	err := common.BodyParser(c, cr)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := crc.CasbinRuleService.Create(ctx, cr)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 CasbinRule
//
//	@Description CreateBulk 批量创建 CasbinRule
//	@Summary CreateBulk 批量创建 CasbinRule
//	@Tags CasbinRule
//	@Accept json
//	@Produce json
//	@Param casbinrule body []ent.CasbinRule true "CasbinRule"
//	@Success 200 {object} common.Result{data=[]ent.CasbinRule}
//	@Router /api/v1/casbinrule/bulk [post]
func (crc *CasbinRuleController) CreateBulk(c *fiber.Ctx) error {
	cr := make([]*ent.CasbinRule, 10)
	err := common.RequestBodyParser(c, &cr)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := crc.CasbinRuleService.CreateBulk(ctx, cr)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 CasbinRule
//
//	@Description UpdateByID 根据 ID 修改 CasbinRule
//	@Summary UpdateByID 根据 ID 修改 CasbinRule
//	@Tags CasbinRule
//	@Accept json
//	@Produce json
//	@Param id path int true "CasbinRule ID"
//
// @Param casbinrule body ent.CasbinRule true "CasbinRule"
//
//	@Success 200 {object} common.Result{data=ent.CasbinRule}
//	@Router /api/v1/casbinrule/{id} [put]
func (crc *CasbinRuleController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	cr, err := crc.CasbinRuleService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, cr)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := crc.CasbinRuleService.UpdateByID(ctx, cr, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 CasbinRule
//
//	@Description DeleteByID 根据 ID 删除 CasbinRule
//	@Summary DeleteByID 根据 ID 删除 CasbinRule
//	@Tags CasbinRule
//	@Accept json
//	@Produce json
//	@Param id path int true "CasbinRule ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/casbinrule/{id} [delete]
func (crc *CasbinRuleController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := crc.CasbinRuleService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 CasbinRule
//
//	@Description DeleteBulk 根据 IDs 批量删除 CasbinRule
//	@Summary DeleteBulk 根据 IDs 批量删除 CasbinRule
//	@Tags CasbinRule
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/casbinrule/bulk/delete [post]
func (crc *CasbinRuleController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = crc.CasbinRuleService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
