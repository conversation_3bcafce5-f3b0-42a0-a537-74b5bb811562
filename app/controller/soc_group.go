/**
* <AUTHOR>
* @date 2023-02-10 15:21
* @description
 */

package controller

import (
	"github.com/gofiber/fiber/v2"
	"meta/app/entity/config"
	"meta/app/entity/netease/socgroup"
	"meta/pkg/common"
	"meta/pkg/http"
)

type SocGroupController struct {
}

// Query 根据 起始时间和偏移值 查询 DDoS工单
//
//	@Description Query 根据 起始时间和偏移值 查询 DDoS
//	@Summary Query 根据 起始时间和偏移值 查询 DDoS
//	@Tags SocGroup
//	@Accept json
//	@Produce json
//	@Param startTime query string true "startTime" Format(date-time)
//	@Param offset query int true "偏移值"
//
// @Success 200 {object} common.Result{data=socgroup.QueryResponseData{dataitems=[]socgroup.WorkOrder}}
// @Router /api/v1/socgroup/ticket [get]
func (sgc *SocGroupController) Query(c *fiber.Ctx) error {
	requestData := &socgroup.QueryRequestVO{}
	_, err := common.QueryParser(c, requestData)
	if err != nil {
		return common.NewResult(c, err)
	}
	offset := 0
	if requestData.Offset > 1 {
		offset = requestData.Offset - 1
	}
	bodyData := &socgroup.QueryRequestData{StartTime: requestData.StartTime.Unix(), Offset: offset}
	//FIXME:
	ticket, err := http.QueryTicket(config.CFG.External.SocGroup.Api, bodyData)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err, ticket)
}

// Add 提交 四层DDOS清洗防护 工单
//
//	@Description Add 提交 四层DDOS清洗防护 工单
//	@Summary Add 提交 四层DDOS清洗防护 工单
//	@Tags SocGroup
//	@Accept json
//	@Produce json
//	@Param ticket body socgroup.AddRequestData true "Ticket"
//
// @Success 200 {object} common.Result{data=socgroup.AddResponseData{dataitems=socgroup.WorkOrder}}
// @Router /api/v1/socgroup/ticket [post]
func (sgc *SocGroupController) Add(c *fiber.Ctx) error {
	requestData := &socgroup.AddRequestData{}
	err := common.BodyParser(c, &requestData)
	if err != nil {
		return common.NewResult(c, err)
	}
	if requestData.WorkOrderType != "DOSDIVERT" {
		requestData.WorkOrderType = "DOSDIVERT"
	}
	ticket, err := http.AddTicket(config.CFG.External.SocGroup.Api, requestData)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err, ticket)
}
