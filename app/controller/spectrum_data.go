package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type SpectrumDataController struct {
	SpectrumDataService *service.SpectrumDataService
	Logger              *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 SpectrumData
//
//	@Description Query 根据指定字段、时间范围查询或搜索 SpectrumData
//	@Summary Query 根据指定字段、时间范围查询或搜索 SpectrumData
//	@Tags SpectrumData
//	@Accept json
//	@Produce json
//	@Param IP query string false "IP" Format(ipv4)
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param Time query string false "Time" Format(date-time)
// @Param DataType query integer false "DataType"
// @Param MatrixBps query integer false "MatrixBps"
// @Param MatrixPps query integer false "MatrixPps"
// @Param SynBps query integer false "SynBps"
// @Param SynPps query integer false "SynPps"
// @Param AckBps query integer false "AckBps"
// @Param AckPps query integer false "AckPps"
// @Param SynAckBps query integer false "SynAckBps"
// @Param SynAckPps query integer false "SynAckPps"
// @Param IcmpBps query integer false "IcmpBps"
// @Param IcmpPps query integer false "IcmpPps"
// @Param SmallPps query integer false "SmallPps"
// @Param NtpPps query integer false "NtpPps"
// @Param NtpBps query integer false "NtpBps"
// @Param DnsQueryPps query integer false "DnsQueryPps"
// @Param DnsQueryBps query integer false "DnsQueryBps"
// @Param DnsAnswerPps query integer false "DnsAnswerPps"
// @Param DnsAnswerBps query integer false "DnsAnswerBps"
// @Param SsdpBps query integer false "SsdpBps"
// @Param SsdpPps query integer false "SsdpPps"
// @Param UdpPps query integer false "UdpPps"
// @Param UdpBps query integer false "UdpBps"
// @Param QPS query integer false "QPS"
// @Param ReceiveCount query integer false "ReceiveCount"
// @Param IpType query integer false "IpType"
// @Param Monitor query string false "Monitor"
// @Param Product query string false "Product"
// @Param Host query string false "Host"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.SpectrumData}
//	@Router /api/v1/spectrumdata [get]
func (sdc *SpectrumDataController) Query(c *fiber.Ctx) error {
	sd := &ent.SpectrumData{}
	qp, err := common.QueryParser(c, sd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := sdc.SpectrumDataService.Query(ctx, sd, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 SpectrumData
//
//	@Description QueryByID 根据 ID 查询 SpectrumData
//	@Summary QueryByID 根据 ID 查询 SpectrumData
//	@Tags SpectrumData
//	@Accept json
//	@Produce json
//	@Param id path int true "SpectrumData ID"
//	@Success 200 {object} common.Result{data=ent.SpectrumData}
//	@Router /api/v1/spectrumdata/{id} [get]
func (sdc *SpectrumDataController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := sdc.SpectrumDataService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 SpectrumData
//
//	@Description Create 创建 SpectrumData
//	@Summary Create 创建 SpectrumData
//	@Tags SpectrumData
//	@Accept json
//	@Produce json
//	@Param spectrumdata body ent.SpectrumData true "SpectrumData"
//	@Success 200 {object} common.Result{data=ent.SpectrumData}
//	@Router /api/v1/spectrumdata [post]
func (sdc *SpectrumDataController) Create(c *fiber.Ctx) error {
	sd := &ent.SpectrumData{}
	err := common.BodyParser(c, sd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := sdc.SpectrumDataService.Create(ctx, sd)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 SpectrumData
//
//	@Description CreateBulk 批量创建 SpectrumData
//	@Summary CreateBulk 批量创建 SpectrumData
//	@Tags SpectrumData
//	@Accept json
//	@Produce json
//	@Param spectrumdata body []ent.SpectrumData true "SpectrumData"
//	@Success 200 {object} common.Result{data=[]ent.SpectrumData}
//	@Router /api/v1/spectrumdata/bulk [post]
func (sdc *SpectrumDataController) CreateBulk(c *fiber.Ctx) error {
	sd := make([]*ent.SpectrumData, 10)
	err := common.RequestBodyParser(c, &sd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := sdc.SpectrumDataService.CreateBulk(ctx, sd)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 SpectrumData
//
//	@Description UpdateByID 根据 ID 修改 SpectrumData
//	@Summary UpdateByID 根据 ID 修改 SpectrumData
//	@Tags SpectrumData
//	@Accept json
//	@Produce json
//	@Param id path int true "SpectrumData ID"
//
// @Param spectrumdata body ent.SpectrumData true "SpectrumData"
//
//	@Success 200 {object} common.Result{data=ent.SpectrumData}
//	@Router /api/v1/spectrumdata/{id} [put]
func (sdc *SpectrumDataController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	sd, err := sdc.SpectrumDataService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, sd)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := sdc.SpectrumDataService.UpdateByID(ctx, sd, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 SpectrumData
//
//	@Description DeleteByID 根据 ID 删除 SpectrumData
//	@Summary DeleteByID 根据 ID 删除 SpectrumData
//	@Tags SpectrumData
//	@Accept json
//	@Produce json
//	@Param id path int true "SpectrumData ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/spectrumdata/{id} [delete]
func (sdc *SpectrumDataController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := sdc.SpectrumDataService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 SpectrumData
//
//	@Description DeleteBulk 根据 IDs 批量删除 SpectrumData
//	@Summary DeleteBulk 根据 IDs 批量删除 SpectrumData
//	@Tags SpectrumData
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/spectrumdata/bulk/delete [post]
func (sdc *SpectrumDataController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = sdc.SpectrumDataService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
