package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type StrategyController struct {
	StrategyService *service.StrategyService
	Logger          *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 Strategy
//
//	@Description Query 根据指定字段、时间范围查询或搜索 Strategy
//	@Summary Query 根据指定字段、时间范围查询或搜索 Strategy
//	@Tags Strategy
//	@Accept json
//	@Produce json
//	@Param Remark query string false "Remark"
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param Name query string false "Name"
// @Param NetType query string false "NetType"
// @Param Base query bool false "Base"
// @Param MatrixBps query integer false "MatrixBps"
// @Param MatrixPps query integer false "MatrixPps"
// @Param BpsCount query integer false "BpsCount"
// @Param PpsCount query integer false "PpsCount"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.Strategy}
//	@Router /api/v1/strategy [get]
func (sc *StrategyController) Query(c *fiber.Ctx) error {
	s := &ent.Strategy{}
	qp, err := common.QueryParser(c, s)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := sc.StrategyService.Query(ctx, s, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 Strategy
//
//	@Description QueryByID 根据 ID 查询 Strategy
//	@Summary QueryByID 根据 ID 查询 Strategy
//	@Tags Strategy
//	@Accept json
//	@Produce json
//	@Param id path int true "Strategy ID"
//	@Success 200 {object} common.Result{data=ent.Strategy}
//	@Router /api/v1/strategy/{id} [get]
func (sc *StrategyController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := sc.StrategyService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 Strategy
//
//	@Description Create 创建 Strategy
//	@Summary Create 创建 Strategy
//	@Tags Strategy
//	@Accept json
//	@Produce json
//	@Param strategy body ent.Strategy true "Strategy"
//	@Success 200 {object} common.Result{data=ent.Strategy}
//	@Router /api/v1/strategy [post]
func (sc *StrategyController) Create(c *fiber.Ctx) error {
	s := &ent.Strategy{}
	err := common.BodyParser(c, s)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := sc.StrategyService.Create(ctx, s)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 Strategy
//
//	@Description CreateBulk 批量创建 Strategy
//	@Summary CreateBulk 批量创建 Strategy
//	@Tags Strategy
//	@Accept json
//	@Produce json
//	@Param strategy body []ent.Strategy true "Strategy"
//	@Success 200 {object} common.Result{data=[]ent.Strategy}
//	@Router /api/v1/strategy/bulk [post]
func (sc *StrategyController) CreateBulk(c *fiber.Ctx) error {
	s := make([]*ent.Strategy, 10)
	err := common.RequestBodyParser(c, &s)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := sc.StrategyService.CreateBulk(ctx, s)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 Strategy
//
//	@Description UpdateByID 根据 ID 修改 Strategy
//	@Summary UpdateByID 根据 ID 修改 Strategy
//	@Tags Strategy
//	@Accept json
//	@Produce json
//	@Param id path int true "Strategy ID"
//
// @Param strategy body ent.Strategy true "Strategy"
//
//	@Success 200 {object} common.Result{data=ent.Strategy}
//	@Router /api/v1/strategy/{id} [put]
func (sc *StrategyController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	s, err := sc.StrategyService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, s)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := sc.StrategyService.UpdateByID(ctx, s, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 Strategy
//
//	@Description DeleteByID 根据 ID 删除 Strategy
//	@Summary DeleteByID 根据 ID 删除 Strategy
//	@Tags Strategy
//	@Accept json
//	@Produce json
//	@Param id path int true "Strategy ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/strategy/{id} [delete]
func (sc *StrategyController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := sc.StrategyService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 Strategy
//
//	@Description DeleteBulk 根据 IDs 批量删除 Strategy
//	@Summary DeleteBulk 根据 IDs 批量删除 Strategy
//	@Tags Strategy
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/strategy/bulk/delete [post]
func (sc *StrategyController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = sc.StrategyService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
