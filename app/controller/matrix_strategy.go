package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type MatrixStrategyController struct {
	MatrixStrategyService *service.MatrixStrategyService
	Logger                *zap.Logger
	Rdb                   *redis.Client
}

// Query 根据指定字段、时间范围查询或搜索 MatrixStrategy
//
//	@Description Query 根据指定字段、时间范围查询或搜索 MatrixStrategy
//	@Summary Query 根据指定字段、时间范围查询或搜索 MatrixStrategy
//	@Tags MatrixStrategy
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param name query string false "name"
// @Param device query string false "device"
// @Param monitor_bps query integer false "monitor_bps"
// @Param drag_bps query integer false "drag_bps"
// @Param drag_type query string false "drag_type"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.MatrixStrategy}
//	@Router /api/v1/matrixstrategy [get]
func (msc *MatrixStrategyController) Query(c *fiber.Ctx) error {
	ms := &ent.MatrixStrategy{}
	qp, err := common.QueryParser(c, ms)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := msc.MatrixStrategyService.Query(ctx, ms, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 MatrixStrategy
//
//	@Description QueryByID 根据 ID 查询 MatrixStrategy
//	@Summary QueryByID 根据 ID 查询 MatrixStrategy
//	@Tags MatrixStrategy
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixStrategy ID"
//	@Success 200 {object} common.Result{data=ent.MatrixStrategy}
//	@Router /api/v1/matrixstrategy/{id} [get]
func (msc *MatrixStrategyController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := msc.MatrixStrategyService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 MatrixStrategy
//
//	@Description Create 创建 MatrixStrategy
//	@Summary Create 创建 MatrixStrategy
//	@Tags MatrixStrategy
//	@Accept json
//	@Produce json
//	@Param matrixstrategy body ent.MatrixStrategy true "MatrixStrategy"
//	@Success 200 {object} common.Result{data=ent.MatrixStrategy}
//	@Router /api/v1/matrixstrategy [post]
func (msc *MatrixStrategyController) Create(c *fiber.Ctx) error {
	ms := &ent.MatrixStrategy{}
	err := common.BodyParser(c, ms)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := msc.MatrixStrategyService.Create(ctx, ms)
	freshCache(create, msc.Rdb, ctx)
	return common.NewResult(c, err, create)
}

func freshCache(data *ent.MatrixStrategy, rdb *redis.Client, ctx context.Context) {
	configKey := common.GenRedisKey("matrix", data.Region, data.NetType, data.Isp)
	rdb.HSet(ctx, configKey, "id", data.ID)
	rdb.HSet(ctx, configKey, "name", data.Name)
	rdb.HSet(ctx, configKey, "monitorBps", data.MonitorBps)
	rdb.HSet(ctx, configKey, "dragBps", data.DragBps)
	rdb.HSet(ctx, configKey, "dragType", data.DragType)
}

func cleanCache(data *ent.MatrixStrategy, rdb *redis.Client, ctx context.Context) {
	configKey := common.GenRedisKey("matrix", data.Region, data.NetType, data.Isp)
	rdb.Del(ctx, configKey)
}

// CreateBulk 批量创建 MatrixStrategy
//
//	@Description CreateBulk 批量创建 MatrixStrategy
//	@Summary CreateBulk 批量创建 MatrixStrategy
//	@Tags MatrixStrategy
//	@Accept json
//	@Produce json
//	@Param matrixstrategy body []ent.MatrixStrategy true "MatrixStrategy"
//	@Success 200 {object} common.Result{data=[]ent.MatrixStrategy}
//	@Router /api/v1/matrixstrategy/bulk [post]
func (msc *MatrixStrategyController) CreateBulk(c *fiber.Ctx) error {
	ms := make([]*ent.MatrixStrategy, 10)
	err := common.RequestBodyParser(c, &ms)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := msc.MatrixStrategyService.CreateBulk(ctx, ms)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 MatrixStrategy
//
//	@Description UpdateByID 根据 ID 修改 MatrixStrategy
//	@Summary UpdateByID 根据 ID 修改 MatrixStrategy
//	@Tags MatrixStrategy
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixStrategy ID"
//
// @Param matrixstrategy body ent.MatrixStrategy true "MatrixStrategy"
//
//	@Success 200 {object} common.Result{data=ent.MatrixStrategy}
//	@Router /api/v1/matrixstrategy/{id} [put]
func (msc *MatrixStrategyController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	ms, err := msc.MatrixStrategyService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, ms)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := msc.MatrixStrategyService.UpdateByID(ctx, ms, id)
	freshCache(data, msc.Rdb, ctx)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 MatrixStrategy
//
//	@Description DeleteByID 根据 ID 删除 MatrixStrategy
//	@Summary DeleteByID 根据 ID 删除 MatrixStrategy
//	@Tags MatrixStrategy
//	@Accept json
//	@Produce json
//	@Param id path int true "MatrixStrategy ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/matrixstrategy/{id} [delete]
func (msc *MatrixStrategyController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	ms, _ := msc.MatrixStrategyService.QueryByID(ctx, id)
	cleanCache(ms, msc.Rdb, ctx)
	err := msc.MatrixStrategyService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 MatrixStrategy
//
//	@Description DeleteBulk 根据 IDs 批量删除 MatrixStrategy
//	@Summary DeleteBulk 根据 IDs 批量删除 MatrixStrategy
//	@Tags MatrixStrategy
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/matrixstrategy/bulk/delete [post]
func (msc *MatrixStrategyController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = msc.MatrixStrategyService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
