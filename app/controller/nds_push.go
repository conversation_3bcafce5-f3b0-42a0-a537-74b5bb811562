package controller

import (
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"golang.org/x/exp/slices"
	"meta/app/ent"
	"meta/app/ent/notify"
	"meta/app/ent/spectrumalert"
	"meta/app/entity/config"
	"meta/app/entity/netease"
	"meta/app/entity/wofang"
	"meta/app/service"
	"meta/pkg/common"
	"meta/pkg/common/cache"
	"meta/pkg/http"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
)

// Push 集团NDS推送告警 PushAlert
// @Description 集团NDS推送告警 PushAlert
// @Summary 集团NDS推送告警 PushAlert
// @Tags NDS
// @Accept json
// @Produce json
// @Param PushAlert body netease.PushAlert true "PushAlert"
// @Success 200 {object} common.Message
// @Router /api/v1/nds/push [post]
func (nc *NdsController) Push(c *fiber.Ctx) error {
	pushAlert := &netease.PushAlert{}
	err := common.BodyParser(c, pushAlert)
	if err != nil {
		return common.NewResult(c, err)
	}

	go func() {
		marshal, _ := sonic.Marshal(pushAlert)
		nc.Logger.Sugar().Infof("nds push alert: %s", marshal)
	}()

	parseIP := net.ParseIP(strings.TrimSpace(pushAlert.Ip))
	if parseIP == nil {
		return common.NewResult(c, errors.New("ip错误"))
	}
	nowSecond := time.Now().Unix()
	if config.CFG.Stage.Status == "prod" {
		if pushAlert.Time < nowSecond-86400 {
			return common.NewResult(c, errors.New("时间范围错误"))
		}
	}
	ctx := c.Locals("ctx").(context.Context)

	if pushAlert.AlertType != 1 && pushAlert.AlertType != 2 {
		pushAlert.AlertType = 1
	}

	//NDS机房满载告警IP
	if pushAlert.AlertType == 2 {
		go func() {
			ip := pushAlert.Ip
			fmt.Println(time.Now())
			fmt.Println("机房满载告警开始", ip)
			var projectId, ispID int
			wg := &sync.WaitGroup{}
			wg.Add(1)
			go func() {
				defer wg.Done()
				realProject := nc.ProjectCtl.GetRealProject(ctx, ip)
				if realProject != nil {
					projectId = realProject.ID
				}
			}()
			wg.Add(1)
			go func() {
				defer wg.Done()
				ispID, _ = nc.GetISPIDAndStrategyType(ip, ctx)
			}()
			wg.Wait()
			fmt.Println("projectId", projectId)
			fmt.Println("ispID", ispID)
			//bgp
			if ispID == 1 {
				nc.DragWoFangClean(ctx, ip, projectId, "NDS机房满载告警模块", 0)
			}
			rKey := common.KeyMatrixAttackPrefix + strconv.Itoa(ispID)
			nc.Rdb.SAdd(ctx, rKey, ip)
			nc.Rdb.Expire(ctx, rKey, time.Minute*10)
		}()
		return common.NewResult(c, err)
	}

	fmt.Printf("nds push alert %+v\n", pushAlert)
	fmt.Println("bps", common.Bit2Str(pushAlert.MaxBps))
	fmt.Println("pps", common.Bit2Str(pushAlert.MaxPps))
	sAlert := &ent.SpectrumAlert{
		IP:         strings.TrimSpace(pushAlert.Ip),
		AttackType: pushAlert.AttackTypes,
		MaxBps:     pushAlert.MaxBps,
		MaxPps:     pushAlert.MaxPps,
	}
	ip := sAlert.IP
	var (
		protectGroup        *ent.ProtectGroup
		notifyFlag          bool
		projectId           int
		createSpectrumAlert *ent.SpectrumAlert
		protectStatus       []int
		wofangID            int
		strategyWofangID    int
		wofangNotifyFlag    bool
	)
	go func() {
		ndsAttack := &netease.NdsAttackData{
			Prefix:            common.Prefix,
			Domain:            config.CFG.Stage.Domain,
			Schema:            config.CFG.Stage.HttpSchema,
			ProcessSuggestion: common.ProcessSuggestion,
			IP:                ip,
			Target:            ip,
		}
		wofangDrainData := &netease.WofangDrainData{
			Prefix:            common.Prefix,
			Domain:            config.CFG.Stage.Domain,
			Schema:            config.CFG.Stage.HttpSchema,
			ProcessSuggestion: common.ProcessSuggestion,
			IP:                ip,
			Target:            ip,
		}

		// 获取ip的项目
		realProject := nc.ProjectCtl.GetRealProject(ctx, ip)
		if realProject != nil {
			ndsAttack.Project = fmt.Sprintf("%s-%s", realProject.Name, realProject.Code)
			sAlert.TenantID = &realProject.ID
			projectId = realProject.ID
			ndsAttack.ProjectCode = realProject.Code
			ndsAttack.ProjectID = realProject.ID

			wofangDrainData.Project = ndsAttack.Project
			wofangDrainData.ProjectCode = realProject.Code
			wofangDrainData.ProjectID = realProject.ID
		}
		// 处理时间
		if pushAlert.Time < nowSecond-86400 {
			pushAlert.Time = time.Now().Unix()
		}
		// 根据nds IP查询防护群组
		protectGroup = nc.GetNdsProtectGroup(ip, ctx)
		if protectGroup != nil {
			sAlert.ProtectGroupID = &protectGroup.ID
		}

		if pushAlert.Status == "start" {
			sAlert.StartTime = common.Timestamp2Time(pushAlert.Time)

			ndsAttack.StartTime = sAlert.StartTime.Local().String()
			ndsAttack.AlertBps = common.Bit2Str(pushAlert.MaxBps)
			ndsAttack.AlertPps = common.Bit2Str(pushAlert.MaxPps)
			ndsAttack.AttackType = sAlert.AttackType
			sAlert.ProtectStatus = &[]int{common.NDSNotProtect}
			ndsAttack.Status = "开始"

			ispID, strategyType := nc.GetISPIDAndStrategyType(ip, ctx)
			sAlert.IspCode = ispID
			createSpectrumAlert, _ = nc.SpectrumAlertService.Create(ctx, sAlert)

			ndsAttack.AlertID = createSpectrumAlert.ID
			wofangDrainData.AlertID = createSpectrumAlert.ID

			protectStrategy := nc.GetProtectStrategy(projectId, ispID, strategyType, ctx)
			// 当前告警数据进缓存
			attackInfo := &netease.AttackInfo{
				MaxBps: sAlert.MaxBps,
				MaxPps: sAlert.MaxPps,
			}
			fmt.Printf("protectStrategy: %+v\n", protectStrategy)
			CalculateCount(nc, ctx, ip, attackInfo, protectStrategy)

			//计算攻击阈值，并存到redis中
			//attack:ip => map: 攻击:超过阈值次数，同时增加最大的攻击数据，清洗数据（如有）
			counterReachMax := nc.CounterReachMax(ctx, ip, protectStrategy)
			fmt.Printf("%s =====> counterReachMax: %v\n", ip, counterReachMax)
			if counterReachMax {
				fmt.Println("isp id", ispID)
				fmt.Println("strategy id", strategyType)
				//bgp
				if strategyType == 1 {
					fmt.Println(" ======> 提交沃防工单.... ", ip)
					nc.Logger.Sugar().Infof("提交沃防工单：%s", ip)
					keyCountBps := common.KeyCounterBpsPrefix + ip
					keyCountPps := common.KeyCounterPpsPrefix + ip
					nc.Rdb.Del(ctx, keyCountBps)
					nc.Rdb.Del(ctx, keyCountPps)

					woFangId, status, createStatus, errInfo := nc.DragWoFangClean(ctx, ip, projectId, "NDS告警模块", 0)
					wofangNotifyFlag = true
					wofangDrainData.Action = "牵引"
					if status != 0 {
						ndsAttack.WoFangProtect = createStatus
						wofangDrainData.Message = errInfo
						wofangDrainData.Status = createStatus
						protectStatus = append(protectStatus, status)
						if woFangId != 0 {
							wofangDrainData.StartTime = time.Now().Local().String()
							wofangID = woFangId
							strategyWofangID = protectStrategy.ID
							wofangDrainData.WofangID = wofangID
						}
					}
				}
				if ispID == 10000 {
					fmt.Println(" ======> 提交电信工单.... ", ip)
				}
				if ispID == 10086 {
					fmt.Println(" ======> 提交移动工单.... ", ip)
				}
			}
			notifyFlag = true

			//判断是否已经提交沃防
			wfResult, _ := nc.Rdb.Get(ctx, common.Key10010CleanPrefix+ip).Result()
			wfFailedResult, _ := nc.Rdb.Get(ctx, common.Key10010CleanFailedPrefix+ip).Result()
			// 已提交成功
			if wfResult == "1" {
				//已防护
				protectStatus = append(protectStatus, common.WofangInProtect)
			} else {
				//失败
				if wfFailedResult == "1" {
					protectStatus = append(protectStatus, common.WofangFailed)
				}
			}

			// 有防护群组
			if protectGroup != nil {
				dragInfo := protectGroup.DragInfo
				dragText := "自动牵引"
				backDragText := "自动回牵"
				if dragInfo.AutoDrag {
					// 1
					protectStatus = append(protectStatus, common.NDSAutoDrag)
				} else {
					protectStatus = append(protectStatus, common.NDSNotAutoDrag)
					// 自动牵引：否 3
					dragText = "不自动牵引"
				}
				if dragInfo.AutoUnDrag {
					// 2
					protectStatus = append(protectStatus, common.NDSAutoUnDrag)
				} else {
					protectStatus = append(protectStatus, common.NDSNotAutoUnDrag)
					// 自动回牵：否 4
					backDragText = "不自动回牵"
				}
				ndsAttack.NdsProtect = fmt.Sprintf("%s | %s", dragText, backDragText)
			} else {
				// 不在NDS防护群组
				protectStatus = append(protectStatus, common.NDSNotProtect)
				go func() {
					systemConfig, _ := cache.GetSystemConfigCache(nc.Rdb, nc.SystemConfigService, ctx)
					if strings.Contains(systemConfig.NotifyScenes, "NDS告警开始&被攻击IP不在NDS防护群组") {
						content := common.ConcatString("【告警提升】" + common.Prefix + "【被攻击IP不在NDS防护群组】\nIP：" + ip)
						if !slices.Contains(strings.Split(systemConfig.IpWhitelists, ","), ip) {
							err = http.NotifyPhone(content, strings.Split(systemConfig.NotifyPhones, ","))
						}
						err = http.NotifyPopo(content, strings.Split(systemConfig.NotifyEmails, ","))
						if err != nil {
							nc.Logger.Sugar().Error(err)
						}
					}
				}()
			}
			if createSpectrumAlert != nil && protectStatus != nil {
				resultSlice := common.SortRemoveDuplElement(protectStatus)
				spectrumAlertUpdateOne := nc.SpectrumAlertService.Dao.SpectrumAlert.UpdateOneID(createSpectrumAlert.ID)
				if strategyWofangID != 0 {
					spectrumAlertUpdateOne.SetStrategyID(strategyWofangID)
				}
				if wofangID != 0 {
					spectrumAlertUpdateOne.SetWofangID(wofangID)
				}
				spectrumAlertUpdateOne.SetProtectStatus(&resultSlice).Save(ctx)
			}
		}
		// status end,更新已有记录状态
		if pushAlert.Status == "end" {
			ndsAttack.Status = "结束"
			spectrumAlerts, err := nc.SpectrumAlertService.Dao.SpectrumAlert.Query().Where(spectrumalert.IP(ip), spectrumalert.EndTimeIsNil()).Order(ent.Desc(spectrumalert.FieldCreatedAt)).Limit(1).All(ctx)
			if err != nil {
				nc.Logger.Sugar().Error(err)
				// return common.NewResult(c, err)
			}
			if len(spectrumAlerts) >= 1 {
				spectrumAlert := spectrumAlerts[0]
				ndsAttack.AlertID = spectrumAlert.ID
				wofangDrainData.AlertID = spectrumAlert.ID
				spectrumAlert.EndTime = common.Timestamp2Time(pushAlert.Time)

				// 获取攻击时间段的分光数据和清洗数据
				start := spectrumAlert.StartTime
				end := spectrumAlert.EndTime
				nc.Logger.Sugar().Infof("兜底获取攻击时间段[%s-%s]的分光数据和清洗数据: %s", start.Local(), end.Local(), ip)
				nc.GetNdsSpectrumData(ip, start, end, spectrumAlert.ID, projectId, nil, ctx)
				nc.GetNdsCleanData(ip, start, end, spectrumAlert.ID, projectId, ctx)

				keyAttack := common.KeyAttackPrefix + ip
				keyCountBps := common.KeyCounterBpsPrefix + ip
				keyCountPps := common.KeyCounterPpsPrefix + ip
				keyWofangFailed := common.Key10010CleanFailedPrefix + ip
				var attackInfo netease.AttackInfo
				// redis hash => struct
				if err := nc.Rdb.HGetAll(ctx, keyAttack).Scan(&attackInfo); err != nil {
					nc.Logger.Sugar().Error(err.Error())
				}
				nc.Rdb.Del(ctx, keyAttack)
				nc.Rdb.Del(ctx, keyCountBps)
				nc.Rdb.Del(ctx, keyCountPps)
				nc.Rdb.Del(ctx, keyWofangFailed)

				ndsAttack.StartTime = spectrumAlert.StartTime.Local().String()
				ndsAttack.EndTime = spectrumAlert.EndTime.Local().String()
				ndsAttack.AttackType = spectrumAlert.AttackType
				ndsAttack.AlertBps = common.Bit2Str(spectrumAlert.MaxBps)
				ndsAttack.AlertPps = common.Bit2Str(spectrumAlert.MaxPps)

				if attackInfo.MaxBps != 0 {
					ndsAttack.MaxAttackBps = common.Bit2Str(attackInfo.MaxBps)
					ndsAttack.MaxAttackPps = common.Bit2Str(attackInfo.MaxPps)
				}
				if attackInfo.MaxCleanBps != 0 {
					ndsAttack.MaxCleanBps = common.Bit2Str(attackInfo.MaxCleanBps)
					ndsAttack.MaxCleanPps = common.Bit2Str(attackInfo.MaxCleanPps)
				}
				nc.SpectrumAlertService.Dao.SpectrumAlert.UpdateOneID(spectrumAlert.ID).SetAttackInfo(attackInfo).SetEndTime(spectrumAlert.EndTime).Save(ctx)

				notifyFlag = true

				////兜底最后获取分光和清洗数据（5分钟）
				//_, _ = GScheduler.LimitRunsTo(30).Every(10).Second().Do(func() {
				//	// 获取攻击时间段的分光数据和清洗数据
				//	start := dbAlert.StartTime.Add(-time.Second * 1)
				//	end := dbAlert.EndTime
				//	nc.Logger.Sugar().Infof("兜底获取攻击时间段[%s-%s]的分光数据和清洗数据: %s", start.Local(), end.Local(), ip)
				//	nc.GetNdsSpectrumData(ip, start, end, dbAlert.ID, projectId, nil, ctx)
				//	nc.GetNdsCleanData(ip, start, end, dbAlert.ID, projectId, ctx)
				//})
			}

			//// 停止获取分光数据和清洗数据的定时任务
			//runTask := TaskMap[ip]
			//if runTask != nil {
			//	GScheduler.RemoveByReference(runTask)
			//	nc.Logger.Sugar().Infof("结束查询分光数据和清洗数据任务：%s", ip)
			//}

			//有自动回牵，不需要手动
			ispID, _ := nc.GetISPIDAndStrategyType(ip, ctx)
			if ispID == 1 {
				msg := nc.UnDragWoFangClean(ctx, ip)
				if msg != "" {
					ndsAttack.WoFangProtect = msg
					wofangNotifyFlag = true
					if strings.Contains(msg, "失败") {
						wofangDrainData.Status = "失败"
					} else {
						wofangDrainData.Status = "成功"
					}
					wofangDrainData.Action = "回牵"
					wofangDrainData.Message = msg
				}
			}
		}

		// NDS防护群组
		if protectGroup != nil {
			ndsAttack.NdsGroup = fmt.Sprintf("【%s】", protectGroup.GroupName)
		}
		ndsAttack.Title = fmt.Sprintf("%s【攻击%s】【%s】%s", common.Prefix, ndsAttack.Status, ndsAttack.IP, ndsAttack.NdsGroup)
		wofangDrainData.Title = fmt.Sprintf("%s【沃防清洗-%s-%s】【%s】", common.Prefix, wofangDrainData.Action, wofangDrainData.Status, wofangDrainData.IP)

		if wofangNotifyFlag && wofangID != 0 {
			content, err := common.ParseTemplate("wofangNotify", common.WofangPopoTemplate, wofangDrainData)
			if err != nil {
				nc.Logger.Sugar().Error(err)
			}
			nc.Logger.Sugar().Infof("wofangAlert content: %s", content)
			nc.NotifyAlert(ctx, projectId, ip, wofangDrainData.ProjectCode, wofangDrainData.Title, content)
		}

		if notifyFlag {
			content, err := common.ParseTemplate("popoNotify", common.NdsPopoTemplate, ndsAttack)
			if err != nil {
				nc.Logger.Sugar().Error(err)
			}
			nc.Logger.Sugar().Infof("notifyNdsAlert content: %s", content)
			nc.NotifyAlert(ctx, projectId, ip, ndsAttack.ProjectCode, ndsAttack.Title, content)
		}
	}()
	return common.NewResult(c, err)
}

// GetISPIDAndStrategyType
// id：运营商id，只有中国大陆的IP存在运营商字段，
// 现有id规则如下：BGP 1，Anycast 2，电信10000，联通10010，
// 移动10086，铁通10050，教育网985211，鹏博士600804，华数96171，
// 方正网络9990，歌华网络96196，阿里云1688，电信通6800，有线通8882，
// 科技网8883，视讯宽带8884，其它（包括未知） 0(转为-1)。
func (nc *NdsController) GetISPIDAndStrategyType(ip string, ctx context.Context) (ispID, strategyType int) {
	keyIspDrag := common.KeyIspDragPrefix + ip
	result, _ := nc.Rdb.Exists(ctx, keyIspDrag).Result()
	//存在
	if result == 1 {
		ispID, _ = nc.Rdb.HGet(ctx, keyIspDrag, "IspID").Int()
		strategyType, _ = nc.Rdb.HGet(ctx, keyIspDrag, "dragType").Int()
		return
	}

	// 新增根据ip.netease获取ip运营商
	rd, err := http.GetIPLocationInfo(ip)
	if err != nil {
		nc.Logger.Sugar().Error(err)
	}
	// code的值的含义为，0：成功，1：失败。
	if rd != nil && rd.Code == 0 {
		if rd.Data.Isp.Id != 0 {
			ispID = rd.Data.Isp.Id
		}
	} else {
		nc.Logger.Sugar().Error("neteaseIp code not equal to 0")
	}

	strategyType = ispID
	if ispID == 0 {
		ispID = -1
	}
	go func() {
		nc.Rdb.HSet(ctx, keyIspDrag, "IspID", ispID)
		nc.Rdb.HSet(ctx, keyIspDrag, "dragType", strategyType)
		if rd == nil {
			nc.Rdb.Expire(ctx, keyIspDrag, time.Minute)
		} else {
			nc.Rdb.Expire(ctx, keyIspDrag, time.Hour*24*30)
		}
	}()
	return
}

func (nc *NdsController) DragWoFangClean(ctx context.Context, ip string, projectId int, protectType string, unDragSecond int, createFailedOnly ...bool) (createWofangID int, status int, createStatus string, errInfo string) {

	// 判断当前告警是否正在提交沃防工单
	wfResult, _ := nc.Rdb.Get(ctx, common.Key10010SubmitCleanPrefix+ip).Result()

	// 已在缓存中（已经提交沃防工单）
	if wfResult != "" {
		nc.Logger.Sugar().Infof("%s 正在提交沃防工单", ip)
	} else {
		nc.Rdb.Set(ctx, common.Key10010SubmitCleanPrefix+ip, "1", time.Second*5)

		nc.Logger.Sugar().Infof("提交IP: %s 到沃防", ip)
		nc.Logger.Sugar().Info("【沃防接口】判断是否在引流中/有权限")
		// 判断当前IP是否已提交沃防工单
		dataMap := wofang.NewDrag2("GetDrainList", ip, 0, time.Time{})
		drag, err := http.WoFangDrag2(dataMap)
		nc.Logger.Sugar().Info("【沃防接口】判断结束")
		if err != nil {
			nc.Logger.Sugar().Error(err)
			fmt.Println("沃防接口判断err：", err)
		}
		if unDragSecond == 0 {
			unDragSecond = config.CFG.External.WoFang.AutoUnDrogSecond
		}
		if protectType == "" {
			protectType = "自动"
		}
		w := &ent.Wofang{
			Name:         protectType + "提交",
			IP:           ip,
			Type:         "qy",
			UnDragSecond: unDragSecond,
		}
		if projectId != 0 {
			w.TenantID = &projectId
		}

		// 0 成功/引流中
		// 404 未引流
		// 403 无权限
		if drag == nil || drag.Code == 403 || drag.Code == 500 {
			if drag == nil {
				w.ErrorInfo = "未知错误"
			} else {
				w.ErrorInfo = drag.Message
			}
			nc.Logger.Sugar().Info("提交失败")

			w.Status = "failed"
			createWofang, _ := nc.WofangService.Create(ctx, w)
			createWofangID = createWofang.ID
			status = common.WofangFailed
			createStatus = "失败"
			// Get "http://api2.wofangyun.com/open_api/v3/?AccessKeyId=100796&Action=Drain&DefendIp=************&Duration=60&Nonce=944&SignatureMethod=HMAC-SHA1&Timestamp=1703822401&Version=1.0&Signature=%2BmfvVMoie%2F1Q2NHaPVj4xNZV2dg%3D": net/http: timeout awaiting response headers
			if strings.Contains(w.ErrorInfo, "timeout awaiting response headers") {
				errInfo = "api 请求超时"
			} else {
				errInfo = w.ErrorInfo
			}
		} else if drag.Code == 0 {
			nc.Logger.Sugar().Infof("IP: %s 已在防护中", ip)
			status = common.WofangInProtect
			createStatus = "成功"
			errInfo = w.ErrorInfo
		} else if drag.Code == 404 {
			nc.Logger.Sugar().Infof("IP：%s 未在引流中", ip)
			nc.Logger.Sugar().Info("【沃防接口】开始提交")
			// 未提交工单
			w.StartTime = time.Now()
			dataMap := wofang.NewDrag2("Drain", ip, unDragSecond, time.Time{})
			drag, err := http.WoFangDrag2(dataMap)
			nc.Logger.Sugar().Info("【沃防接口】提交完成")
			if drag == nil || drag.Code != 0 || err != nil {
				nc.Logger.Sugar().Errorf("提交沃防工单失败，%s：%s", ip, err.Error())
			}

			// 添加成功，增加缓存，缓存时间就是 unDragSecond
			var protectMsg string
			if drag != nil && drag.Code == 0 {
				w.Status = "success"
				status = common.WofangSuccess
				protectMsg = fmt.Sprintf("已提交牵引清洗，牵引时长 %d 秒", unDragSecond)
			} else {
				w.Status = "failed"
				if drag != nil {
					w.ErrorInfo = drag.Message
				} else {
					w.ErrorInfo = err.Error()
				}
				protectMsg = "失败"
				status = common.WofangFailed
			}
			createWofang := &ent.Wofang{}
			if len(createFailedOnly) == 0 || status == common.WofangFailed && createFailedOnly[0] {
				createWofang, _ = nc.WofangService.Create(ctx, w)
				nc.Logger.Sugar().Info("沃防工单入库完成")
			}
			createWofangID = createWofang.ID
			createStatus = protectMsg
			errInfo = w.ErrorInfo
		}

		if w.Status == "failed" {
			// 失败
			go func() {
				systemConfig, _ := cache.GetSystemConfigCache(nc.Rdb, nc.SystemConfigService, ctx)
				if strings.Contains(systemConfig.NotifyScenes, "沃防提交牵引失败") {
					content := common.ConcatString("【告警提升】" + common.Prefix + "【沃防提交牵引失败】\nIP：" + ip)
					if !slices.Contains(strings.Split(systemConfig.IpWhitelists, ","), ip) {
						err = http.NotifyPhone(content, strings.Split(systemConfig.NotifyPhones, ","))
					}
					err = http.NotifyPopo(content, strings.Split(systemConfig.NotifyEmails, ","))
					if err != nil {
						nc.Logger.Sugar().Error(err)
					}
					nc.Logger.Sugar().Infof("Notify system config: %s", content)
				}
			}()
		}
	}
	return
}

func (nc *NdsController) UnDragWoFangClean(ctx context.Context, ip string) string {
	// 判断当前IP是否已提交沃防工单
	dataMap := wofang.NewDrag2("GetDrainList", ip, 0, time.Time{})
	drag, err := http.WoFangDrag2(dataMap)
	if err != nil {
		nc.Logger.Sugar().Error(err)
	}
	// 0 成功/引流中
	// 404 未引流
	// 403 无权限
	if drag != nil && drag.Code == 0 {
		dataMap := wofang.NewDrag2("Undrain", ip, 0, time.Time{})
		drag, err := http.WoFangDrag2(dataMap)
		if err != nil {
			nc.Logger.Sugar().Errorf("redis get wofangClean ip：%s", err.Error())
		}
		if drag != nil {
			if drag != nil && drag.Code == 0 {
				return "已完成回牵"
			} else {
				return "回牵失败：" + drag.Message
			}
		}
	}
	return ""
}

func (nc *NdsController) NotifyAlert(ctx context.Context, projectId int, ip, projectCode, title, content string) {
	notifySystem, err := CheckSystemNotify(ctx, nc.NotifyService)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	// 项目id不为空，通知项目
	notifyProject, err := CheckNotifyProject(ctx, projectId, nc.NotifyService)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	_, headerMap, err := nc.AuthV2.GetV2Token()
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
	fmt.Printf("notifySystem:%+v\n", notifySystem)
	err = http.NotifyAll(notifySystem, notifyProject, projectCode, content, title, common.MailSignData, headerMap, ip)
	if err != nil {
		nc.Logger.Sugar().Error(err.Error())
	}
}

func (nc *NdsController) CounterReachMax(ctx context.Context, ip string, stra *ent.Strategy) bool {
	bpsStr, err := nc.Rdb.Get(ctx, common.KeyCounterBpsPrefix+ip).Result()
	if err != nil {
		nc.Logger.Sugar().Errorf("redis get bps count：%s", err.Error())
	}
	fmt.Println("bps count: ", ip, bpsStr)
	bpsCount, err := strconv.Atoi(bpsStr)
	if err != nil {
		return false
	}
	if bpsCount >= stra.BpsCount && stra.Enabled {
		fmt.Println("bps count超过沃防配置策略")
		nc.Logger.Sugar().Infof("%s 分光bps阈值次数：%d 超过沃防配置策略次数：%d", ip, bpsCount, stra.BpsCount)
		return true
	}
	ppsStr, err := nc.Rdb.Get(ctx, common.KeyCounterPpsPrefix+ip).Result()
	if err != nil {
		nc.Logger.Sugar().Errorf("redis get pps count：%s", err.Error())
	}
	fmt.Println("pps count: ", ip, ppsStr)
	ppsCount, err := strconv.Atoi(ppsStr)
	if err != nil {
		return false
	}
	if ppsCount >= stra.PpsCount && stra.Enabled {
		fmt.Println("pps count超过沃防配置策略")
		nc.Logger.Sugar().Infof("%s 分光pps阈值次数：%d 超过沃防配置策略次数：%d", ip, ppsCount, stra.PpsCount)
		return true
	}
	return false
}

func CheckNotifyProject(ctx context.Context, projectId int, dao *service.NotifyService) (*ent.Notify, error) {
	if projectId != 0 {
		notifyProject, err := dao.Dao.Notify.Query().Where(notify.TenantID(projectId), notify.SystemEQ(false)).Only(ctx)
		if err != nil {
			return nil, err
		}
		return notifyProject, nil
	}
	return nil, errors.New("unknown error")
}

func CheckSystemNotify(ctx context.Context, nc *service.NotifyService) (*ent.Notify, error) {
	// 通知系统默认设置
	notifySystem, err := nc.Dao.Notify.Query().Where(notify.SystemEQ(true)).Only(ctx)
	if err != nil {
		fmt.Println("notifySystem ", err)
	}
	if notifySystem == nil {
		ns := &ent.Notify{
			Remark:     nil,
			Name:       "系统通知策略",
			Popo:       true,
			PopoGroups: nil,
			Emails:     &[]string{"<EMAIL>"},
			Phones:     nil,
			System:     true,
			Enabled:    true,
		}
		notifySystem, _ = nc.Create(ctx, ns)
	}
	return notifySystem, nil
}
