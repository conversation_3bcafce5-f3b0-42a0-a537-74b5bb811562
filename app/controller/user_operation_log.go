package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type UserOperationLogController struct {
	UserOperationLogService *service.UserOperationLogService
	Logger                  *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 UserOperationLog
//
//	@Description Query 根据指定字段、时间范围查询或搜索 UserOperationLog
//	@Summary Query 根据指定字段、时间范围查询或搜索 UserOperationLog
//	@Tags UserOperationLog
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param username query string false "username"
// @Param method query string false "method"
// @Param uri query string false "uri"
// @Param request_body query string false "request_body"
// @Param project query string false "project"
// @Param time query string false "time" Format(date-time)
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.UserOperationLog}
//	@Router /api/v1/useroperationlog [get]
func (uolc *UserOperationLogController) Query(c *fiber.Ctx) error {
	uol := &ent.UserOperationLog{}
	qp, err := common.QueryParser(c, uol)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := uolc.UserOperationLogService.Query(ctx, uol, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 UserOperationLog
//
//	@Description QueryByID 根据 ID 查询 UserOperationLog
//	@Summary QueryByID 根据 ID 查询 UserOperationLog
//	@Tags UserOperationLog
//	@Accept json
//	@Produce json
//	@Param id path int true "UserOperationLog ID"
//	@Success 200 {object} common.Result{data=ent.UserOperationLog}
//	@Router /api/v1/useroperationlog/{id} [get]
func (uolc *UserOperationLogController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := uolc.UserOperationLogService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 UserOperationLog
//
//	@Description Create 创建 UserOperationLog
//	@Summary Create 创建 UserOperationLog
//	@Tags UserOperationLog
//	@Accept json
//	@Produce json
//	@Param useroperationlog body ent.UserOperationLog true "UserOperationLog"
//	@Success 200 {object} common.Result{data=ent.UserOperationLog}
//	@Router /api/v1/useroperationlog [post]
func (uolc *UserOperationLogController) Create(c *fiber.Ctx) error {
	uol := &ent.UserOperationLog{}
	err := common.BodyParser(c, uol)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := uolc.UserOperationLogService.Create(ctx, uol)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 UserOperationLog
//
//	@Description CreateBulk 批量创建 UserOperationLog
//	@Summary CreateBulk 批量创建 UserOperationLog
//	@Tags UserOperationLog
//	@Accept json
//	@Produce json
//	@Param useroperationlog body []ent.UserOperationLog true "UserOperationLog"
//	@Success 200 {object} common.Result{data=[]ent.UserOperationLog}
//	@Router /api/v1/useroperationlog/bulk [post]
func (uolc *UserOperationLogController) CreateBulk(c *fiber.Ctx) error {
	uol := make([]*ent.UserOperationLog, 10)
	err := common.RequestBodyParser(c, &uol)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := uolc.UserOperationLogService.CreateBulk(ctx, uol)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 UserOperationLog
//
//	@Description UpdateByID 根据 ID 修改 UserOperationLog
//	@Summary UpdateByID 根据 ID 修改 UserOperationLog
//	@Tags UserOperationLog
//	@Accept json
//	@Produce json
//	@Param id path int true "UserOperationLog ID"
//
// @Param useroperationlog body ent.UserOperationLog true "UserOperationLog"
//
//	@Success 200 {object} common.Result{data=ent.UserOperationLog}
//	@Router /api/v1/useroperationlog/{id} [put]
func (uolc *UserOperationLogController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	uol, err := uolc.UserOperationLogService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, uol)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := uolc.UserOperationLogService.UpdateByID(ctx, uol, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 UserOperationLog
//
//	@Description DeleteByID 根据 ID 删除 UserOperationLog
//	@Summary DeleteByID 根据 ID 删除 UserOperationLog
//	@Tags UserOperationLog
//	@Accept json
//	@Produce json
//	@Param id path int true "UserOperationLog ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/useroperationlog/{id} [delete]
func (uolc *UserOperationLogController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := uolc.UserOperationLogService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 UserOperationLog
//
//	@Description DeleteBulk 根据 IDs 批量删除 UserOperationLog
//	@Summary DeleteBulk 根据 IDs 批量删除 UserOperationLog
//	@Tags UserOperationLog
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/useroperationlog/bulk/delete [post]
func (uolc *UserOperationLogController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = uolc.UserOperationLogService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
