package controller

import (
	"github.com/gofiber/fiber/v2"
	"meta/app/entity/wofang"
	"meta/pkg/common"
	"meta/pkg/http"
)

// Deprecated: 旧沃防api，已失效
type WoFangApi struct {
}

// Query 根据 牵引类型和ip 查询 沃防ip牵引状态
//
//	@Description Query 根据 牵引类型和ip 查询 沃防ip牵引状态
//	@Summary Query 根据 牵引类型和ip 查询 沃防ip牵引状态
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param dragType path string true "牵引类型：hd | qy"
//	@Param ips path string true "查询ip"
//
// @Success 200 {object} common.Result{data=wofang.Response}
// @Router /api/v1/wofang/api [get]
func (wfc *WoFangApi) Query(c *fiber.Ctx) error {
	wfq := &wofang.Query{}
	_, err := common.QueryParser(c, wfq)
	if err != nil {
		return common.NewResult(c, err)
	}
	dataMap := make(map[string]any)
	// 牵引类型: qy；黑洞：hd
	dataMap["qynet"] = wfq.DragType
	dataMap["ips"] = wfq.Ips
	drag, err := http.WoFangDrag("GET", "qystatus", dataMap)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err, drag)
}

// Add 将ip添加到沃防牵引或黑洞清洗
//
//	@Description Add 将ip添加到沃防牵引或黑洞清洗
//	@Summary Add 将ip添加到沃防牵引或黑洞清洗
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param add body wofang.Add true "需要操作的数据"
//
// @Success 200 {object} common.Result{data=wofang.Response}
// @Router /api/v1/wofang/api [post]
func (wfc *WoFangApi) Add(c *fiber.Ctx) error {
	wfq := &wofang.Add{}
	err := common.BodyParser(c, wfq)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.CheckWoFang(wfq.DragType, wfq.Ip)
	if err != nil {
		return common.NewResult(c, err)
	}
	dataMap := wofang.NewDrag(wfq.DragType, wfq.Ip, wfq.UnDragSecond)
	drag, err := http.WoFangDrag("POST", "addqy", dataMap)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err, drag)
}

// Delete 将ip从沃防牵引或黑洞清洗 删除
//
//	@Description Delete 将ip从沃防牵引或黑洞清洗 删除
//	@Summary Delete 将ip从沃防牵引或黑洞清洗 删除
//	@Tags Wofang
//	@Accept json
//	@Produce json
//	@Param dragType path string true "牵引类型"
//	@Param ip path string true "需要删除的ip"
//
// @Success 200 {object} common.Result{data=wofang.Response}
// @Router /api/v1/wofang/api/{dragType}/{ip} [delete]
func (wfc *WoFangApi) Delete(c *fiber.Ctx) error {
	dragType := c.Params("dragType")
	ip := c.Params("ip")
	err := common.CheckWoFang(dragType, ip)
	if err != nil {
		return common.NewResult(c, err)
	}
	dataMap := make(map[string]any)
	// 牵引类型: qy；黑洞：hd
	dataMap["qynet"] = dragType
	dataMap["ip"] = ip
	drag, err := http.WoFangDrag("POST", "delqy", dataMap)
	if err != nil {
		return common.NewResult(c, err)
	}
	return common.NewResult(c, err, drag)
}
