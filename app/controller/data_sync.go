package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
	"sort"
)

type DataSyncController struct {
	DataSyncService *service.DataSyncService
	Logger          *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 DataSync
//
//	@Description Query 根据指定字段、时间范围查询或搜索 DataSync
//	@Summary Query 根据指定字段、时间范围查询或搜索 DataSync
//	@Tags DataSync
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param net_type query string false "net_type"
// @Param type query string false "type"
// @Param region query string false "region"
// @Param source query string false "source"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.DataSync}
//	@Router /api/v1/datasync [get]
func (dsc *DataSyncController) Query(c *fiber.Ctx) error {
	ds := &ent.DataSync{}
	qp, err := common.QueryParser(c, ds)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := dsc.DataSyncService.Query(ctx, ds, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 DataSync
//
//	@Description QueryByID 根据 ID 查询 DataSync
//	@Summary QueryByID 根据 ID 查询 DataSync
//	@Tags DataSync
//	@Accept json
//	@Produce json
//	@Param id path int true "DataSync ID"
//	@Success 200 {object} common.Result{data=ent.DataSync}
//	@Router /api/v1/datasync/{id} [get]
func (dsc *DataSyncController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := dsc.DataSyncService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 DataSync
//
//	@Description Create 创建 DataSync
//	@Summary Create 创建 DataSync
//	@Tags DataSync
//	@Accept json
//	@Produce json
//	@Param datasync body ent.DataSync true "DataSync"
//	@Success 200 {object} common.Result{data=ent.DataSync}
//	@Router /api/v1/datasync [post]
func (dsc *DataSyncController) Create(c *fiber.Ctx) error {
	ds := &ent.DataSync{}
	err := common.BodyParser(c, ds)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := dsc.DataSyncService.Create(ctx, ds)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 DataSync
//
//	@Description CreateBulk 批量创建 DataSync
//	@Summary CreateBulk 批量创建 DataSync
//	@Tags DataSync
//	@Accept json
//	@Produce json
//	@Param datasync body []ent.DataSync true "DataSync"
//	@Success 200 {object} common.Result{data=[]ent.DataSync}
//	@Router /api/v1/datasync/bulk [post]
func (dsc *DataSyncController) CreateBulk(c *fiber.Ctx) error {
	ds := make([]*ent.DataSync, 10)
	err := common.RequestBodyParser(c, &ds)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := dsc.DataSyncService.CreateBulk(ctx, ds)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 DataSync
//
//	@Description UpdateByID 根据 ID 修改 DataSync
//	@Summary UpdateByID 根据 ID 修改 DataSync
//	@Tags DataSync
//	@Accept json
//	@Produce json
//	@Param id path int true "DataSync ID"
//
// @Param datasync body ent.DataSync true "DataSync"
//
//	@Success 200 {object} common.Result{data=ent.DataSync}
//	@Router /api/v1/datasync/{id} [put]
func (dsc *DataSyncController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	ds, err := dsc.DataSyncService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, ds)
	if err != nil {
		return common.NewResult(c, err)
	}
	sort.Strings(*ds.DataList)
	sort.Strings(*ds.PreDataList)
	data, err := dsc.DataSyncService.UpdateByID(ctx, ds, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 DataSync
//
//	@Description DeleteByID 根据 ID 删除 DataSync
//	@Summary DeleteByID 根据 ID 删除 DataSync
//	@Tags DataSync
//	@Accept json
//	@Produce json
//	@Param id path int true "DataSync ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/datasync/{id} [delete]
func (dsc *DataSyncController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := dsc.DataSyncService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 DataSync
//
//	@Description DeleteBulk 根据 IDs 批量删除 DataSync
//	@Summary DeleteBulk 根据 IDs 批量删除 DataSync
//	@Tags DataSync
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/datasync/bulk/delete [post]
func (dsc *DataSyncController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = dsc.DataSyncService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
