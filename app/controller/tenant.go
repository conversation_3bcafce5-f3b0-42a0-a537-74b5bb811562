package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type TenantController struct {
	TenantService *service.TenantService
	Logger        *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 Tenant
//
//	@Description Query 根据指定字段、时间范围查询或搜索 Tenant
//	@Summary Query 根据指定字段、时间范围查询或搜索 Tenant
//	@Tags Tenant
//	@Accept json
//	@Produce json
//	@Param Name query string false "Name"
//
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.Tenant}
//	@Router /api/v1/tenant [get]
func (tc *TenantController) Query(c *fiber.Ctx) error {
	t := &ent.Tenant{}
	qp, err := common.QueryParser(c, t)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := tc.TenantService.Query(ctx, t, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 Tenant
//
//	@Description QueryByID 根据 ID 查询 Tenant
//	@Summary QueryByID 根据 ID 查询 Tenant
//	@Tags Tenant
//	@Accept json
//	@Produce json
//	@Param id path int true "Tenant ID"
//	@Success 200 {object} common.Result{data=ent.Tenant}
//	@Router /api/v1/tenant/{id} [get]
func (tc *TenantController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := tc.TenantService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 Tenant
//
//	@Description Create 创建 Tenant
//	@Summary Create 创建 Tenant
//	@Tags Tenant
//	@Accept json
//	@Produce json
//	@Param tenant body ent.Tenant true "Tenant"
//	@Success 200 {object} common.Result{data=ent.Tenant}
//	@Router /api/v1/tenant [post]
func (tc *TenantController) Create(c *fiber.Ctx) error {
	t := &ent.Tenant{}
	err := common.BodyParser(c, t)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := tc.TenantService.Create(ctx, t)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 Tenant
//
//	@Description CreateBulk 批量创建 Tenant
//	@Summary CreateBulk 批量创建 Tenant
//	@Tags Tenant
//	@Accept json
//	@Produce json
//	@Param tenant body []ent.Tenant true "Tenant"
//	@Success 200 {object} common.Result{data=[]ent.Tenant}
//	@Router /api/v1/tenant/bulk [post]
func (tc *TenantController) CreateBulk(c *fiber.Ctx) error {
	t := make([]*ent.Tenant, 10)
	err := common.RequestBodyParser(c, &t)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := tc.TenantService.CreateBulk(ctx, t)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 Tenant
//
//	@Description UpdateByID 根据 ID 修改 Tenant
//	@Summary UpdateByID 根据 ID 修改 Tenant
//	@Tags Tenant
//	@Accept json
//	@Produce json
//	@Param id path int true "Tenant ID"
//
// @Param tenant body ent.Tenant true "Tenant"
//
//	@Success 200 {object} common.Result{data=ent.Tenant}
//	@Router /api/v1/tenant/{id} [put]
func (tc *TenantController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	t, err := tc.TenantService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, t)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := tc.TenantService.UpdateByID(ctx, t, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 Tenant
//
//	@Description DeleteByID 根据 ID 删除 Tenant
//	@Summary DeleteByID 根据 ID 删除 Tenant
//	@Tags Tenant
//	@Accept json
//	@Produce json
//	@Param id path int true "Tenant ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/tenant/{id} [delete]
func (tc *TenantController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := tc.TenantService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 Tenant
//
//	@Description DeleteBulk 根据 IDs 批量删除 Tenant
//	@Summary DeleteBulk 根据 IDs 批量删除 Tenant
//	@Tags Tenant
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/tenant/bulk/delete [post]
func (tc *TenantController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = tc.TenantService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
