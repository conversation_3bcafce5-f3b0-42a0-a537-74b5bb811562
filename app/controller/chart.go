package controller

import (
	"meta/app/entity/chart"
	"meta/app/entity/netease"
	"meta/pkg/common"
	"meta/pkg/http"

	"github.com/gofiber/fiber/v2"
)

type (
	ChartController struct{}
)

// Query 根据 IP和起始时间 查询 分光流量信息
// @Description Query 根据 IP和起始时间 查询 分光流量信息
// @Summary Query 根据 IP和起始时间 查询 分光流量信息
// @Tags Chart
// @Accept json
// @Produce json
// @Param IP query string false "ip" Format(ipv4)
// @Param startTime query string false "start_time" Format(date-time)
// @Param endTime query string false "end_time" Format(date-time)
// @Success 200 {object} common.Result{data=[]chart.LineData}
// @Router /api/v1/chart/spectrum [get]
func (cc *ChartController) Query(c *fiber.Ctx) error {
	sqi := &netease.SpectrumQueryInfo{}
	_, err := common.QueryParser(c, sqi)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.ValidateIP(sqi.IP)
	if err != nil {
		return common.NewResult(c, err)
	}
	gsd, err := http.GetRealTimeSpectrumData(sqi.IP, sqi.StartTime, sqi.EndTime)
	var result []chart.LineData
	for _, v := range gsd {
		t := common.Timestamp2Time(v.Time)
		charData := &chart.LineData{Time: t}
		result = append(result, *transData("bps", v.Bps, charData))
		result = append(result, *transData("pps", v.Pps, charData))
		result = append(result, *transData("syn_bps", v.SynBps, charData))
		result = append(result, *transData("syn_pps", v.SynPps, charData))
		result = append(result, *transData("ack_bps", v.AckBps, charData))
		result = append(result, *transData("ack_pps", v.AckPps, charData))
		result = append(result, *transData("syn_ack_bps", v.SynAckBps, charData))
		result = append(result, *transData("syn_ack_pps", v.SynAckPps, charData))
		result = append(result, *transData("icmp_bps", v.IcmpBps, charData))
		result = append(result, *transData("icmp_pps", v.IcmpPps, charData))
		result = append(result, *transData("small_pps", v.SmallPps, charData))
		result = append(result, *transData("ntp_bps", v.NtpBps, charData))
		result = append(result, *transData("ntp_pps", v.NtpPps, charData))
		result = append(result, *transData("dns_query_pps", v.DnsQueryPps, charData))
		result = append(result, *transData("dns_answer_bps", v.DnsAnswerBps, charData))
		result = append(result, *transData("dns_answer_pps", v.DnsAnswerPps, charData))
		result = append(result, *transData("ssdp_bps", v.SsdpBps, charData))
		result = append(result, *transData("ssdp_pps", v.SsdpPps, charData))
		result = append(result, *transData("udp_bps", v.UdpBps, charData))
		result = append(result, *transData("udp_pps", v.UdpPps, charData))
		result = append(result, *transData("qps", v.Qps, charData))
		result = append(result, *transData("receive_count", int64(v.ReceiveCount), charData))
	}
	return common.NewResult(c, err, result)
}

func transData(name string, value int64, chartData *chart.LineData) *chart.LineData {
	chartData.Name = name
	chartData.Value = value
	return chartData
}
