package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type CloudAttackDataController struct {
	CloudAttackDataService *service.CloudAttackDataService
	Logger                 *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 CloudAttackData
//
//	@Description Query 根据指定字段、时间范围查询或搜索 CloudAttackData
//	@Summary Query 根据指定字段、时间范围查询或搜索 CloudAttackData
//	@Tags CloudAttackData
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param src_ip query string false "src_ip"
// @Param src_port query integer false "src_port"
// @Param dst_ip query string false "dst_ip"
// @Param dst_port query integer false "dst_port"
// @Param protocol query integer false "protocol"
// @Param current_attack_pps query integer false "current_attack_pps"
// @Param start_time query string false "start_time" Format(date-time)
// @Param end_time query string false "end_time" Format(date-time)
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.CloudAttackData}
//	@Router /api/v1/cloudattackdata [get]
func (cadc *CloudAttackDataController) Query(c *fiber.Ctx) error {
	cad := &ent.CloudAttackData{}
	qp, err := common.QueryParser(c, cad)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := cadc.CloudAttackDataService.Query(ctx, cad, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 CloudAttackData
//
//	@Description QueryByID 根据 ID 查询 CloudAttackData
//	@Summary QueryByID 根据 ID 查询 CloudAttackData
//	@Tags CloudAttackData
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudAttackData ID"
//	@Success 200 {object} common.Result{data=ent.CloudAttackData}
//	@Router /api/v1/cloudattackdata/{id} [get]
func (cadc *CloudAttackDataController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := cadc.CloudAttackDataService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 CloudAttackData
//
//	@Description Create 创建 CloudAttackData
//	@Summary Create 创建 CloudAttackData
//	@Tags CloudAttackData
//	@Accept json
//	@Produce json
//	@Param cloudattackdata body ent.CloudAttackData true "CloudAttackData"
//	@Success 200 {object} common.Result{data=ent.CloudAttackData}
//	@Router /api/v1/cloudattackdata [post]
func (cadc *CloudAttackDataController) Create(c *fiber.Ctx) error {
	cad := &ent.CloudAttackData{}
	err := common.BodyParser(c, cad)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := cadc.CloudAttackDataService.Create(ctx, cad)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 CloudAttackData
//
//	@Description CreateBulk 批量创建 CloudAttackData
//	@Summary CreateBulk 批量创建 CloudAttackData
//	@Tags CloudAttackData
//	@Accept json
//	@Produce json
//	@Param cloudattackdata body []ent.CloudAttackData true "CloudAttackData"
//	@Success 200 {object} common.Result{data=[]ent.CloudAttackData}
//	@Router /api/v1/cloudattackdata/bulk [post]
func (cadc *CloudAttackDataController) CreateBulk(c *fiber.Ctx) error {
	cad := make([]*ent.CloudAttackData, 10)
	err := common.RequestBodyParser(c, &cad)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := cadc.CloudAttackDataService.CreateBulk(ctx, cad)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 CloudAttackData
//
//	@Description UpdateByID 根据 ID 修改 CloudAttackData
//	@Summary UpdateByID 根据 ID 修改 CloudAttackData
//	@Tags CloudAttackData
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudAttackData ID"
//
// @Param cloudattackdata body ent.CloudAttackData true "CloudAttackData"
//
//	@Success 200 {object} common.Result{data=ent.CloudAttackData}
//	@Router /api/v1/cloudattackdata/{id} [put]
func (cadc *CloudAttackDataController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	cad, err := cadc.CloudAttackDataService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, cad)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := cadc.CloudAttackDataService.UpdateByID(ctx, cad, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 CloudAttackData
//
//	@Description DeleteByID 根据 ID 删除 CloudAttackData
//	@Summary DeleteByID 根据 ID 删除 CloudAttackData
//	@Tags CloudAttackData
//	@Accept json
//	@Produce json
//	@Param id path int true "CloudAttackData ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/cloudattackdata/{id} [delete]
func (cadc *CloudAttackDataController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := cadc.CloudAttackDataService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 CloudAttackData
//
//	@Description DeleteBulk 根据 IDs 批量删除 CloudAttackData
//	@Summary DeleteBulk 根据 IDs 批量删除 CloudAttackData
//	@Tags CloudAttackData
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/cloudattackdata/bulk/delete [post]
func (cadc *CloudAttackDataController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = cadc.CloudAttackDataService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
