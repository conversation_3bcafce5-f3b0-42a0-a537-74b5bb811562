package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/entity"
	"meta/app/service"
	"meta/pkg/common"
)

type SpectrumAlertController struct {
	SpectrumAlertService *service.SpectrumAlertService
	Logger               *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 SpectrumAlert
//
//	@Description Query 根据指定字段、时间范围查询或搜索 SpectrumAlert
//	@Summary Query 根据指定字段、时间范围查询或搜索 SpectrumAlert
//	@Tags SpectrumAlert
//	@Accept json
//	@Produce json
//	@Param Remark query string false "Remark"
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param IP query string false "IP" Format(ipv4)
// @Param StartTime query string false "StartTime" Format(date-time)
// @Param EndTime query string false "EndTime" Format(date-time)
// @Param AttackType query string false "AttackType"
// @Param Source query string false "Source"
// @Param MaxPps query string false "MaxPps"
// @Param MaxBps query string false "MaxBps"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.SpectrumAlert}
//	@Router /api/v1/spectrumalert [get]
func (sac *SpectrumAlertController) Query(c *fiber.Ctx) error {
	sa := &ent.SpectrumAlert{}
	qp, err := common.QueryParser(c, sa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := sac.SpectrumAlertService.Query(ctx, sa, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 SpectrumAlert
//
//	@Description QueryByID 根据 ID 查询 SpectrumAlert
//	@Summary QueryByID 根据 ID 查询 SpectrumAlert
//	@Tags SpectrumAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "SpectrumAlert ID"
//	@Success 200 {object} common.Result{data=ent.SpectrumAlert}
//	@Router /api/v1/spectrumalert/{id} [get]
func (sac *SpectrumAlertController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := sac.SpectrumAlertService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 SpectrumAlert
//
//	@Description Create 创建 SpectrumAlert
//	@Summary Create 创建 SpectrumAlert
//	@Tags SpectrumAlert
//	@Accept json
//	@Produce json
//	@Param spectrumalert body ent.SpectrumAlert true "SpectrumAlert"
//	@Success 200 {object} common.Result{data=ent.SpectrumAlert}
//	@Router /api/v1/spectrumalert [post]
func (sac *SpectrumAlertController) Create(c *fiber.Ctx) error {
	sa := &ent.SpectrumAlert{}
	err := common.BodyParser(c, sa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := sac.SpectrumAlertService.Create(ctx, sa)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 SpectrumAlert
//
//	@Description CreateBulk 批量创建 SpectrumAlert
//	@Summary CreateBulk 批量创建 SpectrumAlert
//	@Tags SpectrumAlert
//	@Accept json
//	@Produce json
//	@Param spectrumalert body []ent.SpectrumAlert true "SpectrumAlert"
//	@Success 200 {object} common.Result{data=[]ent.SpectrumAlert}
//	@Router /api/v1/spectrumalert/bulk [post]
func (sac *SpectrumAlertController) CreateBulk(c *fiber.Ctx) error {
	sa := make([]*ent.SpectrumAlert, 10)
	err := common.RequestBodyParser(c, &sa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := sac.SpectrumAlertService.CreateBulk(ctx, sa)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 SpectrumAlert
//
//	@Description UpdateByID 根据 ID 修改 SpectrumAlert
//	@Summary UpdateByID 根据 ID 修改 SpectrumAlert
//	@Tags SpectrumAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "SpectrumAlert ID"
//
// @Param spectrumalert body ent.SpectrumAlert true "SpectrumAlert"
//
//	@Success 200 {object} common.Result{data=ent.SpectrumAlert}
//	@Router /api/v1/spectrumalert/{id} [put]
func (sac *SpectrumAlertController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	sa, err := sac.SpectrumAlertService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, sa)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := sac.SpectrumAlertService.UpdateByID(ctx, sa, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 SpectrumAlert
//
//	@Description DeleteByID 根据 ID 删除 SpectrumAlert
//	@Summary DeleteByID 根据 ID 删除 SpectrumAlert
//	@Tags SpectrumAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "SpectrumAlert ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/spectrumalert/{id} [delete]
func (sac *SpectrumAlertController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := sac.SpectrumAlertService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 SpectrumAlert
//
//	@Description DeleteBulk 根据 IDs 批量删除 SpectrumAlert
//	@Summary DeleteBulk 根据 IDs 批量删除 SpectrumAlert
//	@Tags SpectrumAlert
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/spectrumalert/bulk/delete [post]
func (sac *SpectrumAlertController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = sac.SpectrumAlertService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}

// GetAttackData 获取攻击数据  SpectrumAlert
//
//	@Description Query 获取攻击数据 SpectrumAlert
//	@Summary Query 获取攻击数据 SpectrumAlert
//	@Tags SpectrumAlert
//	@Accept json
//	@Produce json
//
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.SpectrumAlert}
//	@Router /api/v1/spectrumalert/attacking [get]
func (sac *SpectrumAlertController) GetAttackData(c *fiber.Ctx) error {
	sa := &ent.SpectrumAlert{}
	attack := &entity.AttackQuery{}
	qp, err := common.QueryParser(c, attack)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	var count int
	var result []*ent.SpectrumAlert
	if !attack.Attacking {
		count, result, err = sac.SpectrumAlertService.GetEndTimeNil(ctx, sa, qp)
	} else {
		count, result, err = sac.SpectrumAlertService.GetEndTimeNotNil(ctx, sa, qp)
	}
	return common.NewPageResult(c, err, count, result)
}
