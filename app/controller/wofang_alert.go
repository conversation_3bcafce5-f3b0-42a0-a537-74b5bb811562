package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type WofangAlertController struct {
	WofangAlertService *service.WofangAlertService
	Logger             *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 WofangAlert
//
//	@Description Query 根据指定字段、时间范围查询或搜索 WofangAlert
//	@Summary Query 根据指定字段、时间范围查询或搜索 WofangAlert
//	@Tags WofangAlert
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param attack_status query string false "attack_status"
// @Param attack_type query string false "attack_type"
// @Param device_ip query string false "device_ip"
// @Param zone_ip query string false "zone_ip"
// @Param start_time query string false "start_time" Format(date-time)
// @Param end_time query string false "end_time" Format(date-time)
// @Param max_drop_kbps query integer false "max_drop_kbps"
// @Param max_in_kbps query integer false "max_in_kbps"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.WofangAlert}
//	@Router /api/v1/wofangalert [get]
func (wac *WofangAlertController) Query(c *fiber.Ctx) error {
	wa := &ent.WofangAlert{}
	qp, err := common.QueryParser(c, wa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := wac.WofangAlertService.Query(ctx, wa, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 WofangAlert
//
//	@Description QueryByID 根据 ID 查询 WofangAlert
//	@Summary QueryByID 根据 ID 查询 WofangAlert
//	@Tags WofangAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "WofangAlert ID"
//	@Success 200 {object} common.Result{data=ent.WofangAlert}
//	@Router /api/v1/wofangalert/{id} [get]
func (wac *WofangAlertController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := wac.WofangAlertService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 WofangAlert
//
//	@Description Create 创建 WofangAlert
//	@Summary Create 创建 WofangAlert
//	@Tags WofangAlert
//	@Accept json
//	@Produce json
//	@Param wofangalert body ent.WofangAlert true "WofangAlert"
//	@Success 200 {object} common.Result{data=ent.WofangAlert}
//	@Router /api/v1/wofangalert [post]
func (wac *WofangAlertController) Create(c *fiber.Ctx) error {
	wa := &ent.WofangAlert{}
	err := common.BodyParser(c, wa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := wac.WofangAlertService.Create(ctx, wa)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 WofangAlert
//
//	@Description CreateBulk 批量创建 WofangAlert
//	@Summary CreateBulk 批量创建 WofangAlert
//	@Tags WofangAlert
//	@Accept json
//	@Produce json
//	@Param wofangalert body []ent.WofangAlert true "WofangAlert"
//	@Success 200 {object} common.Result{data=[]ent.WofangAlert}
//	@Router /api/v1/wofangalert/bulk [post]
func (wac *WofangAlertController) CreateBulk(c *fiber.Ctx) error {
	wa := make([]*ent.WofangAlert, 10)
	err := common.RequestBodyParser(c, &wa)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := wac.WofangAlertService.CreateBulk(ctx, wa)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 WofangAlert
//
//	@Description UpdateByID 根据 ID 修改 WofangAlert
//	@Summary UpdateByID 根据 ID 修改 WofangAlert
//	@Tags WofangAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "WofangAlert ID"
//
// @Param wofangalert body ent.WofangAlert true "WofangAlert"
//
//	@Success 200 {object} common.Result{data=ent.WofangAlert}
//	@Router /api/v1/wofangalert/{id} [put]
func (wac *WofangAlertController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	wa, err := wac.WofangAlertService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, wa)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := wac.WofangAlertService.UpdateByID(ctx, wa, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 WofangAlert
//
//	@Description DeleteByID 根据 ID 删除 WofangAlert
//	@Summary DeleteByID 根据 ID 删除 WofangAlert
//	@Tags WofangAlert
//	@Accept json
//	@Produce json
//	@Param id path int true "WofangAlert ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/wofangalert/{id} [delete]
func (wac *WofangAlertController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := wac.WofangAlertService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 WofangAlert
//
//	@Description DeleteBulk 根据 IDs 批量删除 WofangAlert
//	@Summary DeleteBulk 根据 IDs 批量删除 WofangAlert
//	@Tags WofangAlert
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/wofangalert/bulk/delete [post]
func (wac *WofangAlertController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = wac.WofangAlertService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
