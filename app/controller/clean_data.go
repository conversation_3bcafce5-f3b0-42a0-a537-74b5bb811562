package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type CleanDataController struct {
	CleanDataService *service.CleanDataService
	Logger           *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 CleanData
//
//	@Description Query 根据指定字段、时间范围查询或搜索 CleanData
//	@Summary Query 根据指定字段、时间范围查询或搜索 CleanData
//	@Tags CleanData
//	@Accept json
//	@Produce json
//	@Param IP query string false "ip" Format(ipv4)
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param Time query string false "Time" Format(date-time)
// @Param InBps query integer false "InBps"
// @Param OutBps query integer false "OutBps"
// @Param InPps query integer false "InPps"
// @Param OutPps query integer false "OutPps"
// @Param InAckPps query integer false "InAckPps"
// @Param OutAckPps query integer false "OutAckPps"
// @Param InAckBps query integer false "InAckBps"
// @Param OutAckBps query integer false "OutAckBps"
// @Param InSynPps query integer false "InSynPps"
// @Param OutSynPps query integer false "OutSynPps"
// @Param InUdpPps query integer false "InUdpPps"
// @Param OutUdpPps query integer false "OutUdpPps"
// @Param InUdpBps query integer false "InUdpBps"
// @Param OutUdpBps query integer false "OutUdpBps"
// @Param InIcmpPps query integer false "InIcmpPps"
// @Param InIcmpBps query integer false "InIcmpBps"
// @Param OutIcmpBps query integer false "OutIcmpBps"
// @Param OutIcmpPps query integer false "OutIcmpPps"
// @Param InDnsPps query integer false "InDnsPps"
// @Param OutDnsPps query integer false "OutDnsPps"
// @Param InDnsBps query integer false "InDnsBps"
// @Param OutDnsBps query integer false "OutDnsBps"
// @Param AttackFlags query integer false "AttackFlags"
// @Param Count query integer false "Count"
// @Param IpType query integer false "IpType"
// @Param FFilter query string false "FFilter"
// @Param Host query string false "Host"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.CleanData}
//	@Router /api/v1/cleandata [get]
func (cdc *CleanDataController) Query(c *fiber.Ctx) error {
	cd := &ent.CleanData{}
	qp, err := common.QueryParser(c, cd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := cdc.CleanDataService.Query(ctx, cd, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 CleanData
//
//	@Description QueryByID 根据 ID 查询 CleanData
//	@Summary QueryByID 根据 ID 查询 CleanData
//	@Tags CleanData
//	@Accept json
//	@Produce json
//	@Param id path int true "CleanData ID"
//	@Success 200 {object} common.Result{data=ent.CleanData}
//	@Router /api/v1/cleandata/{id} [get]
func (cdc *CleanDataController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := cdc.CleanDataService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 CleanData
//
//	@Description Create 创建 CleanData
//	@Summary Create 创建 CleanData
//	@Tags CleanData
//	@Accept json
//	@Produce json
//	@Param cleandata body ent.CleanData true "CleanData"
//	@Success 200 {object} common.Result{data=ent.CleanData}
//	@Router /api/v1/cleandata [post]
func (cdc *CleanDataController) Create(c *fiber.Ctx) error {
	cd := &ent.CleanData{}
	err := common.BodyParser(c, cd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := cdc.CleanDataService.Create(ctx, cd)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 CleanData
//
//	@Description CreateBulk 批量创建 CleanData
//	@Summary CreateBulk 批量创建 CleanData
//	@Tags CleanData
//	@Accept json
//	@Produce json
//	@Param cleandata body []ent.CleanData true "CleanData"
//	@Success 200 {object} common.Result{data=[]ent.CleanData}
//	@Router /api/v1/cleandata/bulk [post]
func (cdc *CleanDataController) CreateBulk(c *fiber.Ctx) error {
	cd := make([]*ent.CleanData, 10)
	err := common.RequestBodyParser(c, &cd)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := cdc.CleanDataService.CreateBulk(ctx, cd)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 CleanData
//
//	@Description UpdateByID 根据 ID 修改 CleanData
//	@Summary UpdateByID 根据 ID 修改 CleanData
//	@Tags CleanData
//	@Accept json
//	@Produce json
//	@Param id path int true "CleanData ID"
//
// @Param cleandata body ent.CleanData true "CleanData"
//
//	@Success 200 {object} common.Result{data=ent.CleanData}
//	@Router /api/v1/cleandata/{id} [put]
func (cdc *CleanDataController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	cd, err := cdc.CleanDataService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, cd)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := cdc.CleanDataService.UpdateByID(ctx, cd, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 CleanData
//
//	@Description DeleteByID 根据 ID 删除 CleanData
//	@Summary DeleteByID 根据 ID 删除 CleanData
//	@Tags CleanData
//	@Accept json
//	@Produce json
//	@Param id path int true "CleanData ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/cleandata/{id} [delete]
func (cdc *CleanDataController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := cdc.CleanDataService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 CleanData
//
//	@Description DeleteBulk 根据 IDs 批量删除 CleanData
//	@Summary DeleteBulk 根据 IDs 批量删除 CleanData
//	@Tags CleanData
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/cleandata/bulk/delete [post]
func (cdc *CleanDataController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = cdc.CleanDataService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
