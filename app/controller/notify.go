package controller

import (
	"context"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"meta/app/ent"
	"meta/app/service"
	"meta/pkg/common"
)

type NotifyController struct {
	NotifyService *service.NotifyService
	Logger        *zap.Logger
}

// Query 根据指定字段、时间范围查询或搜索 Notify
//
//	@Description Query 根据指定字段、时间范围查询或搜索 Notify
//	@Summary Query 根据指定字段、时间范围查询或搜索 Notify
//	@Tags Notify
//	@Accept json
//	@Produce json
//	@Param created_at query string false "created_at" Format(date-time)
//
// @Param updated_at query string false "updated_at" Format(date-time)
// @Param remark query string false "remark"
// @Param name query string false "name"
// @Param popo query bool false "popo"
// @Param email query bool false "email"
// @Param sms query bool false "sms"
// @Param phone query bool false "phone"
// @Param system query bool false "system"
// @Param enabled query bool false "enabled"
// @Param sa_notify_popo query bool false "sa_notify_popo"
// @Param sa_notify_email query bool false "sa_notify_email"
// @Param search query string false "需要搜索的值，多个值英文逗号,分隔"
// @Param current query integer false "当前页"
// @Param pageSize query integer false "分页大小"
// @Param order query string false "排序，默认id逆序(-id)"
//
//	@Success 200 {object} common.Result{data=[]ent.Notify}
//	@Router /api/v1/notify [get]
func (nc *NotifyController) Query(c *fiber.Ctx) error {
	n := &ent.Notify{}
	qp, err := common.QueryParser(c, n)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	count, result, err := nc.NotifyService.Query(ctx, n, qp)
	return common.NewPageResult(c, err, count, result)
}

// QueryByID 根据 ID 查询 Notify
//
//	@Description QueryByID 根据 ID 查询 Notify
//	@Summary QueryByID 根据 ID 查询 Notify
//	@Tags Notify
//	@Accept json
//	@Produce json
//	@Param id path int true "Notify ID"
//	@Success 200 {object} common.Result{data=ent.Notify}
//	@Router /api/v1/notify/{id} [get]
func (nc *NotifyController) QueryByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	result, err := nc.NotifyService.QueryByID(ctx, id)
	return common.NewResult(c, err, result)
}

// Create 创建 Notify
//
//	@Description Create 创建 Notify
//	@Summary Create 创建 Notify
//	@Tags Notify
//	@Accept json
//	@Produce json
//	@Param notify body ent.Notify true "Notify"
//	@Success 200 {object} common.Result{data=ent.Notify}
//	@Router /api/v1/notify [post]
func (nc *NotifyController) Create(c *fiber.Ctx) error {
	n := &ent.Notify{}
	err := common.BodyParser(c, n)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	create, err := nc.NotifyService.Create(ctx, n)
	return common.NewResult(c, err, create)
}

// CreateBulk 批量创建 Notify
//
//	@Description CreateBulk 批量创建 Notify
//	@Summary CreateBulk 批量创建 Notify
//	@Tags Notify
//	@Accept json
//	@Produce json
//	@Param notify body []ent.Notify true "Notify"
//	@Success 200 {object} common.Result{data=[]ent.Notify}
//	@Router /api/v1/notify/bulk [post]
func (nc *NotifyController) CreateBulk(c *fiber.Ctx) error {
	n := make([]*ent.Notify, 10)
	err := common.RequestBodyParser(c, &n)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	bulkData, err := nc.NotifyService.CreateBulk(ctx, n)
	return common.NewResult(c, err, bulkData)
}

// UpdateByID 根据 ID 修改 Notify
//
//	@Description UpdateByID 根据 ID 修改 Notify
//	@Summary UpdateByID 根据 ID 修改 Notify
//	@Tags Notify
//	@Accept json
//	@Produce json
//	@Param id path int true "Notify ID"
//
// @Param notify body ent.Notify true "Notify"
//
//	@Success 200 {object} common.Result{data=ent.Notify}
//	@Router /api/v1/notify/{id} [put]
func (nc *NotifyController) UpdateByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	n, err := nc.NotifyService.QueryByID(ctx, id)
	if err != nil {
		return common.NewResult(c, err)
	}
	err = common.BodyParser(c, n)
	if err != nil {
		return common.NewResult(c, err)
	}
	data, err := nc.NotifyService.UpdateByID(ctx, n, id)
	return common.NewResult(c, err, data)
}

// DeleteByID 根据 ID 删除 Notify
//
//	@Description DeleteByID 根据 ID 删除 Notify
//	@Summary DeleteByID 根据 ID 删除 Notify
//	@Tags Notify
//	@Accept json
//	@Produce json
//	@Param id path int true "Notify ID"
//
//	@Success 200 {object} common.Message
//	@Router /api/v1/notify/{id} [delete]
func (nc *NotifyController) DeleteByID(c *fiber.Ctx) error {
	id := c.Locals("id").(int)
	ctx := c.Locals("ctx").(context.Context)
	err := nc.NotifyService.DeleteByID(ctx, id)
	return common.NewResult(c, err)
}

// DeleteBulk 根据 IDs 批量删除 Notify
//
//	@Description DeleteBulk 根据 IDs 批量删除 Notify
//	@Summary DeleteBulk 根据 IDs 批量删除 Notify
//	@Tags Notify
//	@Accept json
//	@Produce json
//	@Param ids body common.DeleteItem true "需要删除的id列表"
//	@Success 200 {object} common.Message
//	@Router /api/v1/notify/bulk/delete [post]
func (nc *NotifyController) DeleteBulk(c *fiber.Ctx) error {
	deleteItem := &common.DeleteItem{}
	err := common.RequestBodyParser(c, &deleteItem)
	if err != nil {
		return common.NewResult(c, err)
	}
	ctx := c.Locals("ctx").(context.Context)
	_, err = nc.NotifyService.DeleteBulk(ctx, deleteItem.Ids)
	return common.NewResult(c, err)
}
