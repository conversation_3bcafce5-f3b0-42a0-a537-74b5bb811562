package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/spectrumdata"
	"meta/app/entity"
)

type SpectrumDataService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 SpectrumData
func (sds *SpectrumDataService) Query(ctx context.Context, sd *ent.SpectrumData, qp *entity.QueryParam) (int, []*ent.SpectrumData, error) {
	if len(qp.Search) == 0 {
		return sds.QueryPage(ctx, sd, qp)
	} else {
		return sds.QuerySearch(ctx, sd, qp)
	}
}

// QueryByID 根据 ID 查询 SpectrumData
func (sds *SpectrumDataService) QueryByID(ctx context.Context, id int) (*ent.SpectrumData, error) {
	return sds.Dao.SpectrumData.Query().Where(spectrumdata.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 SpectrumData
func (sds *SpectrumDataService) Create(ctx context.Context, sd *ent.SpectrumData) (*ent.SpectrumData, error) {
	return sds.Dao.SpectrumData.Create().SetItemSpectrumData(sd).Save(ctx)
}

// CreateBulk 批量创建 SpectrumData
func (sds *SpectrumDataService) CreateBulk(ctx context.Context, sd []*ent.SpectrumData) ([]*ent.SpectrumData, error) {
	bulks := make([]*ent.SpectrumDataCreate, len(sd))
	for i, v := range sd {
		bulks[i] = sds.Dao.SpectrumData.Create().SetItemSpectrumData(v)
	}
	return sds.Dao.SpectrumData.CreateBulk(bulks...).Save(ctx)
}

// CreateBulk2 批量创建 包含分光告警的SpectrumData
func (sds *SpectrumDataService) CreateBulk2(ctx context.Context, sd []*ent.SpectrumData, id int) ([]*ent.SpectrumData, error) {
	bulks := make([]*ent.SpectrumDataCreate, len(sd))
	for i, v := range sd {
		bulks[i] = sds.Dao.SpectrumData.Create().SetItemSpectrumData(v).SetSpectrumAlertID(id)
	}
	return sds.Dao.SpectrumData.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 SpectrumData
func (sds *SpectrumDataService) UpdateByID(ctx context.Context, sd *ent.SpectrumData, id int) (*ent.SpectrumData, error) {
	return sds.Dao.SpectrumData.UpdateOneID(id).SetItemSpectrumData(sd).Save(ctx)
}

// DeleteByID 根据 ID 删除 SpectrumData
func (sds *SpectrumDataService) DeleteByID(ctx context.Context, id int) error {
	return sds.Dao.SpectrumData.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 SpectrumData
func (sds *SpectrumDataService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := sds.Dao.SpectrumData.Delete().Where(spectrumdata.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 SpectrumData
func (sds *SpectrumDataService) QueryPage(ctx context.Context, sd *ent.SpectrumData, qp *entity.QueryParam) (int, []*ent.SpectrumData, error) {
	count, err := sds.Dao.SpectrumData.Query().QueryItemSpectrumData(sd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sds.Dao.SpectrumData.Query().QueryItemSpectrumData(sd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 SpectrumData
func (sds *SpectrumDataService) QuerySearch(ctx context.Context, sd *ent.SpectrumData, qp *entity.QueryParam) (int, []*ent.SpectrumData, error) {
	count, err := sds.Dao.SpectrumData.Query().SearchSpectrumData(sd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sds.Dao.SpectrumData.Query().SearchSpectrumData(sd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
