package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/strategy"
	"meta/app/entity"
)

type StrategyService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 Strategy
func (ss *StrategyService) Query(ctx context.Context, s *ent.Strategy, qp *entity.QueryParam) (int, []*ent.Strategy, error) {
	if len(qp.Search) == 0 {
		return ss.QueryPage(ctx, s, qp)
	} else {
		return ss.QuerySearch(ctx, s, qp)
	}
}

// QueryByID 根据 ID 查询 Strategy
func (ss *StrategyService) QueryByID(ctx context.Context, id int) (*ent.Strategy, error) {
	return ss.Dao.Strategy.Query().Where(strategy.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 Strategy
func (ss *StrategyService) Create(ctx context.Context, s *ent.Strategy) (*ent.Strategy, error) {
	return ss.Dao.Strategy.Create().SetItemStrategy(s).Save(ctx)
}

// CreateBulk 批量创建 Strategy
func (ss *StrategyService) CreateBulk(ctx context.Context, s []*ent.Strategy) ([]*ent.Strategy, error) {
	bulks := make([]*ent.StrategyCreate, len(s))
	for i, v := range s {
		bulks[i] = ss.Dao.Strategy.Create().SetItemStrategy(v)
	}
	return ss.Dao.Strategy.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 Strategy
func (ss *StrategyService) UpdateByID(ctx context.Context, s *ent.Strategy, id int) (*ent.Strategy, error) {
	return ss.Dao.Strategy.UpdateOneID(id).SetItemStrategy(s).Save(ctx)
}

// DeleteByID 根据 ID 删除 Strategy
func (ss *StrategyService) DeleteByID(ctx context.Context, id int) error {
	return ss.Dao.Strategy.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 Strategy
func (ss *StrategyService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := ss.Dao.Strategy.Delete().Where(strategy.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 Strategy
func (ss *StrategyService) QueryPage(ctx context.Context, s *ent.Strategy, qp *entity.QueryParam) (int, []*ent.Strategy, error) {
	count, err := ss.Dao.Strategy.Query().QueryItemStrategy(s, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := ss.Dao.Strategy.Query().QueryItemStrategy(s, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 Strategy
func (ss *StrategyService) QuerySearch(ctx context.Context, s *ent.Strategy, qp *entity.QueryParam) (int, []*ent.Strategy, error) {
	count, err := ss.Dao.Strategy.Query().SearchStrategy(s, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := ss.Dao.Strategy.Query().SearchStrategy(s, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
