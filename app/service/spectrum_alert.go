package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/spectrumalert"
	"meta/app/entity"
)

type SpectrumAlertService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 SpectrumAlert
func (sas *SpectrumAlertService) Query(ctx context.Context, sa *ent.SpectrumAlert, qp *entity.QueryParam) (int, []*ent.SpectrumAlert, error) {
	if len(qp.Search) == 0 {
		return sas.QueryPage(ctx, sa, qp)
	} else {
		return sas.QuerySearch(ctx, sa, qp)
	}
}

// QueryByID 根据 ID 查询 SpectrumAlert
func (sas *SpectrumAlertService) QueryByID(ctx context.Context, id int) (*ent.SpectrumAlert, error) {
	return sas.Dao.SpectrumAlert.Query().Where(spectrumalert.ID(id)).WithTenant().WithWofangTicket().WithProtectGroup().Only(ctx)

}

// Create 创建 SpectrumAlert
func (sas *SpectrumAlertService) Create(ctx context.Context, sa *ent.SpectrumAlert) (*ent.SpectrumAlert, error) {
	return sas.Dao.SpectrumAlert.Create().SetItemSpectrumAlert(sa).Save(ctx)
}

// CreateBulk 批量创建 SpectrumAlert
func (sas *SpectrumAlertService) CreateBulk(ctx context.Context, sa []*ent.SpectrumAlert) ([]*ent.SpectrumAlert, error) {
	bulks := make([]*ent.SpectrumAlertCreate, len(sa))
	for i, v := range sa {
		bulks[i] = sas.Dao.SpectrumAlert.Create().SetItemSpectrumAlert(v)
	}
	return sas.Dao.SpectrumAlert.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 SpectrumAlert
func (sas *SpectrumAlertService) UpdateByID(ctx context.Context, sa *ent.SpectrumAlert, id int) (*ent.SpectrumAlert, error) {
	return sas.Dao.SpectrumAlert.UpdateOneID(id).SetItemSpectrumAlert(sa).Save(ctx)
}

// DeleteByID 根据 ID 删除 SpectrumAlert
func (sas *SpectrumAlertService) DeleteByID(ctx context.Context, id int) error {
	return sas.Dao.SpectrumAlert.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 SpectrumAlert
func (sas *SpectrumAlertService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := sas.Dao.SpectrumAlert.Delete().Where(spectrumalert.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 SpectrumAlert
func (sas *SpectrumAlertService) QueryPage(ctx context.Context, sa *ent.SpectrumAlert, qp *entity.QueryParam) (int, []*ent.SpectrumAlert, error) {
	count, err := sas.Dao.SpectrumAlert.Query().QueryItemSpectrumAlert(sa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sas.Dao.SpectrumAlert.Query().QueryItemSpectrumAlert(sa, qp, false).WithTenant().WithWofangTicket(selectWofangFields()).WithProtectGroup(selectProtecGroupFields()).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 SpectrumAlert
func (sas *SpectrumAlertService) QuerySearch(ctx context.Context, sa *ent.SpectrumAlert, qp *entity.QueryParam) (int, []*ent.SpectrumAlert, error) {
	count, err := sas.Dao.SpectrumAlert.Query().SearchSpectrumAlert(sa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sas.Dao.SpectrumAlert.Query().SearchSpectrumAlert(sa, qp, false).WithTenant().WithWofangTicket(selectWofangFields()).WithProtectGroup(selectProtecGroupFields()).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

func (sas *SpectrumAlertService) GetEndTimeNotNil(ctx context.Context, sa *ent.SpectrumAlert, qp *entity.QueryParam) (int, []*ent.SpectrumAlert, error) {
	count, err := sas.Dao.SpectrumAlert.Query().QueryItemSpectrumAlert(sa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sas.Dao.SpectrumAlert.Query().
		QueryItemSpectrumAlert(sa, qp, false).
		WithTenant().
		WithWofangTicket(selectWofangFields()).
		WithProtectGroup(selectProtecGroupFields()).
		WithStrategy(selectStrategyFields()).
		All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

func (sas *SpectrumAlertService) GetEndTimeNil(ctx context.Context, sa *ent.SpectrumAlert, qp *entity.QueryParam) (int, []*ent.SpectrumAlert, error) {
	count, err := sas.Dao.SpectrumAlert.Query().Where(spectrumalert.EndTimeIsNil()).QueryItemSpectrumAlert(sa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sas.Dao.SpectrumAlert.Query().
		Where(spectrumalert.EndTimeIsNil()).QueryItemSpectrumAlert(sa, qp, false).
		WithTenant().
		WithWofangTicket(selectWofangFields()).
		WithProtectGroup(selectProtecGroupFields()).
		WithStrategy(selectStrategyFields()).
		All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
