package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/cloudflowdata"
	"meta/app/entity"
)

type CloudFlowDataService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 CloudFlowData
func (cfds *CloudFlowDataService) Query(ctx context.Context, cfd *ent.CloudFlowData, qp *entity.QueryParam) (int, []*ent.CloudFlowData, error) {
	if len(qp.Search) == 0 {
		return cfds.QueryPage(ctx, cfd, qp)
	} else {
		return cfds.QuerySearch(ctx, cfd, qp)
	}
}

// QueryByID 根据 ID 查询 CloudFlowData
func (cfds *CloudFlowDataService) QueryByID(ctx context.Context, id int) (*ent.CloudFlowData, error) {
	return cfds.Dao.CloudFlowData.Query().Where(cloudflowdata.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 CloudFlowData
func (cfds *CloudFlowDataService) Create(ctx context.Context, cfd *ent.CloudFlowData) (*ent.CloudFlowData, error) {
	return cfds.Dao.CloudFlowData.Create().SetItemCloudFlowData(cfd).Save(ctx)
}

// CreateBulk 批量创建 CloudFlowData
func (cfds *CloudFlowDataService) CreateBulk(ctx context.Context, cfd []*ent.CloudFlowData) ([]*ent.CloudFlowData, error) {
	bulks := make([]*ent.CloudFlowDataCreate, len(cfd))
	for i, v := range cfd {
		bulks[i] = cfds.Dao.CloudFlowData.Create().SetItemCloudFlowData(v)
	}
	return cfds.Dao.CloudFlowData.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 CloudFlowData
func (cfds *CloudFlowDataService) UpdateByID(ctx context.Context, cfd *ent.CloudFlowData, id int) (*ent.CloudFlowData, error) {
	return cfds.Dao.CloudFlowData.UpdateOneID(id).SetItemCloudFlowData(cfd).Save(ctx)
}

// DeleteByID 根据 ID 删除 CloudFlowData
func (cfds *CloudFlowDataService) DeleteByID(ctx context.Context, id int) error {
	return cfds.Dao.CloudFlowData.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 CloudFlowData
func (cfds *CloudFlowDataService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := cfds.Dao.CloudFlowData.Delete().Where(cloudflowdata.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 CloudFlowData
func (cfds *CloudFlowDataService) QueryPage(ctx context.Context, cfd *ent.CloudFlowData, qp *entity.QueryParam) (int, []*ent.CloudFlowData, error) {
	count, err := cfds.Dao.CloudFlowData.Query().QueryItemCloudFlowData(cfd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := cfds.Dao.CloudFlowData.Query().QueryItemCloudFlowData(cfd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 CloudFlowData
func (cfds *CloudFlowDataService) QuerySearch(ctx context.Context, cfd *ent.CloudFlowData, qp *entity.QueryParam) (int, []*ent.CloudFlowData, error) {
	count, err := cfds.Dao.CloudFlowData.Query().SearchCloudFlowData(cfd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := cfds.Dao.CloudFlowData.Query().SearchCloudFlowData(cfd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
