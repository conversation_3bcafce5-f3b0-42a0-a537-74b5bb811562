/**
* <AUTHOR>
* @date 2023-02-24 10:08
* @description
 */

package service

import (
	"meta/app/ent"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/protectgroup"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"
)

func selectTenantFieldName() func(q *ent.TenantQuery) {
	return func(q *ent.TenantQuery) {
		q.Select(tenant.FieldName)
	}
}

func selectWofangFields() func(q *ent.WofangQuery) {
	return func(q *ent.WofangQuery) {
		q.Select(wofang.FieldStatus)
		q.Select(wofang.FieldName)
		q.Select(wofang.FieldCreatedAt)
	}
}

func selectProtecGroupFields() func(q *ent.ProtectGroupQuery) {
	return func(q *ent.ProtectGroupQuery) {
		q.Select(protectgroup.FieldDragInfo)
		q.Select(protectgroup.FieldGroupName)
	}
}

func selectMatrixStrategyFields() func(q *ent.MatrixStrategyQuery) {
	return func(q *ent.MatrixStrategyQuery) {
		q.Select(matrixstrategy.FieldName)
		q.Select(matrixstrategy.FieldMonitorBps)
		q.Select(matrixstrategy.FieldDragBps)
	}
}

func selectStrategyFields() func(q *ent.StrategyQuery) {
	return func(q *ent.StrategyQuery) {
		q.Select(strategy.FieldName)
		q.Select(strategy.FieldBps)
		q.Select(strategy.FieldPps)
	}
}
