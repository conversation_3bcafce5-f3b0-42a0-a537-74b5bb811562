package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/wofangalert"
	"meta/app/entity"
)

type WofangAlertService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 WofangAlert
func (was *WofangAlertService) Query(ctx context.Context, wa *ent.WofangAlert, qp *entity.QueryParam) (int, []*ent.WofangAlert, error) {
	if len(qp.Search) == 0 {
		return was.QueryPage(ctx, wa, qp)
	} else {
		return was.QuerySearch(ctx, wa, qp)
	}
}

// QueryByID 根据 ID 查询 WofangAlert
func (was *WofangAlertService) QueryByID(ctx context.Context, id int) (*ent.WofangAlert, error) {
	return was.Dao.WofangAlert.Query().Where(wofangalert.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 WofangAlert
func (was *WofangAlertService) Create(ctx context.Context, wa *ent.WofangAlert) (*ent.WofangAlert, error) {
	return was.Dao.WofangAlert.Create().SetItemWofangAlert(wa).Save(ctx)
}

// CreateBulk 批量创建 WofangAlert
func (was *WofangAlertService) CreateBulk(ctx context.Context, wa []*ent.WofangAlert) ([]*ent.WofangAlert, error) {
	bulks := make([]*ent.WofangAlertCreate, len(wa))
	for i, v := range wa {
		bulks[i] = was.Dao.WofangAlert.Create().SetItemWofangAlert(v)
	}
	return was.Dao.WofangAlert.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 WofangAlert
func (was *WofangAlertService) UpdateByID(ctx context.Context, wa *ent.WofangAlert, id int) (*ent.WofangAlert, error) {
	return was.Dao.WofangAlert.UpdateOneID(id).SetItemWofangAlert(wa).Save(ctx)
}

// DeleteByID 根据 ID 删除 WofangAlert
func (was *WofangAlertService) DeleteByID(ctx context.Context, id int) error {
	return was.Dao.WofangAlert.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 WofangAlert
func (was *WofangAlertService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := was.Dao.WofangAlert.Delete().Where(wofangalert.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 WofangAlert
func (was *WofangAlertService) QueryPage(ctx context.Context, wa *ent.WofangAlert, qp *entity.QueryParam) (int, []*ent.WofangAlert, error) {
	count, err := was.Dao.WofangAlert.Query().QueryItemWofangAlert(wa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := was.Dao.WofangAlert.Query().QueryItemWofangAlert(wa, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 WofangAlert
func (was *WofangAlertService) QuerySearch(ctx context.Context, wa *ent.WofangAlert, qp *entity.QueryParam) (int, []*ent.WofangAlert, error) {
	count, err := was.Dao.WofangAlert.Query().SearchWofangAlert(wa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := was.Dao.WofangAlert.Query().SearchWofangAlert(wa, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
