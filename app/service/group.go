package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/group"
	"meta/app/entity"
)

type GroupService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 Group
func (gs *GroupService) Query(ctx context.Context, g *ent.Group, qp *entity.QueryParam) (int, []*ent.Group, error) {
	if len(qp.Search) == 0 {
		return gs.QueryPage(ctx, g, qp)
	} else {
		return gs.QuerySearch(ctx, g, qp)
	}
}

// QueryByID 根据 ID 查询 Group
func (gs *GroupService) QueryByID(ctx context.Context, id int) (*ent.Group, error) {
	return gs.Dao.Group.Get(ctx, id)
}

// Create 创建 Group
func (gs *GroupService) Create(ctx context.Context, g *ent.Group) (*ent.Group, error) {
	return gs.Dao.Group.Create().SetItemGroup(g).Save(ctx)
}

// CreateBulk 批量创建 Group
func (gs *GroupService) CreateBulk(ctx context.Context, g []*ent.Group) ([]*ent.Group, error) {
	bulks := make([]*ent.GroupCreate, len(g))
	for i, v := range g {
		bulks[i] = gs.Dao.Group.Create().SetItemGroup(v)
	}
	return gs.Dao.Group.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 Group
func (gs *GroupService) UpdateByID(ctx context.Context, g *ent.Group, id int) (*ent.Group, error) {
	return gs.Dao.Group.UpdateOneID(id).SetItemGroup(g).Save(ctx)
}

// DeleteByID 根据 ID 删除 Group
func (gs *GroupService) DeleteByID(ctx context.Context, id int) error {
	return gs.Dao.Group.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 Group
func (gs *GroupService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := gs.Dao.Group.Delete().Where(group.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 Group
func (gs *GroupService) QueryPage(ctx context.Context, g *ent.Group, qp *entity.QueryParam) (int, []*ent.Group, error) {
	count, err := gs.Dao.Group.Query().QueryItemGroup(g, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := gs.Dao.Group.Query().QueryItemGroup(g, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 Group
func (gs *GroupService) QuerySearch(ctx context.Context, g *ent.Group, qp *entity.QueryParam) (int, []*ent.Group, error) {
	count, err := gs.Dao.Group.Query().SearchGroup(g, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := gs.Dao.Group.Query().SearchGroup(g, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
