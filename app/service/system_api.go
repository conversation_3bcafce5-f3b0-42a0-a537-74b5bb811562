package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/systemapi"
	"meta/app/entity"
)

type SystemApiService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 SystemApi
func (sas *SystemApiService) Query(ctx context.Context, sa *ent.SystemApi, qp *entity.QueryParam) (int, []*ent.SystemApi, error) {
	if len(qp.Search) == 0 {
		return sas.QueryPage(ctx, sa, qp)
	} else {
		return sas.QuerySearch(ctx, sa, qp)
	}
}

// QueryByID 根据 ID 查询 SystemApi
func (sas *SystemApiService) QueryByID(ctx context.Context, id int) (*ent.SystemApi, error) {
	return sas.Dao.SystemApi.Query().Where(systemapi.ID(id)).Only(ctx)
}

// Create 创建 SystemApi
func (sas *SystemApiService) Create(ctx context.Context, sa *ent.SystemApi) (*ent.SystemApi, error) {
	return sas.Dao.SystemApi.Create().SetItemSystemApi(sa).Save(ctx)
}

// CreateBulk 批量创建 SystemApi
func (sas *SystemApiService) CreateBulk(ctx context.Context, sa []*ent.SystemApi) ([]*ent.SystemApi, error) {
	bulks := make([]*ent.SystemApiCreate, len(sa))
	for i, v := range sa {
		bulks[i] = sas.Dao.SystemApi.Create().SetItemSystemApi(v)
	}
	return sas.Dao.SystemApi.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 SystemApi
func (sas *SystemApiService) UpdateByID(ctx context.Context, sa *ent.SystemApi, id int) (*ent.SystemApi, error) {
	return sas.Dao.SystemApi.UpdateOneID(id).SetItemSystemApi(sa).Save(ctx)
}

// DeleteByID 根据 ID 删除 SystemApi
func (sas *SystemApiService) DeleteByID(ctx context.Context, id int) error {
	return sas.Dao.SystemApi.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 SystemApi
func (sas *SystemApiService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := sas.Dao.SystemApi.Delete().Where(systemapi.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 SystemApi
func (sas *SystemApiService) QueryPage(ctx context.Context, sa *ent.SystemApi, qp *entity.QueryParam) (int, []*ent.SystemApi, error) {
	count, err := sas.Dao.SystemApi.Query().QueryItemSystemApi(sa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sas.Dao.SystemApi.Query().QueryItemSystemApi(sa, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 SystemApi
func (sas *SystemApiService) QuerySearch(ctx context.Context, sa *ent.SystemApi, qp *entity.QueryParam) (int, []*ent.SystemApi, error) {
	count, err := sas.Dao.SystemApi.Query().SearchSystemApi(sa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sas.Dao.SystemApi.Query().SearchSystemApi(sa, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
