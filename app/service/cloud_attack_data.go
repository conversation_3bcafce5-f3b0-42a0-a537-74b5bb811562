package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/cloudattackdata"
	"meta/app/entity"
)

type CloudAttackDataService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 CloudAttackData
func (cads *CloudAttackDataService) Query(ctx context.Context, cad *ent.CloudAttackData, qp *entity.QueryParam) (int, []*ent.CloudAttackData, error) {
	if len(qp.Search) == 0 {
		return cads.QueryPage(ctx, cad, qp)
	} else {
		return cads.QuerySearch(ctx, cad, qp)
	}
}

// QueryByID 根据 ID 查询 CloudAttackData
func (cads *CloudAttackDataService) QueryByID(ctx context.Context, id int) (*ent.CloudAttackData, error) {
	return cads.Dao.CloudAttackData.Query().Where(cloudattackdata.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 CloudAttackData
func (cads *CloudAttackDataService) Create(ctx context.Context, cad *ent.CloudAttackData) (*ent.CloudAttackData, error) {
	return cads.Dao.CloudAttackData.Create().SetItemCloudAttackData(cad).Save(ctx)
}

// CreateBulk 批量创建 CloudAttackData
func (cads *CloudAttackDataService) CreateBulk(ctx context.Context, cad []*ent.CloudAttackData) ([]*ent.CloudAttackData, error) {
	bulks := make([]*ent.CloudAttackDataCreate, len(cad))
	for i, v := range cad {
		bulks[i] = cads.Dao.CloudAttackData.Create().SetItemCloudAttackData(v)
	}
	return cads.Dao.CloudAttackData.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 CloudAttackData
func (cads *CloudAttackDataService) UpdateByID(ctx context.Context, cad *ent.CloudAttackData, id int) (*ent.CloudAttackData, error) {
	return cads.Dao.CloudAttackData.UpdateOneID(id).SetItemCloudAttackData(cad).Save(ctx)
}

// DeleteByID 根据 ID 删除 CloudAttackData
func (cads *CloudAttackDataService) DeleteByID(ctx context.Context, id int) error {
	return cads.Dao.CloudAttackData.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 CloudAttackData
func (cads *CloudAttackDataService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := cads.Dao.CloudAttackData.Delete().Where(cloudattackdata.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 CloudAttackData
func (cads *CloudAttackDataService) QueryPage(ctx context.Context, cad *ent.CloudAttackData, qp *entity.QueryParam) (int, []*ent.CloudAttackData, error) {
	count, err := cads.Dao.CloudAttackData.Query().QueryItemCloudAttackData(cad, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := cads.Dao.CloudAttackData.Query().QueryItemCloudAttackData(cad, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 CloudAttackData
func (cads *CloudAttackDataService) QuerySearch(ctx context.Context, cad *ent.CloudAttackData, qp *entity.QueryParam) (int, []*ent.CloudAttackData, error) {
	count, err := cads.Dao.CloudAttackData.Query().SearchCloudAttackData(cad, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := cads.Dao.CloudAttackData.Query().SearchCloudAttackData(cad, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
