package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/entity"
)

type MatrixSpectrumAlertService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) Query(ctx context.Context, msa *ent.MatrixSpectrumAlert, qp *entity.QueryParam) (int, []*ent.MatrixSpectrumAlert, error) {
	if len(qp.Search) == 0 {
		return msas.QueryPage(ctx, msa, qp)
	} else {
		return msas.QuerySearch(ctx, msa, qp)
	}
}

// QueryByID 根据 ID 查询 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) QueryByID(ctx context.Context, id int) (*ent.MatrixSpectrumAlert, error) {
	return msas.Dao.MatrixSpectrumAlert.Query().Where(matrixspectrumalert.ID(id)).WithWofangTicket().WithTenant().WithMatrixStrategy().Only(ctx)
}

// Create 创建 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) Create(ctx context.Context, msa *ent.MatrixSpectrumAlert) (*ent.MatrixSpectrumAlert, error) {
	return msas.Dao.MatrixSpectrumAlert.Create().SetItemMatrixSpectrumAlert(msa).Save(ctx)
}

// CreateBulk 批量创建 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) CreateBulk(ctx context.Context, msa []*ent.MatrixSpectrumAlert) ([]*ent.MatrixSpectrumAlert, error) {
	bulks := make([]*ent.MatrixSpectrumAlertCreate, len(msa))
	for i, v := range msa {
		bulks[i] = msas.Dao.MatrixSpectrumAlert.Create().SetItemMatrixSpectrumAlert(v)
	}
	return msas.Dao.MatrixSpectrumAlert.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) UpdateByID(ctx context.Context, msa *ent.MatrixSpectrumAlert, id int) (*ent.MatrixSpectrumAlert, error) {
	return msas.Dao.MatrixSpectrumAlert.UpdateOneID(id).SetItemMatrixSpectrumAlert(msa).Save(ctx)
}

// DeleteByID 根据 ID 删除 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) DeleteByID(ctx context.Context, id int) error {
	return msas.Dao.MatrixSpectrumAlert.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := msas.Dao.MatrixSpectrumAlert.Delete().Where(matrixspectrumalert.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) QueryPage(ctx context.Context, msa *ent.MatrixSpectrumAlert, qp *entity.QueryParam) (int, []*ent.MatrixSpectrumAlert, error) {
	count, err := msas.Dao.MatrixSpectrumAlert.Query().QueryItemMatrixSpectrumAlert(msa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := msas.Dao.MatrixSpectrumAlert.Query().QueryItemMatrixSpectrumAlert(msa, qp, false).WithTenant().WithMatrixStrategy(selectMatrixStrategyFields()).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 MatrixSpectrumAlert
func (msas *MatrixSpectrumAlertService) QuerySearch(ctx context.Context, msa *ent.MatrixSpectrumAlert, qp *entity.QueryParam) (int, []*ent.MatrixSpectrumAlert, error) {
	count, err := msas.Dao.MatrixSpectrumAlert.Query().SearchMatrixSpectrumAlert(msa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := msas.Dao.MatrixSpectrumAlert.Query().SearchMatrixSpectrumAlert(msa, qp, false).WithTenant().WithMatrixStrategy(selectMatrixStrategyFields()).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

func (msas *MatrixSpectrumAlertService) GetEndTimeNil(ctx context.Context, msa *ent.MatrixSpectrumAlert, qp *entity.QueryParam) (int, []*ent.MatrixSpectrumAlert, error) {
	count, err := msas.Dao.MatrixSpectrumAlert.Query().Where(matrixspectrumalert.EndTimeIsNil()).QueryItemMatrixSpectrumAlert(msa, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := msas.Dao.MatrixSpectrumAlert.Query().
		Where(matrixspectrumalert.EndTimeIsNil()).
		QueryItemMatrixSpectrumAlert(msa, qp, false).WithTenant().WithMatrixStrategy(selectMatrixStrategyFields()).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
