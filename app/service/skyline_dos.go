package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/skylinedos"
	"meta/app/entity"
)

type SkylineDosService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 SkylineDos
func (sds *SkylineDosService) Query(ctx context.Context, sd *ent.SkylineDos, qp *entity.QueryParam) (int, []*ent.SkylineDos, error) {
	if len(qp.Search) == 0 {
		return sds.QueryPage(ctx, sd, qp)
	} else {
		return sds.QuerySearch(ctx, sd, qp)
	}
}

// QueryByID 根据 ID 查询 SkylineDos
func (sds *SkylineDosService) QueryByID(ctx context.Context, id int) (*ent.SkylineDos, error) {
	return sds.Dao.SkylineDos.Query().Where(skylinedos.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 SkylineDos
func (sds *SkylineDosService) Create(ctx context.Context, sd *ent.SkylineDos) (*ent.SkylineDos, error) {
	return sds.Dao.SkylineDos.Create().SetItemSkylineDos(sd).Save(ctx)
}

// CreateBulk 批量创建 SkylineDos
func (sds *SkylineDosService) CreateBulk(ctx context.Context, sd []*ent.SkylineDos) ([]*ent.SkylineDos, error) {
	bulks := make([]*ent.SkylineDosCreate, len(sd))
	for i, v := range sd {
		bulks[i] = sds.Dao.SkylineDos.Create().SetItemSkylineDos(v)
	}
	return sds.Dao.SkylineDos.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 SkylineDos
func (sds *SkylineDosService) UpdateByID(ctx context.Context, sd *ent.SkylineDos, id int) (*ent.SkylineDos, error) {
	return sds.Dao.SkylineDos.UpdateOneID(id).SetItemSkylineDos(sd).Save(ctx)
}

// DeleteByID 根据 ID 删除 SkylineDos
func (sds *SkylineDosService) DeleteByID(ctx context.Context, id int) error {
	return sds.Dao.SkylineDos.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 SkylineDos
func (sds *SkylineDosService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := sds.Dao.SkylineDos.Delete().Where(skylinedos.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 SkylineDos
func (sds *SkylineDosService) QueryPage(ctx context.Context, sd *ent.SkylineDos, qp *entity.QueryParam) (int, []*ent.SkylineDos, error) {
	count, err := sds.Dao.SkylineDos.Query().QueryItemSkylineDos(sd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sds.Dao.SkylineDos.Query().QueryItemSkylineDos(sd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 SkylineDos
func (sds *SkylineDosService) QuerySearch(ctx context.Context, sd *ent.SkylineDos, qp *entity.QueryParam) (int, []*ent.SkylineDos, error) {
	count, err := sds.Dao.SkylineDos.Query().SearchSkylineDos(sd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sds.Dao.SkylineDos.Query().SearchSkylineDos(sd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
