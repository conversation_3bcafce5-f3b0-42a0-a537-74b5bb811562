package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/wofang"
	"meta/app/entity"
)

type WofangService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 Wofang
func (ws *WofangService) Query(ctx context.Context, w *ent.Wofang, qp *entity.QueryParam) (int, []*ent.Wofang, error) {
	if len(qp.Search) == 0 {
		return ws.QueryPage(ctx, w, qp)
	} else {
		return ws.QuerySearch(ctx, w, qp)
	}
}

// QueryByID 根据 ID 查询 Wofang
func (ws *WofangService) QueryByID(ctx context.Context, id int) (*ent.Wofang, error) {
	return ws.Dao.Wofang.Query().Where(wofang.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 Wofang
func (ws *WofangService) Create(ctx context.Context, w *ent.Wofang) (*ent.Wofang, error) {
	return ws.Dao.Wofang.Create().SetItemWofang(w).Save(ctx)
}

// CreateBulk 批量创建 Wofang
func (ws *WofangService) CreateBulk(ctx context.Context, w []*ent.Wofang) ([]*ent.Wofang, error) {
	bulks := make([]*ent.WofangCreate, len(w))
	for i, v := range w {
		bulks[i] = ws.Dao.Wofang.Create().SetItemWofang(v)
	}
	return ws.Dao.Wofang.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 Wofang
func (ws *WofangService) UpdateByID(ctx context.Context, w *ent.Wofang, id int) (*ent.Wofang, error) {
	return ws.Dao.Wofang.UpdateOneID(id).SetItemWofang(w).Save(ctx)
}

// DeleteByID 根据 ID 删除 Wofang
func (ws *WofangService) DeleteByID(ctx context.Context, id int) error {
	return ws.Dao.Wofang.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 Wofang
func (ws *WofangService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := ws.Dao.Wofang.Delete().Where(wofang.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 Wofang
func (ws *WofangService) QueryPage(ctx context.Context, w *ent.Wofang, qp *entity.QueryParam) (int, []*ent.Wofang, error) {
	count, err := ws.Dao.Wofang.Query().QueryItemWofang(w, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := ws.Dao.Wofang.Query().QueryItemWofang(w, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 Wofang
func (ws *WofangService) QuerySearch(ctx context.Context, w *ent.Wofang, qp *entity.QueryParam) (int, []*ent.Wofang, error) {
	count, err := ws.Dao.Wofang.Query().SearchWofang(w, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := ws.Dao.Wofang.Query().SearchWofang(w, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
