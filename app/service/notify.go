package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/notify"
	"meta/app/entity"
)

type NotifyService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 Notify
func (ns *NotifyService) Query(ctx context.Context, n *ent.Notify, qp *entity.QueryParam) (int, []*ent.Notify, error) {
	if len(qp.Search) == 0 {
		return ns.QueryPage(ctx, n, qp)
	} else {
		return ns.QuerySearch(ctx, n, qp)
	}
}

// QueryByID 根据 ID 查询 Notify
func (ns *NotifyService) QueryByID(ctx context.Context, id int) (*ent.Notify, error) {
	return ns.Dao.Notify.Query().Where(notify.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 Notify
func (ns *NotifyService) Create(ctx context.Context, n *ent.Notify) (*ent.Notify, error) {
	return ns.Dao.Notify.Create().SetItemNotify(n).Save(ctx)
}

// CreateBulk 批量创建 Notify
func (ns *NotifyService) CreateBulk(ctx context.Context, n []*ent.Notify) ([]*ent.Notify, error) {
	bulks := make([]*ent.NotifyCreate, len(n))
	for i, v := range n {
		bulks[i] = ns.Dao.Notify.Create().SetItemNotify(v)
	}
	return ns.Dao.Notify.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 Notify
func (ns *NotifyService) UpdateByID(ctx context.Context, n *ent.Notify, id int) (*ent.Notify, error) {
	return ns.Dao.Notify.UpdateOneID(id).SetItemNotify(n).Save(ctx)
}

// DeleteByID 根据 ID 删除 Notify
func (ns *NotifyService) DeleteByID(ctx context.Context, id int) error {
	return ns.Dao.Notify.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 Notify
func (ns *NotifyService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := ns.Dao.Notify.Delete().Where(notify.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 Notify
func (ns *NotifyService) QueryPage(ctx context.Context, n *ent.Notify, qp *entity.QueryParam) (int, []*ent.Notify, error) {
	count, err := ns.Dao.Notify.Query().QueryItemNotify(n, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := ns.Dao.Notify.Query().QueryItemNotify(n, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 Notify
func (ns *NotifyService) QuerySearch(ctx context.Context, n *ent.Notify, qp *entity.QueryParam) (int, []*ent.Notify, error) {
	count, err := ns.Dao.Notify.Query().SearchNotify(n, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := ns.Dao.Notify.Query().SearchNotify(n, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
