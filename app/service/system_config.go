package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/systemconfig"
	"meta/app/entity"
)

type SystemConfigService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 SystemConfig
func (scs *SystemConfigService) Query(ctx context.Context, sc *ent.SystemConfig, qp *entity.QueryParam) (int, []*ent.SystemConfig, error) {
	if len(qp.Search) == 0 {
		return scs.QueryPage(ctx, sc, qp)
	} else {
		return scs.QuerySearch(ctx, sc, qp)
	}
}

// QueryByID 根据 ID 查询 SystemConfig
func (scs *SystemConfigService) QueryByID(ctx context.Context, id int) (*ent.SystemConfig, error) {
	return scs.Dao.SystemConfig.Query().Where(systemconfig.ID(id)).Only(ctx)
}

// Create 创建 SystemConfig
func (scs *SystemConfigService) Create(ctx context.Context, sc *ent.SystemConfig) (*ent.SystemConfig, error) {
	return scs.Dao.SystemConfig.Create().SetItemSystemConfig(sc).Save(ctx)
}

// CreateBulk 批量创建 SystemConfig
func (scs *SystemConfigService) CreateBulk(ctx context.Context, sc []*ent.SystemConfig) ([]*ent.SystemConfig, error) {
	bulks := make([]*ent.SystemConfigCreate, len(sc))
	for i, v := range sc {
		bulks[i] = scs.Dao.SystemConfig.Create().SetItemSystemConfig(v)
	}
	return scs.Dao.SystemConfig.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 SystemConfig
func (scs *SystemConfigService) UpdateByID(ctx context.Context, sc *ent.SystemConfig, id int) (*ent.SystemConfig, error) {
	return scs.Dao.SystemConfig.UpdateOneID(id).SetItemSystemConfig(sc).Save(ctx)
}

// DeleteByID 根据 ID 删除 SystemConfig
func (scs *SystemConfigService) DeleteByID(ctx context.Context, id int) error {
	return scs.Dao.SystemConfig.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 SystemConfig
func (scs *SystemConfigService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := scs.Dao.SystemConfig.Delete().Where(systemconfig.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 SystemConfig
func (scs *SystemConfigService) QueryPage(ctx context.Context, sc *ent.SystemConfig, qp *entity.QueryParam) (int, []*ent.SystemConfig, error) {
	count, err := scs.Dao.SystemConfig.Query().QueryItemSystemConfig(sc, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := scs.Dao.SystemConfig.Query().QueryItemSystemConfig(sc, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 SystemConfig
func (scs *SystemConfigService) QuerySearch(ctx context.Context, sc *ent.SystemConfig, qp *entity.QueryParam) (int, []*ent.SystemConfig, error) {
	count, err := scs.Dao.SystemConfig.Query().SearchSystemConfig(sc, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := scs.Dao.SystemConfig.Query().SearchSystemConfig(sc, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
