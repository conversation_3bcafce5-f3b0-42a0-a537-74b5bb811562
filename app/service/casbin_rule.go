package service

import (
	"context"
	"errors"
	"github.com/casbin/casbin/v2"
	"meta/app/ent"
	"meta/app/ent/casbinrule"
	"meta/app/entity"
	"meta/pkg/common"
)

type CasbinRuleService struct {
	Dao *Dao
	Enf *casbin.Enforcer
}

// Query 根据指定字段、时间范围查询或搜索 CasbinRule
func (crs *CasbinRuleService) Query(ctx context.Context, cr *ent.CasbinRule, qp *entity.QueryParam) (int, []*ent.CasbinRule, error) {
	if len(qp.Search) == 0 {
		return crs.QueryPage(ctx, cr, qp)
	} else {
		return crs.QuerySearch(ctx, cr, qp)
	}
}

// QueryByID 根据 ID 查询 CasbinRule
func (crs *CasbinRuleService) QueryByID(ctx context.Context, id int) (*ent.CasbinRule, error) {
	return crs.Dao.CasbinRule.Query().Where(casbinrule.ID(id)).Only(ctx)
}

// Create 创建 CasbinRule
func (crs *CasbinRuleService) Create(ctx context.Context, cr *ent.CasbinRule) (*ent.CasbinRule, error) {
	var err error
	switch cr.Type {
	case "g":
		_, err = crs.Enf.AddGroupingPolicy(cr.Sub, cr.Dom, cr.Obj)
	case "p":
		_, err = crs.Enf.AddPolicy(cr.Sub, cr.Dom, cr.Obj, cr.Act)
	}
	if err != nil {
		return nil, err
	}
	return cr, err
}

// CreateBulk 批量创建 CasbinRule
func (crs *CasbinRuleService) CreateBulk(ctx context.Context, cr []*ent.CasbinRule) ([]*ent.CasbinRule, error) {
	var (
		err         error
		policyRules [][]string
		groupRules  [][]string
	)
	for _, rule := range cr {
		err = common.CheckCasbinRule(rule)
		if err != nil {
			return []*ent.CasbinRule{rule}, err
		}
		switch rule.Type {
		case "g":
			groupRules = append(groupRules, []string{rule.Sub, rule.Dom, rule.Obj})
		case "p":
			policyRules = append(policyRules, []string{rule.Sub, rule.Dom, rule.Obj, rule.Act})
		}
	}
	if len(policyRules) != 0 {
		_, err = crs.Enf.AddPolicies(policyRules)
	}
	if len(groupRules) != 0 {
		_, err = crs.Enf.AddGroupingPolicies(groupRules)
	}
	if err != nil {
		return nil, err
	}
	return cr, nil
}

// UpdateByID 根据 ID 修改 CasbinRule
func (crs *CasbinRuleService) UpdateByID(ctx context.Context, cr *ent.CasbinRule, id int) (*ent.CasbinRule, error) {
	var err error
	dbRule, err := crs.QueryByID(ctx, id)
	crStr := getRuleSlice(cr)
	dbStr := getRuleSlice(dbRule)
	if cr.Type == dbRule.Type {
		switch cr.Type {
		case "g":
			_, err = crs.Enf.UpdateGroupingPolicy(dbStr, crStr)
		case "p":
			_, err = crs.Enf.UpdatePolicy(dbStr, crStr)
		}
	} else {
		return nil, errors.New("dismactch rule type")
	}
	if err != nil {
		return nil, err
	}
	return cr, nil
}

// DeleteByID 根据 ID 删除 CasbinRule
func (crs *CasbinRuleService) DeleteByID(ctx context.Context, id int) error {
	rule, err := crs.QueryByID(ctx, id)
	if err != nil {
		return err
	}
	switch rule.Type {
	case "g":
		_, err = crs.Enf.RemoveGroupingPolicy(rule.Sub, rule.Dom, rule.Obj)
	case "p":
		_, err = crs.Enf.RemovePolicy(rule.Sub, rule.Dom, rule.Obj, rule.Act)
	}
	if err != nil {
		return err
	}
	return nil
}

// DeleteBulk 根据 IDs 批量删除 CasbinRule
func (crs *CasbinRuleService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	for _, id := range ids {
		err := crs.DeleteByID(ctx, id)
		if err != nil {
			return 1, err
		}
	}
	return 0, nil
}

// QueryPage 分页查询 CasbinRule
func (crs *CasbinRuleService) QueryPage(ctx context.Context, cr *ent.CasbinRule, qp *entity.QueryParam) (int, []*ent.CasbinRule, error) {
	count, err := crs.Dao.CasbinRule.Query().QueryItemCasbinRule(cr, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := crs.Dao.CasbinRule.Query().QueryItemCasbinRule(cr, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 CasbinRule
func (crs *CasbinRuleService) QuerySearch(ctx context.Context, cr *ent.CasbinRule, qp *entity.QueryParam) (int, []*ent.CasbinRule, error) {
	count, err := crs.Dao.CasbinRule.Query().SearchCasbinRule(cr, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := crs.Dao.CasbinRule.Query().SearchCasbinRule(cr, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// getRuleSlice
func getRuleSlice(rule *ent.CasbinRule) []string {
	switch rule.Type {
	case "g":
		return []string{rule.Sub, rule.Dom, rule.Obj}
	case "p":
		return []string{rule.Sub, rule.Dom, rule.Obj, rule.Act}
	default:
		return nil
	}
}
