package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/datasync"
	"meta/app/entity"
)

type DataSyncService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 DataSync
func (dss *DataSyncService) Query(ctx context.Context, ds *ent.DataSync, qp *entity.QueryParam) (int, []*ent.DataSync, error) {
	if len(qp.Search) == 0 {
		return dss.QueryPage(ctx, ds, qp)
	} else {
		return dss.QuerySearch(ctx, ds, qp)
	}
}

// QueryByID 根据 ID 查询 DataSync
func (dss *DataSyncService) QueryByID(ctx context.Context, id int) (*ent.DataSync, error) {
	return dss.Dao.DataSync.Query().Where(datasync.ID(id)).Only(ctx)
}

// Create 创建 DataSync
func (dss *DataSyncService) Create(ctx context.Context, ds *ent.DataSync) (*ent.DataSync, error) {
	return dss.Dao.DataSync.Create().SetItemDataSync(ds).Save(ctx)
}

// CreateBulk 批量创建 DataSync
func (dss *DataSyncService) CreateBulk(ctx context.Context, ds []*ent.DataSync) ([]*ent.DataSync, error) {
	bulks := make([]*ent.DataSyncCreate, len(ds))
	for i, v := range ds {
		bulks[i] = dss.Dao.DataSync.Create().SetItemDataSync(v)
	}
	return dss.Dao.DataSync.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 DataSync
func (dss *DataSyncService) UpdateByID(ctx context.Context, ds *ent.DataSync, id int) (*ent.DataSync, error) {
	return dss.Dao.DataSync.UpdateOneID(id).SetItemDataSync(ds).Save(ctx)
}

// DeleteByID 根据 ID 删除 DataSync
func (dss *DataSyncService) DeleteByID(ctx context.Context, id int) error {
	return dss.Dao.DataSync.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 DataSync
func (dss *DataSyncService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := dss.Dao.DataSync.Delete().Where(datasync.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 DataSync
func (dss *DataSyncService) QueryPage(ctx context.Context, ds *ent.DataSync, qp *entity.QueryParam) (int, []*ent.DataSync, error) {
	count, err := dss.Dao.DataSync.Query().QueryItemDataSync(ds, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := dss.Dao.DataSync.Query().QueryItemDataSync(ds, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 DataSync
func (dss *DataSyncService) QuerySearch(ctx context.Context, ds *ent.DataSync, qp *entity.QueryParam) (int, []*ent.DataSync, error) {
	count, err := dss.Dao.DataSync.Query().SearchDataSync(ds, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := dss.Dao.DataSync.Query().SearchDataSync(ds, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
