package service

import (
	"context"
	"errors"
	"meta/app/ent"
	"meta/app/ent/user"
	"meta/app/entity"
	"meta/app/entity/config"
	"meta/pkg/bcrypt"
	"meta/pkg/jwt"
	"strings"
)

type UserService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 User
func (us *UserService) Query(ctx context.Context, u *ent.User, qp *entity.QueryParam) (int, []*ent.User, error) {
	if len(qp.Search) == 0 {
		return us.QueryPage(ctx, u, qp)
	} else {
		return us.QuerySearch(ctx, u, qp)
	}
}

// QueryByID 根据 ID 查询 User
func (us *UserService) QueryByID(ctx context.Context, id int) (*ent.User, error) {
	return us.Dao.User.Get(ctx, id)
}

// QueryByUserName 根据 Name 查询 User
func (us *UserService) QueryByUserName(ctx context.Context, name string) (*ent.User, error) {
	return us.Dao.User.Query().Where(user.Name(name)).Only(ctx)
}

// Create 创建 User
func (us *UserService) Create(ctx context.Context, u *ent.User) (*ent.User, error) {
	encodePassword, err := bcrypt.Encode(u.Password)
	if err != nil {
		return nil, err
	}
	u.Password = encodePassword
	return us.Dao.User.Create().SetItemUser(u).Save(ctx)
}

// CreateBulk 批量创建 User
func (us *UserService) CreateBulk(ctx context.Context, u []*ent.User) ([]*ent.User, error) {
	for _, v := range u {
		encodePassword, err := bcrypt.Encode(v.Password)
		if err != nil {
			return nil, err
		}
		v.Valid = true
		v.SuperAdmin = false
		v.Password = encodePassword
	}
	bulks := make([]*ent.UserCreate, len(u))
	for i, v := range u {
		bulks[i] = us.Dao.User.Create().SetItemUser(v)
	}
	return us.Dao.User.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 User
func (us *UserService) UpdateByID(ctx context.Context, u *ent.User, id int) (*ent.User, error) {
	if !strings.HasPrefix(u.Password, "$2a$10$") {
		encodePassword, err := bcrypt.Encode(u.Password)
		if err != nil {
			return nil, err
		}
		u.Password = encodePassword
	}
	return us.Dao.User.UpdateOneID(id).SetItemUser(u).Save(ctx)
}

// DeleteByID 根据 ID 删除 User
func (us *UserService) DeleteByID(ctx context.Context, id int) error {
	return us.Dao.User.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 User
func (us *UserService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := us.Dao.User.Delete().Where(user.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 User
func (us *UserService) QueryPage(ctx context.Context, u *ent.User, qp *entity.QueryParam) (int, []*ent.User, error) {
	count, err := us.Dao.User.Query().QueryItemUser(u, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := us.Dao.User.Query().QueryItemUser(u, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 User
func (us *UserService) QuerySearch(ctx context.Context, u *ent.User, qp *entity.QueryParam) (int, []*ent.User, error) {
	count, err := us.Dao.User.Query().SearchUser(u, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := us.Dao.User.Query().SearchUser(u, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// Login 根据用户名和密码登录 User
func (us *UserService) Login(ctx context.Context, u *ent.User) (*ent.User, string, error) {
	qp, err := us.Dao.User.Query().Where(user.Name(u.Name)).First(ctx)
	if err != nil {
		return nil, "", errors.New("login error")
	}
	matches := bcrypt.Matches(qp.Password, u.Password)
	if matches {
		jwtConfig := config.CFG.Auth.JWT
		token, err := jwt.CreateJWT("", qp.Name, jwtConfig.Key, jwtConfig.TTL, jwtConfig.Hmac)
		if err != nil {
			return nil, "", err
		}
		return qp, token, nil
	}
	return nil, "", errors.New("login error")
}
