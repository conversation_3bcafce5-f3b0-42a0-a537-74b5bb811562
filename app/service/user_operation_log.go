package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/useroperationlog"
	"meta/app/entity"
)

type UserOperationLogService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 UserOperationLog
func (uols *UserOperationLogService) Query(ctx context.Context, uol *ent.UserOperationLog, qp *entity.QueryParam) (int, []*ent.UserOperationLog, error) {
	if len(qp.Search) == 0 {
		return uols.QueryPage(ctx, uol, qp)
	} else {
		return uols.QuerySearch(ctx, uol, qp)
	}
}

// QueryByID 根据 ID 查询 UserOperationLog
func (uols *UserOperationLogService) QueryByID(ctx context.Context, id int) (*ent.UserOperationLog, error) {
	return uols.Dao.UserOperationLog.Query().Where(useroperationlog.ID(id)).Only(ctx)
}

// Create 创建 UserOperationLog
func (uols *UserOperationLogService) Create(ctx context.Context, uol *ent.UserOperationLog) (*ent.UserOperationLog, error) {
	return uols.Dao.UserOperationLog.Create().SetItemUserOperationLog(uol).Save(ctx)
}

// CreateBulk 批量创建 UserOperationLog
func (uols *UserOperationLogService) CreateBulk(ctx context.Context, uol []*ent.UserOperationLog) ([]*ent.UserOperationLog, error) {
	bulks := make([]*ent.UserOperationLogCreate, len(uol))
	for i, v := range uol {
		bulks[i] = uols.Dao.UserOperationLog.Create().SetItemUserOperationLog(v)
	}
	return uols.Dao.UserOperationLog.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 UserOperationLog
func (uols *UserOperationLogService) UpdateByID(ctx context.Context, uol *ent.UserOperationLog, id int) (*ent.UserOperationLog, error) {
	return uols.Dao.UserOperationLog.UpdateOneID(id).SetItemUserOperationLog(uol).Save(ctx)
}

// DeleteByID 根据 ID 删除 UserOperationLog
func (uols *UserOperationLogService) DeleteByID(ctx context.Context, id int) error {
	return uols.Dao.UserOperationLog.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 UserOperationLog
func (uols *UserOperationLogService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := uols.Dao.UserOperationLog.Delete().Where(useroperationlog.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 UserOperationLog
func (uols *UserOperationLogService) QueryPage(ctx context.Context, uol *ent.UserOperationLog, qp *entity.QueryParam) (int, []*ent.UserOperationLog, error) {
	count, err := uols.Dao.UserOperationLog.Query().QueryItemUserOperationLog(uol, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := uols.Dao.UserOperationLog.Query().QueryItemUserOperationLog(uol, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 UserOperationLog
func (uols *UserOperationLogService) QuerySearch(ctx context.Context, uol *ent.UserOperationLog, qp *entity.QueryParam) (int, []*ent.UserOperationLog, error) {
	count, err := uols.Dao.UserOperationLog.Query().SearchUserOperationLog(uol, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := uols.Dao.UserOperationLog.Query().SearchUserOperationLog(uol, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
