package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/cleandata"
	"meta/app/entity"
)

type CleanDataService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 CleanData
func (cds *CleanDataService) Query(ctx context.Context, cd *ent.CleanData, qp *entity.QueryParam) (int, []*ent.CleanData, error) {
	if len(qp.Search) == 0 {
		return cds.QueryPage(ctx, cd, qp)
	} else {
		return cds.QuerySearch(ctx, cd, qp)
	}
}

// QueryByID 根据 ID 查询 CleanData
func (cds *CleanDataService) QueryByID(ctx context.Context, id int) (*ent.CleanData, error) {
	return cds.Dao.CleanData.Query().Where(cleandata.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 CleanData
func (cds *CleanDataService) Create(ctx context.Context, cd *ent.CleanData) (*ent.CleanData, error) {
	return cds.Dao.CleanData.Create().SetItemCleanData(cd).Save(ctx)
}

// CreateBulk 批量创建 CleanData
func (cds *CleanDataService) CreateBulk(ctx context.Context, cd []*ent.CleanData) ([]*ent.CleanData, error) {
	bulks := make([]*ent.CleanDataCreate, len(cd))
	for i, v := range cd {
		bulks[i] = cds.Dao.CleanData.Create().SetItemCleanData(v)
	}
	return cds.Dao.CleanData.CreateBulk(bulks...).Save(ctx)
}

// CreateBulk2 批量创建 CleanData
func (cds *CleanDataService) CreateBulk2(ctx context.Context, cd []*ent.CleanData, id int) ([]*ent.CleanData, error) {
	bulks := make([]*ent.CleanDataCreate, len(cd))
	for i, v := range cd {
		bulks[i] = cds.Dao.CleanData.Create().SetItemCleanData(v).SetSpectrumAlertID(id)
	}
	return cds.Dao.CleanData.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 CleanData
func (cds *CleanDataService) UpdateByID(ctx context.Context, cd *ent.CleanData, id int) (*ent.CleanData, error) {
	return cds.Dao.CleanData.UpdateOneID(id).SetItemCleanData(cd).Save(ctx)
}

// DeleteByID 根据 ID 删除 CleanData
func (cds *CleanDataService) DeleteByID(ctx context.Context, id int) error {
	return cds.Dao.CleanData.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 CleanData
func (cds *CleanDataService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := cds.Dao.CleanData.Delete().Where(cleandata.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 CleanData
func (cds *CleanDataService) QueryPage(ctx context.Context, cd *ent.CleanData, qp *entity.QueryParam) (int, []*ent.CleanData, error) {
	count, err := cds.Dao.CleanData.Query().QueryItemCleanData(cd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := cds.Dao.CleanData.Query().QueryItemCleanData(cd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 CleanData
func (cds *CleanDataService) QuerySearch(ctx context.Context, cd *ent.CleanData, qp *entity.QueryParam) (int, []*ent.CleanData, error) {
	count, err := cds.Dao.CleanData.Query().SearchCleanData(cd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := cds.Dao.CleanData.Query().SearchCleanData(cd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
