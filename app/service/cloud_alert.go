package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/cloudalert"
	"meta/app/entity"
)

type CloudAlertService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 CloudAlert
func (cas *CloudAlertService) Query(ctx context.Context, ca *ent.CloudAlert, qp *entity.QueryParam) (int, []*ent.CloudAlert, error) {
	if len(qp.Search) == 0 {
		return cas.QueryPage(ctx, ca, qp)
	} else {
		return cas.QuerySearch(ctx, ca, qp)
	}
}

// QueryByID 根据 ID 查询 CloudAlert
func (cas *CloudAlertService) QueryByID(ctx context.Context, id int) (*ent.CloudAlert, error) {
	return cas.Dao.CloudAlert.Query().Where(cloudalert.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 CloudAlert
func (cas *CloudAlertService) Create(ctx context.Context, ca *ent.CloudAlert) (*ent.CloudAlert, error) {
	return cas.Dao.CloudAlert.Create().SetItemCloudAlert(ca).Save(ctx)
}

// CreateBulk 批量创建 CloudAlert
func (cas *CloudAlertService) CreateBulk(ctx context.Context, ca []*ent.CloudAlert) ([]*ent.CloudAlert, error) {
	bulks := make([]*ent.CloudAlertCreate, len(ca))
	for i, v := range ca {
		bulks[i] = cas.Dao.CloudAlert.Create().SetItemCloudAlert(v)
	}
	return cas.Dao.CloudAlert.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 CloudAlert
func (cas *CloudAlertService) UpdateByID(ctx context.Context, ca *ent.CloudAlert, id int) (*ent.CloudAlert, error) {
	return cas.Dao.CloudAlert.UpdateOneID(id).SetItemCloudAlert(ca).Save(ctx)
}

// DeleteByID 根据 ID 删除 CloudAlert
func (cas *CloudAlertService) DeleteByID(ctx context.Context, id int) error {
	return cas.Dao.CloudAlert.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 CloudAlert
func (cas *CloudAlertService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := cas.Dao.CloudAlert.Delete().Where(cloudalert.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 CloudAlert
func (cas *CloudAlertService) QueryPage(ctx context.Context, ca *ent.CloudAlert, qp *entity.QueryParam) (int, []*ent.CloudAlert, error) {
	count, err := cas.Dao.CloudAlert.Query().QueryItemCloudAlert(ca, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := cas.Dao.CloudAlert.Query().QueryItemCloudAlert(ca, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 CloudAlert
func (cas *CloudAlertService) QuerySearch(ctx context.Context, ca *ent.CloudAlert, qp *entity.QueryParam) (int, []*ent.CloudAlert, error) {
	count, err := cas.Dao.CloudAlert.Query().SearchCloudAlert(ca, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := cas.Dao.CloudAlert.Query().SearchCloudAlert(ca, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
