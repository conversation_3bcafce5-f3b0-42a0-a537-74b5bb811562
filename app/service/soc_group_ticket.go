package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/socgroupticket"
	"meta/app/entity"
)

type SocGroupTicketService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 SocGroupTicket
func (sgts *SocGroupTicketService) Query(ctx context.Context, sgt *ent.SocGroupTicket, qp *entity.QueryParam) (int, []*ent.SocGroupTicket, error) {
	if len(qp.Search) == 0 {
		return sgts.QueryPage(ctx, sgt, qp)
	} else {
		return sgts.QuerySearch(ctx, sgt, qp)
	}
}

// QueryByID 根据 ID 查询 SocGroupTicket
func (sgts *SocGroupTicketService) QueryByID(ctx context.Context, id int) (*ent.SocGroupTicket, error) {
	return sgts.Dao.SocGroupTicket.Query().Where(socgroupticket.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 SocGroupTicket
func (sgts *SocGroupTicketService) Create(ctx context.Context, sgt *ent.SocGroupTicket) (*ent.SocGroupTicket, error) {
	return sgts.Dao.SocGroupTicket.Create().SetItemSocGroupTicket(sgt).Save(ctx)
}

// CreateBulk 批量创建 SocGroupTicket
func (sgts *SocGroupTicketService) CreateBulk(ctx context.Context, sgt []*ent.SocGroupTicket) ([]*ent.SocGroupTicket, error) {
	bulks := make([]*ent.SocGroupTicketCreate, len(sgt))
	for i, v := range sgt {
		bulks[i] = sgts.Dao.SocGroupTicket.Create().SetItemSocGroupTicket(v)
	}
	return sgts.Dao.SocGroupTicket.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 SocGroupTicket
func (sgts *SocGroupTicketService) UpdateByID(ctx context.Context, sgt *ent.SocGroupTicket, id int) (*ent.SocGroupTicket, error) {
	return sgts.Dao.SocGroupTicket.UpdateOneID(id).SetItemSocGroupTicket(sgt).Save(ctx)
}

// DeleteByID 根据 ID 删除 SocGroupTicket
func (sgts *SocGroupTicketService) DeleteByID(ctx context.Context, id int) error {
	return sgts.Dao.SocGroupTicket.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 SocGroupTicket
func (sgts *SocGroupTicketService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := sgts.Dao.SocGroupTicket.Delete().Where(socgroupticket.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 SocGroupTicket
func (sgts *SocGroupTicketService) QueryPage(ctx context.Context, sgt *ent.SocGroupTicket, qp *entity.QueryParam) (int, []*ent.SocGroupTicket, error) {
	count, err := sgts.Dao.SocGroupTicket.Query().QueryItemSocGroupTicket(sgt, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sgts.Dao.SocGroupTicket.Query().QueryItemSocGroupTicket(sgt, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 SocGroupTicket
func (sgts *SocGroupTicketService) QuerySearch(ctx context.Context, sgt *ent.SocGroupTicket, qp *entity.QueryParam) (int, []*ent.SocGroupTicket, error) {
	count, err := sgts.Dao.SocGroupTicket.Query().SearchSocGroupTicket(sgt, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := sgts.Dao.SocGroupTicket.Query().SearchSocGroupTicket(sgt, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
