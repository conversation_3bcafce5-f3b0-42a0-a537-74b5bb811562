package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/entity"
)

type MatrixSpectrumDataService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) Query(ctx context.Context, msd *ent.MatrixSpectrumData, qp *entity.QueryParam) (int, []*ent.MatrixSpectrumData, error) {
	if len(qp.Search) == 0 {
		return msds.QueryPage(ctx, msd, qp)
	} else {
		return msds.QuerySearch(ctx, msd, qp)
	}
}

// QueryByID 根据 ID 查询 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) QueryByID(ctx context.Context, id int) (*ent.MatrixSpectrumData, error) {
	return msds.Dao.MatrixSpectrumData.Query().Where(matrixspectrumdata.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) Create(ctx context.Context, msd *ent.MatrixSpectrumData) (*ent.MatrixSpectrumData, error) {
	return msds.Dao.MatrixSpectrumData.Create().SetItemMatrixSpectrumData(msd).Save(ctx)
}

// CreateBulk 批量创建 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) CreateBulk(ctx context.Context, msd []*ent.MatrixSpectrumData) ([]*ent.MatrixSpectrumData, error) {
	bulks := make([]*ent.MatrixSpectrumDataCreate, len(msd))
	for i, v := range msd {
		bulks[i] = msds.Dao.MatrixSpectrumData.Create().SetItemMatrixSpectrumData(v)
	}
	return msds.Dao.MatrixSpectrumData.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) UpdateByID(ctx context.Context, msd *ent.MatrixSpectrumData, id int) (*ent.MatrixSpectrumData, error) {
	return msds.Dao.MatrixSpectrumData.UpdateOneID(id).SetItemMatrixSpectrumData(msd).Save(ctx)
}

// DeleteByID 根据 ID 删除 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) DeleteByID(ctx context.Context, id int) error {
	return msds.Dao.MatrixSpectrumData.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := msds.Dao.MatrixSpectrumData.Delete().Where(matrixspectrumdata.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) QueryPage(ctx context.Context, msd *ent.MatrixSpectrumData, qp *entity.QueryParam) (int, []*ent.MatrixSpectrumData, error) {
	count, err := msds.Dao.MatrixSpectrumData.Query().QueryItemMatrixSpectrumData(msd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := msds.Dao.MatrixSpectrumData.Query().QueryItemMatrixSpectrumData(msd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 MatrixSpectrumData
func (msds *MatrixSpectrumDataService) QuerySearch(ctx context.Context, msd *ent.MatrixSpectrumData, qp *entity.QueryParam) (int, []*ent.MatrixSpectrumData, error) {
	count, err := msds.Dao.MatrixSpectrumData.Query().SearchMatrixSpectrumData(msd, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := msds.Dao.MatrixSpectrumData.Query().SearchMatrixSpectrumData(msd, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
