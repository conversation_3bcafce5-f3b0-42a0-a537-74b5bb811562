package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/tenant"
	"meta/app/entity"
)

type TenantService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 Tenant
func (ts *TenantService) Query(ctx context.Context, t *ent.Tenant, qp *entity.QueryParam) (int, []*ent.Tenant, error) {
	if len(qp.Search) == 0 {
		return ts.QueryPage(ctx, t, qp)
	} else {
		return ts.QuerySearch(ctx, t, qp)
	}
}

// QueryByID 根据 ID 查询 Tenant
func (ts *TenantService) QueryByID(ctx context.Context, id int) (*ent.Tenant, error) {
	return ts.Dao.Tenant.Get(ctx, id)
}

// Create 创建 Tenant
func (ts *TenantService) Create(ctx context.Context, t *ent.Tenant) (*ent.Tenant, error) {
	return ts.Dao.Tenant.Create().SetItemTenant(t).Save(ctx)
}

// CreateBulk 批量创建 Tenant
func (ts *TenantService) CreateBulk(ctx context.Context, t []*ent.Tenant) ([]*ent.Tenant, error) {
	bulks := make([]*ent.TenantCreate, len(t))
	for i, v := range t {
		bulks[i] = ts.Dao.Tenant.Create().SetItemTenant(v)
	}
	return ts.Dao.Tenant.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 Tenant
func (ts *TenantService) UpdateByID(ctx context.Context, t *ent.Tenant, id int) (*ent.Tenant, error) {
	return ts.Dao.Tenant.UpdateOneID(id).SetItemTenant(t).Save(ctx)
}

// DeleteByID 根据 ID 删除 Tenant
func (ts *TenantService) DeleteByID(ctx context.Context, id int) error {
	return ts.Dao.Tenant.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 Tenant
func (ts *TenantService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := ts.Dao.Tenant.Delete().Where(tenant.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 Tenant
func (ts *TenantService) QueryPage(ctx context.Context, t *ent.Tenant, qp *entity.QueryParam) (int, []*ent.Tenant, error) {
	count, err := ts.Dao.Tenant.Query().QueryItemTenant(t, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := ts.Dao.Tenant.Query().QueryItemTenant(t, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 Tenant
func (ts *TenantService) QuerySearch(ctx context.Context, t *ent.Tenant, qp *entity.QueryParam) (int, []*ent.Tenant, error) {
	count, err := ts.Dao.Tenant.Query().SearchTenant(t, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := ts.Dao.Tenant.Query().SearchTenant(t, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
