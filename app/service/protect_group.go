package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/protectgroup"
	"meta/app/entity"
)

type ProtectGroupService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 Group
func (pgs *ProtectGroupService) Query(ctx context.Context, pg *ent.ProtectGroup, qp *entity.QueryParam) (int, []*ent.ProtectGroup, error) {
	if len(qp.Search) == 0 {
		return pgs.QueryPage(ctx, pg, qp)
	} else {
		return pgs.QuerySearch(ctx, pg, qp)
	}
}

// QueryByID 根据 ID 查询 Group
func (pgs *ProtectGroupService) QueryByID(ctx context.Context, id int) (*ent.ProtectGroup, error) {
	return pgs.Dao.ProtectGroup.Query().Where(protectgroup.ID(id)).WithTenant().Only(ctx)
}

// Create 创建 Group
func (pgs *ProtectGroupService) Create(ctx context.Context, pg *ent.ProtectGroup) (*ent.ProtectGroup, error) {
	return pgs.Dao.ProtectGroup.Create().SetItemProtectGroup(pg).Save(ctx)
}

// CreateBulk 批量创建 Group
func (pgs *ProtectGroupService) CreateBulk(ctx context.Context, pg []*ent.ProtectGroup) ([]*ent.ProtectGroup, error) {
	bulks := make([]*ent.ProtectGroupCreate, len(pg))
	for i, v := range pg {
		bulks[i] = pgs.Dao.ProtectGroup.Create().SetItemProtectGroup(v)
	}
	return pgs.Dao.ProtectGroup.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 Group
func (pgs *ProtectGroupService) UpdateByID(ctx context.Context, pg *ent.ProtectGroup, id int) (*ent.ProtectGroup, error) {
	return pgs.Dao.ProtectGroup.UpdateOneID(id).SetItemProtectGroup(pg).Save(ctx)
}

// DeleteByID 根据 ID 删除 Group
func (pgs *ProtectGroupService) DeleteByID(ctx context.Context, id int) error {
	return pgs.Dao.ProtectGroup.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 Group
func (pgs *ProtectGroupService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := pgs.Dao.ProtectGroup.Delete().Where(protectgroup.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 Group
func (pgs *ProtectGroupService) QueryPage(ctx context.Context, pg *ent.ProtectGroup, qp *entity.QueryParam) (int, []*ent.ProtectGroup, error) {
	count, err := pgs.Dao.ProtectGroup.Query().QueryItemProtectGroup(pg, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := pgs.Dao.ProtectGroup.Query().QueryItemProtectGroup(pg, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 Group
func (pgs *ProtectGroupService) QuerySearch(ctx context.Context, pg *ent.ProtectGroup, qp *entity.QueryParam) (int, []*ent.ProtectGroup, error) {
	count, err := pgs.Dao.ProtectGroup.Query().SearchProtectGroup(pg, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := pgs.Dao.ProtectGroup.Query().SearchProtectGroup(pg, qp, false).WithTenant().All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
