package service

import (
	"context"
	"meta/app/ent"
	"meta/app/ent/matrixstrategy"
	"meta/app/entity"
)

type MatrixStrategyService struct {
	Dao *Dao
}

// Query 根据指定字段、时间范围查询或搜索 MatrixStrategy
func (mss *MatrixStrategyService) Query(ctx context.Context, ms *ent.MatrixStrategy, qp *entity.QueryParam) (int, []*ent.MatrixStrategy, error) {
	if len(qp.Search) == 0 {
		return mss.QueryPage(ctx, ms, qp)
	} else {
		return mss.QuerySearch(ctx, ms, qp)
	}
}

// QueryByID 根据 ID 查询 MatrixStrategy
func (mss *MatrixStrategyService) QueryByID(ctx context.Context, id int) (*ent.MatrixStrategy, error) {
	return mss.Dao.MatrixStrategy.Query().Where(matrixstrategy.ID(id)).Only(ctx)
}

// Create 创建 MatrixStrategy
func (mss *MatrixStrategyService) Create(ctx context.Context, ms *ent.MatrixStrategy) (*ent.MatrixStrategy, error) {
	return mss.Dao.MatrixStrategy.Create().SetItemMatrixStrategy(ms).Save(ctx)
}

// CreateBulk 批量创建 MatrixStrategy
func (mss *MatrixStrategyService) CreateBulk(ctx context.Context, ms []*ent.MatrixStrategy) ([]*ent.MatrixStrategy, error) {
	bulks := make([]*ent.MatrixStrategyCreate, len(ms))
	for i, v := range ms {
		bulks[i] = mss.Dao.MatrixStrategy.Create().SetItemMatrixStrategy(v)
	}
	return mss.Dao.MatrixStrategy.CreateBulk(bulks...).Save(ctx)
}

// UpdateByID 根据 ID 修改 MatrixStrategy
func (mss *MatrixStrategyService) UpdateByID(ctx context.Context, ms *ent.MatrixStrategy, id int) (*ent.MatrixStrategy, error) {
	return mss.Dao.MatrixStrategy.UpdateOneID(id).SetItemMatrixStrategy(ms).Save(ctx)
}

// DeleteByID 根据 ID 删除 MatrixStrategy
func (mss *MatrixStrategyService) DeleteByID(ctx context.Context, id int) error {
	return mss.Dao.MatrixStrategy.DeleteOneID(id).Exec(ctx)
}

// DeleteBulk 根据 IDs 批量删除 MatrixStrategy
func (mss *MatrixStrategyService) DeleteBulk(ctx context.Context, ids []int) (int, error) {
	count, err := mss.Dao.MatrixStrategy.Delete().Where(matrixstrategy.IDIn(ids...)).Exec(ctx)
	return count, err
}

// QueryPage 分页查询 MatrixStrategy
func (mss *MatrixStrategyService) QueryPage(ctx context.Context, ms *ent.MatrixStrategy, qp *entity.QueryParam) (int, []*ent.MatrixStrategy, error) {
	count, err := mss.Dao.MatrixStrategy.Query().QueryItemMatrixStrategy(ms, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := mss.Dao.MatrixStrategy.Query().QueryItemMatrixStrategy(ms, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}

// QuerySearch 分页搜索 MatrixStrategy
func (mss *MatrixStrategyService) QuerySearch(ctx context.Context, ms *ent.MatrixStrategy, qp *entity.QueryParam) (int, []*ent.MatrixStrategy, error) {
	count, err := mss.Dao.MatrixStrategy.Query().SearchMatrixStrategy(ms, qp, true).Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	results, err := mss.Dao.MatrixStrategy.Query().SearchMatrixStrategy(ms, qp, false).All(ctx)
	if err != nil {
		return 0, nil, err
	}
	if len(results) == 0 {
		count = 0
	}
	return count, results, nil
}
