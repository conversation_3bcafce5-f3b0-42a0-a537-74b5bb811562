// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// MatrixSpectrumData is the model entity for the MatrixSpectrumData schema.
type MatrixSpectrumData struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// MatrixSpectrumAlertID holds the value of the "matrix_spectrum_alert_id" field.
	MatrixSpectrumAlertID *int `json:"matrix_spectrum_alert_id,omitempty"`
	// Region holds the value of the "region" field.
	Region string `json:"region,omitempty"`
	// NetType holds the value of the "net_type" field.
	NetType string `json:"net_type,omitempty" query:"net_type"`
	// Isp holds the value of the "isp" field.
	Isp string `json:"isp,omitempty"`
	// Bps holds the value of the "bps" field.
	Bps int64 `json:"bps,omitempty"`
	// Time holds the value of the "time" field.
	Time time.Time `json:"time,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the MatrixSpectrumDataQuery when eager-loading is set.
	Edges        MatrixSpectrumDataEdges `json:"edges"`
	selectValues sql.SelectValues
}

// MatrixSpectrumDataEdges holds the relations/edges for other nodes in the graph.
type MatrixSpectrumDataEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// MatrixSpectrumAlert holds the value of the matrix_spectrum_alert edge.
	MatrixSpectrumAlert *MatrixSpectrumAlert `json:"matrix_spectrum_alert,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MatrixSpectrumDataEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// MatrixSpectrumAlertOrErr returns the MatrixSpectrumAlert value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MatrixSpectrumDataEdges) MatrixSpectrumAlertOrErr() (*MatrixSpectrumAlert, error) {
	if e.loadedTypes[1] {
		if e.MatrixSpectrumAlert == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: matrixspectrumalert.Label}
		}
		return e.MatrixSpectrumAlert, nil
	}
	return nil, &NotLoadedError{edge: "matrix_spectrum_alert"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*MatrixSpectrumData) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case matrixspectrumdata.FieldID, matrixspectrumdata.FieldTenantID, matrixspectrumdata.FieldMatrixSpectrumAlertID, matrixspectrumdata.FieldBps:
			values[i] = new(sql.NullInt64)
		case matrixspectrumdata.FieldRegion, matrixspectrumdata.FieldNetType, matrixspectrumdata.FieldIsp:
			values[i] = new(sql.NullString)
		case matrixspectrumdata.FieldCreatedAt, matrixspectrumdata.FieldUpdatedAt, matrixspectrumdata.FieldTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the MatrixSpectrumData fields.
func (msd *MatrixSpectrumData) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case matrixspectrumdata.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			msd.ID = int(value.Int64)
		case matrixspectrumdata.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				msd.TenantID = new(int)
				*msd.TenantID = int(value.Int64)
			}
		case matrixspectrumdata.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				msd.CreatedAt = value.Time
			}
		case matrixspectrumdata.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				msd.UpdatedAt = value.Time
			}
		case matrixspectrumdata.FieldMatrixSpectrumAlertID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field matrix_spectrum_alert_id", values[i])
			} else if value.Valid {
				msd.MatrixSpectrumAlertID = new(int)
				*msd.MatrixSpectrumAlertID = int(value.Int64)
			}
		case matrixspectrumdata.FieldRegion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field region", values[i])
			} else if value.Valid {
				msd.Region = value.String
			}
		case matrixspectrumdata.FieldNetType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field net_type", values[i])
			} else if value.Valid {
				msd.NetType = value.String
			}
		case matrixspectrumdata.FieldIsp:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field isp", values[i])
			} else if value.Valid {
				msd.Isp = value.String
			}
		case matrixspectrumdata.FieldBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field bps", values[i])
			} else if value.Valid {
				msd.Bps = value.Int64
			}
		case matrixspectrumdata.FieldTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field time", values[i])
			} else if value.Valid {
				msd.Time = value.Time
			}
		default:
			msd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the MatrixSpectrumData.
// This includes values selected through modifiers, order, etc.
func (msd *MatrixSpectrumData) Value(name string) (ent.Value, error) {
	return msd.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the MatrixSpectrumData entity.
func (msd *MatrixSpectrumData) QueryTenant() *TenantQuery {
	return NewMatrixSpectrumDataClient(msd.config).QueryTenant(msd)
}

// QueryMatrixSpectrumAlert queries the "matrix_spectrum_alert" edge of the MatrixSpectrumData entity.
func (msd *MatrixSpectrumData) QueryMatrixSpectrumAlert() *MatrixSpectrumAlertQuery {
	return NewMatrixSpectrumDataClient(msd.config).QueryMatrixSpectrumAlert(msd)
}

// Update returns a builder for updating this MatrixSpectrumData.
// Note that you need to call MatrixSpectrumData.Unwrap() before calling this method if this MatrixSpectrumData
// was returned from a transaction, and the transaction was committed or rolled back.
func (msd *MatrixSpectrumData) Update() *MatrixSpectrumDataUpdateOne {
	return NewMatrixSpectrumDataClient(msd.config).UpdateOne(msd)
}

// Unwrap unwraps the MatrixSpectrumData entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (msd *MatrixSpectrumData) Unwrap() *MatrixSpectrumData {
	_tx, ok := msd.config.driver.(*txDriver)
	if !ok {
		panic("ent: MatrixSpectrumData is not a transactional entity")
	}
	msd.config.driver = _tx.drv
	return msd
}

// String implements the fmt.Stringer.
func (msd *MatrixSpectrumData) String() string {
	var builder strings.Builder
	builder.WriteString("MatrixSpectrumData(")
	builder.WriteString(fmt.Sprintf("id=%v, ", msd.ID))
	if v := msd.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(msd.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(msd.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := msd.MatrixSpectrumAlertID; v != nil {
		builder.WriteString("matrix_spectrum_alert_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("region=")
	builder.WriteString(msd.Region)
	builder.WriteString(", ")
	builder.WriteString("net_type=")
	builder.WriteString(msd.NetType)
	builder.WriteString(", ")
	builder.WriteString("isp=")
	builder.WriteString(msd.Isp)
	builder.WriteString(", ")
	builder.WriteString("bps=")
	builder.WriteString(fmt.Sprintf("%v", msd.Bps))
	builder.WriteString(", ")
	builder.WriteString("time=")
	builder.WriteString(msd.Time.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// MatrixSpectrumDataSlice is a parsable slice of MatrixSpectrumData.
type MatrixSpectrumDataSlice []*MatrixSpectrumData
