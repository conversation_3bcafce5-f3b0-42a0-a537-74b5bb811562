// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudAlertQuery is the builder for querying CloudAlert entities.
type CloudAlertQuery struct {
	config
	ctx                *QueryContext
	order              []cloudalert.OrderOption
	inters             []Interceptor
	predicates         []predicate.CloudAlert
	withTenant         *TenantQuery
	withCloudflowDatas *CloudFlowDataQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the CloudAlertQuery builder.
func (caq *CloudAlertQuery) Where(ps ...predicate.CloudAlert) *CloudAlertQuery {
	caq.predicates = append(caq.predicates, ps...)
	return caq
}

// Limit the number of records to be returned by this query.
func (caq *CloudAlertQuery) Limit(limit int) *CloudAlertQuery {
	caq.ctx.Limit = &limit
	return caq
}

// Offset to start from.
func (caq *CloudAlertQuery) Offset(offset int) *CloudAlertQuery {
	caq.ctx.Offset = &offset
	return caq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (caq *CloudAlertQuery) Unique(unique bool) *CloudAlertQuery {
	caq.ctx.Unique = &unique
	return caq
}

// Order specifies how the records should be ordered.
func (caq *CloudAlertQuery) Order(o ...cloudalert.OrderOption) *CloudAlertQuery {
	caq.order = append(caq.order, o...)
	return caq
}

// QueryTenant chains the current query on the "tenant" edge.
func (caq *CloudAlertQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: caq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := caq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := caq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudalert.Table, cloudalert.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cloudalert.TenantTable, cloudalert.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(caq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryCloudflowDatas chains the current query on the "cloudflow_datas" edge.
func (caq *CloudAlertQuery) QueryCloudflowDatas() *CloudFlowDataQuery {
	query := (&CloudFlowDataClient{config: caq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := caq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := caq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudalert.Table, cloudalert.FieldID, selector),
			sqlgraph.To(cloudflowdata.Table, cloudflowdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, cloudalert.CloudflowDatasTable, cloudalert.CloudflowDatasColumn),
		)
		fromU = sqlgraph.SetNeighbors(caq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first CloudAlert entity from the query.
// Returns a *NotFoundError when no CloudAlert was found.
func (caq *CloudAlertQuery) First(ctx context.Context) (*CloudAlert, error) {
	nodes, err := caq.Limit(1).All(setContextOp(ctx, caq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{cloudalert.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (caq *CloudAlertQuery) FirstX(ctx context.Context) *CloudAlert {
	node, err := caq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first CloudAlert ID from the query.
// Returns a *NotFoundError when no CloudAlert ID was found.
func (caq *CloudAlertQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = caq.Limit(1).IDs(setContextOp(ctx, caq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{cloudalert.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (caq *CloudAlertQuery) FirstIDX(ctx context.Context) int {
	id, err := caq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single CloudAlert entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one CloudAlert entity is found.
// Returns a *NotFoundError when no CloudAlert entities are found.
func (caq *CloudAlertQuery) Only(ctx context.Context) (*CloudAlert, error) {
	nodes, err := caq.Limit(2).All(setContextOp(ctx, caq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{cloudalert.Label}
	default:
		return nil, &NotSingularError{cloudalert.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (caq *CloudAlertQuery) OnlyX(ctx context.Context) *CloudAlert {
	node, err := caq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only CloudAlert ID in the query.
// Returns a *NotSingularError when more than one CloudAlert ID is found.
// Returns a *NotFoundError when no entities are found.
func (caq *CloudAlertQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = caq.Limit(2).IDs(setContextOp(ctx, caq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{cloudalert.Label}
	default:
		err = &NotSingularError{cloudalert.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (caq *CloudAlertQuery) OnlyIDX(ctx context.Context) int {
	id, err := caq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of CloudAlerts.
func (caq *CloudAlertQuery) All(ctx context.Context) ([]*CloudAlert, error) {
	ctx = setContextOp(ctx, caq.ctx, "All")
	if err := caq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*CloudAlert, *CloudAlertQuery]()
	return withInterceptors[[]*CloudAlert](ctx, caq, qr, caq.inters)
}

// AllX is like All, but panics if an error occurs.
func (caq *CloudAlertQuery) AllX(ctx context.Context) []*CloudAlert {
	nodes, err := caq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of CloudAlert IDs.
func (caq *CloudAlertQuery) IDs(ctx context.Context) (ids []int, err error) {
	if caq.ctx.Unique == nil && caq.path != nil {
		caq.Unique(true)
	}
	ctx = setContextOp(ctx, caq.ctx, "IDs")
	if err = caq.Select(cloudalert.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (caq *CloudAlertQuery) IDsX(ctx context.Context) []int {
	ids, err := caq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (caq *CloudAlertQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, caq.ctx, "Count")
	if err := caq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, caq, querierCount[*CloudAlertQuery](), caq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (caq *CloudAlertQuery) CountX(ctx context.Context) int {
	count, err := caq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (caq *CloudAlertQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, caq.ctx, "Exist")
	switch _, err := caq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (caq *CloudAlertQuery) ExistX(ctx context.Context) bool {
	exist, err := caq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the CloudAlertQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (caq *CloudAlertQuery) Clone() *CloudAlertQuery {
	if caq == nil {
		return nil
	}
	return &CloudAlertQuery{
		config:             caq.config,
		ctx:                caq.ctx.Clone(),
		order:              append([]cloudalert.OrderOption{}, caq.order...),
		inters:             append([]Interceptor{}, caq.inters...),
		predicates:         append([]predicate.CloudAlert{}, caq.predicates...),
		withTenant:         caq.withTenant.Clone(),
		withCloudflowDatas: caq.withCloudflowDatas.Clone(),
		// clone intermediate query.
		sql:  caq.sql.Clone(),
		path: caq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (caq *CloudAlertQuery) WithTenant(opts ...func(*TenantQuery)) *CloudAlertQuery {
	query := (&TenantClient{config: caq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	caq.withTenant = query
	return caq
}

// WithCloudflowDatas tells the query-builder to eager-load the nodes that are connected to
// the "cloudflow_datas" edge. The optional arguments are used to configure the query builder of the edge.
func (caq *CloudAlertQuery) WithCloudflowDatas(opts ...func(*CloudFlowDataQuery)) *CloudAlertQuery {
	query := (&CloudFlowDataClient{config: caq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	caq.withCloudflowDatas = query
	return caq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.CloudAlert.Query().
//		GroupBy(cloudalert.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (caq *CloudAlertQuery) GroupBy(field string, fields ...string) *CloudAlertGroupBy {
	caq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &CloudAlertGroupBy{build: caq}
	grbuild.flds = &caq.ctx.Fields
	grbuild.label = cloudalert.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.CloudAlert.Query().
//		Select(cloudalert.FieldTenantID).
//		Scan(ctx, &v)
func (caq *CloudAlertQuery) Select(fields ...string) *CloudAlertSelect {
	caq.ctx.Fields = append(caq.ctx.Fields, fields...)
	sbuild := &CloudAlertSelect{CloudAlertQuery: caq}
	sbuild.label = cloudalert.Label
	sbuild.flds, sbuild.scan = &caq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a CloudAlertSelect configured with the given aggregations.
func (caq *CloudAlertQuery) Aggregate(fns ...AggregateFunc) *CloudAlertSelect {
	return caq.Select().Aggregate(fns...)
}

func (caq *CloudAlertQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range caq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, caq); err != nil {
				return err
			}
		}
	}
	for _, f := range caq.ctx.Fields {
		if !cloudalert.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if caq.path != nil {
		prev, err := caq.path(ctx)
		if err != nil {
			return err
		}
		caq.sql = prev
	}
	if cloudalert.Policy == nil {
		return errors.New("ent: uninitialized cloudalert.Policy (forgotten import ent/runtime?)")
	}
	if err := cloudalert.Policy.EvalQuery(ctx, caq); err != nil {
		return err
	}
	return nil
}

func (caq *CloudAlertQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*CloudAlert, error) {
	var (
		nodes       = []*CloudAlert{}
		_spec       = caq.querySpec()
		loadedTypes = [2]bool{
			caq.withTenant != nil,
			caq.withCloudflowDatas != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*CloudAlert).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &CloudAlert{config: caq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, caq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := caq.withTenant; query != nil {
		if err := caq.loadTenant(ctx, query, nodes, nil,
			func(n *CloudAlert, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := caq.withCloudflowDatas; query != nil {
		if err := caq.loadCloudflowDatas(ctx, query, nodes,
			func(n *CloudAlert) { n.Edges.CloudflowDatas = []*CloudFlowData{} },
			func(n *CloudAlert, e *CloudFlowData) { n.Edges.CloudflowDatas = append(n.Edges.CloudflowDatas, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (caq *CloudAlertQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*CloudAlert, init func(*CloudAlert), assign func(*CloudAlert, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*CloudAlert)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (caq *CloudAlertQuery) loadCloudflowDatas(ctx context.Context, query *CloudFlowDataQuery, nodes []*CloudAlert, init func(*CloudAlert), assign func(*CloudAlert, *CloudFlowData)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*CloudAlert)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(cloudflowdata.FieldCloudAlertID)
	}
	query.Where(predicate.CloudFlowData(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(cloudalert.CloudflowDatasColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.CloudAlertID
		if fk == nil {
			return fmt.Errorf(`foreign-key "cloud_alert_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "cloud_alert_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (caq *CloudAlertQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := caq.querySpec()
	_spec.Node.Columns = caq.ctx.Fields
	if len(caq.ctx.Fields) > 0 {
		_spec.Unique = caq.ctx.Unique != nil && *caq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, caq.driver, _spec)
}

func (caq *CloudAlertQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(cloudalert.Table, cloudalert.Columns, sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt))
	_spec.From = caq.sql
	if unique := caq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if caq.path != nil {
		_spec.Unique = true
	}
	if fields := caq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cloudalert.FieldID)
		for i := range fields {
			if fields[i] != cloudalert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if caq.withTenant != nil {
			_spec.Node.AddColumnOnce(cloudalert.FieldTenantID)
		}
	}
	if ps := caq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := caq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := caq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := caq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (caq *CloudAlertQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(caq.driver.Dialect())
	t1 := builder.Table(cloudalert.Table)
	columns := caq.ctx.Fields
	if len(columns) == 0 {
		columns = cloudalert.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if caq.sql != nil {
		selector = caq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if caq.ctx.Unique != nil && *caq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range caq.predicates {
		p(selector)
	}
	for _, p := range caq.order {
		p(selector)
	}
	if offset := caq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := caq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// CloudAlertGroupBy is the group-by builder for CloudAlert entities.
type CloudAlertGroupBy struct {
	selector
	build *CloudAlertQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (cagb *CloudAlertGroupBy) Aggregate(fns ...AggregateFunc) *CloudAlertGroupBy {
	cagb.fns = append(cagb.fns, fns...)
	return cagb
}

// Scan applies the selector query and scans the result into the given value.
func (cagb *CloudAlertGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cagb.build.ctx, "GroupBy")
	if err := cagb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CloudAlertQuery, *CloudAlertGroupBy](ctx, cagb.build, cagb, cagb.build.inters, v)
}

func (cagb *CloudAlertGroupBy) sqlScan(ctx context.Context, root *CloudAlertQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(cagb.fns))
	for _, fn := range cagb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*cagb.flds)+len(cagb.fns))
		for _, f := range *cagb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*cagb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cagb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// CloudAlertSelect is the builder for selecting fields of CloudAlert entities.
type CloudAlertSelect struct {
	*CloudAlertQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cas *CloudAlertSelect) Aggregate(fns ...AggregateFunc) *CloudAlertSelect {
	cas.fns = append(cas.fns, fns...)
	return cas
}

// Scan applies the selector query and scans the result into the given value.
func (cas *CloudAlertSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cas.ctx, "Select")
	if err := cas.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CloudAlertQuery, *CloudAlertSelect](ctx, cas.CloudAlertQuery, cas, cas.inters, v)
}

func (cas *CloudAlertSelect) sqlScan(ctx context.Context, root *CloudAlertQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cas.fns))
	for _, fn := range cas.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cas.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cas.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
