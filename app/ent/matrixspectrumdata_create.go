// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumDataCreate is the builder for creating a MatrixSpectrumData entity.
type MatrixSpectrumDataCreate struct {
	config
	mutation *MatrixSpectrumDataMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (msdc *MatrixSpectrumDataCreate) SetTenantID(i int) *MatrixSpectrumDataCreate {
	msdc.mutation.SetTenantID(i)
	return msdc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (msdc *MatrixSpectrumDataCreate) SetNillableTenantID(i *int) *MatrixSpectrumDataCreate {
	if i != nil {
		msdc.SetTenantID(*i)
	}
	return msdc
}

// SetCreatedAt sets the "created_at" field.
func (msdc *MatrixSpectrumDataCreate) SetCreatedAt(t time.Time) *MatrixSpectrumDataCreate {
	msdc.mutation.SetCreatedAt(t)
	return msdc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (msdc *MatrixSpectrumDataCreate) SetNillableCreatedAt(t *time.Time) *MatrixSpectrumDataCreate {
	if t != nil {
		msdc.SetCreatedAt(*t)
	}
	return msdc
}

// SetUpdatedAt sets the "updated_at" field.
func (msdc *MatrixSpectrumDataCreate) SetUpdatedAt(t time.Time) *MatrixSpectrumDataCreate {
	msdc.mutation.SetUpdatedAt(t)
	return msdc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (msdc *MatrixSpectrumDataCreate) SetNillableUpdatedAt(t *time.Time) *MatrixSpectrumDataCreate {
	if t != nil {
		msdc.SetUpdatedAt(*t)
	}
	return msdc
}

// SetMatrixSpectrumAlertID sets the "matrix_spectrum_alert_id" field.
func (msdc *MatrixSpectrumDataCreate) SetMatrixSpectrumAlertID(i int) *MatrixSpectrumDataCreate {
	msdc.mutation.SetMatrixSpectrumAlertID(i)
	return msdc
}

// SetNillableMatrixSpectrumAlertID sets the "matrix_spectrum_alert_id" field if the given value is not nil.
func (msdc *MatrixSpectrumDataCreate) SetNillableMatrixSpectrumAlertID(i *int) *MatrixSpectrumDataCreate {
	if i != nil {
		msdc.SetMatrixSpectrumAlertID(*i)
	}
	return msdc
}

// SetRegion sets the "region" field.
func (msdc *MatrixSpectrumDataCreate) SetRegion(s string) *MatrixSpectrumDataCreate {
	msdc.mutation.SetRegion(s)
	return msdc
}

// SetNetType sets the "net_type" field.
func (msdc *MatrixSpectrumDataCreate) SetNetType(s string) *MatrixSpectrumDataCreate {
	msdc.mutation.SetNetType(s)
	return msdc
}

// SetIsp sets the "isp" field.
func (msdc *MatrixSpectrumDataCreate) SetIsp(s string) *MatrixSpectrumDataCreate {
	msdc.mutation.SetIsp(s)
	return msdc
}

// SetBps sets the "bps" field.
func (msdc *MatrixSpectrumDataCreate) SetBps(i int64) *MatrixSpectrumDataCreate {
	msdc.mutation.SetBps(i)
	return msdc
}

// SetTime sets the "time" field.
func (msdc *MatrixSpectrumDataCreate) SetTime(t time.Time) *MatrixSpectrumDataCreate {
	msdc.mutation.SetTime(t)
	return msdc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (msdc *MatrixSpectrumDataCreate) SetTenant(t *Tenant) *MatrixSpectrumDataCreate {
	return msdc.SetTenantID(t.ID)
}

// SetMatrixSpectrumAlert sets the "matrix_spectrum_alert" edge to the MatrixSpectrumAlert entity.
func (msdc *MatrixSpectrumDataCreate) SetMatrixSpectrumAlert(m *MatrixSpectrumAlert) *MatrixSpectrumDataCreate {
	return msdc.SetMatrixSpectrumAlertID(m.ID)
}

// Mutation returns the MatrixSpectrumDataMutation object of the builder.
func (msdc *MatrixSpectrumDataCreate) Mutation() *MatrixSpectrumDataMutation {
	return msdc.mutation
}

// Save creates the MatrixSpectrumData in the database.
func (msdc *MatrixSpectrumDataCreate) Save(ctx context.Context) (*MatrixSpectrumData, error) {
	if err := msdc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, msdc.sqlSave, msdc.mutation, msdc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (msdc *MatrixSpectrumDataCreate) SaveX(ctx context.Context) *MatrixSpectrumData {
	v, err := msdc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (msdc *MatrixSpectrumDataCreate) Exec(ctx context.Context) error {
	_, err := msdc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msdc *MatrixSpectrumDataCreate) ExecX(ctx context.Context) {
	if err := msdc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msdc *MatrixSpectrumDataCreate) defaults() error {
	if _, ok := msdc.mutation.CreatedAt(); !ok {
		if matrixspectrumdata.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixspectrumdata.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := matrixspectrumdata.DefaultCreatedAt()
		msdc.mutation.SetCreatedAt(v)
	}
	if _, ok := msdc.mutation.UpdatedAt(); !ok {
		if matrixspectrumdata.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixspectrumdata.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixspectrumdata.DefaultUpdatedAt()
		msdc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (msdc *MatrixSpectrumDataCreate) check() error {
	if _, ok := msdc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "MatrixSpectrumData.created_at"`)}
	}
	if _, ok := msdc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "MatrixSpectrumData.updated_at"`)}
	}
	if _, ok := msdc.mutation.Region(); !ok {
		return &ValidationError{Name: "region", err: errors.New(`ent: missing required field "MatrixSpectrumData.region"`)}
	}
	if _, ok := msdc.mutation.NetType(); !ok {
		return &ValidationError{Name: "net_type", err: errors.New(`ent: missing required field "MatrixSpectrumData.net_type"`)}
	}
	if _, ok := msdc.mutation.Isp(); !ok {
		return &ValidationError{Name: "isp", err: errors.New(`ent: missing required field "MatrixSpectrumData.isp"`)}
	}
	if _, ok := msdc.mutation.Bps(); !ok {
		return &ValidationError{Name: "bps", err: errors.New(`ent: missing required field "MatrixSpectrumData.bps"`)}
	}
	if _, ok := msdc.mutation.Time(); !ok {
		return &ValidationError{Name: "time", err: errors.New(`ent: missing required field "MatrixSpectrumData.time"`)}
	}
	return nil
}

func (msdc *MatrixSpectrumDataCreate) sqlSave(ctx context.Context) (*MatrixSpectrumData, error) {
	if err := msdc.check(); err != nil {
		return nil, err
	}
	_node, _spec := msdc.createSpec()
	if err := sqlgraph.CreateNode(ctx, msdc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	msdc.mutation.id = &_node.ID
	msdc.mutation.done = true
	return _node, nil
}

func (msdc *MatrixSpectrumDataCreate) createSpec() (*MatrixSpectrumData, *sqlgraph.CreateSpec) {
	var (
		_node = &MatrixSpectrumData{config: msdc.config}
		_spec = sqlgraph.NewCreateSpec(matrixspectrumdata.Table, sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt))
	)
	if value, ok := msdc.mutation.CreatedAt(); ok {
		_spec.SetField(matrixspectrumdata.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := msdc.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixspectrumdata.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := msdc.mutation.Region(); ok {
		_spec.SetField(matrixspectrumdata.FieldRegion, field.TypeString, value)
		_node.Region = value
	}
	if value, ok := msdc.mutation.NetType(); ok {
		_spec.SetField(matrixspectrumdata.FieldNetType, field.TypeString, value)
		_node.NetType = value
	}
	if value, ok := msdc.mutation.Isp(); ok {
		_spec.SetField(matrixspectrumdata.FieldIsp, field.TypeString, value)
		_node.Isp = value
	}
	if value, ok := msdc.mutation.Bps(); ok {
		_spec.SetField(matrixspectrumdata.FieldBps, field.TypeInt64, value)
		_node.Bps = value
	}
	if value, ok := msdc.mutation.Time(); ok {
		_spec.SetField(matrixspectrumdata.FieldTime, field.TypeTime, value)
		_node.Time = value
	}
	if nodes := msdc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumdata.TenantTable,
			Columns: []string{matrixspectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := msdc.mutation.MatrixSpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumdata.MatrixSpectrumAlertTable,
			Columns: []string{matrixspectrumdata.MatrixSpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.MatrixSpectrumAlertID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// MatrixSpectrumDataCreateBulk is the builder for creating many MatrixSpectrumData entities in bulk.
type MatrixSpectrumDataCreateBulk struct {
	config
	err      error
	builders []*MatrixSpectrumDataCreate
}

// Save creates the MatrixSpectrumData entities in the database.
func (msdcb *MatrixSpectrumDataCreateBulk) Save(ctx context.Context) ([]*MatrixSpectrumData, error) {
	if msdcb.err != nil {
		return nil, msdcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(msdcb.builders))
	nodes := make([]*MatrixSpectrumData, len(msdcb.builders))
	mutators := make([]Mutator, len(msdcb.builders))
	for i := range msdcb.builders {
		func(i int, root context.Context) {
			builder := msdcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*MatrixSpectrumDataMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, msdcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, msdcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, msdcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (msdcb *MatrixSpectrumDataCreateBulk) SaveX(ctx context.Context) []*MatrixSpectrumData {
	v, err := msdcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (msdcb *MatrixSpectrumDataCreateBulk) Exec(ctx context.Context) error {
	_, err := msdcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msdcb *MatrixSpectrumDataCreateBulk) ExecX(ctx context.Context) {
	if err := msdcb.Exec(ctx); err != nil {
		panic(err)
	}
}
