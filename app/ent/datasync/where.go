// Code generated by ent, DO NOT EDIT.

package datasync

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldID, id))
}

// ID<PERSON><PERSON> applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.DataSync {
	return predicate.DataSync(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.DataSync {
	return predicate.DataSync(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.DataSync {
	return predicate.DataSync(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.DataSync {
	return predicate.DataSync(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.DataSync {
	return predicate.DataSync(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.DataSync {
	return predicate.DataSync(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.DataSync {
	return predicate.DataSync(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldRemark, v))
}

// DataType applies equality check predicate on the "data_type" field. It's identical to DataTypeEQ.
func DataType(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldDataType, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldType, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.DataSync {
	return predicate.DataSync(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.DataSync {
	return predicate.DataSync(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.DataSync {
	return predicate.DataSync(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.DataSync {
	return predicate.DataSync(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.DataSync {
	return predicate.DataSync(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldContainsFold(FieldRemark, v))
}

// PreDataListIsNil applies the IsNil predicate on the "pre_data_list" field.
func PreDataListIsNil() predicate.DataSync {
	return predicate.DataSync(sql.FieldIsNull(FieldPreDataList))
}

// PreDataListNotNil applies the NotNil predicate on the "pre_data_list" field.
func PreDataListNotNil() predicate.DataSync {
	return predicate.DataSync(sql.FieldNotNull(FieldPreDataList))
}

// DataListIsNil applies the IsNil predicate on the "data_list" field.
func DataListIsNil() predicate.DataSync {
	return predicate.DataSync(sql.FieldIsNull(FieldDataList))
}

// DataListNotNil applies the NotNil predicate on the "data_list" field.
func DataListNotNil() predicate.DataSync {
	return predicate.DataSync(sql.FieldNotNull(FieldDataList))
}

// DataTypeEQ applies the EQ predicate on the "data_type" field.
func DataTypeEQ(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldDataType, v))
}

// DataTypeNEQ applies the NEQ predicate on the "data_type" field.
func DataTypeNEQ(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldNEQ(FieldDataType, v))
}

// DataTypeIn applies the In predicate on the "data_type" field.
func DataTypeIn(vs ...string) predicate.DataSync {
	return predicate.DataSync(sql.FieldIn(FieldDataType, vs...))
}

// DataTypeNotIn applies the NotIn predicate on the "data_type" field.
func DataTypeNotIn(vs ...string) predicate.DataSync {
	return predicate.DataSync(sql.FieldNotIn(FieldDataType, vs...))
}

// DataTypeGT applies the GT predicate on the "data_type" field.
func DataTypeGT(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldGT(FieldDataType, v))
}

// DataTypeGTE applies the GTE predicate on the "data_type" field.
func DataTypeGTE(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldGTE(FieldDataType, v))
}

// DataTypeLT applies the LT predicate on the "data_type" field.
func DataTypeLT(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldLT(FieldDataType, v))
}

// DataTypeLTE applies the LTE predicate on the "data_type" field.
func DataTypeLTE(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldLTE(FieldDataType, v))
}

// DataTypeContains applies the Contains predicate on the "data_type" field.
func DataTypeContains(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldContains(FieldDataType, v))
}

// DataTypeHasPrefix applies the HasPrefix predicate on the "data_type" field.
func DataTypeHasPrefix(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldHasPrefix(FieldDataType, v))
}

// DataTypeHasSuffix applies the HasSuffix predicate on the "data_type" field.
func DataTypeHasSuffix(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldHasSuffix(FieldDataType, v))
}

// DataTypeEqualFold applies the EqualFold predicate on the "data_type" field.
func DataTypeEqualFold(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEqualFold(FieldDataType, v))
}

// DataTypeContainsFold applies the ContainsFold predicate on the "data_type" field.
func DataTypeContainsFold(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldContainsFold(FieldDataType, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.DataSync {
	return predicate.DataSync(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.DataSync {
	return predicate.DataSync(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.DataSync {
	return predicate.DataSync(sql.FieldContainsFold(FieldType, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DataSync) predicate.DataSync {
	return predicate.DataSync(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DataSync) predicate.DataSync {
	return predicate.DataSync(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DataSync) predicate.DataSync {
	return predicate.DataSync(sql.NotPredicates(p))
}
