// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/casbinrule"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CasbinRuleCreate is the builder for creating a CasbinRule entity.
type CasbinRuleCreate struct {
	config
	mutation *CasbinRuleMutation
	hooks    []Hook
}

// SetType sets the "type" field.
func (crc *CasbinRuleCreate) SetType(s string) *CasbinRuleCreate {
	crc.mutation.SetType(s)
	return crc
}

// SetSub sets the "sub" field.
func (crc *CasbinRuleCreate) SetSub(s string) *CasbinRuleCreate {
	crc.mutation.SetSub(s)
	return crc
}

// SetDom sets the "dom" field.
func (crc *CasbinRuleCreate) SetDom(s string) *CasbinRuleCreate {
	crc.mutation.SetDom(s)
	return crc
}

// SetObj sets the "obj" field.
func (crc *CasbinRuleCreate) SetObj(s string) *CasbinRuleCreate {
	crc.mutation.SetObj(s)
	return crc
}

// SetAct sets the "act" field.
func (crc *CasbinRuleCreate) SetAct(s string) *CasbinRuleCreate {
	crc.mutation.SetAct(s)
	return crc
}

// SetV4 sets the "v4" field.
func (crc *CasbinRuleCreate) SetV4(s string) *CasbinRuleCreate {
	crc.mutation.SetV4(s)
	return crc
}

// SetV5 sets the "v5" field.
func (crc *CasbinRuleCreate) SetV5(s string) *CasbinRuleCreate {
	crc.mutation.SetV5(s)
	return crc
}

// Mutation returns the CasbinRuleMutation object of the builder.
func (crc *CasbinRuleCreate) Mutation() *CasbinRuleMutation {
	return crc.mutation
}

// Save creates the CasbinRule in the database.
func (crc *CasbinRuleCreate) Save(ctx context.Context) (*CasbinRule, error) {
	return withHooks(ctx, crc.sqlSave, crc.mutation, crc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (crc *CasbinRuleCreate) SaveX(ctx context.Context) *CasbinRule {
	v, err := crc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (crc *CasbinRuleCreate) Exec(ctx context.Context) error {
	_, err := crc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (crc *CasbinRuleCreate) ExecX(ctx context.Context) {
	if err := crc.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (crc *CasbinRuleCreate) check() error {
	if _, ok := crc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "CasbinRule.type"`)}
	}
	if v, ok := crc.mutation.GetType(); ok {
		if err := casbinrule.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.type": %w`, err)}
		}
	}
	if _, ok := crc.mutation.Sub(); !ok {
		return &ValidationError{Name: "sub", err: errors.New(`ent: missing required field "CasbinRule.sub"`)}
	}
	if v, ok := crc.mutation.Sub(); ok {
		if err := casbinrule.SubValidator(v); err != nil {
			return &ValidationError{Name: "sub", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.sub": %w`, err)}
		}
	}
	if _, ok := crc.mutation.Dom(); !ok {
		return &ValidationError{Name: "dom", err: errors.New(`ent: missing required field "CasbinRule.dom"`)}
	}
	if v, ok := crc.mutation.Dom(); ok {
		if err := casbinrule.DomValidator(v); err != nil {
			return &ValidationError{Name: "dom", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.dom": %w`, err)}
		}
	}
	if _, ok := crc.mutation.Obj(); !ok {
		return &ValidationError{Name: "obj", err: errors.New(`ent: missing required field "CasbinRule.obj"`)}
	}
	if v, ok := crc.mutation.Obj(); ok {
		if err := casbinrule.ObjValidator(v); err != nil {
			return &ValidationError{Name: "obj", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.obj": %w`, err)}
		}
	}
	if _, ok := crc.mutation.Act(); !ok {
		return &ValidationError{Name: "act", err: errors.New(`ent: missing required field "CasbinRule.act"`)}
	}
	if _, ok := crc.mutation.V4(); !ok {
		return &ValidationError{Name: "v4", err: errors.New(`ent: missing required field "CasbinRule.v4"`)}
	}
	if _, ok := crc.mutation.V5(); !ok {
		return &ValidationError{Name: "v5", err: errors.New(`ent: missing required field "CasbinRule.v5"`)}
	}
	return nil
}

func (crc *CasbinRuleCreate) sqlSave(ctx context.Context) (*CasbinRule, error) {
	if err := crc.check(); err != nil {
		return nil, err
	}
	_node, _spec := crc.createSpec()
	if err := sqlgraph.CreateNode(ctx, crc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	crc.mutation.id = &_node.ID
	crc.mutation.done = true
	return _node, nil
}

func (crc *CasbinRuleCreate) createSpec() (*CasbinRule, *sqlgraph.CreateSpec) {
	var (
		_node = &CasbinRule{config: crc.config}
		_spec = sqlgraph.NewCreateSpec(casbinrule.Table, sqlgraph.NewFieldSpec(casbinrule.FieldID, field.TypeInt))
	)
	if value, ok := crc.mutation.GetType(); ok {
		_spec.SetField(casbinrule.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := crc.mutation.Sub(); ok {
		_spec.SetField(casbinrule.FieldSub, field.TypeString, value)
		_node.Sub = value
	}
	if value, ok := crc.mutation.Dom(); ok {
		_spec.SetField(casbinrule.FieldDom, field.TypeString, value)
		_node.Dom = value
	}
	if value, ok := crc.mutation.Obj(); ok {
		_spec.SetField(casbinrule.FieldObj, field.TypeString, value)
		_node.Obj = value
	}
	if value, ok := crc.mutation.Act(); ok {
		_spec.SetField(casbinrule.FieldAct, field.TypeString, value)
		_node.Act = value
	}
	if value, ok := crc.mutation.V4(); ok {
		_spec.SetField(casbinrule.FieldV4, field.TypeString, value)
		_node.V4 = value
	}
	if value, ok := crc.mutation.V5(); ok {
		_spec.SetField(casbinrule.FieldV5, field.TypeString, value)
		_node.V5 = value
	}
	return _node, _spec
}

// CasbinRuleCreateBulk is the builder for creating many CasbinRule entities in bulk.
type CasbinRuleCreateBulk struct {
	config
	err      error
	builders []*CasbinRuleCreate
}

// Save creates the CasbinRule entities in the database.
func (crcb *CasbinRuleCreateBulk) Save(ctx context.Context) ([]*CasbinRule, error) {
	if crcb.err != nil {
		return nil, crcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(crcb.builders))
	nodes := make([]*CasbinRule, len(crcb.builders))
	mutators := make([]Mutator, len(crcb.builders))
	for i := range crcb.builders {
		func(i int, root context.Context) {
			builder := crcb.builders[i]
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CasbinRuleMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, crcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, crcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, crcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (crcb *CasbinRuleCreateBulk) SaveX(ctx context.Context) []*CasbinRule {
	v, err := crcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (crcb *CasbinRuleCreateBulk) Exec(ctx context.Context) error {
	_, err := crcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (crcb *CasbinRuleCreateBulk) ExecX(ctx context.Context) {
	if err := crcb.Exec(ctx); err != nil {
		panic(err)
	}
}
