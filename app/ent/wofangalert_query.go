// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"meta/app/ent/wofangalert"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WofangAlertQuery is the builder for querying WofangAlert entities.
type WofangAlertQuery struct {
	config
	ctx        *QueryContext
	order      []wofangalert.OrderOption
	inters     []Interceptor
	predicates []predicate.WofangAlert
	withTenant *TenantQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the WofangAlertQuery builder.
func (waq *WofangAlertQuery) Where(ps ...predicate.WofangAlert) *WofangAlertQuery {
	waq.predicates = append(waq.predicates, ps...)
	return waq
}

// Limit the number of records to be returned by this query.
func (waq *WofangAlertQuery) Limit(limit int) *WofangAlertQuery {
	waq.ctx.Limit = &limit
	return waq
}

// Offset to start from.
func (waq *WofangAlertQuery) Offset(offset int) *WofangAlertQuery {
	waq.ctx.Offset = &offset
	return waq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (waq *WofangAlertQuery) Unique(unique bool) *WofangAlertQuery {
	waq.ctx.Unique = &unique
	return waq
}

// Order specifies how the records should be ordered.
func (waq *WofangAlertQuery) Order(o ...wofangalert.OrderOption) *WofangAlertQuery {
	waq.order = append(waq.order, o...)
	return waq
}

// QueryTenant chains the current query on the "tenant" edge.
func (waq *WofangAlertQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: waq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := waq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := waq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(wofangalert.Table, wofangalert.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, wofangalert.TenantTable, wofangalert.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(waq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first WofangAlert entity from the query.
// Returns a *NotFoundError when no WofangAlert was found.
func (waq *WofangAlertQuery) First(ctx context.Context) (*WofangAlert, error) {
	nodes, err := waq.Limit(1).All(setContextOp(ctx, waq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{wofangalert.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (waq *WofangAlertQuery) FirstX(ctx context.Context) *WofangAlert {
	node, err := waq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first WofangAlert ID from the query.
// Returns a *NotFoundError when no WofangAlert ID was found.
func (waq *WofangAlertQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = waq.Limit(1).IDs(setContextOp(ctx, waq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{wofangalert.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (waq *WofangAlertQuery) FirstIDX(ctx context.Context) int {
	id, err := waq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single WofangAlert entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one WofangAlert entity is found.
// Returns a *NotFoundError when no WofangAlert entities are found.
func (waq *WofangAlertQuery) Only(ctx context.Context) (*WofangAlert, error) {
	nodes, err := waq.Limit(2).All(setContextOp(ctx, waq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{wofangalert.Label}
	default:
		return nil, &NotSingularError{wofangalert.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (waq *WofangAlertQuery) OnlyX(ctx context.Context) *WofangAlert {
	node, err := waq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only WofangAlert ID in the query.
// Returns a *NotSingularError when more than one WofangAlert ID is found.
// Returns a *NotFoundError when no entities are found.
func (waq *WofangAlertQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = waq.Limit(2).IDs(setContextOp(ctx, waq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{wofangalert.Label}
	default:
		err = &NotSingularError{wofangalert.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (waq *WofangAlertQuery) OnlyIDX(ctx context.Context) int {
	id, err := waq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of WofangAlerts.
func (waq *WofangAlertQuery) All(ctx context.Context) ([]*WofangAlert, error) {
	ctx = setContextOp(ctx, waq.ctx, "All")
	if err := waq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*WofangAlert, *WofangAlertQuery]()
	return withInterceptors[[]*WofangAlert](ctx, waq, qr, waq.inters)
}

// AllX is like All, but panics if an error occurs.
func (waq *WofangAlertQuery) AllX(ctx context.Context) []*WofangAlert {
	nodes, err := waq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of WofangAlert IDs.
func (waq *WofangAlertQuery) IDs(ctx context.Context) (ids []int, err error) {
	if waq.ctx.Unique == nil && waq.path != nil {
		waq.Unique(true)
	}
	ctx = setContextOp(ctx, waq.ctx, "IDs")
	if err = waq.Select(wofangalert.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (waq *WofangAlertQuery) IDsX(ctx context.Context) []int {
	ids, err := waq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (waq *WofangAlertQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, waq.ctx, "Count")
	if err := waq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, waq, querierCount[*WofangAlertQuery](), waq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (waq *WofangAlertQuery) CountX(ctx context.Context) int {
	count, err := waq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (waq *WofangAlertQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, waq.ctx, "Exist")
	switch _, err := waq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (waq *WofangAlertQuery) ExistX(ctx context.Context) bool {
	exist, err := waq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the WofangAlertQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (waq *WofangAlertQuery) Clone() *WofangAlertQuery {
	if waq == nil {
		return nil
	}
	return &WofangAlertQuery{
		config:     waq.config,
		ctx:        waq.ctx.Clone(),
		order:      append([]wofangalert.OrderOption{}, waq.order...),
		inters:     append([]Interceptor{}, waq.inters...),
		predicates: append([]predicate.WofangAlert{}, waq.predicates...),
		withTenant: waq.withTenant.Clone(),
		// clone intermediate query.
		sql:  waq.sql.Clone(),
		path: waq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (waq *WofangAlertQuery) WithTenant(opts ...func(*TenantQuery)) *WofangAlertQuery {
	query := (&TenantClient{config: waq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	waq.withTenant = query
	return waq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.WofangAlert.Query().
//		GroupBy(wofangalert.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (waq *WofangAlertQuery) GroupBy(field string, fields ...string) *WofangAlertGroupBy {
	waq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &WofangAlertGroupBy{build: waq}
	grbuild.flds = &waq.ctx.Fields
	grbuild.label = wofangalert.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.WofangAlert.Query().
//		Select(wofangalert.FieldTenantID).
//		Scan(ctx, &v)
func (waq *WofangAlertQuery) Select(fields ...string) *WofangAlertSelect {
	waq.ctx.Fields = append(waq.ctx.Fields, fields...)
	sbuild := &WofangAlertSelect{WofangAlertQuery: waq}
	sbuild.label = wofangalert.Label
	sbuild.flds, sbuild.scan = &waq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a WofangAlertSelect configured with the given aggregations.
func (waq *WofangAlertQuery) Aggregate(fns ...AggregateFunc) *WofangAlertSelect {
	return waq.Select().Aggregate(fns...)
}

func (waq *WofangAlertQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range waq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, waq); err != nil {
				return err
			}
		}
	}
	for _, f := range waq.ctx.Fields {
		if !wofangalert.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if waq.path != nil {
		prev, err := waq.path(ctx)
		if err != nil {
			return err
		}
		waq.sql = prev
	}
	if wofangalert.Policy == nil {
		return errors.New("ent: uninitialized wofangalert.Policy (forgotten import ent/runtime?)")
	}
	if err := wofangalert.Policy.EvalQuery(ctx, waq); err != nil {
		return err
	}
	return nil
}

func (waq *WofangAlertQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*WofangAlert, error) {
	var (
		nodes       = []*WofangAlert{}
		_spec       = waq.querySpec()
		loadedTypes = [1]bool{
			waq.withTenant != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*WofangAlert).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &WofangAlert{config: waq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, waq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := waq.withTenant; query != nil {
		if err := waq.loadTenant(ctx, query, nodes, nil,
			func(n *WofangAlert, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (waq *WofangAlertQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*WofangAlert, init func(*WofangAlert), assign func(*WofangAlert, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*WofangAlert)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (waq *WofangAlertQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := waq.querySpec()
	_spec.Node.Columns = waq.ctx.Fields
	if len(waq.ctx.Fields) > 0 {
		_spec.Unique = waq.ctx.Unique != nil && *waq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, waq.driver, _spec)
}

func (waq *WofangAlertQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(wofangalert.Table, wofangalert.Columns, sqlgraph.NewFieldSpec(wofangalert.FieldID, field.TypeInt))
	_spec.From = waq.sql
	if unique := waq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if waq.path != nil {
		_spec.Unique = true
	}
	if fields := waq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wofangalert.FieldID)
		for i := range fields {
			if fields[i] != wofangalert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if waq.withTenant != nil {
			_spec.Node.AddColumnOnce(wofangalert.FieldTenantID)
		}
	}
	if ps := waq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := waq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := waq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := waq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (waq *WofangAlertQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(waq.driver.Dialect())
	t1 := builder.Table(wofangalert.Table)
	columns := waq.ctx.Fields
	if len(columns) == 0 {
		columns = wofangalert.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if waq.sql != nil {
		selector = waq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if waq.ctx.Unique != nil && *waq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range waq.predicates {
		p(selector)
	}
	for _, p := range waq.order {
		p(selector)
	}
	if offset := waq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := waq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WofangAlertGroupBy is the group-by builder for WofangAlert entities.
type WofangAlertGroupBy struct {
	selector
	build *WofangAlertQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (wagb *WofangAlertGroupBy) Aggregate(fns ...AggregateFunc) *WofangAlertGroupBy {
	wagb.fns = append(wagb.fns, fns...)
	return wagb
}

// Scan applies the selector query and scans the result into the given value.
func (wagb *WofangAlertGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wagb.build.ctx, "GroupBy")
	if err := wagb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WofangAlertQuery, *WofangAlertGroupBy](ctx, wagb.build, wagb, wagb.build.inters, v)
}

func (wagb *WofangAlertGroupBy) sqlScan(ctx context.Context, root *WofangAlertQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(wagb.fns))
	for _, fn := range wagb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*wagb.flds)+len(wagb.fns))
		for _, f := range *wagb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*wagb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wagb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// WofangAlertSelect is the builder for selecting fields of WofangAlert entities.
type WofangAlertSelect struct {
	*WofangAlertQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (was *WofangAlertSelect) Aggregate(fns ...AggregateFunc) *WofangAlertSelect {
	was.fns = append(was.fns, fns...)
	return was
}

// Scan applies the selector query and scans the result into the given value.
func (was *WofangAlertSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, was.ctx, "Select")
	if err := was.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WofangAlertQuery, *WofangAlertSelect](ctx, was.WofangAlertQuery, was, was.inters, v)
}

func (was *WofangAlertSelect) sqlScan(ctx context.Context, root *WofangAlertQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(was.fns))
	for _, fn := range was.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*was.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := was.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
