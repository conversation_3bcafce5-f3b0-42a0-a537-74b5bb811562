// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/cleandata"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CleanDataQuery is the builder for querying CleanData entities.
type CleanDataQuery struct {
	config
	ctx               *QueryContext
	order             []cleandata.OrderOption
	inters            []Interceptor
	predicates        []predicate.CleanData
	withTenant        *TenantQuery
	withSpectrumAlert *SpectrumAlertQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the CleanDataQuery builder.
func (cdq *CleanDataQuery) Where(ps ...predicate.CleanData) *CleanDataQuery {
	cdq.predicates = append(cdq.predicates, ps...)
	return cdq
}

// Limit the number of records to be returned by this query.
func (cdq *CleanDataQuery) Limit(limit int) *CleanDataQuery {
	cdq.ctx.Limit = &limit
	return cdq
}

// Offset to start from.
func (cdq *CleanDataQuery) Offset(offset int) *CleanDataQuery {
	cdq.ctx.Offset = &offset
	return cdq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (cdq *CleanDataQuery) Unique(unique bool) *CleanDataQuery {
	cdq.ctx.Unique = &unique
	return cdq
}

// Order specifies how the records should be ordered.
func (cdq *CleanDataQuery) Order(o ...cleandata.OrderOption) *CleanDataQuery {
	cdq.order = append(cdq.order, o...)
	return cdq
}

// QueryTenant chains the current query on the "tenant" edge.
func (cdq *CleanDataQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: cdq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := cdq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := cdq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(cleandata.Table, cleandata.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cleandata.TenantTable, cleandata.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(cdq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QuerySpectrumAlert chains the current query on the "spectrum_alert" edge.
func (cdq *CleanDataQuery) QuerySpectrumAlert() *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: cdq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := cdq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := cdq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(cleandata.Table, cleandata.FieldID, selector),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, cleandata.SpectrumAlertTable, cleandata.SpectrumAlertColumn),
		)
		fromU = sqlgraph.SetNeighbors(cdq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first CleanData entity from the query.
// Returns a *NotFoundError when no CleanData was found.
func (cdq *CleanDataQuery) First(ctx context.Context) (*CleanData, error) {
	nodes, err := cdq.Limit(1).All(setContextOp(ctx, cdq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{cleandata.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (cdq *CleanDataQuery) FirstX(ctx context.Context) *CleanData {
	node, err := cdq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first CleanData ID from the query.
// Returns a *NotFoundError when no CleanData ID was found.
func (cdq *CleanDataQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = cdq.Limit(1).IDs(setContextOp(ctx, cdq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{cleandata.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (cdq *CleanDataQuery) FirstIDX(ctx context.Context) int {
	id, err := cdq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single CleanData entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one CleanData entity is found.
// Returns a *NotFoundError when no CleanData entities are found.
func (cdq *CleanDataQuery) Only(ctx context.Context) (*CleanData, error) {
	nodes, err := cdq.Limit(2).All(setContextOp(ctx, cdq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{cleandata.Label}
	default:
		return nil, &NotSingularError{cleandata.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (cdq *CleanDataQuery) OnlyX(ctx context.Context) *CleanData {
	node, err := cdq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only CleanData ID in the query.
// Returns a *NotSingularError when more than one CleanData ID is found.
// Returns a *NotFoundError when no entities are found.
func (cdq *CleanDataQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = cdq.Limit(2).IDs(setContextOp(ctx, cdq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{cleandata.Label}
	default:
		err = &NotSingularError{cleandata.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (cdq *CleanDataQuery) OnlyIDX(ctx context.Context) int {
	id, err := cdq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of CleanDataSlice.
func (cdq *CleanDataQuery) All(ctx context.Context) ([]*CleanData, error) {
	ctx = setContextOp(ctx, cdq.ctx, "All")
	if err := cdq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*CleanData, *CleanDataQuery]()
	return withInterceptors[[]*CleanData](ctx, cdq, qr, cdq.inters)
}

// AllX is like All, but panics if an error occurs.
func (cdq *CleanDataQuery) AllX(ctx context.Context) []*CleanData {
	nodes, err := cdq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of CleanData IDs.
func (cdq *CleanDataQuery) IDs(ctx context.Context) (ids []int, err error) {
	if cdq.ctx.Unique == nil && cdq.path != nil {
		cdq.Unique(true)
	}
	ctx = setContextOp(ctx, cdq.ctx, "IDs")
	if err = cdq.Select(cleandata.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (cdq *CleanDataQuery) IDsX(ctx context.Context) []int {
	ids, err := cdq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (cdq *CleanDataQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, cdq.ctx, "Count")
	if err := cdq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, cdq, querierCount[*CleanDataQuery](), cdq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (cdq *CleanDataQuery) CountX(ctx context.Context) int {
	count, err := cdq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (cdq *CleanDataQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, cdq.ctx, "Exist")
	switch _, err := cdq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (cdq *CleanDataQuery) ExistX(ctx context.Context) bool {
	exist, err := cdq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the CleanDataQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (cdq *CleanDataQuery) Clone() *CleanDataQuery {
	if cdq == nil {
		return nil
	}
	return &CleanDataQuery{
		config:            cdq.config,
		ctx:               cdq.ctx.Clone(),
		order:             append([]cleandata.OrderOption{}, cdq.order...),
		inters:            append([]Interceptor{}, cdq.inters...),
		predicates:        append([]predicate.CleanData{}, cdq.predicates...),
		withTenant:        cdq.withTenant.Clone(),
		withSpectrumAlert: cdq.withSpectrumAlert.Clone(),
		// clone intermediate query.
		sql:  cdq.sql.Clone(),
		path: cdq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (cdq *CleanDataQuery) WithTenant(opts ...func(*TenantQuery)) *CleanDataQuery {
	query := (&TenantClient{config: cdq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	cdq.withTenant = query
	return cdq
}

// WithSpectrumAlert tells the query-builder to eager-load the nodes that are connected to
// the "spectrum_alert" edge. The optional arguments are used to configure the query builder of the edge.
func (cdq *CleanDataQuery) WithSpectrumAlert(opts ...func(*SpectrumAlertQuery)) *CleanDataQuery {
	query := (&SpectrumAlertClient{config: cdq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	cdq.withSpectrumAlert = query
	return cdq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.CleanData.Query().
//		GroupBy(cleandata.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (cdq *CleanDataQuery) GroupBy(field string, fields ...string) *CleanDataGroupBy {
	cdq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &CleanDataGroupBy{build: cdq}
	grbuild.flds = &cdq.ctx.Fields
	grbuild.label = cleandata.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.CleanData.Query().
//		Select(cleandata.FieldTenantID).
//		Scan(ctx, &v)
func (cdq *CleanDataQuery) Select(fields ...string) *CleanDataSelect {
	cdq.ctx.Fields = append(cdq.ctx.Fields, fields...)
	sbuild := &CleanDataSelect{CleanDataQuery: cdq}
	sbuild.label = cleandata.Label
	sbuild.flds, sbuild.scan = &cdq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a CleanDataSelect configured with the given aggregations.
func (cdq *CleanDataQuery) Aggregate(fns ...AggregateFunc) *CleanDataSelect {
	return cdq.Select().Aggregate(fns...)
}

func (cdq *CleanDataQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range cdq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, cdq); err != nil {
				return err
			}
		}
	}
	for _, f := range cdq.ctx.Fields {
		if !cleandata.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if cdq.path != nil {
		prev, err := cdq.path(ctx)
		if err != nil {
			return err
		}
		cdq.sql = prev
	}
	if cleandata.Policy == nil {
		return errors.New("ent: uninitialized cleandata.Policy (forgotten import ent/runtime?)")
	}
	if err := cleandata.Policy.EvalQuery(ctx, cdq); err != nil {
		return err
	}
	return nil
}

func (cdq *CleanDataQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*CleanData, error) {
	var (
		nodes       = []*CleanData{}
		_spec       = cdq.querySpec()
		loadedTypes = [2]bool{
			cdq.withTenant != nil,
			cdq.withSpectrumAlert != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*CleanData).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &CleanData{config: cdq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, cdq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := cdq.withTenant; query != nil {
		if err := cdq.loadTenant(ctx, query, nodes, nil,
			func(n *CleanData, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := cdq.withSpectrumAlert; query != nil {
		if err := cdq.loadSpectrumAlert(ctx, query, nodes, nil,
			func(n *CleanData, e *SpectrumAlert) { n.Edges.SpectrumAlert = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (cdq *CleanDataQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*CleanData, init func(*CleanData), assign func(*CleanData, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*CleanData)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (cdq *CleanDataQuery) loadSpectrumAlert(ctx context.Context, query *SpectrumAlertQuery, nodes []*CleanData, init func(*CleanData), assign func(*CleanData, *SpectrumAlert)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*CleanData)
	for i := range nodes {
		if nodes[i].SpectrumAlertID == nil {
			continue
		}
		fk := *nodes[i].SpectrumAlertID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(spectrumalert.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "spectrum_alert_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (cdq *CleanDataQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := cdq.querySpec()
	_spec.Node.Columns = cdq.ctx.Fields
	if len(cdq.ctx.Fields) > 0 {
		_spec.Unique = cdq.ctx.Unique != nil && *cdq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, cdq.driver, _spec)
}

func (cdq *CleanDataQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(cleandata.Table, cleandata.Columns, sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt))
	_spec.From = cdq.sql
	if unique := cdq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if cdq.path != nil {
		_spec.Unique = true
	}
	if fields := cdq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cleandata.FieldID)
		for i := range fields {
			if fields[i] != cleandata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if cdq.withTenant != nil {
			_spec.Node.AddColumnOnce(cleandata.FieldTenantID)
		}
		if cdq.withSpectrumAlert != nil {
			_spec.Node.AddColumnOnce(cleandata.FieldSpectrumAlertID)
		}
	}
	if ps := cdq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := cdq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := cdq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := cdq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (cdq *CleanDataQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(cdq.driver.Dialect())
	t1 := builder.Table(cleandata.Table)
	columns := cdq.ctx.Fields
	if len(columns) == 0 {
		columns = cleandata.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if cdq.sql != nil {
		selector = cdq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if cdq.ctx.Unique != nil && *cdq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range cdq.predicates {
		p(selector)
	}
	for _, p := range cdq.order {
		p(selector)
	}
	if offset := cdq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := cdq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// CleanDataGroupBy is the group-by builder for CleanData entities.
type CleanDataGroupBy struct {
	selector
	build *CleanDataQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (cdgb *CleanDataGroupBy) Aggregate(fns ...AggregateFunc) *CleanDataGroupBy {
	cdgb.fns = append(cdgb.fns, fns...)
	return cdgb
}

// Scan applies the selector query and scans the result into the given value.
func (cdgb *CleanDataGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cdgb.build.ctx, "GroupBy")
	if err := cdgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CleanDataQuery, *CleanDataGroupBy](ctx, cdgb.build, cdgb, cdgb.build.inters, v)
}

func (cdgb *CleanDataGroupBy) sqlScan(ctx context.Context, root *CleanDataQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(cdgb.fns))
	for _, fn := range cdgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*cdgb.flds)+len(cdgb.fns))
		for _, f := range *cdgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*cdgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cdgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// CleanDataSelect is the builder for selecting fields of CleanData entities.
type CleanDataSelect struct {
	*CleanDataQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cds *CleanDataSelect) Aggregate(fns ...AggregateFunc) *CleanDataSelect {
	cds.fns = append(cds.fns, fns...)
	return cds
}

// Scan applies the selector query and scans the result into the given value.
func (cds *CleanDataSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cds.ctx, "Select")
	if err := cds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CleanDataQuery, *CleanDataSelect](ctx, cds.CleanDataQuery, cds, cds.inters, v)
}

func (cds *CleanDataSelect) sqlScan(ctx context.Context, root *CleanDataQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cds.fns))
	for _, fn := range cds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
