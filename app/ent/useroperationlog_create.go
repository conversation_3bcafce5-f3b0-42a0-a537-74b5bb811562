// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/useroperationlog"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserOperationLogCreate is the builder for creating a UserOperationLog entity.
type UserOperationLogCreate struct {
	config
	mutation *UserOperationLogMutation
	hooks    []Hook
}

// SetRemark sets the "remark" field.
func (uolc *UserOperationLogCreate) SetRemark(s string) *UserOperationLogCreate {
	uolc.mutation.SetRemark(s)
	return uolc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (uolc *UserOperationLogCreate) SetNillableRemark(s *string) *UserOperationLogCreate {
	if s != nil {
		uolc.SetRemark(*s)
	}
	return uolc
}

// SetCreatedAt sets the "created_at" field.
func (uolc *UserOperationLogCreate) SetCreatedAt(t time.Time) *UserOperationLogCreate {
	uolc.mutation.SetCreatedAt(t)
	return uolc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (uolc *UserOperationLogCreate) SetNillableCreatedAt(t *time.Time) *UserOperationLogCreate {
	if t != nil {
		uolc.SetCreatedAt(*t)
	}
	return uolc
}

// SetUpdatedAt sets the "updated_at" field.
func (uolc *UserOperationLogCreate) SetUpdatedAt(t time.Time) *UserOperationLogCreate {
	uolc.mutation.SetUpdatedAt(t)
	return uolc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (uolc *UserOperationLogCreate) SetNillableUpdatedAt(t *time.Time) *UserOperationLogCreate {
	if t != nil {
		uolc.SetUpdatedAt(*t)
	}
	return uolc
}

// SetUsername sets the "username" field.
func (uolc *UserOperationLogCreate) SetUsername(s string) *UserOperationLogCreate {
	uolc.mutation.SetUsername(s)
	return uolc
}

// SetMethod sets the "method" field.
func (uolc *UserOperationLogCreate) SetMethod(s string) *UserOperationLogCreate {
	uolc.mutation.SetMethod(s)
	return uolc
}

// SetRequestID sets the "request_id" field.
func (uolc *UserOperationLogCreate) SetRequestID(s string) *UserOperationLogCreate {
	uolc.mutation.SetRequestID(s)
	return uolc
}

// SetURI sets the "uri" field.
func (uolc *UserOperationLogCreate) SetURI(s string) *UserOperationLogCreate {
	uolc.mutation.SetURI(s)
	return uolc
}

// SetRequestBody sets the "request_body" field.
func (uolc *UserOperationLogCreate) SetRequestBody(s string) *UserOperationLogCreate {
	uolc.mutation.SetRequestBody(s)
	return uolc
}

// SetProject sets the "project" field.
func (uolc *UserOperationLogCreate) SetProject(s string) *UserOperationLogCreate {
	uolc.mutation.SetProject(s)
	return uolc
}

// Mutation returns the UserOperationLogMutation object of the builder.
func (uolc *UserOperationLogCreate) Mutation() *UserOperationLogMutation {
	return uolc.mutation
}

// Save creates the UserOperationLog in the database.
func (uolc *UserOperationLogCreate) Save(ctx context.Context) (*UserOperationLog, error) {
	uolc.defaults()
	return withHooks(ctx, uolc.sqlSave, uolc.mutation, uolc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uolc *UserOperationLogCreate) SaveX(ctx context.Context) *UserOperationLog {
	v, err := uolc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uolc *UserOperationLogCreate) Exec(ctx context.Context) error {
	_, err := uolc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uolc *UserOperationLogCreate) ExecX(ctx context.Context) {
	if err := uolc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uolc *UserOperationLogCreate) defaults() {
	if _, ok := uolc.mutation.CreatedAt(); !ok {
		v := useroperationlog.DefaultCreatedAt()
		uolc.mutation.SetCreatedAt(v)
	}
	if _, ok := uolc.mutation.UpdatedAt(); !ok {
		v := useroperationlog.DefaultUpdatedAt()
		uolc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uolc *UserOperationLogCreate) check() error {
	if v, ok := uolc.mutation.Remark(); ok {
		if err := useroperationlog.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "UserOperationLog.remark": %w`, err)}
		}
	}
	if _, ok := uolc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "UserOperationLog.created_at"`)}
	}
	if _, ok := uolc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "UserOperationLog.updated_at"`)}
	}
	if _, ok := uolc.mutation.Username(); !ok {
		return &ValidationError{Name: "username", err: errors.New(`ent: missing required field "UserOperationLog.username"`)}
	}
	if _, ok := uolc.mutation.Method(); !ok {
		return &ValidationError{Name: "method", err: errors.New(`ent: missing required field "UserOperationLog.method"`)}
	}
	if _, ok := uolc.mutation.RequestID(); !ok {
		return &ValidationError{Name: "request_id", err: errors.New(`ent: missing required field "UserOperationLog.request_id"`)}
	}
	if _, ok := uolc.mutation.URI(); !ok {
		return &ValidationError{Name: "uri", err: errors.New(`ent: missing required field "UserOperationLog.uri"`)}
	}
	if v, ok := uolc.mutation.URI(); ok {
		if err := useroperationlog.URIValidator(v); err != nil {
			return &ValidationError{Name: "uri", err: fmt.Errorf(`ent: validator failed for field "UserOperationLog.uri": %w`, err)}
		}
	}
	if _, ok := uolc.mutation.RequestBody(); !ok {
		return &ValidationError{Name: "request_body", err: errors.New(`ent: missing required field "UserOperationLog.request_body"`)}
	}
	if _, ok := uolc.mutation.Project(); !ok {
		return &ValidationError{Name: "project", err: errors.New(`ent: missing required field "UserOperationLog.project"`)}
	}
	return nil
}

func (uolc *UserOperationLogCreate) sqlSave(ctx context.Context) (*UserOperationLog, error) {
	if err := uolc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uolc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uolc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	uolc.mutation.id = &_node.ID
	uolc.mutation.done = true
	return _node, nil
}

func (uolc *UserOperationLogCreate) createSpec() (*UserOperationLog, *sqlgraph.CreateSpec) {
	var (
		_node = &UserOperationLog{config: uolc.config}
		_spec = sqlgraph.NewCreateSpec(useroperationlog.Table, sqlgraph.NewFieldSpec(useroperationlog.FieldID, field.TypeInt))
	)
	if value, ok := uolc.mutation.Remark(); ok {
		_spec.SetField(useroperationlog.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := uolc.mutation.CreatedAt(); ok {
		_spec.SetField(useroperationlog.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := uolc.mutation.UpdatedAt(); ok {
		_spec.SetField(useroperationlog.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := uolc.mutation.Username(); ok {
		_spec.SetField(useroperationlog.FieldUsername, field.TypeString, value)
		_node.Username = value
	}
	if value, ok := uolc.mutation.Method(); ok {
		_spec.SetField(useroperationlog.FieldMethod, field.TypeString, value)
		_node.Method = value
	}
	if value, ok := uolc.mutation.RequestID(); ok {
		_spec.SetField(useroperationlog.FieldRequestID, field.TypeString, value)
		_node.RequestID = value
	}
	if value, ok := uolc.mutation.URI(); ok {
		_spec.SetField(useroperationlog.FieldURI, field.TypeString, value)
		_node.URI = value
	}
	if value, ok := uolc.mutation.RequestBody(); ok {
		_spec.SetField(useroperationlog.FieldRequestBody, field.TypeString, value)
		_node.RequestBody = value
	}
	if value, ok := uolc.mutation.Project(); ok {
		_spec.SetField(useroperationlog.FieldProject, field.TypeString, value)
		_node.Project = value
	}
	return _node, _spec
}

// UserOperationLogCreateBulk is the builder for creating many UserOperationLog entities in bulk.
type UserOperationLogCreateBulk struct {
	config
	err      error
	builders []*UserOperationLogCreate
}

// Save creates the UserOperationLog entities in the database.
func (uolcb *UserOperationLogCreateBulk) Save(ctx context.Context) ([]*UserOperationLog, error) {
	if uolcb.err != nil {
		return nil, uolcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(uolcb.builders))
	nodes := make([]*UserOperationLog, len(uolcb.builders))
	mutators := make([]Mutator, len(uolcb.builders))
	for i := range uolcb.builders {
		func(i int, root context.Context) {
			builder := uolcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UserOperationLogMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, uolcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, uolcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, uolcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (uolcb *UserOperationLogCreateBulk) SaveX(ctx context.Context) []*UserOperationLog {
	v, err := uolcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uolcb *UserOperationLogCreateBulk) Exec(ctx context.Context) error {
	_, err := uolcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uolcb *UserOperationLogCreateBulk) ExecX(ctx context.Context) {
	if err := uolcb.Exec(ctx); err != nil {
		panic(err)
	}
}
