// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/ent/wofang"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Wofang is the model entity for the Wofang schema.
type Wofang struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// IP
	IP string `json:"ip,omitempty"`
	// 类型
	Type string `json:"type,omitempty"`
	// 自动解封时间，单位：秒
	UnDragSecond int `json:"un_drag_second,omitempty"`
	// 牵引清洗开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 错误信息
	ErrorInfo string `json:"error_info,omitempty"`
	// 状态
	Status string `json:"status,omitempty"`
	// 创建用户Id，可选
	CreateUserID *int `json:"create_user_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the WofangQuery when eager-loading is set.
	Edges        WofangEdges `json:"edges"`
	selectValues sql.SelectValues
}

// WofangEdges holds the relations/edges for other nodes in the graph.
type WofangEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// SpectrumAlerts holds the value of the spectrum_alerts edge.
	SpectrumAlerts []*SpectrumAlert `json:"spectrum_alerts,omitempty"`
	// MatrixSpectrumAlerts holds the value of the matrix_spectrum_alerts edge.
	MatrixSpectrumAlerts []*MatrixSpectrumAlert `json:"matrix_spectrum_alerts,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [4]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e WofangEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e WofangEdges) UserOrErr() (*User, error) {
	if e.loadedTypes[1] {
		if e.User == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: user.Label}
		}
		return e.User, nil
	}
	return nil, &NotLoadedError{edge: "user"}
}

// SpectrumAlertsOrErr returns the SpectrumAlerts value or an error if the edge
// was not loaded in eager-loading.
func (e WofangEdges) SpectrumAlertsOrErr() ([]*SpectrumAlert, error) {
	if e.loadedTypes[2] {
		return e.SpectrumAlerts, nil
	}
	return nil, &NotLoadedError{edge: "spectrum_alerts"}
}

// MatrixSpectrumAlertsOrErr returns the MatrixSpectrumAlerts value or an error if the edge
// was not loaded in eager-loading.
func (e WofangEdges) MatrixSpectrumAlertsOrErr() ([]*MatrixSpectrumAlert, error) {
	if e.loadedTypes[3] {
		return e.MatrixSpectrumAlerts, nil
	}
	return nil, &NotLoadedError{edge: "matrix_spectrum_alerts"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Wofang) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case wofang.FieldID, wofang.FieldTenantID, wofang.FieldUnDragSecond, wofang.FieldCreateUserID:
			values[i] = new(sql.NullInt64)
		case wofang.FieldRemark, wofang.FieldName, wofang.FieldIP, wofang.FieldType, wofang.FieldErrorInfo, wofang.FieldStatus:
			values[i] = new(sql.NullString)
		case wofang.FieldCreatedAt, wofang.FieldUpdatedAt, wofang.FieldStartTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Wofang fields.
func (w *Wofang) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case wofang.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			w.ID = int(value.Int64)
		case wofang.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				w.CreatedAt = value.Time
			}
		case wofang.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				w.UpdatedAt = value.Time
			}
		case wofang.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				w.TenantID = new(int)
				*w.TenantID = int(value.Int64)
			}
		case wofang.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				w.Remark = new(string)
				*w.Remark = value.String
			}
		case wofang.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				w.Name = value.String
			}
		case wofang.FieldIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ip", values[i])
			} else if value.Valid {
				w.IP = value.String
			}
		case wofang.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				w.Type = value.String
			}
		case wofang.FieldUnDragSecond:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field un_drag_second", values[i])
			} else if value.Valid {
				w.UnDragSecond = int(value.Int64)
			}
		case wofang.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				w.StartTime = value.Time
			}
		case wofang.FieldErrorInfo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field error_info", values[i])
			} else if value.Valid {
				w.ErrorInfo = value.String
			}
		case wofang.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				w.Status = value.String
			}
		case wofang.FieldCreateUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field create_user_id", values[i])
			} else if value.Valid {
				w.CreateUserID = new(int)
				*w.CreateUserID = int(value.Int64)
			}
		default:
			w.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Wofang.
// This includes values selected through modifiers, order, etc.
func (w *Wofang) Value(name string) (ent.Value, error) {
	return w.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the Wofang entity.
func (w *Wofang) QueryTenant() *TenantQuery {
	return NewWofangClient(w.config).QueryTenant(w)
}

// QueryUser queries the "user" edge of the Wofang entity.
func (w *Wofang) QueryUser() *UserQuery {
	return NewWofangClient(w.config).QueryUser(w)
}

// QuerySpectrumAlerts queries the "spectrum_alerts" edge of the Wofang entity.
func (w *Wofang) QuerySpectrumAlerts() *SpectrumAlertQuery {
	return NewWofangClient(w.config).QuerySpectrumAlerts(w)
}

// QueryMatrixSpectrumAlerts queries the "matrix_spectrum_alerts" edge of the Wofang entity.
func (w *Wofang) QueryMatrixSpectrumAlerts() *MatrixSpectrumAlertQuery {
	return NewWofangClient(w.config).QueryMatrixSpectrumAlerts(w)
}

// Update returns a builder for updating this Wofang.
// Note that you need to call Wofang.Unwrap() before calling this method if this Wofang
// was returned from a transaction, and the transaction was committed or rolled back.
func (w *Wofang) Update() *WofangUpdateOne {
	return NewWofangClient(w.config).UpdateOne(w)
}

// Unwrap unwraps the Wofang entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (w *Wofang) Unwrap() *Wofang {
	_tx, ok := w.config.driver.(*txDriver)
	if !ok {
		panic("ent: Wofang is not a transactional entity")
	}
	w.config.driver = _tx.drv
	return w
}

// String implements the fmt.Stringer.
func (w *Wofang) String() string {
	var builder strings.Builder
	builder.WriteString("Wofang(")
	builder.WriteString(fmt.Sprintf("id=%v, ", w.ID))
	builder.WriteString("created_at=")
	builder.WriteString(w.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(w.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := w.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := w.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(w.Name)
	builder.WriteString(", ")
	builder.WriteString("ip=")
	builder.WriteString(w.IP)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(w.Type)
	builder.WriteString(", ")
	builder.WriteString("un_drag_second=")
	builder.WriteString(fmt.Sprintf("%v", w.UnDragSecond))
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(w.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("error_info=")
	builder.WriteString(w.ErrorInfo)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(w.Status)
	builder.WriteString(", ")
	if v := w.CreateUserID; v != nil {
		builder.WriteString("create_user_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// Wofangs is a parsable slice of Wofang.
type Wofangs []*Wofang
