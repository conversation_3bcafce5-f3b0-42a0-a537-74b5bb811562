// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/cleandata"
	"meta/app/ent/predicate"
	"meta/app/ent/protectgroup"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SpectrumAlertQuery is the builder for querying SpectrumAlert entities.
type SpectrumAlertQuery struct {
	config
	ctx               *QueryContext
	order             []spectrumalert.OrderOption
	inters            []Interceptor
	predicates        []predicate.SpectrumAlert
	withTenant        *TenantQuery
	withSpectrumDatas *SpectrumDataQuery
	withCleanDatas    *CleanDataQuery
	withProtectGroup  *ProtectGroupQuery
	withStrategy      *StrategyQuery
	withWofangTicket  *WofangQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the SpectrumAlertQuery builder.
func (saq *SpectrumAlertQuery) Where(ps ...predicate.SpectrumAlert) *SpectrumAlertQuery {
	saq.predicates = append(saq.predicates, ps...)
	return saq
}

// Limit the number of records to be returned by this query.
func (saq *SpectrumAlertQuery) Limit(limit int) *SpectrumAlertQuery {
	saq.ctx.Limit = &limit
	return saq
}

// Offset to start from.
func (saq *SpectrumAlertQuery) Offset(offset int) *SpectrumAlertQuery {
	saq.ctx.Offset = &offset
	return saq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (saq *SpectrumAlertQuery) Unique(unique bool) *SpectrumAlertQuery {
	saq.ctx.Unique = &unique
	return saq
}

// Order specifies how the records should be ordered.
func (saq *SpectrumAlertQuery) Order(o ...spectrumalert.OrderOption) *SpectrumAlertQuery {
	saq.order = append(saq.order, o...)
	return saq
}

// QueryTenant chains the current query on the "tenant" edge.
func (saq *SpectrumAlertQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: saq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := saq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := saq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, spectrumalert.TenantTable, spectrumalert.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(saq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QuerySpectrumDatas chains the current query on the "spectrum_datas" edge.
func (saq *SpectrumAlertQuery) QuerySpectrumDatas() *SpectrumDataQuery {
	query := (&SpectrumDataClient{config: saq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := saq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := saq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, selector),
			sqlgraph.To(spectrumdata.Table, spectrumdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, spectrumalert.SpectrumDatasTable, spectrumalert.SpectrumDatasColumn),
		)
		fromU = sqlgraph.SetNeighbors(saq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryCleanDatas chains the current query on the "clean_datas" edge.
func (saq *SpectrumAlertQuery) QueryCleanDatas() *CleanDataQuery {
	query := (&CleanDataClient{config: saq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := saq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := saq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, selector),
			sqlgraph.To(cleandata.Table, cleandata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, spectrumalert.CleanDatasTable, spectrumalert.CleanDatasColumn),
		)
		fromU = sqlgraph.SetNeighbors(saq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryProtectGroup chains the current query on the "protect_group" edge.
func (saq *SpectrumAlertQuery) QueryProtectGroup() *ProtectGroupQuery {
	query := (&ProtectGroupClient{config: saq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := saq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := saq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, selector),
			sqlgraph.To(protectgroup.Table, protectgroup.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, spectrumalert.ProtectGroupTable, spectrumalert.ProtectGroupColumn),
		)
		fromU = sqlgraph.SetNeighbors(saq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryStrategy chains the current query on the "strategy" edge.
func (saq *SpectrumAlertQuery) QueryStrategy() *StrategyQuery {
	query := (&StrategyClient{config: saq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := saq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := saq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, selector),
			sqlgraph.To(strategy.Table, strategy.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, spectrumalert.StrategyTable, spectrumalert.StrategyColumn),
		)
		fromU = sqlgraph.SetNeighbors(saq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryWofangTicket chains the current query on the "wofang_ticket" edge.
func (saq *SpectrumAlertQuery) QueryWofangTicket() *WofangQuery {
	query := (&WofangClient{config: saq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := saq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := saq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, selector),
			sqlgraph.To(wofang.Table, wofang.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, spectrumalert.WofangTicketTable, spectrumalert.WofangTicketColumn),
		)
		fromU = sqlgraph.SetNeighbors(saq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first SpectrumAlert entity from the query.
// Returns a *NotFoundError when no SpectrumAlert was found.
func (saq *SpectrumAlertQuery) First(ctx context.Context) (*SpectrumAlert, error) {
	nodes, err := saq.Limit(1).All(setContextOp(ctx, saq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{spectrumalert.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (saq *SpectrumAlertQuery) FirstX(ctx context.Context) *SpectrumAlert {
	node, err := saq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first SpectrumAlert ID from the query.
// Returns a *NotFoundError when no SpectrumAlert ID was found.
func (saq *SpectrumAlertQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = saq.Limit(1).IDs(setContextOp(ctx, saq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{spectrumalert.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (saq *SpectrumAlertQuery) FirstIDX(ctx context.Context) int {
	id, err := saq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single SpectrumAlert entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one SpectrumAlert entity is found.
// Returns a *NotFoundError when no SpectrumAlert entities are found.
func (saq *SpectrumAlertQuery) Only(ctx context.Context) (*SpectrumAlert, error) {
	nodes, err := saq.Limit(2).All(setContextOp(ctx, saq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{spectrumalert.Label}
	default:
		return nil, &NotSingularError{spectrumalert.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (saq *SpectrumAlertQuery) OnlyX(ctx context.Context) *SpectrumAlert {
	node, err := saq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only SpectrumAlert ID in the query.
// Returns a *NotSingularError when more than one SpectrumAlert ID is found.
// Returns a *NotFoundError when no entities are found.
func (saq *SpectrumAlertQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = saq.Limit(2).IDs(setContextOp(ctx, saq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{spectrumalert.Label}
	default:
		err = &NotSingularError{spectrumalert.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (saq *SpectrumAlertQuery) OnlyIDX(ctx context.Context) int {
	id, err := saq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of SpectrumAlerts.
func (saq *SpectrumAlertQuery) All(ctx context.Context) ([]*SpectrumAlert, error) {
	ctx = setContextOp(ctx, saq.ctx, "All")
	if err := saq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*SpectrumAlert, *SpectrumAlertQuery]()
	return withInterceptors[[]*SpectrumAlert](ctx, saq, qr, saq.inters)
}

// AllX is like All, but panics if an error occurs.
func (saq *SpectrumAlertQuery) AllX(ctx context.Context) []*SpectrumAlert {
	nodes, err := saq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of SpectrumAlert IDs.
func (saq *SpectrumAlertQuery) IDs(ctx context.Context) (ids []int, err error) {
	if saq.ctx.Unique == nil && saq.path != nil {
		saq.Unique(true)
	}
	ctx = setContextOp(ctx, saq.ctx, "IDs")
	if err = saq.Select(spectrumalert.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (saq *SpectrumAlertQuery) IDsX(ctx context.Context) []int {
	ids, err := saq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (saq *SpectrumAlertQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, saq.ctx, "Count")
	if err := saq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, saq, querierCount[*SpectrumAlertQuery](), saq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (saq *SpectrumAlertQuery) CountX(ctx context.Context) int {
	count, err := saq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (saq *SpectrumAlertQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, saq.ctx, "Exist")
	switch _, err := saq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (saq *SpectrumAlertQuery) ExistX(ctx context.Context) bool {
	exist, err := saq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the SpectrumAlertQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (saq *SpectrumAlertQuery) Clone() *SpectrumAlertQuery {
	if saq == nil {
		return nil
	}
	return &SpectrumAlertQuery{
		config:            saq.config,
		ctx:               saq.ctx.Clone(),
		order:             append([]spectrumalert.OrderOption{}, saq.order...),
		inters:            append([]Interceptor{}, saq.inters...),
		predicates:        append([]predicate.SpectrumAlert{}, saq.predicates...),
		withTenant:        saq.withTenant.Clone(),
		withSpectrumDatas: saq.withSpectrumDatas.Clone(),
		withCleanDatas:    saq.withCleanDatas.Clone(),
		withProtectGroup:  saq.withProtectGroup.Clone(),
		withStrategy:      saq.withStrategy.Clone(),
		withWofangTicket:  saq.withWofangTicket.Clone(),
		// clone intermediate query.
		sql:  saq.sql.Clone(),
		path: saq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (saq *SpectrumAlertQuery) WithTenant(opts ...func(*TenantQuery)) *SpectrumAlertQuery {
	query := (&TenantClient{config: saq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	saq.withTenant = query
	return saq
}

// WithSpectrumDatas tells the query-builder to eager-load the nodes that are connected to
// the "spectrum_datas" edge. The optional arguments are used to configure the query builder of the edge.
func (saq *SpectrumAlertQuery) WithSpectrumDatas(opts ...func(*SpectrumDataQuery)) *SpectrumAlertQuery {
	query := (&SpectrumDataClient{config: saq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	saq.withSpectrumDatas = query
	return saq
}

// WithCleanDatas tells the query-builder to eager-load the nodes that are connected to
// the "clean_datas" edge. The optional arguments are used to configure the query builder of the edge.
func (saq *SpectrumAlertQuery) WithCleanDatas(opts ...func(*CleanDataQuery)) *SpectrumAlertQuery {
	query := (&CleanDataClient{config: saq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	saq.withCleanDatas = query
	return saq
}

// WithProtectGroup tells the query-builder to eager-load the nodes that are connected to
// the "protect_group" edge. The optional arguments are used to configure the query builder of the edge.
func (saq *SpectrumAlertQuery) WithProtectGroup(opts ...func(*ProtectGroupQuery)) *SpectrumAlertQuery {
	query := (&ProtectGroupClient{config: saq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	saq.withProtectGroup = query
	return saq
}

// WithStrategy tells the query-builder to eager-load the nodes that are connected to
// the "strategy" edge. The optional arguments are used to configure the query builder of the edge.
func (saq *SpectrumAlertQuery) WithStrategy(opts ...func(*StrategyQuery)) *SpectrumAlertQuery {
	query := (&StrategyClient{config: saq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	saq.withStrategy = query
	return saq
}

// WithWofangTicket tells the query-builder to eager-load the nodes that are connected to
// the "wofang_ticket" edge. The optional arguments are used to configure the query builder of the edge.
func (saq *SpectrumAlertQuery) WithWofangTicket(opts ...func(*WofangQuery)) *SpectrumAlertQuery {
	query := (&WofangClient{config: saq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	saq.withWofangTicket = query
	return saq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.SpectrumAlert.Query().
//		GroupBy(spectrumalert.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (saq *SpectrumAlertQuery) GroupBy(field string, fields ...string) *SpectrumAlertGroupBy {
	saq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &SpectrumAlertGroupBy{build: saq}
	grbuild.flds = &saq.ctx.Fields
	grbuild.label = spectrumalert.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.SpectrumAlert.Query().
//		Select(spectrumalert.FieldTenantID).
//		Scan(ctx, &v)
func (saq *SpectrumAlertQuery) Select(fields ...string) *SpectrumAlertSelect {
	saq.ctx.Fields = append(saq.ctx.Fields, fields...)
	sbuild := &SpectrumAlertSelect{SpectrumAlertQuery: saq}
	sbuild.label = spectrumalert.Label
	sbuild.flds, sbuild.scan = &saq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a SpectrumAlertSelect configured with the given aggregations.
func (saq *SpectrumAlertQuery) Aggregate(fns ...AggregateFunc) *SpectrumAlertSelect {
	return saq.Select().Aggregate(fns...)
}

func (saq *SpectrumAlertQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range saq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, saq); err != nil {
				return err
			}
		}
	}
	for _, f := range saq.ctx.Fields {
		if !spectrumalert.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if saq.path != nil {
		prev, err := saq.path(ctx)
		if err != nil {
			return err
		}
		saq.sql = prev
	}
	if spectrumalert.Policy == nil {
		return errors.New("ent: uninitialized spectrumalert.Policy (forgotten import ent/runtime?)")
	}
	if err := spectrumalert.Policy.EvalQuery(ctx, saq); err != nil {
		return err
	}
	return nil
}

func (saq *SpectrumAlertQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*SpectrumAlert, error) {
	var (
		nodes       = []*SpectrumAlert{}
		_spec       = saq.querySpec()
		loadedTypes = [6]bool{
			saq.withTenant != nil,
			saq.withSpectrumDatas != nil,
			saq.withCleanDatas != nil,
			saq.withProtectGroup != nil,
			saq.withStrategy != nil,
			saq.withWofangTicket != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*SpectrumAlert).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &SpectrumAlert{config: saq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, saq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := saq.withTenant; query != nil {
		if err := saq.loadTenant(ctx, query, nodes, nil,
			func(n *SpectrumAlert, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := saq.withSpectrumDatas; query != nil {
		if err := saq.loadSpectrumDatas(ctx, query, nodes,
			func(n *SpectrumAlert) { n.Edges.SpectrumDatas = []*SpectrumData{} },
			func(n *SpectrumAlert, e *SpectrumData) { n.Edges.SpectrumDatas = append(n.Edges.SpectrumDatas, e) }); err != nil {
			return nil, err
		}
	}
	if query := saq.withCleanDatas; query != nil {
		if err := saq.loadCleanDatas(ctx, query, nodes,
			func(n *SpectrumAlert) { n.Edges.CleanDatas = []*CleanData{} },
			func(n *SpectrumAlert, e *CleanData) { n.Edges.CleanDatas = append(n.Edges.CleanDatas, e) }); err != nil {
			return nil, err
		}
	}
	if query := saq.withProtectGroup; query != nil {
		if err := saq.loadProtectGroup(ctx, query, nodes, nil,
			func(n *SpectrumAlert, e *ProtectGroup) { n.Edges.ProtectGroup = e }); err != nil {
			return nil, err
		}
	}
	if query := saq.withStrategy; query != nil {
		if err := saq.loadStrategy(ctx, query, nodes, nil,
			func(n *SpectrumAlert, e *Strategy) { n.Edges.Strategy = e }); err != nil {
			return nil, err
		}
	}
	if query := saq.withWofangTicket; query != nil {
		if err := saq.loadWofangTicket(ctx, query, nodes, nil,
			func(n *SpectrumAlert, e *Wofang) { n.Edges.WofangTicket = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (saq *SpectrumAlertQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*SpectrumAlert, init func(*SpectrumAlert), assign func(*SpectrumAlert, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*SpectrumAlert)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (saq *SpectrumAlertQuery) loadSpectrumDatas(ctx context.Context, query *SpectrumDataQuery, nodes []*SpectrumAlert, init func(*SpectrumAlert), assign func(*SpectrumAlert, *SpectrumData)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*SpectrumAlert)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(spectrumdata.FieldSpectrumAlertID)
	}
	query.Where(predicate.SpectrumData(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(spectrumalert.SpectrumDatasColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.SpectrumAlertID
		if fk == nil {
			return fmt.Errorf(`foreign-key "spectrum_alert_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "spectrum_alert_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (saq *SpectrumAlertQuery) loadCleanDatas(ctx context.Context, query *CleanDataQuery, nodes []*SpectrumAlert, init func(*SpectrumAlert), assign func(*SpectrumAlert, *CleanData)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*SpectrumAlert)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(cleandata.FieldSpectrumAlertID)
	}
	query.Where(predicate.CleanData(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(spectrumalert.CleanDatasColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.SpectrumAlertID
		if fk == nil {
			return fmt.Errorf(`foreign-key "spectrum_alert_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "spectrum_alert_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (saq *SpectrumAlertQuery) loadProtectGroup(ctx context.Context, query *ProtectGroupQuery, nodes []*SpectrumAlert, init func(*SpectrumAlert), assign func(*SpectrumAlert, *ProtectGroup)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*SpectrumAlert)
	for i := range nodes {
		if nodes[i].ProtectGroupID == nil {
			continue
		}
		fk := *nodes[i].ProtectGroupID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(protectgroup.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "protect_group_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (saq *SpectrumAlertQuery) loadStrategy(ctx context.Context, query *StrategyQuery, nodes []*SpectrumAlert, init func(*SpectrumAlert), assign func(*SpectrumAlert, *Strategy)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*SpectrumAlert)
	for i := range nodes {
		if nodes[i].StrategyID == nil {
			continue
		}
		fk := *nodes[i].StrategyID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(strategy.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "strategy_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (saq *SpectrumAlertQuery) loadWofangTicket(ctx context.Context, query *WofangQuery, nodes []*SpectrumAlert, init func(*SpectrumAlert), assign func(*SpectrumAlert, *Wofang)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*SpectrumAlert)
	for i := range nodes {
		if nodes[i].WofangID == nil {
			continue
		}
		fk := *nodes[i].WofangID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(wofang.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "wofang_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (saq *SpectrumAlertQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := saq.querySpec()
	_spec.Node.Columns = saq.ctx.Fields
	if len(saq.ctx.Fields) > 0 {
		_spec.Unique = saq.ctx.Unique != nil && *saq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, saq.driver, _spec)
}

func (saq *SpectrumAlertQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(spectrumalert.Table, spectrumalert.Columns, sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt))
	_spec.From = saq.sql
	if unique := saq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if saq.path != nil {
		_spec.Unique = true
	}
	if fields := saq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, spectrumalert.FieldID)
		for i := range fields {
			if fields[i] != spectrumalert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if saq.withTenant != nil {
			_spec.Node.AddColumnOnce(spectrumalert.FieldTenantID)
		}
		if saq.withProtectGroup != nil {
			_spec.Node.AddColumnOnce(spectrumalert.FieldProtectGroupID)
		}
		if saq.withStrategy != nil {
			_spec.Node.AddColumnOnce(spectrumalert.FieldStrategyID)
		}
		if saq.withWofangTicket != nil {
			_spec.Node.AddColumnOnce(spectrumalert.FieldWofangID)
		}
	}
	if ps := saq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := saq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := saq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := saq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (saq *SpectrumAlertQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(saq.driver.Dialect())
	t1 := builder.Table(spectrumalert.Table)
	columns := saq.ctx.Fields
	if len(columns) == 0 {
		columns = spectrumalert.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if saq.sql != nil {
		selector = saq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if saq.ctx.Unique != nil && *saq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range saq.predicates {
		p(selector)
	}
	for _, p := range saq.order {
		p(selector)
	}
	if offset := saq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := saq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// SpectrumAlertGroupBy is the group-by builder for SpectrumAlert entities.
type SpectrumAlertGroupBy struct {
	selector
	build *SpectrumAlertQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (sagb *SpectrumAlertGroupBy) Aggregate(fns ...AggregateFunc) *SpectrumAlertGroupBy {
	sagb.fns = append(sagb.fns, fns...)
	return sagb
}

// Scan applies the selector query and scans the result into the given value.
func (sagb *SpectrumAlertGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sagb.build.ctx, "GroupBy")
	if err := sagb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SpectrumAlertQuery, *SpectrumAlertGroupBy](ctx, sagb.build, sagb, sagb.build.inters, v)
}

func (sagb *SpectrumAlertGroupBy) sqlScan(ctx context.Context, root *SpectrumAlertQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(sagb.fns))
	for _, fn := range sagb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*sagb.flds)+len(sagb.fns))
		for _, f := range *sagb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*sagb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sagb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// SpectrumAlertSelect is the builder for selecting fields of SpectrumAlert entities.
type SpectrumAlertSelect struct {
	*SpectrumAlertQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (sas *SpectrumAlertSelect) Aggregate(fns ...AggregateFunc) *SpectrumAlertSelect {
	sas.fns = append(sas.fns, fns...)
	return sas
}

// Scan applies the selector query and scans the result into the given value.
func (sas *SpectrumAlertSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sas.ctx, "Select")
	if err := sas.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SpectrumAlertQuery, *SpectrumAlertSelect](ctx, sas.SpectrumAlertQuery, sas, sas.inters, v)
}

func (sas *SpectrumAlertSelect) sqlScan(ctx context.Context, root *SpectrumAlertQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(sas.fns))
	for _, fn := range sas.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*sas.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sas.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
