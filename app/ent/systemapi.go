// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/systemapi"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// SystemApi is the model entity for the SystemApi schema.
type SystemApi struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// 路径
	Path string `json:"path,omitempty"`
	// http方法
	HTTPMethod string `json:"http_method,omitempty" query:"http_method,omitempty"`
	// 角色
	Roles *[]string `json:"roles,omitempty"`
	// 是否公共接口，所有用户可以访问
	Public bool `json:"public,omitempty"`
	// 是否sa接口，sa接口仅sa用户可操作（如果public，则普通用户可以访问(get）
	Sa           bool `json:"sa,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SystemApi) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case systemapi.FieldRoles:
			values[i] = new([]byte)
		case systemapi.FieldPublic, systemapi.FieldSa:
			values[i] = new(sql.NullBool)
		case systemapi.FieldID:
			values[i] = new(sql.NullInt64)
		case systemapi.FieldRemark, systemapi.FieldName, systemapi.FieldPath, systemapi.FieldHTTPMethod:
			values[i] = new(sql.NullString)
		case systemapi.FieldCreatedAt, systemapi.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SystemApi fields.
func (sa *SystemApi) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case systemapi.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			sa.ID = int(value.Int64)
		case systemapi.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				sa.CreatedAt = value.Time
			}
		case systemapi.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				sa.UpdatedAt = value.Time
			}
		case systemapi.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				sa.Remark = new(string)
				*sa.Remark = value.String
			}
		case systemapi.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				sa.Name = value.String
			}
		case systemapi.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				sa.Path = value.String
			}
		case systemapi.FieldHTTPMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field http_method", values[i])
			} else if value.Valid {
				sa.HTTPMethod = value.String
			}
		case systemapi.FieldRoles:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field roles", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sa.Roles); err != nil {
					return fmt.Errorf("unmarshal field roles: %w", err)
				}
			}
		case systemapi.FieldPublic:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field public", values[i])
			} else if value.Valid {
				sa.Public = value.Bool
			}
		case systemapi.FieldSa:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field sa", values[i])
			} else if value.Valid {
				sa.Sa = value.Bool
			}
		default:
			sa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SystemApi.
// This includes values selected through modifiers, order, etc.
func (sa *SystemApi) Value(name string) (ent.Value, error) {
	return sa.selectValues.Get(name)
}

// Update returns a builder for updating this SystemApi.
// Note that you need to call SystemApi.Unwrap() before calling this method if this SystemApi
// was returned from a transaction, and the transaction was committed or rolled back.
func (sa *SystemApi) Update() *SystemApiUpdateOne {
	return NewSystemApiClient(sa.config).UpdateOne(sa)
}

// Unwrap unwraps the SystemApi entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (sa *SystemApi) Unwrap() *SystemApi {
	_tx, ok := sa.config.driver.(*txDriver)
	if !ok {
		panic("ent: SystemApi is not a transactional entity")
	}
	sa.config.driver = _tx.drv
	return sa
}

// String implements the fmt.Stringer.
func (sa *SystemApi) String() string {
	var builder strings.Builder
	builder.WriteString("SystemApi(")
	builder.WriteString(fmt.Sprintf("id=%v, ", sa.ID))
	builder.WriteString("created_at=")
	builder.WriteString(sa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(sa.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := sa.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(sa.Name)
	builder.WriteString(", ")
	builder.WriteString("path=")
	builder.WriteString(sa.Path)
	builder.WriteString(", ")
	builder.WriteString("http_method=")
	builder.WriteString(sa.HTTPMethod)
	builder.WriteString(", ")
	builder.WriteString("roles=")
	builder.WriteString(fmt.Sprintf("%v", sa.Roles))
	builder.WriteString(", ")
	builder.WriteString("public=")
	builder.WriteString(fmt.Sprintf("%v", sa.Public))
	builder.WriteString(", ")
	builder.WriteString("sa=")
	builder.WriteString(fmt.Sprintf("%v", sa.Sa))
	builder.WriteByte(')')
	return builder.String()
}

// SystemApis is a parsable slice of SystemApi.
type SystemApis []*SystemApi
