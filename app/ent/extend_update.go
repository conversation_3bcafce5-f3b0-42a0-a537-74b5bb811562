// Code generated by ent, DO NOT EDIT.

package ent

// SetCasbinRule 设置 CasbinRule 值
func (cruo *CasbinRuleUpdateOne) SetItemCasbinRule(input *CasbinRule) *CasbinRuleUpdateOne {
	cruo.SetType(input.Type)

	cruo.SetSub(input.Sub)

	cruo.SetDom(input.Dom)

	cruo.SetObj(input.Obj)

	cruo.SetAct(input.Act)

	cruo.SetV4(input.V4)

	cruo.SetV5(input.V5)

	return cruo
}

// SetCleanData 设置 CleanData 值
func (cduo *CleanDataUpdateOne) SetItemCleanData(input *CleanData) *CleanDataUpdateOne {
	cduo.SetNillableTenantID(input.TenantID)

	cduo.SetNillableSpectrumAlertID(input.SpectrumAlertID)

	cduo.SetIP(input.IP)

	if !input.Time.IsZero() {
		cduo.SetTime(input.Time)
	}

	cduo.SetInBps(input.InBps)

	cduo.SetOutBps(input.OutBps)

	cduo.SetInPps(input.InPps)

	cduo.SetOutPps(input.OutPps)

	cduo.SetInAckPps(input.InAckPps)

	cduo.SetOutAckPps(input.OutAckPps)

	cduo.SetInAckBps(input.InAckBps)

	cduo.SetOutAckBps(input.OutAckBps)

	cduo.SetInSynPps(input.InSynPps)

	cduo.SetOutSynPps(input.OutSynPps)

	cduo.SetInUDPPps(input.InUDPPps)

	cduo.SetOutUDPPps(input.OutUDPPps)

	cduo.SetInUDPBps(input.InUDPBps)

	cduo.SetOutUDPBps(input.OutUDPBps)

	cduo.SetInIcmpPps(input.InIcmpPps)

	cduo.SetInIcmpBps(input.InIcmpBps)

	cduo.SetOutIcmpBps(input.OutIcmpBps)

	cduo.SetOutIcmpPps(input.OutIcmpPps)

	cduo.SetInDNSPps(input.InDNSPps)

	cduo.SetOutDNSPps(input.OutDNSPps)

	cduo.SetInDNSBps(input.InDNSBps)

	cduo.SetOutDNSBps(input.OutDNSBps)

	cduo.SetCFilterID(input.CFilterID)

	cduo.SetAttackFlags(input.AttackFlags)

	cduo.SetCount(input.Count)

	cduo.SetIPType(input.IPType)

	cduo.SetNillableCFilter(input.CFilter)

	cduo.SetNillableHost(input.Host)

	return cduo
}

// SetCloudAlert 设置 CloudAlert 值
func (cauo *CloudAlertUpdateOne) SetItemCloudAlert(input *CloudAlert) *CloudAlertUpdateOne {
	cauo.SetNillableTenantID(input.TenantID)

	cauo.SetNillableRemark(input.Remark)

	cauo.SetSrcIP(input.SrcIP)

	cauo.SetSrcPort(input.SrcPort)

	cauo.SetDstIP(input.DstIP)

	cauo.SetDstPort(input.DstPort)

	cauo.SetDefenceMode(input.DefenceMode)

	cauo.SetFlowMode(input.FlowMode)

	cauo.SetTCPAckNum(input.TCPAckNum)

	cauo.SetTCPSeqNum(input.TCPSeqNum)

	cauo.SetProtocol(input.Protocol)

	cauo.SetDefenceLevel(input.DefenceLevel)

	cauo.SetMaxPps(input.MaxPps)

	cauo.SetMaxAttackPps(input.MaxAttackPps)

	cauo.SetOverlimitPktCount(input.OverlimitPktCount)

	if !input.StartTime.IsZero() {
		cauo.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		cauo.SetEndTime(input.EndTime)
	}

	return cauo
}

// SetCloudAttackData 设置 CloudAttackData 值
func (caduo *CloudAttackDataUpdateOne) SetItemCloudAttackData(input *CloudAttackData) *CloudAttackDataUpdateOne {
	caduo.SetNillableTenantID(input.TenantID)

	caduo.SetNillableRemark(input.Remark)

	caduo.SetSrcIP(input.SrcIP)

	caduo.SetSrcPort(input.SrcPort)

	caduo.SetDstIP(input.DstIP)

	caduo.SetDstPort(input.DstPort)

	caduo.SetProtocol(input.Protocol)

	caduo.SetCurrentAttackPps(input.CurrentAttackPps)

	if !input.StartTime.IsZero() {
		caduo.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		caduo.SetEndTime(input.EndTime)
	}

	return caduo
}

// SetCloudFlowData 设置 CloudFlowData 值
func (cfduo *CloudFlowDataUpdateOne) SetItemCloudFlowData(input *CloudFlowData) *CloudFlowDataUpdateOne {
	cfduo.SetNillableTenantID(input.TenantID)

	cfduo.SetNillableRemark(input.Remark)

	cfduo.SetNillableCloudAlertID(input.CloudAlertID)

	cfduo.SetSrcIP(input.SrcIP)

	cfduo.SetSrcPort(input.SrcPort)

	cfduo.SetDstIP(input.DstIP)

	cfduo.SetDstPort(input.DstPort)

	cfduo.SetProtocol(input.Protocol)

	cfduo.SetMaxAttackPps(input.MaxAttackPps)

	cfduo.SetFlowOverMaxPpsCount(input.FlowOverMaxPpsCount)

	if !input.StartTime.IsZero() {
		cfduo.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		cfduo.SetEndTime(input.EndTime)
	}

	return cfduo
}

// SetDataSync 设置 DataSync 值
func (dsuo *DataSyncUpdateOne) SetItemDataSync(input *DataSync) *DataSyncUpdateOne {

	dsuo.SetNillableRemark(input.Remark)

	dsuo.SetPreDataList(input.PreDataList)

	dsuo.SetDataList(input.DataList)

	dsuo.SetDataType(input.DataType)

	dsuo.SetType(input.Type)

	return dsuo
}

// SetGroup 设置 Group 值
func (guo *GroupUpdateOne) SetItemGroup(input *Group) *GroupUpdateOne {
	guo.SetNillableTenantID(input.TenantID)

	guo.SetName(input.Name)

	return guo
}

// SetMatrixSpectrumAlert 设置 MatrixSpectrumAlert 值
func (msauo *MatrixSpectrumAlertUpdateOne) SetItemMatrixSpectrumAlert(input *MatrixSpectrumAlert) *MatrixSpectrumAlertUpdateOne {
	msauo.SetNillableTenantID(input.TenantID)

	msauo.SetNillableRemark(input.Remark)

	msauo.SetNillableWofangID(input.WofangID)

	msauo.SetNillableMatrixStrategyID(input.MatrixStrategyID)

	msauo.SetIPList(input.IPList)

	msauo.SetRegion(input.Region)

	msauo.SetNetType(input.NetType)

	msauo.SetIsp(input.Isp)

	if !input.StartTime.IsZero() {
		msauo.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		msauo.SetEndTime(input.EndTime)
	}

	msauo.SetAttackType(input.AttackType)

	msauo.SetBps(input.Bps)

	msauo.SetAttackInfo(input.AttackInfo)

	return msauo
}

// SetMatrixSpectrumData 设置 MatrixSpectrumData 值
func (msduo *MatrixSpectrumDataUpdateOne) SetItemMatrixSpectrumData(input *MatrixSpectrumData) *MatrixSpectrumDataUpdateOne {
	msduo.SetNillableTenantID(input.TenantID)

	msduo.SetNillableMatrixSpectrumAlertID(input.MatrixSpectrumAlertID)

	msduo.SetRegion(input.Region)

	msduo.SetNetType(input.NetType)

	msduo.SetIsp(input.Isp)

	msduo.SetBps(input.Bps)

	if !input.Time.IsZero() {
		msduo.SetTime(input.Time)
	}

	return msduo
}

// SetMatrixStrategy 设置 MatrixStrategy 值
func (msuo *MatrixStrategyUpdateOne) SetItemMatrixStrategy(input *MatrixStrategy) *MatrixStrategyUpdateOne {

	msuo.SetNillableRemark(input.Remark)

	msuo.SetName(input.Name)

	msuo.SetRegion(input.Region)

	msuo.SetNetType(input.NetType)

	msuo.SetIsp(input.Isp)

	msuo.SetMonitorBps(input.MonitorBps)

	msuo.SetDragBps(input.DragBps)

	msuo.SetDragType(input.DragType)

	return msuo
}

// SetNotify 设置 Notify 值
func (nuo *NotifyUpdateOne) SetItemNotify(input *Notify) *NotifyUpdateOne {

	nuo.SetNillableTenantID(input.TenantID)

	nuo.SetNillableRemark(input.Remark)

	nuo.SetName(input.Name)

	nuo.SetPopo(input.Popo)

	nuo.SetEmail(input.Email)

	nuo.SetSms(input.Sms)

	nuo.SetPhone(input.Phone)

	nuo.SetPopoGroups(input.PopoGroups)

	nuo.SetEmails(input.Emails)

	nuo.SetPhones(input.Phones)

	nuo.SetIPWhitelists(input.IPWhitelists)

	nuo.SetSystem(input.System)

	nuo.SetEnabled(input.Enabled)

	nuo.SetSaNotifyPopo(input.SaNotifyPopo)

	nuo.SetSaNotifyEmail(input.SaNotifyEmail)

	return nuo
}

// SetProtectGroup 设置 ProtectGroup 值
func (pguo *ProtectGroupUpdateOne) SetItemProtectGroup(input *ProtectGroup) *ProtectGroupUpdateOne {
	pguo.SetNillableTenantID(input.TenantID)

	pguo.SetNillableRemark(input.Remark)

	pguo.SetGroupName(input.GroupName)

	pguo.SetGroupID(input.GroupID)

	pguo.SetType(input.Type)

	pguo.SetIPList(input.IPList)

	pguo.SetExpandIP(input.ExpandIP)

	pguo.SetMonitorInfo(input.MonitorInfo)

	pguo.SetDragInfo(input.DragInfo)

	pguo.SetNds4Config(input.Nds4Config)

	pguo.SetNds6Config(input.Nds6Config)

	return pguo
}

// SetSkylineDos 设置 SkylineDos 值
func (sduo *SkylineDosUpdateOne) SetItemSkylineDos(input *SkylineDos) *SkylineDosUpdateOne {
	sduo.SetNillableTenantID(input.TenantID)

	sduo.SetNillableRemark(input.Remark)

	if !input.StartTime.IsZero() {
		sduo.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		sduo.SetEndTime(input.EndTime)
	}

	sduo.SetRegion(input.Region)

	sduo.SetResource(input.Resource)

	sduo.SetResourceType(input.ResourceType)

	sduo.SetVectorTypes(input.VectorTypes)

	sduo.SetStatus(input.Status)

	sduo.SetAttackID(input.AttackID)

	sduo.SetAttackCounters(input.AttackCounters)

	sduo.SetProject(input.Project)

	sduo.SetDurationTime(input.DurationTime)

	return sduo
}

// SetSocGroupTicket 设置 SocGroupTicket 值
func (sgtuo *SocGroupTicketUpdateOne) SetItemSocGroupTicket(input *SocGroupTicket) *SocGroupTicketUpdateOne {

	sgtuo.SetNillableTenantID(input.TenantID)

	sgtuo.SetNillableRemark(input.Remark)

	sgtuo.SetName(input.Name)

	sgtuo.SetType(input.Type)

	sgtuo.SetDescription(input.Description)

	sgtuo.SetFollowList(input.FollowList)

	sgtuo.SetDepartmentID(input.DepartmentID)

	sgtuo.SetIPList(input.IPList)

	sgtuo.SetMinBandwidth(input.MinBandwidth)

	sgtuo.SetDivertType(input.DivertType)

	sgtuo.SetOpType(input.OpType)

	if !input.OpTime.IsZero() {
		sgtuo.SetOpTime(input.OpTime)
	}

	sgtuo.SetConfigType(input.ConfigType)

	sgtuo.SetConfigArgs(input.ConfigArgs)

	sgtuo.SetProductName(input.ProductName)

	sgtuo.SetProductCode(input.ProductCode)

	sgtuo.SetContactList(input.ContactList)

	sgtuo.SetGroupTicketID(input.GroupTicketID)

	sgtuo.SetErrorInfo(input.ErrorInfo)

	sgtuo.SetNillableCreateUserID(input.CreateUserID)

	return sgtuo
}

// SetSpectrumAlert 设置 SpectrumAlert 值
func (sauo *SpectrumAlertUpdateOne) SetItemSpectrumAlert(input *SpectrumAlert) *SpectrumAlertUpdateOne {
	sauo.SetNillableTenantID(input.TenantID)

	sauo.SetNillableRemark(input.Remark)

	sauo.SetNillableProtectGroupID(input.ProtectGroupID)

	sauo.SetNillableStrategyID(input.StrategyID)

	sauo.SetNillableWofangID(input.WofangID)

	sauo.SetProtectStatus(input.ProtectStatus)

	sauo.SetIP(input.IP)

	if !input.StartTime.IsZero() {
		sauo.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		sauo.SetEndTime(input.EndTime)
	}

	sauo.SetAttackType(input.AttackType)

	sauo.SetMaxPps(input.MaxPps)

	sauo.SetMaxBps(input.MaxBps)

	sauo.SetAttackInfo(input.AttackInfo)

	sauo.SetIspCode(input.IspCode)

	return sauo
}

// SetSpectrumData 设置 SpectrumData 值
func (sduo *SpectrumDataUpdateOne) SetItemSpectrumData(input *SpectrumData) *SpectrumDataUpdateOne {
	sduo.SetNillableTenantID(input.TenantID)

	sduo.SetNillableSpectrumAlertID(input.SpectrumAlertID)

	sduo.SetIP(input.IP)

	if !input.Time.IsZero() {
		sduo.SetTime(input.Time)
	}

	sduo.SetMonitorID(input.MonitorID)

	sduo.SetDataType(input.DataType)

	sduo.SetBps(input.Bps)

	sduo.SetPps(input.Pps)

	sduo.SetSynBps(input.SynBps)

	sduo.SetSynPps(input.SynPps)

	sduo.SetAckBps(input.AckBps)

	sduo.SetAckPps(input.AckPps)

	sduo.SetSynAckBps(input.SynAckBps)

	sduo.SetSynAckPps(input.SynAckPps)

	sduo.SetIcmpBps(input.IcmpBps)

	sduo.SetIcmpPps(input.IcmpPps)

	sduo.SetSmallPps(input.SmallPps)

	sduo.SetNtpPps(input.NtpPps)

	sduo.SetNtpBps(input.NtpBps)

	sduo.SetDNSQueryPps(input.DNSQueryPps)

	sduo.SetDNSQueryBps(input.DNSQueryBps)

	sduo.SetDNSAnswerPps(input.DNSAnswerPps)

	sduo.SetDNSAnswerBps(input.DNSAnswerBps)

	sduo.SetSsdpBps(input.SsdpBps)

	sduo.SetSsdpPps(input.SsdpPps)

	sduo.SetUDPPps(input.UDPPps)

	sduo.SetUDPBps(input.UDPBps)

	sduo.SetQPS(input.QPS)

	sduo.SetReceiveCount(input.ReceiveCount)

	sduo.SetIPType(input.IPType)

	sduo.SetNillableMonitor(input.Monitor)

	sduo.SetNillableProduct(input.Product)

	sduo.SetNillableHost(input.Host)

	return sduo
}

// SetStrategy 设置 Strategy 值
func (suo *StrategyUpdateOne) SetItemStrategy(input *Strategy) *StrategyUpdateOne {
	suo.SetNillableTenantID(input.TenantID)

	suo.SetNillableRemark(input.Remark)

	suo.SetName(input.Name)

	suo.SetType(input.Type)

	suo.SetEnabled(input.Enabled)

	suo.SetSystem(input.System)

	suo.SetBps(input.Bps)

	suo.SetPps(input.Pps)

	suo.SetBpsCount(input.BpsCount)

	suo.SetPpsCount(input.PpsCount)

	suo.SetIspCode(input.IspCode)

	return suo
}

// SetSystemApi 设置 SystemApi 值
func (sauo *SystemApiUpdateOne) SetItemSystemApi(input *SystemApi) *SystemApiUpdateOne {

	sauo.SetNillableRemark(input.Remark)

	sauo.SetName(input.Name)

	sauo.SetPath(input.Path)

	sauo.SetHTTPMethod(input.HTTPMethod)

	sauo.SetRoles(input.Roles)

	sauo.SetPublic(input.Public)

	sauo.SetSa(input.Sa)

	return sauo
}

// SetSystemConfig 设置 SystemConfig 值
func (scuo *SystemConfigUpdateOne) SetItemSystemConfig(input *SystemConfig) *SystemConfigUpdateOne {

	scuo.SetNillableRemark(input.Remark)

	scuo.SetWofangTestIP(input.WofangTestIP)

	scuo.SetNotifyPhones(input.NotifyPhones)

	scuo.SetNotifyEmails(input.NotifyEmails)

	scuo.SetNotifyScenes(input.NotifyScenes)

	scuo.SetIPWhitelists(input.IPWhitelists)

	return scuo
}

// SetTenant 设置 Tenant 值
func (tuo *TenantUpdateOne) SetItemTenant(input *Tenant) *TenantUpdateOne {
	tuo.SetName(input.Name)

	tuo.SetCode(input.Code)

	tuo.SetOffline(input.Offline)

	tuo.SetIsdefend(input.Isdefend)

	return tuo
}

// SetUser 设置 User 值
func (uuo *UserUpdateOne) SetItemUser(input *User) *UserUpdateOne {
	uuo.SetValid(input.Valid)

	uuo.SetName(input.Name)

	uuo.SetPassword(input.Password)

	uuo.SetSuperAdmin(input.SuperAdmin)

	uuo.SetUpdateAuth(input.UpdateAuth)

	return uuo
}

// SetUserOperationLog 设置 UserOperationLog 值
func (uoluo *UserOperationLogUpdateOne) SetItemUserOperationLog(input *UserOperationLog) *UserOperationLogUpdateOne {
	uoluo.SetNillableRemark(input.Remark)

	uoluo.SetUsername(input.Username)

	uoluo.SetMethod(input.Method)

	uoluo.SetRequestID(input.RequestID)

	uoluo.SetURI(input.URI)

	uoluo.SetRequestBody(input.RequestBody)

	uoluo.SetProject(input.Project)

	return uoluo
}

// SetWofang 设置 Wofang 值
func (wuo *WofangUpdateOne) SetItemWofang(input *Wofang) *WofangUpdateOne {

	wuo.SetNillableTenantID(input.TenantID)

	wuo.SetNillableRemark(input.Remark)

	wuo.SetName(input.Name)

	wuo.SetIP(input.IP)

	wuo.SetType(input.Type)

	wuo.SetUnDragSecond(input.UnDragSecond)

	if !input.StartTime.IsZero() {
		wuo.SetStartTime(input.StartTime)
	}

	wuo.SetErrorInfo(input.ErrorInfo)

	wuo.SetStatus(input.Status)

	wuo.SetNillableCreateUserID(input.CreateUserID)

	return wuo
}

// SetWofangAlert 设置 WofangAlert 值
func (wauo *WofangAlertUpdateOne) SetItemWofangAlert(input *WofangAlert) *WofangAlertUpdateOne {
	wauo.SetNillableTenantID(input.TenantID)

	wauo.SetNillableRemark(input.Remark)

	wauo.SetAttackStatus(input.AttackStatus)

	wauo.SetAttackType(input.AttackType)

	wauo.SetDeviceIP(input.DeviceIP)

	wauo.SetZoneIP(input.ZoneIP)

	wauo.SetAttackID(input.AttackID)

	if !input.StartTime.IsZero() {
		wauo.SetStartTime(input.StartTime)
	}

	if !input.EndTime.IsZero() {
		wauo.SetEndTime(input.EndTime)
	}

	wauo.SetMaxDropBps(input.MaxDropBps)

	wauo.SetMaxInBps(input.MaxInBps)

	return wauo
}
