// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudAttackDataUpdate is the builder for updating CloudAttackData entities.
type CloudAttackDataUpdate struct {
	config
	hooks    []Hook
	mutation *CloudAttackDataMutation
}

// Where appends a list predicates to the CloudAttackDataUpdate builder.
func (cadu *CloudAttackDataUpdate) Where(ps ...predicate.CloudAttackData) *CloudAttackDataUpdate {
	cadu.mutation.Where(ps...)
	return cadu
}

// SetTenantID sets the "tenant_id" field.
func (cadu *CloudAttackDataUpdate) SetTenantID(i int) *CloudAttackDataUpdate {
	cadu.mutation.SetTenantID(i)
	return cadu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableTenantID(i *int) *CloudAttackDataUpdate {
	if i != nil {
		cadu.SetTenantID(*i)
	}
	return cadu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (cadu *CloudAttackDataUpdate) ClearTenantID() *CloudAttackDataUpdate {
	cadu.mutation.ClearTenantID()
	return cadu
}

// SetUpdatedAt sets the "updated_at" field.
func (cadu *CloudAttackDataUpdate) SetUpdatedAt(t time.Time) *CloudAttackDataUpdate {
	cadu.mutation.SetUpdatedAt(t)
	return cadu
}

// SetRemark sets the "remark" field.
func (cadu *CloudAttackDataUpdate) SetRemark(s string) *CloudAttackDataUpdate {
	cadu.mutation.SetRemark(s)
	return cadu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableRemark(s *string) *CloudAttackDataUpdate {
	if s != nil {
		cadu.SetRemark(*s)
	}
	return cadu
}

// ClearRemark clears the value of the "remark" field.
func (cadu *CloudAttackDataUpdate) ClearRemark() *CloudAttackDataUpdate {
	cadu.mutation.ClearRemark()
	return cadu
}

// SetSrcIP sets the "src_ip" field.
func (cadu *CloudAttackDataUpdate) SetSrcIP(s string) *CloudAttackDataUpdate {
	cadu.mutation.SetSrcIP(s)
	return cadu
}

// SetNillableSrcIP sets the "src_ip" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableSrcIP(s *string) *CloudAttackDataUpdate {
	if s != nil {
		cadu.SetSrcIP(*s)
	}
	return cadu
}

// SetSrcPort sets the "src_port" field.
func (cadu *CloudAttackDataUpdate) SetSrcPort(i int) *CloudAttackDataUpdate {
	cadu.mutation.ResetSrcPort()
	cadu.mutation.SetSrcPort(i)
	return cadu
}

// SetNillableSrcPort sets the "src_port" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableSrcPort(i *int) *CloudAttackDataUpdate {
	if i != nil {
		cadu.SetSrcPort(*i)
	}
	return cadu
}

// AddSrcPort adds i to the "src_port" field.
func (cadu *CloudAttackDataUpdate) AddSrcPort(i int) *CloudAttackDataUpdate {
	cadu.mutation.AddSrcPort(i)
	return cadu
}

// SetDstIP sets the "dst_ip" field.
func (cadu *CloudAttackDataUpdate) SetDstIP(s string) *CloudAttackDataUpdate {
	cadu.mutation.SetDstIP(s)
	return cadu
}

// SetNillableDstIP sets the "dst_ip" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableDstIP(s *string) *CloudAttackDataUpdate {
	if s != nil {
		cadu.SetDstIP(*s)
	}
	return cadu
}

// SetDstPort sets the "dst_port" field.
func (cadu *CloudAttackDataUpdate) SetDstPort(i int) *CloudAttackDataUpdate {
	cadu.mutation.ResetDstPort()
	cadu.mutation.SetDstPort(i)
	return cadu
}

// SetNillableDstPort sets the "dst_port" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableDstPort(i *int) *CloudAttackDataUpdate {
	if i != nil {
		cadu.SetDstPort(*i)
	}
	return cadu
}

// AddDstPort adds i to the "dst_port" field.
func (cadu *CloudAttackDataUpdate) AddDstPort(i int) *CloudAttackDataUpdate {
	cadu.mutation.AddDstPort(i)
	return cadu
}

// SetProtocol sets the "protocol" field.
func (cadu *CloudAttackDataUpdate) SetProtocol(i int) *CloudAttackDataUpdate {
	cadu.mutation.ResetProtocol()
	cadu.mutation.SetProtocol(i)
	return cadu
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableProtocol(i *int) *CloudAttackDataUpdate {
	if i != nil {
		cadu.SetProtocol(*i)
	}
	return cadu
}

// AddProtocol adds i to the "protocol" field.
func (cadu *CloudAttackDataUpdate) AddProtocol(i int) *CloudAttackDataUpdate {
	cadu.mutation.AddProtocol(i)
	return cadu
}

// SetCurrentAttackPps sets the "current_attack_pps" field.
func (cadu *CloudAttackDataUpdate) SetCurrentAttackPps(i int64) *CloudAttackDataUpdate {
	cadu.mutation.ResetCurrentAttackPps()
	cadu.mutation.SetCurrentAttackPps(i)
	return cadu
}

// SetNillableCurrentAttackPps sets the "current_attack_pps" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableCurrentAttackPps(i *int64) *CloudAttackDataUpdate {
	if i != nil {
		cadu.SetCurrentAttackPps(*i)
	}
	return cadu
}

// AddCurrentAttackPps adds i to the "current_attack_pps" field.
func (cadu *CloudAttackDataUpdate) AddCurrentAttackPps(i int64) *CloudAttackDataUpdate {
	cadu.mutation.AddCurrentAttackPps(i)
	return cadu
}

// SetStartTime sets the "start_time" field.
func (cadu *CloudAttackDataUpdate) SetStartTime(t time.Time) *CloudAttackDataUpdate {
	cadu.mutation.SetStartTime(t)
	return cadu
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableStartTime(t *time.Time) *CloudAttackDataUpdate {
	if t != nil {
		cadu.SetStartTime(*t)
	}
	return cadu
}

// SetEndTime sets the "end_time" field.
func (cadu *CloudAttackDataUpdate) SetEndTime(t time.Time) *CloudAttackDataUpdate {
	cadu.mutation.SetEndTime(t)
	return cadu
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (cadu *CloudAttackDataUpdate) SetNillableEndTime(t *time.Time) *CloudAttackDataUpdate {
	if t != nil {
		cadu.SetEndTime(*t)
	}
	return cadu
}

// ClearEndTime clears the value of the "end_time" field.
func (cadu *CloudAttackDataUpdate) ClearEndTime() *CloudAttackDataUpdate {
	cadu.mutation.ClearEndTime()
	return cadu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cadu *CloudAttackDataUpdate) SetTenant(t *Tenant) *CloudAttackDataUpdate {
	return cadu.SetTenantID(t.ID)
}

// Mutation returns the CloudAttackDataMutation object of the builder.
func (cadu *CloudAttackDataUpdate) Mutation() *CloudAttackDataMutation {
	return cadu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (cadu *CloudAttackDataUpdate) ClearTenant() *CloudAttackDataUpdate {
	cadu.mutation.ClearTenant()
	return cadu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cadu *CloudAttackDataUpdate) Save(ctx context.Context) (int, error) {
	if err := cadu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, cadu.sqlSave, cadu.mutation, cadu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cadu *CloudAttackDataUpdate) SaveX(ctx context.Context) int {
	affected, err := cadu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cadu *CloudAttackDataUpdate) Exec(ctx context.Context) error {
	_, err := cadu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cadu *CloudAttackDataUpdate) ExecX(ctx context.Context) {
	if err := cadu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cadu *CloudAttackDataUpdate) defaults() error {
	if _, ok := cadu.mutation.UpdatedAt(); !ok {
		if cloudattackdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudattackdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudattackdata.UpdateDefaultUpdatedAt()
		cadu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cadu *CloudAttackDataUpdate) check() error {
	if v, ok := cadu.mutation.Remark(); ok {
		if err := cloudattackdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudAttackData.remark": %w`, err)}
		}
	}
	return nil
}

func (cadu *CloudAttackDataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := cadu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(cloudattackdata.Table, cloudattackdata.Columns, sqlgraph.NewFieldSpec(cloudattackdata.FieldID, field.TypeInt))
	if ps := cadu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cadu.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudattackdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := cadu.mutation.Remark(); ok {
		_spec.SetField(cloudattackdata.FieldRemark, field.TypeString, value)
	}
	if cadu.mutation.RemarkCleared() {
		_spec.ClearField(cloudattackdata.FieldRemark, field.TypeString)
	}
	if value, ok := cadu.mutation.SrcIP(); ok {
		_spec.SetField(cloudattackdata.FieldSrcIP, field.TypeString, value)
	}
	if value, ok := cadu.mutation.SrcPort(); ok {
		_spec.SetField(cloudattackdata.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cadu.mutation.AddedSrcPort(); ok {
		_spec.AddField(cloudattackdata.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cadu.mutation.DstIP(); ok {
		_spec.SetField(cloudattackdata.FieldDstIP, field.TypeString, value)
	}
	if value, ok := cadu.mutation.DstPort(); ok {
		_spec.SetField(cloudattackdata.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cadu.mutation.AddedDstPort(); ok {
		_spec.AddField(cloudattackdata.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cadu.mutation.Protocol(); ok {
		_spec.SetField(cloudattackdata.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cadu.mutation.AddedProtocol(); ok {
		_spec.AddField(cloudattackdata.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cadu.mutation.CurrentAttackPps(); ok {
		_spec.SetField(cloudattackdata.FieldCurrentAttackPps, field.TypeInt64, value)
	}
	if value, ok := cadu.mutation.AddedCurrentAttackPps(); ok {
		_spec.AddField(cloudattackdata.FieldCurrentAttackPps, field.TypeInt64, value)
	}
	if value, ok := cadu.mutation.StartTime(); ok {
		_spec.SetField(cloudattackdata.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := cadu.mutation.EndTime(); ok {
		_spec.SetField(cloudattackdata.FieldEndTime, field.TypeTime, value)
	}
	if cadu.mutation.EndTimeCleared() {
		_spec.ClearField(cloudattackdata.FieldEndTime, field.TypeTime)
	}
	if cadu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudattackdata.TenantTable,
			Columns: []string{cloudattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cadu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudattackdata.TenantTable,
			Columns: []string{cloudattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cadu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cloudattackdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cadu.mutation.done = true
	return n, nil
}

// CloudAttackDataUpdateOne is the builder for updating a single CloudAttackData entity.
type CloudAttackDataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CloudAttackDataMutation
}

// SetTenantID sets the "tenant_id" field.
func (caduo *CloudAttackDataUpdateOne) SetTenantID(i int) *CloudAttackDataUpdateOne {
	caduo.mutation.SetTenantID(i)
	return caduo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableTenantID(i *int) *CloudAttackDataUpdateOne {
	if i != nil {
		caduo.SetTenantID(*i)
	}
	return caduo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (caduo *CloudAttackDataUpdateOne) ClearTenantID() *CloudAttackDataUpdateOne {
	caduo.mutation.ClearTenantID()
	return caduo
}

// SetUpdatedAt sets the "updated_at" field.
func (caduo *CloudAttackDataUpdateOne) SetUpdatedAt(t time.Time) *CloudAttackDataUpdateOne {
	caduo.mutation.SetUpdatedAt(t)
	return caduo
}

// SetRemark sets the "remark" field.
func (caduo *CloudAttackDataUpdateOne) SetRemark(s string) *CloudAttackDataUpdateOne {
	caduo.mutation.SetRemark(s)
	return caduo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableRemark(s *string) *CloudAttackDataUpdateOne {
	if s != nil {
		caduo.SetRemark(*s)
	}
	return caduo
}

// ClearRemark clears the value of the "remark" field.
func (caduo *CloudAttackDataUpdateOne) ClearRemark() *CloudAttackDataUpdateOne {
	caduo.mutation.ClearRemark()
	return caduo
}

// SetSrcIP sets the "src_ip" field.
func (caduo *CloudAttackDataUpdateOne) SetSrcIP(s string) *CloudAttackDataUpdateOne {
	caduo.mutation.SetSrcIP(s)
	return caduo
}

// SetNillableSrcIP sets the "src_ip" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableSrcIP(s *string) *CloudAttackDataUpdateOne {
	if s != nil {
		caduo.SetSrcIP(*s)
	}
	return caduo
}

// SetSrcPort sets the "src_port" field.
func (caduo *CloudAttackDataUpdateOne) SetSrcPort(i int) *CloudAttackDataUpdateOne {
	caduo.mutation.ResetSrcPort()
	caduo.mutation.SetSrcPort(i)
	return caduo
}

// SetNillableSrcPort sets the "src_port" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableSrcPort(i *int) *CloudAttackDataUpdateOne {
	if i != nil {
		caduo.SetSrcPort(*i)
	}
	return caduo
}

// AddSrcPort adds i to the "src_port" field.
func (caduo *CloudAttackDataUpdateOne) AddSrcPort(i int) *CloudAttackDataUpdateOne {
	caduo.mutation.AddSrcPort(i)
	return caduo
}

// SetDstIP sets the "dst_ip" field.
func (caduo *CloudAttackDataUpdateOne) SetDstIP(s string) *CloudAttackDataUpdateOne {
	caduo.mutation.SetDstIP(s)
	return caduo
}

// SetNillableDstIP sets the "dst_ip" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableDstIP(s *string) *CloudAttackDataUpdateOne {
	if s != nil {
		caduo.SetDstIP(*s)
	}
	return caduo
}

// SetDstPort sets the "dst_port" field.
func (caduo *CloudAttackDataUpdateOne) SetDstPort(i int) *CloudAttackDataUpdateOne {
	caduo.mutation.ResetDstPort()
	caduo.mutation.SetDstPort(i)
	return caduo
}

// SetNillableDstPort sets the "dst_port" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableDstPort(i *int) *CloudAttackDataUpdateOne {
	if i != nil {
		caduo.SetDstPort(*i)
	}
	return caduo
}

// AddDstPort adds i to the "dst_port" field.
func (caduo *CloudAttackDataUpdateOne) AddDstPort(i int) *CloudAttackDataUpdateOne {
	caduo.mutation.AddDstPort(i)
	return caduo
}

// SetProtocol sets the "protocol" field.
func (caduo *CloudAttackDataUpdateOne) SetProtocol(i int) *CloudAttackDataUpdateOne {
	caduo.mutation.ResetProtocol()
	caduo.mutation.SetProtocol(i)
	return caduo
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableProtocol(i *int) *CloudAttackDataUpdateOne {
	if i != nil {
		caduo.SetProtocol(*i)
	}
	return caduo
}

// AddProtocol adds i to the "protocol" field.
func (caduo *CloudAttackDataUpdateOne) AddProtocol(i int) *CloudAttackDataUpdateOne {
	caduo.mutation.AddProtocol(i)
	return caduo
}

// SetCurrentAttackPps sets the "current_attack_pps" field.
func (caduo *CloudAttackDataUpdateOne) SetCurrentAttackPps(i int64) *CloudAttackDataUpdateOne {
	caduo.mutation.ResetCurrentAttackPps()
	caduo.mutation.SetCurrentAttackPps(i)
	return caduo
}

// SetNillableCurrentAttackPps sets the "current_attack_pps" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableCurrentAttackPps(i *int64) *CloudAttackDataUpdateOne {
	if i != nil {
		caduo.SetCurrentAttackPps(*i)
	}
	return caduo
}

// AddCurrentAttackPps adds i to the "current_attack_pps" field.
func (caduo *CloudAttackDataUpdateOne) AddCurrentAttackPps(i int64) *CloudAttackDataUpdateOne {
	caduo.mutation.AddCurrentAttackPps(i)
	return caduo
}

// SetStartTime sets the "start_time" field.
func (caduo *CloudAttackDataUpdateOne) SetStartTime(t time.Time) *CloudAttackDataUpdateOne {
	caduo.mutation.SetStartTime(t)
	return caduo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableStartTime(t *time.Time) *CloudAttackDataUpdateOne {
	if t != nil {
		caduo.SetStartTime(*t)
	}
	return caduo
}

// SetEndTime sets the "end_time" field.
func (caduo *CloudAttackDataUpdateOne) SetEndTime(t time.Time) *CloudAttackDataUpdateOne {
	caduo.mutation.SetEndTime(t)
	return caduo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (caduo *CloudAttackDataUpdateOne) SetNillableEndTime(t *time.Time) *CloudAttackDataUpdateOne {
	if t != nil {
		caduo.SetEndTime(*t)
	}
	return caduo
}

// ClearEndTime clears the value of the "end_time" field.
func (caduo *CloudAttackDataUpdateOne) ClearEndTime() *CloudAttackDataUpdateOne {
	caduo.mutation.ClearEndTime()
	return caduo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (caduo *CloudAttackDataUpdateOne) SetTenant(t *Tenant) *CloudAttackDataUpdateOne {
	return caduo.SetTenantID(t.ID)
}

// Mutation returns the CloudAttackDataMutation object of the builder.
func (caduo *CloudAttackDataUpdateOne) Mutation() *CloudAttackDataMutation {
	return caduo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (caduo *CloudAttackDataUpdateOne) ClearTenant() *CloudAttackDataUpdateOne {
	caduo.mutation.ClearTenant()
	return caduo
}

// Where appends a list predicates to the CloudAttackDataUpdate builder.
func (caduo *CloudAttackDataUpdateOne) Where(ps ...predicate.CloudAttackData) *CloudAttackDataUpdateOne {
	caduo.mutation.Where(ps...)
	return caduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (caduo *CloudAttackDataUpdateOne) Select(field string, fields ...string) *CloudAttackDataUpdateOne {
	caduo.fields = append([]string{field}, fields...)
	return caduo
}

// Save executes the query and returns the updated CloudAttackData entity.
func (caduo *CloudAttackDataUpdateOne) Save(ctx context.Context) (*CloudAttackData, error) {
	if err := caduo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, caduo.sqlSave, caduo.mutation, caduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (caduo *CloudAttackDataUpdateOne) SaveX(ctx context.Context) *CloudAttackData {
	node, err := caduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (caduo *CloudAttackDataUpdateOne) Exec(ctx context.Context) error {
	_, err := caduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (caduo *CloudAttackDataUpdateOne) ExecX(ctx context.Context) {
	if err := caduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (caduo *CloudAttackDataUpdateOne) defaults() error {
	if _, ok := caduo.mutation.UpdatedAt(); !ok {
		if cloudattackdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudattackdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudattackdata.UpdateDefaultUpdatedAt()
		caduo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (caduo *CloudAttackDataUpdateOne) check() error {
	if v, ok := caduo.mutation.Remark(); ok {
		if err := cloudattackdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudAttackData.remark": %w`, err)}
		}
	}
	return nil
}

func (caduo *CloudAttackDataUpdateOne) sqlSave(ctx context.Context) (_node *CloudAttackData, err error) {
	if err := caduo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(cloudattackdata.Table, cloudattackdata.Columns, sqlgraph.NewFieldSpec(cloudattackdata.FieldID, field.TypeInt))
	id, ok := caduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CloudAttackData.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := caduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cloudattackdata.FieldID)
		for _, f := range fields {
			if !cloudattackdata.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != cloudattackdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := caduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := caduo.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudattackdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := caduo.mutation.Remark(); ok {
		_spec.SetField(cloudattackdata.FieldRemark, field.TypeString, value)
	}
	if caduo.mutation.RemarkCleared() {
		_spec.ClearField(cloudattackdata.FieldRemark, field.TypeString)
	}
	if value, ok := caduo.mutation.SrcIP(); ok {
		_spec.SetField(cloudattackdata.FieldSrcIP, field.TypeString, value)
	}
	if value, ok := caduo.mutation.SrcPort(); ok {
		_spec.SetField(cloudattackdata.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := caduo.mutation.AddedSrcPort(); ok {
		_spec.AddField(cloudattackdata.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := caduo.mutation.DstIP(); ok {
		_spec.SetField(cloudattackdata.FieldDstIP, field.TypeString, value)
	}
	if value, ok := caduo.mutation.DstPort(); ok {
		_spec.SetField(cloudattackdata.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := caduo.mutation.AddedDstPort(); ok {
		_spec.AddField(cloudattackdata.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := caduo.mutation.Protocol(); ok {
		_spec.SetField(cloudattackdata.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := caduo.mutation.AddedProtocol(); ok {
		_spec.AddField(cloudattackdata.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := caduo.mutation.CurrentAttackPps(); ok {
		_spec.SetField(cloudattackdata.FieldCurrentAttackPps, field.TypeInt64, value)
	}
	if value, ok := caduo.mutation.AddedCurrentAttackPps(); ok {
		_spec.AddField(cloudattackdata.FieldCurrentAttackPps, field.TypeInt64, value)
	}
	if value, ok := caduo.mutation.StartTime(); ok {
		_spec.SetField(cloudattackdata.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := caduo.mutation.EndTime(); ok {
		_spec.SetField(cloudattackdata.FieldEndTime, field.TypeTime, value)
	}
	if caduo.mutation.EndTimeCleared() {
		_spec.ClearField(cloudattackdata.FieldEndTime, field.TypeTime)
	}
	if caduo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudattackdata.TenantTable,
			Columns: []string{cloudattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := caduo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudattackdata.TenantTable,
			Columns: []string{cloudattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &CloudAttackData{config: caduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, caduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cloudattackdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	caduo.mutation.done = true
	return _node, nil
}
