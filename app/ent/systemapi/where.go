// Code generated by ent, DO NOT EDIT.

package systemapi

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldRemark, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldName, v))
}

// Path applies equality check predicate on the "path" field. It's identical to PathEQ.
func Path(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldPath, v))
}

// HTTPMethod applies equality check predicate on the "http_method" field. It's identical to HTTPMethodEQ.
func HTTPMethod(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldHTTPMethod, v))
}

// Public applies equality check predicate on the "public" field. It's identical to PublicEQ.
func Public(v bool) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldPublic, v))
}

// Sa applies equality check predicate on the "sa" field. It's identical to SaEQ.
func Sa(v bool) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldSa, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.SystemApi {
	return predicate.SystemApi(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldContainsFold(FieldRemark, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldContainsFold(FieldName, v))
}

// PathEQ applies the EQ predicate on the "path" field.
func PathEQ(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldPath, v))
}

// PathNEQ applies the NEQ predicate on the "path" field.
func PathNEQ(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldPath, v))
}

// PathIn applies the In predicate on the "path" field.
func PathIn(vs ...string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldIn(FieldPath, vs...))
}

// PathNotIn applies the NotIn predicate on the "path" field.
func PathNotIn(vs ...string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNotIn(FieldPath, vs...))
}

// PathGT applies the GT predicate on the "path" field.
func PathGT(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGT(FieldPath, v))
}

// PathGTE applies the GTE predicate on the "path" field.
func PathGTE(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGTE(FieldPath, v))
}

// PathLT applies the LT predicate on the "path" field.
func PathLT(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLT(FieldPath, v))
}

// PathLTE applies the LTE predicate on the "path" field.
func PathLTE(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLTE(FieldPath, v))
}

// PathContains applies the Contains predicate on the "path" field.
func PathContains(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldContains(FieldPath, v))
}

// PathHasPrefix applies the HasPrefix predicate on the "path" field.
func PathHasPrefix(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldHasPrefix(FieldPath, v))
}

// PathHasSuffix applies the HasSuffix predicate on the "path" field.
func PathHasSuffix(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldHasSuffix(FieldPath, v))
}

// PathEqualFold applies the EqualFold predicate on the "path" field.
func PathEqualFold(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEqualFold(FieldPath, v))
}

// PathContainsFold applies the ContainsFold predicate on the "path" field.
func PathContainsFold(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldContainsFold(FieldPath, v))
}

// HTTPMethodEQ applies the EQ predicate on the "http_method" field.
func HTTPMethodEQ(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldHTTPMethod, v))
}

// HTTPMethodNEQ applies the NEQ predicate on the "http_method" field.
func HTTPMethodNEQ(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldHTTPMethod, v))
}

// HTTPMethodIn applies the In predicate on the "http_method" field.
func HTTPMethodIn(vs ...string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldIn(FieldHTTPMethod, vs...))
}

// HTTPMethodNotIn applies the NotIn predicate on the "http_method" field.
func HTTPMethodNotIn(vs ...string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNotIn(FieldHTTPMethod, vs...))
}

// HTTPMethodGT applies the GT predicate on the "http_method" field.
func HTTPMethodGT(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGT(FieldHTTPMethod, v))
}

// HTTPMethodGTE applies the GTE predicate on the "http_method" field.
func HTTPMethodGTE(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldGTE(FieldHTTPMethod, v))
}

// HTTPMethodLT applies the LT predicate on the "http_method" field.
func HTTPMethodLT(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLT(FieldHTTPMethod, v))
}

// HTTPMethodLTE applies the LTE predicate on the "http_method" field.
func HTTPMethodLTE(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldLTE(FieldHTTPMethod, v))
}

// HTTPMethodContains applies the Contains predicate on the "http_method" field.
func HTTPMethodContains(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldContains(FieldHTTPMethod, v))
}

// HTTPMethodHasPrefix applies the HasPrefix predicate on the "http_method" field.
func HTTPMethodHasPrefix(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldHasPrefix(FieldHTTPMethod, v))
}

// HTTPMethodHasSuffix applies the HasSuffix predicate on the "http_method" field.
func HTTPMethodHasSuffix(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldHasSuffix(FieldHTTPMethod, v))
}

// HTTPMethodEqualFold applies the EqualFold predicate on the "http_method" field.
func HTTPMethodEqualFold(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEqualFold(FieldHTTPMethod, v))
}

// HTTPMethodContainsFold applies the ContainsFold predicate on the "http_method" field.
func HTTPMethodContainsFold(v string) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldContainsFold(FieldHTTPMethod, v))
}

// PublicEQ applies the EQ predicate on the "public" field.
func PublicEQ(v bool) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldPublic, v))
}

// PublicNEQ applies the NEQ predicate on the "public" field.
func PublicNEQ(v bool) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldPublic, v))
}

// SaEQ applies the EQ predicate on the "sa" field.
func SaEQ(v bool) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldEQ(FieldSa, v))
}

// SaNEQ applies the NEQ predicate on the "sa" field.
func SaNEQ(v bool) predicate.SystemApi {
	return predicate.SystemApi(sql.FieldNEQ(FieldSa, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SystemApi) predicate.SystemApi {
	return predicate.SystemApi(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SystemApi) predicate.SystemApi {
	return predicate.SystemApi(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SystemApi) predicate.SystemApi {
	return predicate.SystemApi(sql.NotPredicates(p))
}
