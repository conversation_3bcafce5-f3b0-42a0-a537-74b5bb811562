// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"meta/app/ent/migrate"

	"meta/app/ent/casbinrule"
	"meta/app/ent/cleandata"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/datasync"
	"meta/app/ent/group"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/notify"
	"meta/app/ent/protectgroup"
	"meta/app/ent/skylinedos"
	"meta/app/ent/socgroupticket"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/strategy"
	"meta/app/ent/systemapi"
	"meta/app/ent/systemconfig"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/ent/useroperationlog"
	"meta/app/ent/wofang"
	"meta/app/ent/wofangalert"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// CasbinRule is the client for interacting with the CasbinRule builders.
	CasbinRule *CasbinRuleClient
	// CleanData is the client for interacting with the CleanData builders.
	CleanData *CleanDataClient
	// CloudAlert is the client for interacting with the CloudAlert builders.
	CloudAlert *CloudAlertClient
	// CloudAttackData is the client for interacting with the CloudAttackData builders.
	CloudAttackData *CloudAttackDataClient
	// CloudFlowData is the client for interacting with the CloudFlowData builders.
	CloudFlowData *CloudFlowDataClient
	// DataSync is the client for interacting with the DataSync builders.
	DataSync *DataSyncClient
	// Group is the client for interacting with the Group builders.
	Group *GroupClient
	// MatrixSpectrumAlert is the client for interacting with the MatrixSpectrumAlert builders.
	MatrixSpectrumAlert *MatrixSpectrumAlertClient
	// MatrixSpectrumData is the client for interacting with the MatrixSpectrumData builders.
	MatrixSpectrumData *MatrixSpectrumDataClient
	// MatrixStrategy is the client for interacting with the MatrixStrategy builders.
	MatrixStrategy *MatrixStrategyClient
	// Notify is the client for interacting with the Notify builders.
	Notify *NotifyClient
	// ProtectGroup is the client for interacting with the ProtectGroup builders.
	ProtectGroup *ProtectGroupClient
	// SkylineDos is the client for interacting with the SkylineDos builders.
	SkylineDos *SkylineDosClient
	// SocGroupTicket is the client for interacting with the SocGroupTicket builders.
	SocGroupTicket *SocGroupTicketClient
	// SpectrumAlert is the client for interacting with the SpectrumAlert builders.
	SpectrumAlert *SpectrumAlertClient
	// SpectrumData is the client for interacting with the SpectrumData builders.
	SpectrumData *SpectrumDataClient
	// Strategy is the client for interacting with the Strategy builders.
	Strategy *StrategyClient
	// SystemApi is the client for interacting with the SystemApi builders.
	SystemApi *SystemApiClient
	// SystemConfig is the client for interacting with the SystemConfig builders.
	SystemConfig *SystemConfigClient
	// Tenant is the client for interacting with the Tenant builders.
	Tenant *TenantClient
	// User is the client for interacting with the User builders.
	User *UserClient
	// UserOperationLog is the client for interacting with the UserOperationLog builders.
	UserOperationLog *UserOperationLogClient
	// Wofang is the client for interacting with the Wofang builders.
	Wofang *WofangClient
	// WofangAlert is the client for interacting with the WofangAlert builders.
	WofangAlert *WofangAlertClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.CasbinRule = NewCasbinRuleClient(c.config)
	c.CleanData = NewCleanDataClient(c.config)
	c.CloudAlert = NewCloudAlertClient(c.config)
	c.CloudAttackData = NewCloudAttackDataClient(c.config)
	c.CloudFlowData = NewCloudFlowDataClient(c.config)
	c.DataSync = NewDataSyncClient(c.config)
	c.Group = NewGroupClient(c.config)
	c.MatrixSpectrumAlert = NewMatrixSpectrumAlertClient(c.config)
	c.MatrixSpectrumData = NewMatrixSpectrumDataClient(c.config)
	c.MatrixStrategy = NewMatrixStrategyClient(c.config)
	c.Notify = NewNotifyClient(c.config)
	c.ProtectGroup = NewProtectGroupClient(c.config)
	c.SkylineDos = NewSkylineDosClient(c.config)
	c.SocGroupTicket = NewSocGroupTicketClient(c.config)
	c.SpectrumAlert = NewSpectrumAlertClient(c.config)
	c.SpectrumData = NewSpectrumDataClient(c.config)
	c.Strategy = NewStrategyClient(c.config)
	c.SystemApi = NewSystemApiClient(c.config)
	c.SystemConfig = NewSystemConfigClient(c.config)
	c.Tenant = NewTenantClient(c.config)
	c.User = NewUserClient(c.config)
	c.UserOperationLog = NewUserOperationLogClient(c.config)
	c.Wofang = NewWofangClient(c.config)
	c.WofangAlert = NewWofangAlertClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		CasbinRule:          NewCasbinRuleClient(cfg),
		CleanData:           NewCleanDataClient(cfg),
		CloudAlert:          NewCloudAlertClient(cfg),
		CloudAttackData:     NewCloudAttackDataClient(cfg),
		CloudFlowData:       NewCloudFlowDataClient(cfg),
		DataSync:            NewDataSyncClient(cfg),
		Group:               NewGroupClient(cfg),
		MatrixSpectrumAlert: NewMatrixSpectrumAlertClient(cfg),
		MatrixSpectrumData:  NewMatrixSpectrumDataClient(cfg),
		MatrixStrategy:      NewMatrixStrategyClient(cfg),
		Notify:              NewNotifyClient(cfg),
		ProtectGroup:        NewProtectGroupClient(cfg),
		SkylineDos:          NewSkylineDosClient(cfg),
		SocGroupTicket:      NewSocGroupTicketClient(cfg),
		SpectrumAlert:       NewSpectrumAlertClient(cfg),
		SpectrumData:        NewSpectrumDataClient(cfg),
		Strategy:            NewStrategyClient(cfg),
		SystemApi:           NewSystemApiClient(cfg),
		SystemConfig:        NewSystemConfigClient(cfg),
		Tenant:              NewTenantClient(cfg),
		User:                NewUserClient(cfg),
		UserOperationLog:    NewUserOperationLogClient(cfg),
		Wofang:              NewWofangClient(cfg),
		WofangAlert:         NewWofangAlertClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		CasbinRule:          NewCasbinRuleClient(cfg),
		CleanData:           NewCleanDataClient(cfg),
		CloudAlert:          NewCloudAlertClient(cfg),
		CloudAttackData:     NewCloudAttackDataClient(cfg),
		CloudFlowData:       NewCloudFlowDataClient(cfg),
		DataSync:            NewDataSyncClient(cfg),
		Group:               NewGroupClient(cfg),
		MatrixSpectrumAlert: NewMatrixSpectrumAlertClient(cfg),
		MatrixSpectrumData:  NewMatrixSpectrumDataClient(cfg),
		MatrixStrategy:      NewMatrixStrategyClient(cfg),
		Notify:              NewNotifyClient(cfg),
		ProtectGroup:        NewProtectGroupClient(cfg),
		SkylineDos:          NewSkylineDosClient(cfg),
		SocGroupTicket:      NewSocGroupTicketClient(cfg),
		SpectrumAlert:       NewSpectrumAlertClient(cfg),
		SpectrumData:        NewSpectrumDataClient(cfg),
		Strategy:            NewStrategyClient(cfg),
		SystemApi:           NewSystemApiClient(cfg),
		SystemConfig:        NewSystemConfigClient(cfg),
		Tenant:              NewTenantClient(cfg),
		User:                NewUserClient(cfg),
		UserOperationLog:    NewUserOperationLogClient(cfg),
		Wofang:              NewWofangClient(cfg),
		WofangAlert:         NewWofangAlertClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		CasbinRule.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.CasbinRule, c.CleanData, c.CloudAlert, c.CloudAttackData, c.CloudFlowData,
		c.DataSync, c.Group, c.MatrixSpectrumAlert, c.MatrixSpectrumData,
		c.MatrixStrategy, c.Notify, c.ProtectGroup, c.SkylineDos, c.SocGroupTicket,
		c.SpectrumAlert, c.SpectrumData, c.Strategy, c.SystemApi, c.SystemConfig,
		c.Tenant, c.User, c.UserOperationLog, c.Wofang, c.WofangAlert,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.CasbinRule, c.CleanData, c.CloudAlert, c.CloudAttackData, c.CloudFlowData,
		c.DataSync, c.Group, c.MatrixSpectrumAlert, c.MatrixSpectrumData,
		c.MatrixStrategy, c.Notify, c.ProtectGroup, c.SkylineDos, c.SocGroupTicket,
		c.SpectrumAlert, c.SpectrumData, c.Strategy, c.SystemApi, c.SystemConfig,
		c.Tenant, c.User, c.UserOperationLog, c.Wofang, c.WofangAlert,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *CasbinRuleMutation:
		return c.CasbinRule.mutate(ctx, m)
	case *CleanDataMutation:
		return c.CleanData.mutate(ctx, m)
	case *CloudAlertMutation:
		return c.CloudAlert.mutate(ctx, m)
	case *CloudAttackDataMutation:
		return c.CloudAttackData.mutate(ctx, m)
	case *CloudFlowDataMutation:
		return c.CloudFlowData.mutate(ctx, m)
	case *DataSyncMutation:
		return c.DataSync.mutate(ctx, m)
	case *GroupMutation:
		return c.Group.mutate(ctx, m)
	case *MatrixSpectrumAlertMutation:
		return c.MatrixSpectrumAlert.mutate(ctx, m)
	case *MatrixSpectrumDataMutation:
		return c.MatrixSpectrumData.mutate(ctx, m)
	case *MatrixStrategyMutation:
		return c.MatrixStrategy.mutate(ctx, m)
	case *NotifyMutation:
		return c.Notify.mutate(ctx, m)
	case *ProtectGroupMutation:
		return c.ProtectGroup.mutate(ctx, m)
	case *SkylineDosMutation:
		return c.SkylineDos.mutate(ctx, m)
	case *SocGroupTicketMutation:
		return c.SocGroupTicket.mutate(ctx, m)
	case *SpectrumAlertMutation:
		return c.SpectrumAlert.mutate(ctx, m)
	case *SpectrumDataMutation:
		return c.SpectrumData.mutate(ctx, m)
	case *StrategyMutation:
		return c.Strategy.mutate(ctx, m)
	case *SystemApiMutation:
		return c.SystemApi.mutate(ctx, m)
	case *SystemConfigMutation:
		return c.SystemConfig.mutate(ctx, m)
	case *TenantMutation:
		return c.Tenant.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	case *UserOperationLogMutation:
		return c.UserOperationLog.mutate(ctx, m)
	case *WofangMutation:
		return c.Wofang.mutate(ctx, m)
	case *WofangAlertMutation:
		return c.WofangAlert.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// CasbinRuleClient is a client for the CasbinRule schema.
type CasbinRuleClient struct {
	config
}

// NewCasbinRuleClient returns a client for the CasbinRule from the given config.
func NewCasbinRuleClient(c config) *CasbinRuleClient {
	return &CasbinRuleClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `casbinrule.Hooks(f(g(h())))`.
func (c *CasbinRuleClient) Use(hooks ...Hook) {
	c.hooks.CasbinRule = append(c.hooks.CasbinRule, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `casbinrule.Intercept(f(g(h())))`.
func (c *CasbinRuleClient) Intercept(interceptors ...Interceptor) {
	c.inters.CasbinRule = append(c.inters.CasbinRule, interceptors...)
}

// Create returns a builder for creating a CasbinRule entity.
func (c *CasbinRuleClient) Create() *CasbinRuleCreate {
	mutation := newCasbinRuleMutation(c.config, OpCreate)
	return &CasbinRuleCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CasbinRule entities.
func (c *CasbinRuleClient) CreateBulk(builders ...*CasbinRuleCreate) *CasbinRuleCreateBulk {
	return &CasbinRuleCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CasbinRuleClient) MapCreateBulk(slice any, setFunc func(*CasbinRuleCreate, int)) *CasbinRuleCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CasbinRuleCreateBulk{err: fmt.Errorf("calling to CasbinRuleClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CasbinRuleCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CasbinRuleCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CasbinRule.
func (c *CasbinRuleClient) Update() *CasbinRuleUpdate {
	mutation := newCasbinRuleMutation(c.config, OpUpdate)
	return &CasbinRuleUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CasbinRuleClient) UpdateOne(cr *CasbinRule) *CasbinRuleUpdateOne {
	mutation := newCasbinRuleMutation(c.config, OpUpdateOne, withCasbinRule(cr))
	return &CasbinRuleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CasbinRuleClient) UpdateOneID(id int) *CasbinRuleUpdateOne {
	mutation := newCasbinRuleMutation(c.config, OpUpdateOne, withCasbinRuleID(id))
	return &CasbinRuleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CasbinRule.
func (c *CasbinRuleClient) Delete() *CasbinRuleDelete {
	mutation := newCasbinRuleMutation(c.config, OpDelete)
	return &CasbinRuleDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CasbinRuleClient) DeleteOne(cr *CasbinRule) *CasbinRuleDeleteOne {
	return c.DeleteOneID(cr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CasbinRuleClient) DeleteOneID(id int) *CasbinRuleDeleteOne {
	builder := c.Delete().Where(casbinrule.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CasbinRuleDeleteOne{builder}
}

// Query returns a query builder for CasbinRule.
func (c *CasbinRuleClient) Query() *CasbinRuleQuery {
	return &CasbinRuleQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCasbinRule},
		inters: c.Interceptors(),
	}
}

// Get returns a CasbinRule entity by its id.
func (c *CasbinRuleClient) Get(ctx context.Context, id int) (*CasbinRule, error) {
	return c.Query().Where(casbinrule.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CasbinRuleClient) GetX(ctx context.Context, id int) *CasbinRule {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *CasbinRuleClient) Hooks() []Hook {
	hooks := c.hooks.CasbinRule
	return append(hooks[:len(hooks):len(hooks)], casbinrule.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *CasbinRuleClient) Interceptors() []Interceptor {
	return c.inters.CasbinRule
}

func (c *CasbinRuleClient) mutate(ctx context.Context, m *CasbinRuleMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CasbinRuleCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CasbinRuleUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CasbinRuleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CasbinRuleDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CasbinRule mutation op: %q", m.Op())
	}
}

// CleanDataClient is a client for the CleanData schema.
type CleanDataClient struct {
	config
}

// NewCleanDataClient returns a client for the CleanData from the given config.
func NewCleanDataClient(c config) *CleanDataClient {
	return &CleanDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `cleandata.Hooks(f(g(h())))`.
func (c *CleanDataClient) Use(hooks ...Hook) {
	c.hooks.CleanData = append(c.hooks.CleanData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `cleandata.Intercept(f(g(h())))`.
func (c *CleanDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.CleanData = append(c.inters.CleanData, interceptors...)
}

// Create returns a builder for creating a CleanData entity.
func (c *CleanDataClient) Create() *CleanDataCreate {
	mutation := newCleanDataMutation(c.config, OpCreate)
	return &CleanDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CleanData entities.
func (c *CleanDataClient) CreateBulk(builders ...*CleanDataCreate) *CleanDataCreateBulk {
	return &CleanDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CleanDataClient) MapCreateBulk(slice any, setFunc func(*CleanDataCreate, int)) *CleanDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CleanDataCreateBulk{err: fmt.Errorf("calling to CleanDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CleanDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CleanDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CleanData.
func (c *CleanDataClient) Update() *CleanDataUpdate {
	mutation := newCleanDataMutation(c.config, OpUpdate)
	return &CleanDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CleanDataClient) UpdateOne(cd *CleanData) *CleanDataUpdateOne {
	mutation := newCleanDataMutation(c.config, OpUpdateOne, withCleanData(cd))
	return &CleanDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CleanDataClient) UpdateOneID(id int) *CleanDataUpdateOne {
	mutation := newCleanDataMutation(c.config, OpUpdateOne, withCleanDataID(id))
	return &CleanDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CleanData.
func (c *CleanDataClient) Delete() *CleanDataDelete {
	mutation := newCleanDataMutation(c.config, OpDelete)
	return &CleanDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CleanDataClient) DeleteOne(cd *CleanData) *CleanDataDeleteOne {
	return c.DeleteOneID(cd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CleanDataClient) DeleteOneID(id int) *CleanDataDeleteOne {
	builder := c.Delete().Where(cleandata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CleanDataDeleteOne{builder}
}

// Query returns a query builder for CleanData.
func (c *CleanDataClient) Query() *CleanDataQuery {
	return &CleanDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCleanData},
		inters: c.Interceptors(),
	}
}

// Get returns a CleanData entity by its id.
func (c *CleanDataClient) Get(ctx context.Context, id int) (*CleanData, error) {
	return c.Query().Where(cleandata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CleanDataClient) GetX(ctx context.Context, id int) *CleanData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a CleanData.
func (c *CleanDataClient) QueryTenant(cd *CleanData) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cleandata.Table, cleandata.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cleandata.TenantTable, cleandata.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(cd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QuerySpectrumAlert queries the spectrum_alert edge of a CleanData.
func (c *CleanDataClient) QuerySpectrumAlert(cd *CleanData) *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cleandata.Table, cleandata.FieldID, id),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, cleandata.SpectrumAlertTable, cleandata.SpectrumAlertColumn),
		)
		fromV = sqlgraph.Neighbors(cd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CleanDataClient) Hooks() []Hook {
	hooks := c.hooks.CleanData
	return append(hooks[:len(hooks):len(hooks)], cleandata.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *CleanDataClient) Interceptors() []Interceptor {
	return c.inters.CleanData
}

func (c *CleanDataClient) mutate(ctx context.Context, m *CleanDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CleanDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CleanDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CleanDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CleanDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CleanData mutation op: %q", m.Op())
	}
}

// CloudAlertClient is a client for the CloudAlert schema.
type CloudAlertClient struct {
	config
}

// NewCloudAlertClient returns a client for the CloudAlert from the given config.
func NewCloudAlertClient(c config) *CloudAlertClient {
	return &CloudAlertClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `cloudalert.Hooks(f(g(h())))`.
func (c *CloudAlertClient) Use(hooks ...Hook) {
	c.hooks.CloudAlert = append(c.hooks.CloudAlert, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `cloudalert.Intercept(f(g(h())))`.
func (c *CloudAlertClient) Intercept(interceptors ...Interceptor) {
	c.inters.CloudAlert = append(c.inters.CloudAlert, interceptors...)
}

// Create returns a builder for creating a CloudAlert entity.
func (c *CloudAlertClient) Create() *CloudAlertCreate {
	mutation := newCloudAlertMutation(c.config, OpCreate)
	return &CloudAlertCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CloudAlert entities.
func (c *CloudAlertClient) CreateBulk(builders ...*CloudAlertCreate) *CloudAlertCreateBulk {
	return &CloudAlertCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CloudAlertClient) MapCreateBulk(slice any, setFunc func(*CloudAlertCreate, int)) *CloudAlertCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CloudAlertCreateBulk{err: fmt.Errorf("calling to CloudAlertClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CloudAlertCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CloudAlertCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CloudAlert.
func (c *CloudAlertClient) Update() *CloudAlertUpdate {
	mutation := newCloudAlertMutation(c.config, OpUpdate)
	return &CloudAlertUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CloudAlertClient) UpdateOne(ca *CloudAlert) *CloudAlertUpdateOne {
	mutation := newCloudAlertMutation(c.config, OpUpdateOne, withCloudAlert(ca))
	return &CloudAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CloudAlertClient) UpdateOneID(id int) *CloudAlertUpdateOne {
	mutation := newCloudAlertMutation(c.config, OpUpdateOne, withCloudAlertID(id))
	return &CloudAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CloudAlert.
func (c *CloudAlertClient) Delete() *CloudAlertDelete {
	mutation := newCloudAlertMutation(c.config, OpDelete)
	return &CloudAlertDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CloudAlertClient) DeleteOne(ca *CloudAlert) *CloudAlertDeleteOne {
	return c.DeleteOneID(ca.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CloudAlertClient) DeleteOneID(id int) *CloudAlertDeleteOne {
	builder := c.Delete().Where(cloudalert.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CloudAlertDeleteOne{builder}
}

// Query returns a query builder for CloudAlert.
func (c *CloudAlertClient) Query() *CloudAlertQuery {
	return &CloudAlertQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCloudAlert},
		inters: c.Interceptors(),
	}
}

// Get returns a CloudAlert entity by its id.
func (c *CloudAlertClient) Get(ctx context.Context, id int) (*CloudAlert, error) {
	return c.Query().Where(cloudalert.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CloudAlertClient) GetX(ctx context.Context, id int) *CloudAlert {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a CloudAlert.
func (c *CloudAlertClient) QueryTenant(ca *CloudAlert) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ca.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudalert.Table, cloudalert.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cloudalert.TenantTable, cloudalert.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(ca.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCloudflowDatas queries the cloudflow_datas edge of a CloudAlert.
func (c *CloudAlertClient) QueryCloudflowDatas(ca *CloudAlert) *CloudFlowDataQuery {
	query := (&CloudFlowDataClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ca.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudalert.Table, cloudalert.FieldID, id),
			sqlgraph.To(cloudflowdata.Table, cloudflowdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, cloudalert.CloudflowDatasTable, cloudalert.CloudflowDatasColumn),
		)
		fromV = sqlgraph.Neighbors(ca.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CloudAlertClient) Hooks() []Hook {
	hooks := c.hooks.CloudAlert
	return append(hooks[:len(hooks):len(hooks)], cloudalert.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *CloudAlertClient) Interceptors() []Interceptor {
	return c.inters.CloudAlert
}

func (c *CloudAlertClient) mutate(ctx context.Context, m *CloudAlertMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CloudAlertCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CloudAlertUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CloudAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CloudAlertDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CloudAlert mutation op: %q", m.Op())
	}
}

// CloudAttackDataClient is a client for the CloudAttackData schema.
type CloudAttackDataClient struct {
	config
}

// NewCloudAttackDataClient returns a client for the CloudAttackData from the given config.
func NewCloudAttackDataClient(c config) *CloudAttackDataClient {
	return &CloudAttackDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `cloudattackdata.Hooks(f(g(h())))`.
func (c *CloudAttackDataClient) Use(hooks ...Hook) {
	c.hooks.CloudAttackData = append(c.hooks.CloudAttackData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `cloudattackdata.Intercept(f(g(h())))`.
func (c *CloudAttackDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.CloudAttackData = append(c.inters.CloudAttackData, interceptors...)
}

// Create returns a builder for creating a CloudAttackData entity.
func (c *CloudAttackDataClient) Create() *CloudAttackDataCreate {
	mutation := newCloudAttackDataMutation(c.config, OpCreate)
	return &CloudAttackDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CloudAttackData entities.
func (c *CloudAttackDataClient) CreateBulk(builders ...*CloudAttackDataCreate) *CloudAttackDataCreateBulk {
	return &CloudAttackDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CloudAttackDataClient) MapCreateBulk(slice any, setFunc func(*CloudAttackDataCreate, int)) *CloudAttackDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CloudAttackDataCreateBulk{err: fmt.Errorf("calling to CloudAttackDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CloudAttackDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CloudAttackDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CloudAttackData.
func (c *CloudAttackDataClient) Update() *CloudAttackDataUpdate {
	mutation := newCloudAttackDataMutation(c.config, OpUpdate)
	return &CloudAttackDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CloudAttackDataClient) UpdateOne(cad *CloudAttackData) *CloudAttackDataUpdateOne {
	mutation := newCloudAttackDataMutation(c.config, OpUpdateOne, withCloudAttackData(cad))
	return &CloudAttackDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CloudAttackDataClient) UpdateOneID(id int) *CloudAttackDataUpdateOne {
	mutation := newCloudAttackDataMutation(c.config, OpUpdateOne, withCloudAttackDataID(id))
	return &CloudAttackDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CloudAttackData.
func (c *CloudAttackDataClient) Delete() *CloudAttackDataDelete {
	mutation := newCloudAttackDataMutation(c.config, OpDelete)
	return &CloudAttackDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CloudAttackDataClient) DeleteOne(cad *CloudAttackData) *CloudAttackDataDeleteOne {
	return c.DeleteOneID(cad.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CloudAttackDataClient) DeleteOneID(id int) *CloudAttackDataDeleteOne {
	builder := c.Delete().Where(cloudattackdata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CloudAttackDataDeleteOne{builder}
}

// Query returns a query builder for CloudAttackData.
func (c *CloudAttackDataClient) Query() *CloudAttackDataQuery {
	return &CloudAttackDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCloudAttackData},
		inters: c.Interceptors(),
	}
}

// Get returns a CloudAttackData entity by its id.
func (c *CloudAttackDataClient) Get(ctx context.Context, id int) (*CloudAttackData, error) {
	return c.Query().Where(cloudattackdata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CloudAttackDataClient) GetX(ctx context.Context, id int) *CloudAttackData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a CloudAttackData.
func (c *CloudAttackDataClient) QueryTenant(cad *CloudAttackData) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cad.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudattackdata.Table, cloudattackdata.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cloudattackdata.TenantTable, cloudattackdata.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(cad.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CloudAttackDataClient) Hooks() []Hook {
	hooks := c.hooks.CloudAttackData
	return append(hooks[:len(hooks):len(hooks)], cloudattackdata.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *CloudAttackDataClient) Interceptors() []Interceptor {
	return c.inters.CloudAttackData
}

func (c *CloudAttackDataClient) mutate(ctx context.Context, m *CloudAttackDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CloudAttackDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CloudAttackDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CloudAttackDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CloudAttackDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CloudAttackData mutation op: %q", m.Op())
	}
}

// CloudFlowDataClient is a client for the CloudFlowData schema.
type CloudFlowDataClient struct {
	config
}

// NewCloudFlowDataClient returns a client for the CloudFlowData from the given config.
func NewCloudFlowDataClient(c config) *CloudFlowDataClient {
	return &CloudFlowDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `cloudflowdata.Hooks(f(g(h())))`.
func (c *CloudFlowDataClient) Use(hooks ...Hook) {
	c.hooks.CloudFlowData = append(c.hooks.CloudFlowData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `cloudflowdata.Intercept(f(g(h())))`.
func (c *CloudFlowDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.CloudFlowData = append(c.inters.CloudFlowData, interceptors...)
}

// Create returns a builder for creating a CloudFlowData entity.
func (c *CloudFlowDataClient) Create() *CloudFlowDataCreate {
	mutation := newCloudFlowDataMutation(c.config, OpCreate)
	return &CloudFlowDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of CloudFlowData entities.
func (c *CloudFlowDataClient) CreateBulk(builders ...*CloudFlowDataCreate) *CloudFlowDataCreateBulk {
	return &CloudFlowDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *CloudFlowDataClient) MapCreateBulk(slice any, setFunc func(*CloudFlowDataCreate, int)) *CloudFlowDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &CloudFlowDataCreateBulk{err: fmt.Errorf("calling to CloudFlowDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*CloudFlowDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &CloudFlowDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for CloudFlowData.
func (c *CloudFlowDataClient) Update() *CloudFlowDataUpdate {
	mutation := newCloudFlowDataMutation(c.config, OpUpdate)
	return &CloudFlowDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *CloudFlowDataClient) UpdateOne(cfd *CloudFlowData) *CloudFlowDataUpdateOne {
	mutation := newCloudFlowDataMutation(c.config, OpUpdateOne, withCloudFlowData(cfd))
	return &CloudFlowDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *CloudFlowDataClient) UpdateOneID(id int) *CloudFlowDataUpdateOne {
	mutation := newCloudFlowDataMutation(c.config, OpUpdateOne, withCloudFlowDataID(id))
	return &CloudFlowDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for CloudFlowData.
func (c *CloudFlowDataClient) Delete() *CloudFlowDataDelete {
	mutation := newCloudFlowDataMutation(c.config, OpDelete)
	return &CloudFlowDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *CloudFlowDataClient) DeleteOne(cfd *CloudFlowData) *CloudFlowDataDeleteOne {
	return c.DeleteOneID(cfd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *CloudFlowDataClient) DeleteOneID(id int) *CloudFlowDataDeleteOne {
	builder := c.Delete().Where(cloudflowdata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &CloudFlowDataDeleteOne{builder}
}

// Query returns a query builder for CloudFlowData.
func (c *CloudFlowDataClient) Query() *CloudFlowDataQuery {
	return &CloudFlowDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeCloudFlowData},
		inters: c.Interceptors(),
	}
}

// Get returns a CloudFlowData entity by its id.
func (c *CloudFlowDataClient) Get(ctx context.Context, id int) (*CloudFlowData, error) {
	return c.Query().Where(cloudflowdata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *CloudFlowDataClient) GetX(ctx context.Context, id int) *CloudFlowData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a CloudFlowData.
func (c *CloudFlowDataClient) QueryTenant(cfd *CloudFlowData) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cfd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudflowdata.Table, cloudflowdata.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cloudflowdata.TenantTable, cloudflowdata.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(cfd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCloudAlert queries the cloud_alert edge of a CloudFlowData.
func (c *CloudFlowDataClient) QueryCloudAlert(cfd *CloudFlowData) *CloudAlertQuery {
	query := (&CloudAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cfd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudflowdata.Table, cloudflowdata.FieldID, id),
			sqlgraph.To(cloudalert.Table, cloudalert.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, cloudflowdata.CloudAlertTable, cloudflowdata.CloudAlertColumn),
		)
		fromV = sqlgraph.Neighbors(cfd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *CloudFlowDataClient) Hooks() []Hook {
	hooks := c.hooks.CloudFlowData
	return append(hooks[:len(hooks):len(hooks)], cloudflowdata.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *CloudFlowDataClient) Interceptors() []Interceptor {
	return c.inters.CloudFlowData
}

func (c *CloudFlowDataClient) mutate(ctx context.Context, m *CloudFlowDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&CloudFlowDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&CloudFlowDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&CloudFlowDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&CloudFlowDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown CloudFlowData mutation op: %q", m.Op())
	}
}

// DataSyncClient is a client for the DataSync schema.
type DataSyncClient struct {
	config
}

// NewDataSyncClient returns a client for the DataSync from the given config.
func NewDataSyncClient(c config) *DataSyncClient {
	return &DataSyncClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `datasync.Hooks(f(g(h())))`.
func (c *DataSyncClient) Use(hooks ...Hook) {
	c.hooks.DataSync = append(c.hooks.DataSync, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `datasync.Intercept(f(g(h())))`.
func (c *DataSyncClient) Intercept(interceptors ...Interceptor) {
	c.inters.DataSync = append(c.inters.DataSync, interceptors...)
}

// Create returns a builder for creating a DataSync entity.
func (c *DataSyncClient) Create() *DataSyncCreate {
	mutation := newDataSyncMutation(c.config, OpCreate)
	return &DataSyncCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DataSync entities.
func (c *DataSyncClient) CreateBulk(builders ...*DataSyncCreate) *DataSyncCreateBulk {
	return &DataSyncCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DataSyncClient) MapCreateBulk(slice any, setFunc func(*DataSyncCreate, int)) *DataSyncCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DataSyncCreateBulk{err: fmt.Errorf("calling to DataSyncClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DataSyncCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DataSyncCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DataSync.
func (c *DataSyncClient) Update() *DataSyncUpdate {
	mutation := newDataSyncMutation(c.config, OpUpdate)
	return &DataSyncUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DataSyncClient) UpdateOne(ds *DataSync) *DataSyncUpdateOne {
	mutation := newDataSyncMutation(c.config, OpUpdateOne, withDataSync(ds))
	return &DataSyncUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DataSyncClient) UpdateOneID(id int) *DataSyncUpdateOne {
	mutation := newDataSyncMutation(c.config, OpUpdateOne, withDataSyncID(id))
	return &DataSyncUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DataSync.
func (c *DataSyncClient) Delete() *DataSyncDelete {
	mutation := newDataSyncMutation(c.config, OpDelete)
	return &DataSyncDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DataSyncClient) DeleteOne(ds *DataSync) *DataSyncDeleteOne {
	return c.DeleteOneID(ds.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DataSyncClient) DeleteOneID(id int) *DataSyncDeleteOne {
	builder := c.Delete().Where(datasync.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DataSyncDeleteOne{builder}
}

// Query returns a query builder for DataSync.
func (c *DataSyncClient) Query() *DataSyncQuery {
	return &DataSyncQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDataSync},
		inters: c.Interceptors(),
	}
}

// Get returns a DataSync entity by its id.
func (c *DataSyncClient) Get(ctx context.Context, id int) (*DataSync, error) {
	return c.Query().Where(datasync.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DataSyncClient) GetX(ctx context.Context, id int) *DataSync {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DataSyncClient) Hooks() []Hook {
	hooks := c.hooks.DataSync
	return append(hooks[:len(hooks):len(hooks)], datasync.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DataSyncClient) Interceptors() []Interceptor {
	return c.inters.DataSync
}

func (c *DataSyncClient) mutate(ctx context.Context, m *DataSyncMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DataSyncCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DataSyncUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DataSyncUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DataSyncDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DataSync mutation op: %q", m.Op())
	}
}

// GroupClient is a client for the Group schema.
type GroupClient struct {
	config
}

// NewGroupClient returns a client for the Group from the given config.
func NewGroupClient(c config) *GroupClient {
	return &GroupClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `group.Hooks(f(g(h())))`.
func (c *GroupClient) Use(hooks ...Hook) {
	c.hooks.Group = append(c.hooks.Group, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `group.Intercept(f(g(h())))`.
func (c *GroupClient) Intercept(interceptors ...Interceptor) {
	c.inters.Group = append(c.inters.Group, interceptors...)
}

// Create returns a builder for creating a Group entity.
func (c *GroupClient) Create() *GroupCreate {
	mutation := newGroupMutation(c.config, OpCreate)
	return &GroupCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Group entities.
func (c *GroupClient) CreateBulk(builders ...*GroupCreate) *GroupCreateBulk {
	return &GroupCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *GroupClient) MapCreateBulk(slice any, setFunc func(*GroupCreate, int)) *GroupCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &GroupCreateBulk{err: fmt.Errorf("calling to GroupClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*GroupCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &GroupCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Group.
func (c *GroupClient) Update() *GroupUpdate {
	mutation := newGroupMutation(c.config, OpUpdate)
	return &GroupUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *GroupClient) UpdateOne(gr *Group) *GroupUpdateOne {
	mutation := newGroupMutation(c.config, OpUpdateOne, withGroup(gr))
	return &GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *GroupClient) UpdateOneID(id int) *GroupUpdateOne {
	mutation := newGroupMutation(c.config, OpUpdateOne, withGroupID(id))
	return &GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Group.
func (c *GroupClient) Delete() *GroupDelete {
	mutation := newGroupMutation(c.config, OpDelete)
	return &GroupDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *GroupClient) DeleteOne(gr *Group) *GroupDeleteOne {
	return c.DeleteOneID(gr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *GroupClient) DeleteOneID(id int) *GroupDeleteOne {
	builder := c.Delete().Where(group.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &GroupDeleteOne{builder}
}

// Query returns a query builder for Group.
func (c *GroupClient) Query() *GroupQuery {
	return &GroupQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeGroup},
		inters: c.Interceptors(),
	}
}

// Get returns a Group entity by its id.
func (c *GroupClient) Get(ctx context.Context, id int) (*Group, error) {
	return c.Query().Where(group.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *GroupClient) GetX(ctx context.Context, id int) *Group {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Group.
func (c *GroupClient) QueryTenant(gr *Group) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(group.Table, group.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, group.TenantTable, group.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(gr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUsers queries the users edge of a Group.
func (c *GroupClient) QueryUsers(gr *Group) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(group.Table, group.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, group.UsersTable, group.UsersPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(gr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *GroupClient) Hooks() []Hook {
	hooks := c.hooks.Group
	return append(hooks[:len(hooks):len(hooks)], group.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *GroupClient) Interceptors() []Interceptor {
	return c.inters.Group
}

func (c *GroupClient) mutate(ctx context.Context, m *GroupMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&GroupCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&GroupUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&GroupDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Group mutation op: %q", m.Op())
	}
}

// MatrixSpectrumAlertClient is a client for the MatrixSpectrumAlert schema.
type MatrixSpectrumAlertClient struct {
	config
}

// NewMatrixSpectrumAlertClient returns a client for the MatrixSpectrumAlert from the given config.
func NewMatrixSpectrumAlertClient(c config) *MatrixSpectrumAlertClient {
	return &MatrixSpectrumAlertClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `matrixspectrumalert.Hooks(f(g(h())))`.
func (c *MatrixSpectrumAlertClient) Use(hooks ...Hook) {
	c.hooks.MatrixSpectrumAlert = append(c.hooks.MatrixSpectrumAlert, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `matrixspectrumalert.Intercept(f(g(h())))`.
func (c *MatrixSpectrumAlertClient) Intercept(interceptors ...Interceptor) {
	c.inters.MatrixSpectrumAlert = append(c.inters.MatrixSpectrumAlert, interceptors...)
}

// Create returns a builder for creating a MatrixSpectrumAlert entity.
func (c *MatrixSpectrumAlertClient) Create() *MatrixSpectrumAlertCreate {
	mutation := newMatrixSpectrumAlertMutation(c.config, OpCreate)
	return &MatrixSpectrumAlertCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of MatrixSpectrumAlert entities.
func (c *MatrixSpectrumAlertClient) CreateBulk(builders ...*MatrixSpectrumAlertCreate) *MatrixSpectrumAlertCreateBulk {
	return &MatrixSpectrumAlertCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MatrixSpectrumAlertClient) MapCreateBulk(slice any, setFunc func(*MatrixSpectrumAlertCreate, int)) *MatrixSpectrumAlertCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MatrixSpectrumAlertCreateBulk{err: fmt.Errorf("calling to MatrixSpectrumAlertClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MatrixSpectrumAlertCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MatrixSpectrumAlertCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for MatrixSpectrumAlert.
func (c *MatrixSpectrumAlertClient) Update() *MatrixSpectrumAlertUpdate {
	mutation := newMatrixSpectrumAlertMutation(c.config, OpUpdate)
	return &MatrixSpectrumAlertUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MatrixSpectrumAlertClient) UpdateOne(msa *MatrixSpectrumAlert) *MatrixSpectrumAlertUpdateOne {
	mutation := newMatrixSpectrumAlertMutation(c.config, OpUpdateOne, withMatrixSpectrumAlert(msa))
	return &MatrixSpectrumAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MatrixSpectrumAlertClient) UpdateOneID(id int) *MatrixSpectrumAlertUpdateOne {
	mutation := newMatrixSpectrumAlertMutation(c.config, OpUpdateOne, withMatrixSpectrumAlertID(id))
	return &MatrixSpectrumAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for MatrixSpectrumAlert.
func (c *MatrixSpectrumAlertClient) Delete() *MatrixSpectrumAlertDelete {
	mutation := newMatrixSpectrumAlertMutation(c.config, OpDelete)
	return &MatrixSpectrumAlertDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MatrixSpectrumAlertClient) DeleteOne(msa *MatrixSpectrumAlert) *MatrixSpectrumAlertDeleteOne {
	return c.DeleteOneID(msa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MatrixSpectrumAlertClient) DeleteOneID(id int) *MatrixSpectrumAlertDeleteOne {
	builder := c.Delete().Where(matrixspectrumalert.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MatrixSpectrumAlertDeleteOne{builder}
}

// Query returns a query builder for MatrixSpectrumAlert.
func (c *MatrixSpectrumAlertClient) Query() *MatrixSpectrumAlertQuery {
	return &MatrixSpectrumAlertQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMatrixSpectrumAlert},
		inters: c.Interceptors(),
	}
}

// Get returns a MatrixSpectrumAlert entity by its id.
func (c *MatrixSpectrumAlertClient) Get(ctx context.Context, id int) (*MatrixSpectrumAlert, error) {
	return c.Query().Where(matrixspectrumalert.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MatrixSpectrumAlertClient) GetX(ctx context.Context, id int) *MatrixSpectrumAlert {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a MatrixSpectrumAlert.
func (c *MatrixSpectrumAlertClient) QueryTenant(msa *MatrixSpectrumAlert) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := msa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumalert.Table, matrixspectrumalert.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, matrixspectrumalert.TenantTable, matrixspectrumalert.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(msa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMatrixSpectrumDatas queries the matrix_spectrum_datas edge of a MatrixSpectrumAlert.
func (c *MatrixSpectrumAlertClient) QueryMatrixSpectrumDatas(msa *MatrixSpectrumAlert) *MatrixSpectrumDataQuery {
	query := (&MatrixSpectrumDataClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := msa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumalert.Table, matrixspectrumalert.FieldID, id),
			sqlgraph.To(matrixspectrumdata.Table, matrixspectrumdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, matrixspectrumalert.MatrixSpectrumDatasTable, matrixspectrumalert.MatrixSpectrumDatasColumn),
		)
		fromV = sqlgraph.Neighbors(msa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMatrixStrategy queries the matrix_strategy edge of a MatrixSpectrumAlert.
func (c *MatrixSpectrumAlertClient) QueryMatrixStrategy(msa *MatrixSpectrumAlert) *MatrixStrategyQuery {
	query := (&MatrixStrategyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := msa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumalert.Table, matrixspectrumalert.FieldID, id),
			sqlgraph.To(matrixstrategy.Table, matrixstrategy.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, matrixspectrumalert.MatrixStrategyTable, matrixspectrumalert.MatrixStrategyColumn),
		)
		fromV = sqlgraph.Neighbors(msa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryWofangTicket queries the wofang_ticket edge of a MatrixSpectrumAlert.
func (c *MatrixSpectrumAlertClient) QueryWofangTicket(msa *MatrixSpectrumAlert) *WofangQuery {
	query := (&WofangClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := msa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumalert.Table, matrixspectrumalert.FieldID, id),
			sqlgraph.To(wofang.Table, wofang.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, matrixspectrumalert.WofangTicketTable, matrixspectrumalert.WofangTicketColumn),
		)
		fromV = sqlgraph.Neighbors(msa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MatrixSpectrumAlertClient) Hooks() []Hook {
	hooks := c.hooks.MatrixSpectrumAlert
	return append(hooks[:len(hooks):len(hooks)], matrixspectrumalert.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *MatrixSpectrumAlertClient) Interceptors() []Interceptor {
	return c.inters.MatrixSpectrumAlert
}

func (c *MatrixSpectrumAlertClient) mutate(ctx context.Context, m *MatrixSpectrumAlertMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MatrixSpectrumAlertCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MatrixSpectrumAlertUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MatrixSpectrumAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MatrixSpectrumAlertDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown MatrixSpectrumAlert mutation op: %q", m.Op())
	}
}

// MatrixSpectrumDataClient is a client for the MatrixSpectrumData schema.
type MatrixSpectrumDataClient struct {
	config
}

// NewMatrixSpectrumDataClient returns a client for the MatrixSpectrumData from the given config.
func NewMatrixSpectrumDataClient(c config) *MatrixSpectrumDataClient {
	return &MatrixSpectrumDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `matrixspectrumdata.Hooks(f(g(h())))`.
func (c *MatrixSpectrumDataClient) Use(hooks ...Hook) {
	c.hooks.MatrixSpectrumData = append(c.hooks.MatrixSpectrumData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `matrixspectrumdata.Intercept(f(g(h())))`.
func (c *MatrixSpectrumDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.MatrixSpectrumData = append(c.inters.MatrixSpectrumData, interceptors...)
}

// Create returns a builder for creating a MatrixSpectrumData entity.
func (c *MatrixSpectrumDataClient) Create() *MatrixSpectrumDataCreate {
	mutation := newMatrixSpectrumDataMutation(c.config, OpCreate)
	return &MatrixSpectrumDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of MatrixSpectrumData entities.
func (c *MatrixSpectrumDataClient) CreateBulk(builders ...*MatrixSpectrumDataCreate) *MatrixSpectrumDataCreateBulk {
	return &MatrixSpectrumDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MatrixSpectrumDataClient) MapCreateBulk(slice any, setFunc func(*MatrixSpectrumDataCreate, int)) *MatrixSpectrumDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MatrixSpectrumDataCreateBulk{err: fmt.Errorf("calling to MatrixSpectrumDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MatrixSpectrumDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MatrixSpectrumDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for MatrixSpectrumData.
func (c *MatrixSpectrumDataClient) Update() *MatrixSpectrumDataUpdate {
	mutation := newMatrixSpectrumDataMutation(c.config, OpUpdate)
	return &MatrixSpectrumDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MatrixSpectrumDataClient) UpdateOne(msd *MatrixSpectrumData) *MatrixSpectrumDataUpdateOne {
	mutation := newMatrixSpectrumDataMutation(c.config, OpUpdateOne, withMatrixSpectrumData(msd))
	return &MatrixSpectrumDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MatrixSpectrumDataClient) UpdateOneID(id int) *MatrixSpectrumDataUpdateOne {
	mutation := newMatrixSpectrumDataMutation(c.config, OpUpdateOne, withMatrixSpectrumDataID(id))
	return &MatrixSpectrumDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for MatrixSpectrumData.
func (c *MatrixSpectrumDataClient) Delete() *MatrixSpectrumDataDelete {
	mutation := newMatrixSpectrumDataMutation(c.config, OpDelete)
	return &MatrixSpectrumDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MatrixSpectrumDataClient) DeleteOne(msd *MatrixSpectrumData) *MatrixSpectrumDataDeleteOne {
	return c.DeleteOneID(msd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MatrixSpectrumDataClient) DeleteOneID(id int) *MatrixSpectrumDataDeleteOne {
	builder := c.Delete().Where(matrixspectrumdata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MatrixSpectrumDataDeleteOne{builder}
}

// Query returns a query builder for MatrixSpectrumData.
func (c *MatrixSpectrumDataClient) Query() *MatrixSpectrumDataQuery {
	return &MatrixSpectrumDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMatrixSpectrumData},
		inters: c.Interceptors(),
	}
}

// Get returns a MatrixSpectrumData entity by its id.
func (c *MatrixSpectrumDataClient) Get(ctx context.Context, id int) (*MatrixSpectrumData, error) {
	return c.Query().Where(matrixspectrumdata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MatrixSpectrumDataClient) GetX(ctx context.Context, id int) *MatrixSpectrumData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a MatrixSpectrumData.
func (c *MatrixSpectrumDataClient) QueryTenant(msd *MatrixSpectrumData) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := msd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumdata.Table, matrixspectrumdata.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, matrixspectrumdata.TenantTable, matrixspectrumdata.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(msd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMatrixSpectrumAlert queries the matrix_spectrum_alert edge of a MatrixSpectrumData.
func (c *MatrixSpectrumDataClient) QueryMatrixSpectrumAlert(msd *MatrixSpectrumData) *MatrixSpectrumAlertQuery {
	query := (&MatrixSpectrumAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := msd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumdata.Table, matrixspectrumdata.FieldID, id),
			sqlgraph.To(matrixspectrumalert.Table, matrixspectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, matrixspectrumdata.MatrixSpectrumAlertTable, matrixspectrumdata.MatrixSpectrumAlertColumn),
		)
		fromV = sqlgraph.Neighbors(msd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MatrixSpectrumDataClient) Hooks() []Hook {
	hooks := c.hooks.MatrixSpectrumData
	return append(hooks[:len(hooks):len(hooks)], matrixspectrumdata.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *MatrixSpectrumDataClient) Interceptors() []Interceptor {
	return c.inters.MatrixSpectrumData
}

func (c *MatrixSpectrumDataClient) mutate(ctx context.Context, m *MatrixSpectrumDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MatrixSpectrumDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MatrixSpectrumDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MatrixSpectrumDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MatrixSpectrumDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown MatrixSpectrumData mutation op: %q", m.Op())
	}
}

// MatrixStrategyClient is a client for the MatrixStrategy schema.
type MatrixStrategyClient struct {
	config
}

// NewMatrixStrategyClient returns a client for the MatrixStrategy from the given config.
func NewMatrixStrategyClient(c config) *MatrixStrategyClient {
	return &MatrixStrategyClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `matrixstrategy.Hooks(f(g(h())))`.
func (c *MatrixStrategyClient) Use(hooks ...Hook) {
	c.hooks.MatrixStrategy = append(c.hooks.MatrixStrategy, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `matrixstrategy.Intercept(f(g(h())))`.
func (c *MatrixStrategyClient) Intercept(interceptors ...Interceptor) {
	c.inters.MatrixStrategy = append(c.inters.MatrixStrategy, interceptors...)
}

// Create returns a builder for creating a MatrixStrategy entity.
func (c *MatrixStrategyClient) Create() *MatrixStrategyCreate {
	mutation := newMatrixStrategyMutation(c.config, OpCreate)
	return &MatrixStrategyCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of MatrixStrategy entities.
func (c *MatrixStrategyClient) CreateBulk(builders ...*MatrixStrategyCreate) *MatrixStrategyCreateBulk {
	return &MatrixStrategyCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MatrixStrategyClient) MapCreateBulk(slice any, setFunc func(*MatrixStrategyCreate, int)) *MatrixStrategyCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MatrixStrategyCreateBulk{err: fmt.Errorf("calling to MatrixStrategyClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MatrixStrategyCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MatrixStrategyCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for MatrixStrategy.
func (c *MatrixStrategyClient) Update() *MatrixStrategyUpdate {
	mutation := newMatrixStrategyMutation(c.config, OpUpdate)
	return &MatrixStrategyUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MatrixStrategyClient) UpdateOne(ms *MatrixStrategy) *MatrixStrategyUpdateOne {
	mutation := newMatrixStrategyMutation(c.config, OpUpdateOne, withMatrixStrategy(ms))
	return &MatrixStrategyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MatrixStrategyClient) UpdateOneID(id int) *MatrixStrategyUpdateOne {
	mutation := newMatrixStrategyMutation(c.config, OpUpdateOne, withMatrixStrategyID(id))
	return &MatrixStrategyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for MatrixStrategy.
func (c *MatrixStrategyClient) Delete() *MatrixStrategyDelete {
	mutation := newMatrixStrategyMutation(c.config, OpDelete)
	return &MatrixStrategyDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MatrixStrategyClient) DeleteOne(ms *MatrixStrategy) *MatrixStrategyDeleteOne {
	return c.DeleteOneID(ms.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MatrixStrategyClient) DeleteOneID(id int) *MatrixStrategyDeleteOne {
	builder := c.Delete().Where(matrixstrategy.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MatrixStrategyDeleteOne{builder}
}

// Query returns a query builder for MatrixStrategy.
func (c *MatrixStrategyClient) Query() *MatrixStrategyQuery {
	return &MatrixStrategyQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMatrixStrategy},
		inters: c.Interceptors(),
	}
}

// Get returns a MatrixStrategy entity by its id.
func (c *MatrixStrategyClient) Get(ctx context.Context, id int) (*MatrixStrategy, error) {
	return c.Query().Where(matrixstrategy.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MatrixStrategyClient) GetX(ctx context.Context, id int) *MatrixStrategy {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryMatrixStrategyAlerts queries the matrix_strategy_alerts edge of a MatrixStrategy.
func (c *MatrixStrategyClient) QueryMatrixStrategyAlerts(ms *MatrixStrategy) *MatrixSpectrumAlertQuery {
	query := (&MatrixSpectrumAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ms.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixstrategy.Table, matrixstrategy.FieldID, id),
			sqlgraph.To(matrixspectrumalert.Table, matrixspectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, matrixstrategy.MatrixStrategyAlertsTable, matrixstrategy.MatrixStrategyAlertsColumn),
		)
		fromV = sqlgraph.Neighbors(ms.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MatrixStrategyClient) Hooks() []Hook {
	hooks := c.hooks.MatrixStrategy
	return append(hooks[:len(hooks):len(hooks)], matrixstrategy.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *MatrixStrategyClient) Interceptors() []Interceptor {
	return c.inters.MatrixStrategy
}

func (c *MatrixStrategyClient) mutate(ctx context.Context, m *MatrixStrategyMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MatrixStrategyCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MatrixStrategyUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MatrixStrategyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MatrixStrategyDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown MatrixStrategy mutation op: %q", m.Op())
	}
}

// NotifyClient is a client for the Notify schema.
type NotifyClient struct {
	config
}

// NewNotifyClient returns a client for the Notify from the given config.
func NewNotifyClient(c config) *NotifyClient {
	return &NotifyClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `notify.Hooks(f(g(h())))`.
func (c *NotifyClient) Use(hooks ...Hook) {
	c.hooks.Notify = append(c.hooks.Notify, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `notify.Intercept(f(g(h())))`.
func (c *NotifyClient) Intercept(interceptors ...Interceptor) {
	c.inters.Notify = append(c.inters.Notify, interceptors...)
}

// Create returns a builder for creating a Notify entity.
func (c *NotifyClient) Create() *NotifyCreate {
	mutation := newNotifyMutation(c.config, OpCreate)
	return &NotifyCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Notify entities.
func (c *NotifyClient) CreateBulk(builders ...*NotifyCreate) *NotifyCreateBulk {
	return &NotifyCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *NotifyClient) MapCreateBulk(slice any, setFunc func(*NotifyCreate, int)) *NotifyCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &NotifyCreateBulk{err: fmt.Errorf("calling to NotifyClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*NotifyCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &NotifyCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Notify.
func (c *NotifyClient) Update() *NotifyUpdate {
	mutation := newNotifyMutation(c.config, OpUpdate)
	return &NotifyUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *NotifyClient) UpdateOne(n *Notify) *NotifyUpdateOne {
	mutation := newNotifyMutation(c.config, OpUpdateOne, withNotify(n))
	return &NotifyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *NotifyClient) UpdateOneID(id int) *NotifyUpdateOne {
	mutation := newNotifyMutation(c.config, OpUpdateOne, withNotifyID(id))
	return &NotifyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Notify.
func (c *NotifyClient) Delete() *NotifyDelete {
	mutation := newNotifyMutation(c.config, OpDelete)
	return &NotifyDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *NotifyClient) DeleteOne(n *Notify) *NotifyDeleteOne {
	return c.DeleteOneID(n.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *NotifyClient) DeleteOneID(id int) *NotifyDeleteOne {
	builder := c.Delete().Where(notify.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &NotifyDeleteOne{builder}
}

// Query returns a query builder for Notify.
func (c *NotifyClient) Query() *NotifyQuery {
	return &NotifyQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeNotify},
		inters: c.Interceptors(),
	}
}

// Get returns a Notify entity by its id.
func (c *NotifyClient) Get(ctx context.Context, id int) (*Notify, error) {
	return c.Query().Where(notify.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *NotifyClient) GetX(ctx context.Context, id int) *Notify {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Notify.
func (c *NotifyClient) QueryTenant(n *Notify) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := n.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(notify.Table, notify.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, notify.TenantTable, notify.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(n.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *NotifyClient) Hooks() []Hook {
	hooks := c.hooks.Notify
	return append(hooks[:len(hooks):len(hooks)], notify.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *NotifyClient) Interceptors() []Interceptor {
	return c.inters.Notify
}

func (c *NotifyClient) mutate(ctx context.Context, m *NotifyMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&NotifyCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&NotifyUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&NotifyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&NotifyDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Notify mutation op: %q", m.Op())
	}
}

// ProtectGroupClient is a client for the ProtectGroup schema.
type ProtectGroupClient struct {
	config
}

// NewProtectGroupClient returns a client for the ProtectGroup from the given config.
func NewProtectGroupClient(c config) *ProtectGroupClient {
	return &ProtectGroupClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `protectgroup.Hooks(f(g(h())))`.
func (c *ProtectGroupClient) Use(hooks ...Hook) {
	c.hooks.ProtectGroup = append(c.hooks.ProtectGroup, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `protectgroup.Intercept(f(g(h())))`.
func (c *ProtectGroupClient) Intercept(interceptors ...Interceptor) {
	c.inters.ProtectGroup = append(c.inters.ProtectGroup, interceptors...)
}

// Create returns a builder for creating a ProtectGroup entity.
func (c *ProtectGroupClient) Create() *ProtectGroupCreate {
	mutation := newProtectGroupMutation(c.config, OpCreate)
	return &ProtectGroupCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ProtectGroup entities.
func (c *ProtectGroupClient) CreateBulk(builders ...*ProtectGroupCreate) *ProtectGroupCreateBulk {
	return &ProtectGroupCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ProtectGroupClient) MapCreateBulk(slice any, setFunc func(*ProtectGroupCreate, int)) *ProtectGroupCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ProtectGroupCreateBulk{err: fmt.Errorf("calling to ProtectGroupClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ProtectGroupCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ProtectGroupCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ProtectGroup.
func (c *ProtectGroupClient) Update() *ProtectGroupUpdate {
	mutation := newProtectGroupMutation(c.config, OpUpdate)
	return &ProtectGroupUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ProtectGroupClient) UpdateOne(pg *ProtectGroup) *ProtectGroupUpdateOne {
	mutation := newProtectGroupMutation(c.config, OpUpdateOne, withProtectGroup(pg))
	return &ProtectGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ProtectGroupClient) UpdateOneID(id int) *ProtectGroupUpdateOne {
	mutation := newProtectGroupMutation(c.config, OpUpdateOne, withProtectGroupID(id))
	return &ProtectGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ProtectGroup.
func (c *ProtectGroupClient) Delete() *ProtectGroupDelete {
	mutation := newProtectGroupMutation(c.config, OpDelete)
	return &ProtectGroupDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ProtectGroupClient) DeleteOne(pg *ProtectGroup) *ProtectGroupDeleteOne {
	return c.DeleteOneID(pg.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ProtectGroupClient) DeleteOneID(id int) *ProtectGroupDeleteOne {
	builder := c.Delete().Where(protectgroup.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ProtectGroupDeleteOne{builder}
}

// Query returns a query builder for ProtectGroup.
func (c *ProtectGroupClient) Query() *ProtectGroupQuery {
	return &ProtectGroupQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeProtectGroup},
		inters: c.Interceptors(),
	}
}

// Get returns a ProtectGroup entity by its id.
func (c *ProtectGroupClient) Get(ctx context.Context, id int) (*ProtectGroup, error) {
	return c.Query().Where(protectgroup.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ProtectGroupClient) GetX(ctx context.Context, id int) *ProtectGroup {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a ProtectGroup.
func (c *ProtectGroupClient) QueryTenant(pg *ProtectGroup) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pg.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(protectgroup.Table, protectgroup.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, protectgroup.TenantTable, protectgroup.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(pg.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QuerySpectrumAlerts queries the spectrum_alerts edge of a ProtectGroup.
func (c *ProtectGroupClient) QuerySpectrumAlerts(pg *ProtectGroup) *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := pg.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(protectgroup.Table, protectgroup.FieldID, id),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, protectgroup.SpectrumAlertsTable, protectgroup.SpectrumAlertsColumn),
		)
		fromV = sqlgraph.Neighbors(pg.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ProtectGroupClient) Hooks() []Hook {
	hooks := c.hooks.ProtectGroup
	return append(hooks[:len(hooks):len(hooks)], protectgroup.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ProtectGroupClient) Interceptors() []Interceptor {
	return c.inters.ProtectGroup
}

func (c *ProtectGroupClient) mutate(ctx context.Context, m *ProtectGroupMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ProtectGroupCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ProtectGroupUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ProtectGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ProtectGroupDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ProtectGroup mutation op: %q", m.Op())
	}
}

// SkylineDosClient is a client for the SkylineDos schema.
type SkylineDosClient struct {
	config
}

// NewSkylineDosClient returns a client for the SkylineDos from the given config.
func NewSkylineDosClient(c config) *SkylineDosClient {
	return &SkylineDosClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `skylinedos.Hooks(f(g(h())))`.
func (c *SkylineDosClient) Use(hooks ...Hook) {
	c.hooks.SkylineDos = append(c.hooks.SkylineDos, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `skylinedos.Intercept(f(g(h())))`.
func (c *SkylineDosClient) Intercept(interceptors ...Interceptor) {
	c.inters.SkylineDos = append(c.inters.SkylineDos, interceptors...)
}

// Create returns a builder for creating a SkylineDos entity.
func (c *SkylineDosClient) Create() *SkylineDosCreate {
	mutation := newSkylineDosMutation(c.config, OpCreate)
	return &SkylineDosCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SkylineDos entities.
func (c *SkylineDosClient) CreateBulk(builders ...*SkylineDosCreate) *SkylineDosCreateBulk {
	return &SkylineDosCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SkylineDosClient) MapCreateBulk(slice any, setFunc func(*SkylineDosCreate, int)) *SkylineDosCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SkylineDosCreateBulk{err: fmt.Errorf("calling to SkylineDosClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SkylineDosCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SkylineDosCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SkylineDos.
func (c *SkylineDosClient) Update() *SkylineDosUpdate {
	mutation := newSkylineDosMutation(c.config, OpUpdate)
	return &SkylineDosUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SkylineDosClient) UpdateOne(sd *SkylineDos) *SkylineDosUpdateOne {
	mutation := newSkylineDosMutation(c.config, OpUpdateOne, withSkylineDos(sd))
	return &SkylineDosUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SkylineDosClient) UpdateOneID(id int) *SkylineDosUpdateOne {
	mutation := newSkylineDosMutation(c.config, OpUpdateOne, withSkylineDosID(id))
	return &SkylineDosUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SkylineDos.
func (c *SkylineDosClient) Delete() *SkylineDosDelete {
	mutation := newSkylineDosMutation(c.config, OpDelete)
	return &SkylineDosDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SkylineDosClient) DeleteOne(sd *SkylineDos) *SkylineDosDeleteOne {
	return c.DeleteOneID(sd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SkylineDosClient) DeleteOneID(id int) *SkylineDosDeleteOne {
	builder := c.Delete().Where(skylinedos.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SkylineDosDeleteOne{builder}
}

// Query returns a query builder for SkylineDos.
func (c *SkylineDosClient) Query() *SkylineDosQuery {
	return &SkylineDosQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSkylineDos},
		inters: c.Interceptors(),
	}
}

// Get returns a SkylineDos entity by its id.
func (c *SkylineDosClient) Get(ctx context.Context, id int) (*SkylineDos, error) {
	return c.Query().Where(skylinedos.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SkylineDosClient) GetX(ctx context.Context, id int) *SkylineDos {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a SkylineDos.
func (c *SkylineDosClient) QueryTenant(sd *SkylineDos) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(skylinedos.Table, skylinedos.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, skylinedos.TenantTable, skylinedos.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(sd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *SkylineDosClient) Hooks() []Hook {
	hooks := c.hooks.SkylineDos
	return append(hooks[:len(hooks):len(hooks)], skylinedos.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *SkylineDosClient) Interceptors() []Interceptor {
	return c.inters.SkylineDos
}

func (c *SkylineDosClient) mutate(ctx context.Context, m *SkylineDosMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SkylineDosCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SkylineDosUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SkylineDosUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SkylineDosDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SkylineDos mutation op: %q", m.Op())
	}
}

// SocGroupTicketClient is a client for the SocGroupTicket schema.
type SocGroupTicketClient struct {
	config
}

// NewSocGroupTicketClient returns a client for the SocGroupTicket from the given config.
func NewSocGroupTicketClient(c config) *SocGroupTicketClient {
	return &SocGroupTicketClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `socgroupticket.Hooks(f(g(h())))`.
func (c *SocGroupTicketClient) Use(hooks ...Hook) {
	c.hooks.SocGroupTicket = append(c.hooks.SocGroupTicket, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `socgroupticket.Intercept(f(g(h())))`.
func (c *SocGroupTicketClient) Intercept(interceptors ...Interceptor) {
	c.inters.SocGroupTicket = append(c.inters.SocGroupTicket, interceptors...)
}

// Create returns a builder for creating a SocGroupTicket entity.
func (c *SocGroupTicketClient) Create() *SocGroupTicketCreate {
	mutation := newSocGroupTicketMutation(c.config, OpCreate)
	return &SocGroupTicketCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SocGroupTicket entities.
func (c *SocGroupTicketClient) CreateBulk(builders ...*SocGroupTicketCreate) *SocGroupTicketCreateBulk {
	return &SocGroupTicketCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SocGroupTicketClient) MapCreateBulk(slice any, setFunc func(*SocGroupTicketCreate, int)) *SocGroupTicketCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SocGroupTicketCreateBulk{err: fmt.Errorf("calling to SocGroupTicketClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SocGroupTicketCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SocGroupTicketCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SocGroupTicket.
func (c *SocGroupTicketClient) Update() *SocGroupTicketUpdate {
	mutation := newSocGroupTicketMutation(c.config, OpUpdate)
	return &SocGroupTicketUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SocGroupTicketClient) UpdateOne(sgt *SocGroupTicket) *SocGroupTicketUpdateOne {
	mutation := newSocGroupTicketMutation(c.config, OpUpdateOne, withSocGroupTicket(sgt))
	return &SocGroupTicketUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SocGroupTicketClient) UpdateOneID(id int) *SocGroupTicketUpdateOne {
	mutation := newSocGroupTicketMutation(c.config, OpUpdateOne, withSocGroupTicketID(id))
	return &SocGroupTicketUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SocGroupTicket.
func (c *SocGroupTicketClient) Delete() *SocGroupTicketDelete {
	mutation := newSocGroupTicketMutation(c.config, OpDelete)
	return &SocGroupTicketDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SocGroupTicketClient) DeleteOne(sgt *SocGroupTicket) *SocGroupTicketDeleteOne {
	return c.DeleteOneID(sgt.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SocGroupTicketClient) DeleteOneID(id int) *SocGroupTicketDeleteOne {
	builder := c.Delete().Where(socgroupticket.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SocGroupTicketDeleteOne{builder}
}

// Query returns a query builder for SocGroupTicket.
func (c *SocGroupTicketClient) Query() *SocGroupTicketQuery {
	return &SocGroupTicketQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSocGroupTicket},
		inters: c.Interceptors(),
	}
}

// Get returns a SocGroupTicket entity by its id.
func (c *SocGroupTicketClient) Get(ctx context.Context, id int) (*SocGroupTicket, error) {
	return c.Query().Where(socgroupticket.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SocGroupTicketClient) GetX(ctx context.Context, id int) *SocGroupTicket {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a SocGroupTicket.
func (c *SocGroupTicketClient) QueryTenant(sgt *SocGroupTicket) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sgt.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(socgroupticket.Table, socgroupticket.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, socgroupticket.TenantTable, socgroupticket.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(sgt.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a SocGroupTicket.
func (c *SocGroupTicketClient) QueryUser(sgt *SocGroupTicket) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sgt.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(socgroupticket.Table, socgroupticket.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, socgroupticket.UserTable, socgroupticket.UserColumn),
		)
		fromV = sqlgraph.Neighbors(sgt.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *SocGroupTicketClient) Hooks() []Hook {
	hooks := c.hooks.SocGroupTicket
	return append(hooks[:len(hooks):len(hooks)], socgroupticket.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *SocGroupTicketClient) Interceptors() []Interceptor {
	return c.inters.SocGroupTicket
}

func (c *SocGroupTicketClient) mutate(ctx context.Context, m *SocGroupTicketMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SocGroupTicketCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SocGroupTicketUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SocGroupTicketUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SocGroupTicketDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SocGroupTicket mutation op: %q", m.Op())
	}
}

// SpectrumAlertClient is a client for the SpectrumAlert schema.
type SpectrumAlertClient struct {
	config
}

// NewSpectrumAlertClient returns a client for the SpectrumAlert from the given config.
func NewSpectrumAlertClient(c config) *SpectrumAlertClient {
	return &SpectrumAlertClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `spectrumalert.Hooks(f(g(h())))`.
func (c *SpectrumAlertClient) Use(hooks ...Hook) {
	c.hooks.SpectrumAlert = append(c.hooks.SpectrumAlert, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `spectrumalert.Intercept(f(g(h())))`.
func (c *SpectrumAlertClient) Intercept(interceptors ...Interceptor) {
	c.inters.SpectrumAlert = append(c.inters.SpectrumAlert, interceptors...)
}

// Create returns a builder for creating a SpectrumAlert entity.
func (c *SpectrumAlertClient) Create() *SpectrumAlertCreate {
	mutation := newSpectrumAlertMutation(c.config, OpCreate)
	return &SpectrumAlertCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SpectrumAlert entities.
func (c *SpectrumAlertClient) CreateBulk(builders ...*SpectrumAlertCreate) *SpectrumAlertCreateBulk {
	return &SpectrumAlertCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SpectrumAlertClient) MapCreateBulk(slice any, setFunc func(*SpectrumAlertCreate, int)) *SpectrumAlertCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SpectrumAlertCreateBulk{err: fmt.Errorf("calling to SpectrumAlertClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SpectrumAlertCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SpectrumAlertCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SpectrumAlert.
func (c *SpectrumAlertClient) Update() *SpectrumAlertUpdate {
	mutation := newSpectrumAlertMutation(c.config, OpUpdate)
	return &SpectrumAlertUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SpectrumAlertClient) UpdateOne(sa *SpectrumAlert) *SpectrumAlertUpdateOne {
	mutation := newSpectrumAlertMutation(c.config, OpUpdateOne, withSpectrumAlert(sa))
	return &SpectrumAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SpectrumAlertClient) UpdateOneID(id int) *SpectrumAlertUpdateOne {
	mutation := newSpectrumAlertMutation(c.config, OpUpdateOne, withSpectrumAlertID(id))
	return &SpectrumAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SpectrumAlert.
func (c *SpectrumAlertClient) Delete() *SpectrumAlertDelete {
	mutation := newSpectrumAlertMutation(c.config, OpDelete)
	return &SpectrumAlertDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SpectrumAlertClient) DeleteOne(sa *SpectrumAlert) *SpectrumAlertDeleteOne {
	return c.DeleteOneID(sa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SpectrumAlertClient) DeleteOneID(id int) *SpectrumAlertDeleteOne {
	builder := c.Delete().Where(spectrumalert.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SpectrumAlertDeleteOne{builder}
}

// Query returns a query builder for SpectrumAlert.
func (c *SpectrumAlertClient) Query() *SpectrumAlertQuery {
	return &SpectrumAlertQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSpectrumAlert},
		inters: c.Interceptors(),
	}
}

// Get returns a SpectrumAlert entity by its id.
func (c *SpectrumAlertClient) Get(ctx context.Context, id int) (*SpectrumAlert, error) {
	return c.Query().Where(spectrumalert.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SpectrumAlertClient) GetX(ctx context.Context, id int) *SpectrumAlert {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a SpectrumAlert.
func (c *SpectrumAlertClient) QueryTenant(sa *SpectrumAlert) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, spectrumalert.TenantTable, spectrumalert.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(sa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QuerySpectrumDatas queries the spectrum_datas edge of a SpectrumAlert.
func (c *SpectrumAlertClient) QuerySpectrumDatas(sa *SpectrumAlert) *SpectrumDataQuery {
	query := (&SpectrumDataClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, id),
			sqlgraph.To(spectrumdata.Table, spectrumdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, spectrumalert.SpectrumDatasTable, spectrumalert.SpectrumDatasColumn),
		)
		fromV = sqlgraph.Neighbors(sa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryCleanDatas queries the clean_datas edge of a SpectrumAlert.
func (c *SpectrumAlertClient) QueryCleanDatas(sa *SpectrumAlert) *CleanDataQuery {
	query := (&CleanDataClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, id),
			sqlgraph.To(cleandata.Table, cleandata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, spectrumalert.CleanDatasTable, spectrumalert.CleanDatasColumn),
		)
		fromV = sqlgraph.Neighbors(sa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryProtectGroup queries the protect_group edge of a SpectrumAlert.
func (c *SpectrumAlertClient) QueryProtectGroup(sa *SpectrumAlert) *ProtectGroupQuery {
	query := (&ProtectGroupClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, id),
			sqlgraph.To(protectgroup.Table, protectgroup.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, spectrumalert.ProtectGroupTable, spectrumalert.ProtectGroupColumn),
		)
		fromV = sqlgraph.Neighbors(sa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryStrategy queries the strategy edge of a SpectrumAlert.
func (c *SpectrumAlertClient) QueryStrategy(sa *SpectrumAlert) *StrategyQuery {
	query := (&StrategyClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, id),
			sqlgraph.To(strategy.Table, strategy.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, spectrumalert.StrategyTable, spectrumalert.StrategyColumn),
		)
		fromV = sqlgraph.Neighbors(sa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryWofangTicket queries the wofang_ticket edge of a SpectrumAlert.
func (c *SpectrumAlertClient) QueryWofangTicket(sa *SpectrumAlert) *WofangQuery {
	query := (&WofangClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumalert.Table, spectrumalert.FieldID, id),
			sqlgraph.To(wofang.Table, wofang.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, spectrumalert.WofangTicketTable, spectrumalert.WofangTicketColumn),
		)
		fromV = sqlgraph.Neighbors(sa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *SpectrumAlertClient) Hooks() []Hook {
	hooks := c.hooks.SpectrumAlert
	return append(hooks[:len(hooks):len(hooks)], spectrumalert.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *SpectrumAlertClient) Interceptors() []Interceptor {
	return c.inters.SpectrumAlert
}

func (c *SpectrumAlertClient) mutate(ctx context.Context, m *SpectrumAlertMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SpectrumAlertCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SpectrumAlertUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SpectrumAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SpectrumAlertDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SpectrumAlert mutation op: %q", m.Op())
	}
}

// SpectrumDataClient is a client for the SpectrumData schema.
type SpectrumDataClient struct {
	config
}

// NewSpectrumDataClient returns a client for the SpectrumData from the given config.
func NewSpectrumDataClient(c config) *SpectrumDataClient {
	return &SpectrumDataClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `spectrumdata.Hooks(f(g(h())))`.
func (c *SpectrumDataClient) Use(hooks ...Hook) {
	c.hooks.SpectrumData = append(c.hooks.SpectrumData, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `spectrumdata.Intercept(f(g(h())))`.
func (c *SpectrumDataClient) Intercept(interceptors ...Interceptor) {
	c.inters.SpectrumData = append(c.inters.SpectrumData, interceptors...)
}

// Create returns a builder for creating a SpectrumData entity.
func (c *SpectrumDataClient) Create() *SpectrumDataCreate {
	mutation := newSpectrumDataMutation(c.config, OpCreate)
	return &SpectrumDataCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SpectrumData entities.
func (c *SpectrumDataClient) CreateBulk(builders ...*SpectrumDataCreate) *SpectrumDataCreateBulk {
	return &SpectrumDataCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SpectrumDataClient) MapCreateBulk(slice any, setFunc func(*SpectrumDataCreate, int)) *SpectrumDataCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SpectrumDataCreateBulk{err: fmt.Errorf("calling to SpectrumDataClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SpectrumDataCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SpectrumDataCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SpectrumData.
func (c *SpectrumDataClient) Update() *SpectrumDataUpdate {
	mutation := newSpectrumDataMutation(c.config, OpUpdate)
	return &SpectrumDataUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SpectrumDataClient) UpdateOne(sd *SpectrumData) *SpectrumDataUpdateOne {
	mutation := newSpectrumDataMutation(c.config, OpUpdateOne, withSpectrumData(sd))
	return &SpectrumDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SpectrumDataClient) UpdateOneID(id int) *SpectrumDataUpdateOne {
	mutation := newSpectrumDataMutation(c.config, OpUpdateOne, withSpectrumDataID(id))
	return &SpectrumDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SpectrumData.
func (c *SpectrumDataClient) Delete() *SpectrumDataDelete {
	mutation := newSpectrumDataMutation(c.config, OpDelete)
	return &SpectrumDataDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SpectrumDataClient) DeleteOne(sd *SpectrumData) *SpectrumDataDeleteOne {
	return c.DeleteOneID(sd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SpectrumDataClient) DeleteOneID(id int) *SpectrumDataDeleteOne {
	builder := c.Delete().Where(spectrumdata.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SpectrumDataDeleteOne{builder}
}

// Query returns a query builder for SpectrumData.
func (c *SpectrumDataClient) Query() *SpectrumDataQuery {
	return &SpectrumDataQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSpectrumData},
		inters: c.Interceptors(),
	}
}

// Get returns a SpectrumData entity by its id.
func (c *SpectrumDataClient) Get(ctx context.Context, id int) (*SpectrumData, error) {
	return c.Query().Where(spectrumdata.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SpectrumDataClient) GetX(ctx context.Context, id int) *SpectrumData {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a SpectrumData.
func (c *SpectrumDataClient) QueryTenant(sd *SpectrumData) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumdata.Table, spectrumdata.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, spectrumdata.TenantTable, spectrumdata.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(sd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QuerySpectrumAlert queries the spectrum_alert edge of a SpectrumData.
func (c *SpectrumDataClient) QuerySpectrumAlert(sd *SpectrumData) *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := sd.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumdata.Table, spectrumdata.FieldID, id),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, spectrumdata.SpectrumAlertTable, spectrumdata.SpectrumAlertColumn),
		)
		fromV = sqlgraph.Neighbors(sd.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *SpectrumDataClient) Hooks() []Hook {
	hooks := c.hooks.SpectrumData
	return append(hooks[:len(hooks):len(hooks)], spectrumdata.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *SpectrumDataClient) Interceptors() []Interceptor {
	return c.inters.SpectrumData
}

func (c *SpectrumDataClient) mutate(ctx context.Context, m *SpectrumDataMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SpectrumDataCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SpectrumDataUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SpectrumDataUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SpectrumDataDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SpectrumData mutation op: %q", m.Op())
	}
}

// StrategyClient is a client for the Strategy schema.
type StrategyClient struct {
	config
}

// NewStrategyClient returns a client for the Strategy from the given config.
func NewStrategyClient(c config) *StrategyClient {
	return &StrategyClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `strategy.Hooks(f(g(h())))`.
func (c *StrategyClient) Use(hooks ...Hook) {
	c.hooks.Strategy = append(c.hooks.Strategy, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `strategy.Intercept(f(g(h())))`.
func (c *StrategyClient) Intercept(interceptors ...Interceptor) {
	c.inters.Strategy = append(c.inters.Strategy, interceptors...)
}

// Create returns a builder for creating a Strategy entity.
func (c *StrategyClient) Create() *StrategyCreate {
	mutation := newStrategyMutation(c.config, OpCreate)
	return &StrategyCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Strategy entities.
func (c *StrategyClient) CreateBulk(builders ...*StrategyCreate) *StrategyCreateBulk {
	return &StrategyCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *StrategyClient) MapCreateBulk(slice any, setFunc func(*StrategyCreate, int)) *StrategyCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &StrategyCreateBulk{err: fmt.Errorf("calling to StrategyClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*StrategyCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &StrategyCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Strategy.
func (c *StrategyClient) Update() *StrategyUpdate {
	mutation := newStrategyMutation(c.config, OpUpdate)
	return &StrategyUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *StrategyClient) UpdateOne(s *Strategy) *StrategyUpdateOne {
	mutation := newStrategyMutation(c.config, OpUpdateOne, withStrategy(s))
	return &StrategyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *StrategyClient) UpdateOneID(id int) *StrategyUpdateOne {
	mutation := newStrategyMutation(c.config, OpUpdateOne, withStrategyID(id))
	return &StrategyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Strategy.
func (c *StrategyClient) Delete() *StrategyDelete {
	mutation := newStrategyMutation(c.config, OpDelete)
	return &StrategyDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *StrategyClient) DeleteOne(s *Strategy) *StrategyDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *StrategyClient) DeleteOneID(id int) *StrategyDeleteOne {
	builder := c.Delete().Where(strategy.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &StrategyDeleteOne{builder}
}

// Query returns a query builder for Strategy.
func (c *StrategyClient) Query() *StrategyQuery {
	return &StrategyQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeStrategy},
		inters: c.Interceptors(),
	}
}

// Get returns a Strategy entity by its id.
func (c *StrategyClient) Get(ctx context.Context, id int) (*Strategy, error) {
	return c.Query().Where(strategy.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *StrategyClient) GetX(ctx context.Context, id int) *Strategy {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Strategy.
func (c *StrategyClient) QueryTenant(s *Strategy) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(strategy.Table, strategy.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, strategy.TenantTable, strategy.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryStrategyAlerts queries the strategy_alerts edge of a Strategy.
func (c *StrategyClient) QueryStrategyAlerts(s *Strategy) *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := s.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(strategy.Table, strategy.FieldID, id),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, strategy.StrategyAlertsTable, strategy.StrategyAlertsColumn),
		)
		fromV = sqlgraph.Neighbors(s.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *StrategyClient) Hooks() []Hook {
	hooks := c.hooks.Strategy
	return append(hooks[:len(hooks):len(hooks)], strategy.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *StrategyClient) Interceptors() []Interceptor {
	return c.inters.Strategy
}

func (c *StrategyClient) mutate(ctx context.Context, m *StrategyMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&StrategyCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&StrategyUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&StrategyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&StrategyDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Strategy mutation op: %q", m.Op())
	}
}

// SystemApiClient is a client for the SystemApi schema.
type SystemApiClient struct {
	config
}

// NewSystemApiClient returns a client for the SystemApi from the given config.
func NewSystemApiClient(c config) *SystemApiClient {
	return &SystemApiClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `systemapi.Hooks(f(g(h())))`.
func (c *SystemApiClient) Use(hooks ...Hook) {
	c.hooks.SystemApi = append(c.hooks.SystemApi, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `systemapi.Intercept(f(g(h())))`.
func (c *SystemApiClient) Intercept(interceptors ...Interceptor) {
	c.inters.SystemApi = append(c.inters.SystemApi, interceptors...)
}

// Create returns a builder for creating a SystemApi entity.
func (c *SystemApiClient) Create() *SystemApiCreate {
	mutation := newSystemApiMutation(c.config, OpCreate)
	return &SystemApiCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SystemApi entities.
func (c *SystemApiClient) CreateBulk(builders ...*SystemApiCreate) *SystemApiCreateBulk {
	return &SystemApiCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SystemApiClient) MapCreateBulk(slice any, setFunc func(*SystemApiCreate, int)) *SystemApiCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SystemApiCreateBulk{err: fmt.Errorf("calling to SystemApiClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SystemApiCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SystemApiCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SystemApi.
func (c *SystemApiClient) Update() *SystemApiUpdate {
	mutation := newSystemApiMutation(c.config, OpUpdate)
	return &SystemApiUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SystemApiClient) UpdateOne(sa *SystemApi) *SystemApiUpdateOne {
	mutation := newSystemApiMutation(c.config, OpUpdateOne, withSystemApi(sa))
	return &SystemApiUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SystemApiClient) UpdateOneID(id int) *SystemApiUpdateOne {
	mutation := newSystemApiMutation(c.config, OpUpdateOne, withSystemApiID(id))
	return &SystemApiUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SystemApi.
func (c *SystemApiClient) Delete() *SystemApiDelete {
	mutation := newSystemApiMutation(c.config, OpDelete)
	return &SystemApiDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SystemApiClient) DeleteOne(sa *SystemApi) *SystemApiDeleteOne {
	return c.DeleteOneID(sa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SystemApiClient) DeleteOneID(id int) *SystemApiDeleteOne {
	builder := c.Delete().Where(systemapi.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SystemApiDeleteOne{builder}
}

// Query returns a query builder for SystemApi.
func (c *SystemApiClient) Query() *SystemApiQuery {
	return &SystemApiQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSystemApi},
		inters: c.Interceptors(),
	}
}

// Get returns a SystemApi entity by its id.
func (c *SystemApiClient) Get(ctx context.Context, id int) (*SystemApi, error) {
	return c.Query().Where(systemapi.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SystemApiClient) GetX(ctx context.Context, id int) *SystemApi {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SystemApiClient) Hooks() []Hook {
	hooks := c.hooks.SystemApi
	return append(hooks[:len(hooks):len(hooks)], systemapi.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *SystemApiClient) Interceptors() []Interceptor {
	return c.inters.SystemApi
}

func (c *SystemApiClient) mutate(ctx context.Context, m *SystemApiMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SystemApiCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SystemApiUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SystemApiUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SystemApiDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SystemApi mutation op: %q", m.Op())
	}
}

// SystemConfigClient is a client for the SystemConfig schema.
type SystemConfigClient struct {
	config
}

// NewSystemConfigClient returns a client for the SystemConfig from the given config.
func NewSystemConfigClient(c config) *SystemConfigClient {
	return &SystemConfigClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `systemconfig.Hooks(f(g(h())))`.
func (c *SystemConfigClient) Use(hooks ...Hook) {
	c.hooks.SystemConfig = append(c.hooks.SystemConfig, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `systemconfig.Intercept(f(g(h())))`.
func (c *SystemConfigClient) Intercept(interceptors ...Interceptor) {
	c.inters.SystemConfig = append(c.inters.SystemConfig, interceptors...)
}

// Create returns a builder for creating a SystemConfig entity.
func (c *SystemConfigClient) Create() *SystemConfigCreate {
	mutation := newSystemConfigMutation(c.config, OpCreate)
	return &SystemConfigCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of SystemConfig entities.
func (c *SystemConfigClient) CreateBulk(builders ...*SystemConfigCreate) *SystemConfigCreateBulk {
	return &SystemConfigCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *SystemConfigClient) MapCreateBulk(slice any, setFunc func(*SystemConfigCreate, int)) *SystemConfigCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &SystemConfigCreateBulk{err: fmt.Errorf("calling to SystemConfigClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*SystemConfigCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &SystemConfigCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for SystemConfig.
func (c *SystemConfigClient) Update() *SystemConfigUpdate {
	mutation := newSystemConfigMutation(c.config, OpUpdate)
	return &SystemConfigUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *SystemConfigClient) UpdateOne(sc *SystemConfig) *SystemConfigUpdateOne {
	mutation := newSystemConfigMutation(c.config, OpUpdateOne, withSystemConfig(sc))
	return &SystemConfigUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *SystemConfigClient) UpdateOneID(id int) *SystemConfigUpdateOne {
	mutation := newSystemConfigMutation(c.config, OpUpdateOne, withSystemConfigID(id))
	return &SystemConfigUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for SystemConfig.
func (c *SystemConfigClient) Delete() *SystemConfigDelete {
	mutation := newSystemConfigMutation(c.config, OpDelete)
	return &SystemConfigDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *SystemConfigClient) DeleteOne(sc *SystemConfig) *SystemConfigDeleteOne {
	return c.DeleteOneID(sc.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *SystemConfigClient) DeleteOneID(id int) *SystemConfigDeleteOne {
	builder := c.Delete().Where(systemconfig.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &SystemConfigDeleteOne{builder}
}

// Query returns a query builder for SystemConfig.
func (c *SystemConfigClient) Query() *SystemConfigQuery {
	return &SystemConfigQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeSystemConfig},
		inters: c.Interceptors(),
	}
}

// Get returns a SystemConfig entity by its id.
func (c *SystemConfigClient) Get(ctx context.Context, id int) (*SystemConfig, error) {
	return c.Query().Where(systemconfig.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *SystemConfigClient) GetX(ctx context.Context, id int) *SystemConfig {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *SystemConfigClient) Hooks() []Hook {
	hooks := c.hooks.SystemConfig
	return append(hooks[:len(hooks):len(hooks)], systemconfig.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *SystemConfigClient) Interceptors() []Interceptor {
	return c.inters.SystemConfig
}

func (c *SystemConfigClient) mutate(ctx context.Context, m *SystemConfigMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&SystemConfigCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&SystemConfigUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&SystemConfigUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&SystemConfigDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown SystemConfig mutation op: %q", m.Op())
	}
}

// TenantClient is a client for the Tenant schema.
type TenantClient struct {
	config
}

// NewTenantClient returns a client for the Tenant from the given config.
func NewTenantClient(c config) *TenantClient {
	return &TenantClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tenant.Hooks(f(g(h())))`.
func (c *TenantClient) Use(hooks ...Hook) {
	c.hooks.Tenant = append(c.hooks.Tenant, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tenant.Intercept(f(g(h())))`.
func (c *TenantClient) Intercept(interceptors ...Interceptor) {
	c.inters.Tenant = append(c.inters.Tenant, interceptors...)
}

// Create returns a builder for creating a Tenant entity.
func (c *TenantClient) Create() *TenantCreate {
	mutation := newTenantMutation(c.config, OpCreate)
	return &TenantCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Tenant entities.
func (c *TenantClient) CreateBulk(builders ...*TenantCreate) *TenantCreateBulk {
	return &TenantCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TenantClient) MapCreateBulk(slice any, setFunc func(*TenantCreate, int)) *TenantCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TenantCreateBulk{err: fmt.Errorf("calling to TenantClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TenantCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TenantCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Tenant.
func (c *TenantClient) Update() *TenantUpdate {
	mutation := newTenantMutation(c.config, OpUpdate)
	return &TenantUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TenantClient) UpdateOne(t *Tenant) *TenantUpdateOne {
	mutation := newTenantMutation(c.config, OpUpdateOne, withTenant(t))
	return &TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TenantClient) UpdateOneID(id int) *TenantUpdateOne {
	mutation := newTenantMutation(c.config, OpUpdateOne, withTenantID(id))
	return &TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Tenant.
func (c *TenantClient) Delete() *TenantDelete {
	mutation := newTenantMutation(c.config, OpDelete)
	return &TenantDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TenantClient) DeleteOne(t *Tenant) *TenantDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TenantClient) DeleteOneID(id int) *TenantDeleteOne {
	builder := c.Delete().Where(tenant.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TenantDeleteOne{builder}
}

// Query returns a query builder for Tenant.
func (c *TenantClient) Query() *TenantQuery {
	return &TenantQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTenant},
		inters: c.Interceptors(),
	}
}

// Get returns a Tenant entity by its id.
func (c *TenantClient) Get(ctx context.Context, id int) (*Tenant, error) {
	return c.Query().Where(tenant.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TenantClient) GetX(ctx context.Context, id int) *Tenant {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TenantClient) Hooks() []Hook {
	hooks := c.hooks.Tenant
	return append(hooks[:len(hooks):len(hooks)], tenant.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TenantClient) Interceptors() []Interceptor {
	return c.inters.Tenant
}

func (c *TenantClient) mutate(ctx context.Context, m *TenantMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TenantCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TenantUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TenantDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Tenant mutation op: %q", m.Op())
	}
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id int) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id int) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id int) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id int) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryGroups queries the groups edge of a User.
func (c *UserClient) QueryGroups(u *User) *GroupQuery {
	query := (&GroupClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(group.Table, group.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, user.GroupsTable, user.GroupsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	return c.hooks.User
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	return c.inters.User
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown User mutation op: %q", m.Op())
	}
}

// UserOperationLogClient is a client for the UserOperationLog schema.
type UserOperationLogClient struct {
	config
}

// NewUserOperationLogClient returns a client for the UserOperationLog from the given config.
func NewUserOperationLogClient(c config) *UserOperationLogClient {
	return &UserOperationLogClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `useroperationlog.Hooks(f(g(h())))`.
func (c *UserOperationLogClient) Use(hooks ...Hook) {
	c.hooks.UserOperationLog = append(c.hooks.UserOperationLog, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `useroperationlog.Intercept(f(g(h())))`.
func (c *UserOperationLogClient) Intercept(interceptors ...Interceptor) {
	c.inters.UserOperationLog = append(c.inters.UserOperationLog, interceptors...)
}

// Create returns a builder for creating a UserOperationLog entity.
func (c *UserOperationLogClient) Create() *UserOperationLogCreate {
	mutation := newUserOperationLogMutation(c.config, OpCreate)
	return &UserOperationLogCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of UserOperationLog entities.
func (c *UserOperationLogClient) CreateBulk(builders ...*UserOperationLogCreate) *UserOperationLogCreateBulk {
	return &UserOperationLogCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserOperationLogClient) MapCreateBulk(slice any, setFunc func(*UserOperationLogCreate, int)) *UserOperationLogCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserOperationLogCreateBulk{err: fmt.Errorf("calling to UserOperationLogClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserOperationLogCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserOperationLogCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for UserOperationLog.
func (c *UserOperationLogClient) Update() *UserOperationLogUpdate {
	mutation := newUserOperationLogMutation(c.config, OpUpdate)
	return &UserOperationLogUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserOperationLogClient) UpdateOne(uol *UserOperationLog) *UserOperationLogUpdateOne {
	mutation := newUserOperationLogMutation(c.config, OpUpdateOne, withUserOperationLog(uol))
	return &UserOperationLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserOperationLogClient) UpdateOneID(id int) *UserOperationLogUpdateOne {
	mutation := newUserOperationLogMutation(c.config, OpUpdateOne, withUserOperationLogID(id))
	return &UserOperationLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for UserOperationLog.
func (c *UserOperationLogClient) Delete() *UserOperationLogDelete {
	mutation := newUserOperationLogMutation(c.config, OpDelete)
	return &UserOperationLogDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserOperationLogClient) DeleteOne(uol *UserOperationLog) *UserOperationLogDeleteOne {
	return c.DeleteOneID(uol.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserOperationLogClient) DeleteOneID(id int) *UserOperationLogDeleteOne {
	builder := c.Delete().Where(useroperationlog.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserOperationLogDeleteOne{builder}
}

// Query returns a query builder for UserOperationLog.
func (c *UserOperationLogClient) Query() *UserOperationLogQuery {
	return &UserOperationLogQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUserOperationLog},
		inters: c.Interceptors(),
	}
}

// Get returns a UserOperationLog entity by its id.
func (c *UserOperationLogClient) Get(ctx context.Context, id int) (*UserOperationLog, error) {
	return c.Query().Where(useroperationlog.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserOperationLogClient) GetX(ctx context.Context, id int) *UserOperationLog {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *UserOperationLogClient) Hooks() []Hook {
	return c.hooks.UserOperationLog
}

// Interceptors returns the client interceptors.
func (c *UserOperationLogClient) Interceptors() []Interceptor {
	return c.inters.UserOperationLog
}

func (c *UserOperationLogClient) mutate(ctx context.Context, m *UserOperationLogMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserOperationLogCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserOperationLogUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserOperationLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserOperationLogDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown UserOperationLog mutation op: %q", m.Op())
	}
}

// WofangClient is a client for the Wofang schema.
type WofangClient struct {
	config
}

// NewWofangClient returns a client for the Wofang from the given config.
func NewWofangClient(c config) *WofangClient {
	return &WofangClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `wofang.Hooks(f(g(h())))`.
func (c *WofangClient) Use(hooks ...Hook) {
	c.hooks.Wofang = append(c.hooks.Wofang, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `wofang.Intercept(f(g(h())))`.
func (c *WofangClient) Intercept(interceptors ...Interceptor) {
	c.inters.Wofang = append(c.inters.Wofang, interceptors...)
}

// Create returns a builder for creating a Wofang entity.
func (c *WofangClient) Create() *WofangCreate {
	mutation := newWofangMutation(c.config, OpCreate)
	return &WofangCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Wofang entities.
func (c *WofangClient) CreateBulk(builders ...*WofangCreate) *WofangCreateBulk {
	return &WofangCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *WofangClient) MapCreateBulk(slice any, setFunc func(*WofangCreate, int)) *WofangCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &WofangCreateBulk{err: fmt.Errorf("calling to WofangClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*WofangCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &WofangCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Wofang.
func (c *WofangClient) Update() *WofangUpdate {
	mutation := newWofangMutation(c.config, OpUpdate)
	return &WofangUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *WofangClient) UpdateOne(w *Wofang) *WofangUpdateOne {
	mutation := newWofangMutation(c.config, OpUpdateOne, withWofang(w))
	return &WofangUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *WofangClient) UpdateOneID(id int) *WofangUpdateOne {
	mutation := newWofangMutation(c.config, OpUpdateOne, withWofangID(id))
	return &WofangUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Wofang.
func (c *WofangClient) Delete() *WofangDelete {
	mutation := newWofangMutation(c.config, OpDelete)
	return &WofangDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *WofangClient) DeleteOne(w *Wofang) *WofangDeleteOne {
	return c.DeleteOneID(w.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *WofangClient) DeleteOneID(id int) *WofangDeleteOne {
	builder := c.Delete().Where(wofang.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &WofangDeleteOne{builder}
}

// Query returns a query builder for Wofang.
func (c *WofangClient) Query() *WofangQuery {
	return &WofangQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeWofang},
		inters: c.Interceptors(),
	}
}

// Get returns a Wofang entity by its id.
func (c *WofangClient) Get(ctx context.Context, id int) (*Wofang, error) {
	return c.Query().Where(wofang.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *WofangClient) GetX(ctx context.Context, id int) *Wofang {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Wofang.
func (c *WofangClient) QueryTenant(w *Wofang) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := w.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(wofang.Table, wofang.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, wofang.TenantTable, wofang.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(w.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a Wofang.
func (c *WofangClient) QueryUser(w *Wofang) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := w.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(wofang.Table, wofang.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, wofang.UserTable, wofang.UserColumn),
		)
		fromV = sqlgraph.Neighbors(w.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QuerySpectrumAlerts queries the spectrum_alerts edge of a Wofang.
func (c *WofangClient) QuerySpectrumAlerts(w *Wofang) *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := w.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(wofang.Table, wofang.FieldID, id),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, wofang.SpectrumAlertsTable, wofang.SpectrumAlertsColumn),
		)
		fromV = sqlgraph.Neighbors(w.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMatrixSpectrumAlerts queries the matrix_spectrum_alerts edge of a Wofang.
func (c *WofangClient) QueryMatrixSpectrumAlerts(w *Wofang) *MatrixSpectrumAlertQuery {
	query := (&MatrixSpectrumAlertClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := w.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(wofang.Table, wofang.FieldID, id),
			sqlgraph.To(matrixspectrumalert.Table, matrixspectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, wofang.MatrixSpectrumAlertsTable, wofang.MatrixSpectrumAlertsColumn),
		)
		fromV = sqlgraph.Neighbors(w.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *WofangClient) Hooks() []Hook {
	hooks := c.hooks.Wofang
	return append(hooks[:len(hooks):len(hooks)], wofang.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *WofangClient) Interceptors() []Interceptor {
	return c.inters.Wofang
}

func (c *WofangClient) mutate(ctx context.Context, m *WofangMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&WofangCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&WofangUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&WofangUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&WofangDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Wofang mutation op: %q", m.Op())
	}
}

// WofangAlertClient is a client for the WofangAlert schema.
type WofangAlertClient struct {
	config
}

// NewWofangAlertClient returns a client for the WofangAlert from the given config.
func NewWofangAlertClient(c config) *WofangAlertClient {
	return &WofangAlertClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `wofangalert.Hooks(f(g(h())))`.
func (c *WofangAlertClient) Use(hooks ...Hook) {
	c.hooks.WofangAlert = append(c.hooks.WofangAlert, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `wofangalert.Intercept(f(g(h())))`.
func (c *WofangAlertClient) Intercept(interceptors ...Interceptor) {
	c.inters.WofangAlert = append(c.inters.WofangAlert, interceptors...)
}

// Create returns a builder for creating a WofangAlert entity.
func (c *WofangAlertClient) Create() *WofangAlertCreate {
	mutation := newWofangAlertMutation(c.config, OpCreate)
	return &WofangAlertCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of WofangAlert entities.
func (c *WofangAlertClient) CreateBulk(builders ...*WofangAlertCreate) *WofangAlertCreateBulk {
	return &WofangAlertCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *WofangAlertClient) MapCreateBulk(slice any, setFunc func(*WofangAlertCreate, int)) *WofangAlertCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &WofangAlertCreateBulk{err: fmt.Errorf("calling to WofangAlertClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*WofangAlertCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &WofangAlertCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for WofangAlert.
func (c *WofangAlertClient) Update() *WofangAlertUpdate {
	mutation := newWofangAlertMutation(c.config, OpUpdate)
	return &WofangAlertUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *WofangAlertClient) UpdateOne(wa *WofangAlert) *WofangAlertUpdateOne {
	mutation := newWofangAlertMutation(c.config, OpUpdateOne, withWofangAlert(wa))
	return &WofangAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *WofangAlertClient) UpdateOneID(id int) *WofangAlertUpdateOne {
	mutation := newWofangAlertMutation(c.config, OpUpdateOne, withWofangAlertID(id))
	return &WofangAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for WofangAlert.
func (c *WofangAlertClient) Delete() *WofangAlertDelete {
	mutation := newWofangAlertMutation(c.config, OpDelete)
	return &WofangAlertDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *WofangAlertClient) DeleteOne(wa *WofangAlert) *WofangAlertDeleteOne {
	return c.DeleteOneID(wa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *WofangAlertClient) DeleteOneID(id int) *WofangAlertDeleteOne {
	builder := c.Delete().Where(wofangalert.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &WofangAlertDeleteOne{builder}
}

// Query returns a query builder for WofangAlert.
func (c *WofangAlertClient) Query() *WofangAlertQuery {
	return &WofangAlertQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeWofangAlert},
		inters: c.Interceptors(),
	}
}

// Get returns a WofangAlert entity by its id.
func (c *WofangAlertClient) Get(ctx context.Context, id int) (*WofangAlert, error) {
	return c.Query().Where(wofangalert.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *WofangAlertClient) GetX(ctx context.Context, id int) *WofangAlert {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a WofangAlert.
func (c *WofangAlertClient) QueryTenant(wa *WofangAlert) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := wa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(wofangalert.Table, wofangalert.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, wofangalert.TenantTable, wofangalert.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(wa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *WofangAlertClient) Hooks() []Hook {
	hooks := c.hooks.WofangAlert
	return append(hooks[:len(hooks):len(hooks)], wofangalert.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *WofangAlertClient) Interceptors() []Interceptor {
	return c.inters.WofangAlert
}

func (c *WofangAlertClient) mutate(ctx context.Context, m *WofangAlertMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&WofangAlertCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&WofangAlertUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&WofangAlertUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&WofangAlertDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown WofangAlert mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		CasbinRule, CleanData, CloudAlert, CloudAttackData, CloudFlowData, DataSync,
		Group, MatrixSpectrumAlert, MatrixSpectrumData, MatrixStrategy, Notify,
		ProtectGroup, SkylineDos, SocGroupTicket, SpectrumAlert, SpectrumData,
		Strategy, SystemApi, SystemConfig, Tenant, User, UserOperationLog, Wofang,
		WofangAlert []ent.Hook
	}
	inters struct {
		CasbinRule, CleanData, CloudAlert, CloudAttackData, CloudFlowData, DataSync,
		Group, MatrixSpectrumAlert, MatrixSpectrumData, MatrixStrategy, Notify,
		ProtectGroup, SkylineDos, SocGroupTicket, SpectrumAlert, SpectrumData,
		Strategy, SystemApi, SystemConfig, Tenant, User, UserOperationLog, Wofang,
		WofangAlert []ent.Interceptor
	}
)
