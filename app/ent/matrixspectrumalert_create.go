// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"
	"meta/app/entity/netease"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumAlertCreate is the builder for creating a MatrixSpectrumAlert entity.
type MatrixSpectrumAlertCreate struct {
	config
	mutation *MatrixSpectrumAlertMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (msac *MatrixSpectrumAlertCreate) SetTenantID(i int) *MatrixSpectrumAlertCreate {
	msac.mutation.SetTenantID(i)
	return msac
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableTenantID(i *int) *MatrixSpectrumAlertCreate {
	if i != nil {
		msac.SetTenantID(*i)
	}
	return msac
}

// SetCreatedAt sets the "created_at" field.
func (msac *MatrixSpectrumAlertCreate) SetCreatedAt(t time.Time) *MatrixSpectrumAlertCreate {
	msac.mutation.SetCreatedAt(t)
	return msac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableCreatedAt(t *time.Time) *MatrixSpectrumAlertCreate {
	if t != nil {
		msac.SetCreatedAt(*t)
	}
	return msac
}

// SetUpdatedAt sets the "updated_at" field.
func (msac *MatrixSpectrumAlertCreate) SetUpdatedAt(t time.Time) *MatrixSpectrumAlertCreate {
	msac.mutation.SetUpdatedAt(t)
	return msac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableUpdatedAt(t *time.Time) *MatrixSpectrumAlertCreate {
	if t != nil {
		msac.SetUpdatedAt(*t)
	}
	return msac
}

// SetRemark sets the "remark" field.
func (msac *MatrixSpectrumAlertCreate) SetRemark(s string) *MatrixSpectrumAlertCreate {
	msac.mutation.SetRemark(s)
	return msac
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableRemark(s *string) *MatrixSpectrumAlertCreate {
	if s != nil {
		msac.SetRemark(*s)
	}
	return msac
}

// SetWofangID sets the "wofang_id" field.
func (msac *MatrixSpectrumAlertCreate) SetWofangID(i int) *MatrixSpectrumAlertCreate {
	msac.mutation.SetWofangID(i)
	return msac
}

// SetNillableWofangID sets the "wofang_id" field if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableWofangID(i *int) *MatrixSpectrumAlertCreate {
	if i != nil {
		msac.SetWofangID(*i)
	}
	return msac
}

// SetMatrixStrategyID sets the "matrix_strategy_id" field.
func (msac *MatrixSpectrumAlertCreate) SetMatrixStrategyID(i int) *MatrixSpectrumAlertCreate {
	msac.mutation.SetMatrixStrategyID(i)
	return msac
}

// SetNillableMatrixStrategyID sets the "matrix_strategy_id" field if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableMatrixStrategyID(i *int) *MatrixSpectrumAlertCreate {
	if i != nil {
		msac.SetMatrixStrategyID(*i)
	}
	return msac
}

// SetIPList sets the "ip_list" field.
func (msac *MatrixSpectrumAlertCreate) SetIPList(s *[]string) *MatrixSpectrumAlertCreate {
	msac.mutation.SetIPList(s)
	return msac
}

// SetRegion sets the "region" field.
func (msac *MatrixSpectrumAlertCreate) SetRegion(s string) *MatrixSpectrumAlertCreate {
	msac.mutation.SetRegion(s)
	return msac
}

// SetNetType sets the "net_type" field.
func (msac *MatrixSpectrumAlertCreate) SetNetType(s string) *MatrixSpectrumAlertCreate {
	msac.mutation.SetNetType(s)
	return msac
}

// SetIsp sets the "isp" field.
func (msac *MatrixSpectrumAlertCreate) SetIsp(s string) *MatrixSpectrumAlertCreate {
	msac.mutation.SetIsp(s)
	return msac
}

// SetStartTime sets the "start_time" field.
func (msac *MatrixSpectrumAlertCreate) SetStartTime(t time.Time) *MatrixSpectrumAlertCreate {
	msac.mutation.SetStartTime(t)
	return msac
}

// SetEndTime sets the "end_time" field.
func (msac *MatrixSpectrumAlertCreate) SetEndTime(t time.Time) *MatrixSpectrumAlertCreate {
	msac.mutation.SetEndTime(t)
	return msac
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableEndTime(t *time.Time) *MatrixSpectrumAlertCreate {
	if t != nil {
		msac.SetEndTime(*t)
	}
	return msac
}

// SetAttackType sets the "attack_type" field.
func (msac *MatrixSpectrumAlertCreate) SetAttackType(s string) *MatrixSpectrumAlertCreate {
	msac.mutation.SetAttackType(s)
	return msac
}

// SetBps sets the "bps" field.
func (msac *MatrixSpectrumAlertCreate) SetBps(i int64) *MatrixSpectrumAlertCreate {
	msac.mutation.SetBps(i)
	return msac
}

// SetAttackInfo sets the "attack_info" field.
func (msac *MatrixSpectrumAlertCreate) SetAttackInfo(nai netease.MatrixAttackInfo) *MatrixSpectrumAlertCreate {
	msac.mutation.SetAttackInfo(nai)
	return msac
}

// SetNillableAttackInfo sets the "attack_info" field if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableAttackInfo(nai *netease.MatrixAttackInfo) *MatrixSpectrumAlertCreate {
	if nai != nil {
		msac.SetAttackInfo(*nai)
	}
	return msac
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (msac *MatrixSpectrumAlertCreate) SetTenant(t *Tenant) *MatrixSpectrumAlertCreate {
	return msac.SetTenantID(t.ID)
}

// AddMatrixSpectrumDataIDs adds the "matrix_spectrum_datas" edge to the MatrixSpectrumData entity by IDs.
func (msac *MatrixSpectrumAlertCreate) AddMatrixSpectrumDataIDs(ids ...int) *MatrixSpectrumAlertCreate {
	msac.mutation.AddMatrixSpectrumDataIDs(ids...)
	return msac
}

// AddMatrixSpectrumDatas adds the "matrix_spectrum_datas" edges to the MatrixSpectrumData entity.
func (msac *MatrixSpectrumAlertCreate) AddMatrixSpectrumDatas(m ...*MatrixSpectrumData) *MatrixSpectrumAlertCreate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msac.AddMatrixSpectrumDataIDs(ids...)
}

// SetMatrixStrategy sets the "matrix_strategy" edge to the MatrixStrategy entity.
func (msac *MatrixSpectrumAlertCreate) SetMatrixStrategy(m *MatrixStrategy) *MatrixSpectrumAlertCreate {
	return msac.SetMatrixStrategyID(m.ID)
}

// SetWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID.
func (msac *MatrixSpectrumAlertCreate) SetWofangTicketID(id int) *MatrixSpectrumAlertCreate {
	msac.mutation.SetWofangTicketID(id)
	return msac
}

// SetNillableWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID if the given value is not nil.
func (msac *MatrixSpectrumAlertCreate) SetNillableWofangTicketID(id *int) *MatrixSpectrumAlertCreate {
	if id != nil {
		msac = msac.SetWofangTicketID(*id)
	}
	return msac
}

// SetWofangTicket sets the "wofang_ticket" edge to the Wofang entity.
func (msac *MatrixSpectrumAlertCreate) SetWofangTicket(w *Wofang) *MatrixSpectrumAlertCreate {
	return msac.SetWofangTicketID(w.ID)
}

// Mutation returns the MatrixSpectrumAlertMutation object of the builder.
func (msac *MatrixSpectrumAlertCreate) Mutation() *MatrixSpectrumAlertMutation {
	return msac.mutation
}

// Save creates the MatrixSpectrumAlert in the database.
func (msac *MatrixSpectrumAlertCreate) Save(ctx context.Context) (*MatrixSpectrumAlert, error) {
	if err := msac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, msac.sqlSave, msac.mutation, msac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (msac *MatrixSpectrumAlertCreate) SaveX(ctx context.Context) *MatrixSpectrumAlert {
	v, err := msac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (msac *MatrixSpectrumAlertCreate) Exec(ctx context.Context) error {
	_, err := msac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msac *MatrixSpectrumAlertCreate) ExecX(ctx context.Context) {
	if err := msac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msac *MatrixSpectrumAlertCreate) defaults() error {
	if _, ok := msac.mutation.CreatedAt(); !ok {
		if matrixspectrumalert.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixspectrumalert.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := matrixspectrumalert.DefaultCreatedAt()
		msac.mutation.SetCreatedAt(v)
	}
	if _, ok := msac.mutation.UpdatedAt(); !ok {
		if matrixspectrumalert.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixspectrumalert.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixspectrumalert.DefaultUpdatedAt()
		msac.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (msac *MatrixSpectrumAlertCreate) check() error {
	if _, ok := msac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "MatrixSpectrumAlert.created_at"`)}
	}
	if _, ok := msac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "MatrixSpectrumAlert.updated_at"`)}
	}
	if v, ok := msac.mutation.Remark(); ok {
		if err := matrixspectrumalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "MatrixSpectrumAlert.remark": %w`, err)}
		}
	}
	if _, ok := msac.mutation.Region(); !ok {
		return &ValidationError{Name: "region", err: errors.New(`ent: missing required field "MatrixSpectrumAlert.region"`)}
	}
	if _, ok := msac.mutation.NetType(); !ok {
		return &ValidationError{Name: "net_type", err: errors.New(`ent: missing required field "MatrixSpectrumAlert.net_type"`)}
	}
	if _, ok := msac.mutation.Isp(); !ok {
		return &ValidationError{Name: "isp", err: errors.New(`ent: missing required field "MatrixSpectrumAlert.isp"`)}
	}
	if _, ok := msac.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "MatrixSpectrumAlert.start_time"`)}
	}
	if _, ok := msac.mutation.AttackType(); !ok {
		return &ValidationError{Name: "attack_type", err: errors.New(`ent: missing required field "MatrixSpectrumAlert.attack_type"`)}
	}
	if _, ok := msac.mutation.Bps(); !ok {
		return &ValidationError{Name: "bps", err: errors.New(`ent: missing required field "MatrixSpectrumAlert.bps"`)}
	}
	return nil
}

func (msac *MatrixSpectrumAlertCreate) sqlSave(ctx context.Context) (*MatrixSpectrumAlert, error) {
	if err := msac.check(); err != nil {
		return nil, err
	}
	_node, _spec := msac.createSpec()
	if err := sqlgraph.CreateNode(ctx, msac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	msac.mutation.id = &_node.ID
	msac.mutation.done = true
	return _node, nil
}

func (msac *MatrixSpectrumAlertCreate) createSpec() (*MatrixSpectrumAlert, *sqlgraph.CreateSpec) {
	var (
		_node = &MatrixSpectrumAlert{config: msac.config}
		_spec = sqlgraph.NewCreateSpec(matrixspectrumalert.Table, sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt))
	)
	if value, ok := msac.mutation.CreatedAt(); ok {
		_spec.SetField(matrixspectrumalert.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := msac.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixspectrumalert.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := msac.mutation.Remark(); ok {
		_spec.SetField(matrixspectrumalert.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := msac.mutation.IPList(); ok {
		_spec.SetField(matrixspectrumalert.FieldIPList, field.TypeJSON, value)
		_node.IPList = value
	}
	if value, ok := msac.mutation.Region(); ok {
		_spec.SetField(matrixspectrumalert.FieldRegion, field.TypeString, value)
		_node.Region = value
	}
	if value, ok := msac.mutation.NetType(); ok {
		_spec.SetField(matrixspectrumalert.FieldNetType, field.TypeString, value)
		_node.NetType = value
	}
	if value, ok := msac.mutation.Isp(); ok {
		_spec.SetField(matrixspectrumalert.FieldIsp, field.TypeString, value)
		_node.Isp = value
	}
	if value, ok := msac.mutation.StartTime(); ok {
		_spec.SetField(matrixspectrumalert.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := msac.mutation.EndTime(); ok {
		_spec.SetField(matrixspectrumalert.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if value, ok := msac.mutation.AttackType(); ok {
		_spec.SetField(matrixspectrumalert.FieldAttackType, field.TypeString, value)
		_node.AttackType = value
	}
	if value, ok := msac.mutation.Bps(); ok {
		_spec.SetField(matrixspectrumalert.FieldBps, field.TypeInt64, value)
		_node.Bps = value
	}
	if value, ok := msac.mutation.AttackInfo(); ok {
		_spec.SetField(matrixspectrumalert.FieldAttackInfo, field.TypeJSON, value)
		_node.AttackInfo = value
	}
	if nodes := msac.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumalert.TenantTable,
			Columns: []string{matrixspectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := msac.mutation.MatrixSpectrumDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixspectrumalert.MatrixSpectrumDatasTable,
			Columns: []string{matrixspectrumalert.MatrixSpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := msac.mutation.MatrixStrategyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.MatrixStrategyTable,
			Columns: []string{matrixspectrumalert.MatrixStrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.MatrixStrategyID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := msac.mutation.WofangTicketIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.WofangTicketTable,
			Columns: []string{matrixspectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.WofangID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// MatrixSpectrumAlertCreateBulk is the builder for creating many MatrixSpectrumAlert entities in bulk.
type MatrixSpectrumAlertCreateBulk struct {
	config
	err      error
	builders []*MatrixSpectrumAlertCreate
}

// Save creates the MatrixSpectrumAlert entities in the database.
func (msacb *MatrixSpectrumAlertCreateBulk) Save(ctx context.Context) ([]*MatrixSpectrumAlert, error) {
	if msacb.err != nil {
		return nil, msacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(msacb.builders))
	nodes := make([]*MatrixSpectrumAlert, len(msacb.builders))
	mutators := make([]Mutator, len(msacb.builders))
	for i := range msacb.builders {
		func(i int, root context.Context) {
			builder := msacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*MatrixSpectrumAlertMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, msacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, msacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, msacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (msacb *MatrixSpectrumAlertCreateBulk) SaveX(ctx context.Context) []*MatrixSpectrumAlert {
	v, err := msacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (msacb *MatrixSpectrumAlertCreateBulk) Exec(ctx context.Context) error {
	_, err := msacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msacb *MatrixSpectrumAlertCreateBulk) ExecX(ctx context.Context) {
	if err := msacb.Exec(ctx); err != nil {
		panic(err)
	}
}
