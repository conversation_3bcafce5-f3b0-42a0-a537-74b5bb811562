// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/skylinedos"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SkylineDosDelete is the builder for deleting a SkylineDos entity.
type SkylineDosDelete struct {
	config
	hooks    []Hook
	mutation *SkylineDosMutation
}

// Where appends a list predicates to the SkylineDosDelete builder.
func (sdd *SkylineDosDelete) Where(ps ...predicate.SkylineDos) *SkylineDosDelete {
	sdd.mutation.Where(ps...)
	return sdd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (sdd *SkylineDosDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, sdd.sqlExec, sdd.mutation, sdd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (sdd *SkylineDosDelete) ExecX(ctx context.Context) int {
	n, err := sdd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (sdd *SkylineDosDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(skylinedos.Table, sqlgraph.NewFieldSpec(skylinedos.FieldID, field.TypeInt))
	if ps := sdd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, sdd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	sdd.mutation.done = true
	return affected, err
}

// SkylineDosDeleteOne is the builder for deleting a single SkylineDos entity.
type SkylineDosDeleteOne struct {
	sdd *SkylineDosDelete
}

// Where appends a list predicates to the SkylineDosDelete builder.
func (sddo *SkylineDosDeleteOne) Where(ps ...predicate.SkylineDos) *SkylineDosDeleteOne {
	sddo.sdd.mutation.Where(ps...)
	return sddo
}

// Exec executes the deletion query.
func (sddo *SkylineDosDeleteOne) Exec(ctx context.Context) error {
	n, err := sddo.sdd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{skylinedos.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (sddo *SkylineDosDeleteOne) ExecX(ctx context.Context) {
	if err := sddo.Exec(ctx); err != nil {
		panic(err)
	}
}
