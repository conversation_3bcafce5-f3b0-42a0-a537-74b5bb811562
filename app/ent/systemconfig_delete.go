// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/systemconfig"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SystemConfigDelete is the builder for deleting a SystemConfig entity.
type SystemConfigDelete struct {
	config
	hooks    []Hook
	mutation *SystemConfigMutation
}

// Where appends a list predicates to the SystemConfigDelete builder.
func (scd *SystemConfigDelete) Where(ps ...predicate.SystemConfig) *SystemConfigDelete {
	scd.mutation.Where(ps...)
	return scd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (scd *SystemConfigDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, scd.sqlExec, scd.mutation, scd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (scd *SystemConfigDelete) ExecX(ctx context.Context) int {
	n, err := scd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (scd *SystemConfigDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(systemconfig.Table, sqlgraph.NewFieldSpec(systemconfig.FieldID, field.TypeInt))
	if ps := scd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, scd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	scd.mutation.done = true
	return affected, err
}

// SystemConfigDeleteOne is the builder for deleting a single SystemConfig entity.
type SystemConfigDeleteOne struct {
	scd *SystemConfigDelete
}

// Where appends a list predicates to the SystemConfigDelete builder.
func (scdo *SystemConfigDeleteOne) Where(ps ...predicate.SystemConfig) *SystemConfigDeleteOne {
	scdo.scd.mutation.Where(ps...)
	return scdo
}

// Exec executes the deletion query.
func (scdo *SystemConfigDeleteOne) Exec(ctx context.Context) error {
	n, err := scdo.scd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{systemconfig.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (scdo *SystemConfigDeleteOne) ExecX(ctx context.Context) {
	if err := scdo.Exec(ctx); err != nil {
		panic(err)
	}
}
