// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/cloudalert"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// CloudAlert is the model entity for the CloudAlert schema.
type CloudAlert struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 来源IP
	SrcIP string `json:"src_ip,omitempty" query:"src_ip,omitempty"`
	// 来源端口
	SrcPort int `json:"src_port,omitempty" query:"src_port,omitempty"`
	// 被攻击IP
	DstIP string `json:"dst_ip,omitempty" query:"dst_ip,omitempty"`
	// 被攻击端口
	DstPort int `json:"dst_port,omitempty" query:"dst_port,omitempty"`
	// 清洗模式
	DefenceMode int `json:"defence_mode,omitempty"`
	// 流匹配模式
	FlowMode int `json:"flow_mode,omitempty"`
	// TCP报文中的ACK值
	TCPAckNum string `json:"tcp_ack_num,omitempty"`
	// TCP报文中的SEQ值
	TCPSeqNum string `json:"tcp_seq_num,omitempty"`
	// 4层协议：6tcp，17udp
	Protocol int `json:"protocol,omitempty"`
	// 防护水平
	DefenceLevel int `json:"defence_level,omitempty"`
	// 最大攻击PPS
	MaxPps int64 `json:"max_pps,omitempty" query:"max_pps,omitempty"`
	// 从攻击开始到攻击结束，最大的pps
	MaxAttackPps int64 `json:"max_attack_pps,omitempty" query:"max_attack_pps,omitempty"`
	// 超出阈值的报文总数
	OverlimitPktCount int `json:"overlimit_pkt_count,omitempty" query:"overlimit_pkt_count,omitempty"`
	// 攻击开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 攻击结束时间
	EndTime time.Time `json:"end_time,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the CloudAlertQuery when eager-loading is set.
	Edges        CloudAlertEdges `json:"edges"`
	selectValues sql.SelectValues
}

// CloudAlertEdges holds the relations/edges for other nodes in the graph.
type CloudAlertEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// CloudflowDatas holds the value of the cloudflow_datas edge.
	CloudflowDatas []*CloudFlowData `json:"cloudflow_datas,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CloudAlertEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// CloudflowDatasOrErr returns the CloudflowDatas value or an error if the edge
// was not loaded in eager-loading.
func (e CloudAlertEdges) CloudflowDatasOrErr() ([]*CloudFlowData, error) {
	if e.loadedTypes[1] {
		return e.CloudflowDatas, nil
	}
	return nil, &NotLoadedError{edge: "cloudflow_datas"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CloudAlert) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case cloudalert.FieldID, cloudalert.FieldTenantID, cloudalert.FieldSrcPort, cloudalert.FieldDstPort, cloudalert.FieldDefenceMode, cloudalert.FieldFlowMode, cloudalert.FieldProtocol, cloudalert.FieldDefenceLevel, cloudalert.FieldMaxPps, cloudalert.FieldMaxAttackPps, cloudalert.FieldOverlimitPktCount:
			values[i] = new(sql.NullInt64)
		case cloudalert.FieldRemark, cloudalert.FieldSrcIP, cloudalert.FieldDstIP, cloudalert.FieldTCPAckNum, cloudalert.FieldTCPSeqNum:
			values[i] = new(sql.NullString)
		case cloudalert.FieldCreatedAt, cloudalert.FieldUpdatedAt, cloudalert.FieldStartTime, cloudalert.FieldEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CloudAlert fields.
func (ca *CloudAlert) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case cloudalert.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			ca.ID = int(value.Int64)
		case cloudalert.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				ca.TenantID = new(int)
				*ca.TenantID = int(value.Int64)
			}
		case cloudalert.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ca.CreatedAt = value.Time
			}
		case cloudalert.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ca.UpdatedAt = value.Time
			}
		case cloudalert.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				ca.Remark = new(string)
				*ca.Remark = value.String
			}
		case cloudalert.FieldSrcIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field src_ip", values[i])
			} else if value.Valid {
				ca.SrcIP = value.String
			}
		case cloudalert.FieldSrcPort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field src_port", values[i])
			} else if value.Valid {
				ca.SrcPort = int(value.Int64)
			}
		case cloudalert.FieldDstIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field dst_ip", values[i])
			} else if value.Valid {
				ca.DstIP = value.String
			}
		case cloudalert.FieldDstPort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dst_port", values[i])
			} else if value.Valid {
				ca.DstPort = int(value.Int64)
			}
		case cloudalert.FieldDefenceMode:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field defence_mode", values[i])
			} else if value.Valid {
				ca.DefenceMode = int(value.Int64)
			}
		case cloudalert.FieldFlowMode:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field flow_mode", values[i])
			} else if value.Valid {
				ca.FlowMode = int(value.Int64)
			}
		case cloudalert.FieldTCPAckNum:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tcp_ack_num", values[i])
			} else if value.Valid {
				ca.TCPAckNum = value.String
			}
		case cloudalert.FieldTCPSeqNum:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tcp_seq_num", values[i])
			} else if value.Valid {
				ca.TCPSeqNum = value.String
			}
		case cloudalert.FieldProtocol:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field protocol", values[i])
			} else if value.Valid {
				ca.Protocol = int(value.Int64)
			}
		case cloudalert.FieldDefenceLevel:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field defence_level", values[i])
			} else if value.Valid {
				ca.DefenceLevel = int(value.Int64)
			}
		case cloudalert.FieldMaxPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_pps", values[i])
			} else if value.Valid {
				ca.MaxPps = value.Int64
			}
		case cloudalert.FieldMaxAttackPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_attack_pps", values[i])
			} else if value.Valid {
				ca.MaxAttackPps = value.Int64
			}
		case cloudalert.FieldOverlimitPktCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field overlimit_pkt_count", values[i])
			} else if value.Valid {
				ca.OverlimitPktCount = int(value.Int64)
			}
		case cloudalert.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				ca.StartTime = value.Time
			}
		case cloudalert.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				ca.EndTime = value.Time
			}
		default:
			ca.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CloudAlert.
// This includes values selected through modifiers, order, etc.
func (ca *CloudAlert) Value(name string) (ent.Value, error) {
	return ca.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the CloudAlert entity.
func (ca *CloudAlert) QueryTenant() *TenantQuery {
	return NewCloudAlertClient(ca.config).QueryTenant(ca)
}

// QueryCloudflowDatas queries the "cloudflow_datas" edge of the CloudAlert entity.
func (ca *CloudAlert) QueryCloudflowDatas() *CloudFlowDataQuery {
	return NewCloudAlertClient(ca.config).QueryCloudflowDatas(ca)
}

// Update returns a builder for updating this CloudAlert.
// Note that you need to call CloudAlert.Unwrap() before calling this method if this CloudAlert
// was returned from a transaction, and the transaction was committed or rolled back.
func (ca *CloudAlert) Update() *CloudAlertUpdateOne {
	return NewCloudAlertClient(ca.config).UpdateOne(ca)
}

// Unwrap unwraps the CloudAlert entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ca *CloudAlert) Unwrap() *CloudAlert {
	_tx, ok := ca.config.driver.(*txDriver)
	if !ok {
		panic("ent: CloudAlert is not a transactional entity")
	}
	ca.config.driver = _tx.drv
	return ca
}

// String implements the fmt.Stringer.
func (ca *CloudAlert) String() string {
	var builder strings.Builder
	builder.WriteString("CloudAlert(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ca.ID))
	if v := ca.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(ca.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ca.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := ca.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("src_ip=")
	builder.WriteString(ca.SrcIP)
	builder.WriteString(", ")
	builder.WriteString("src_port=")
	builder.WriteString(fmt.Sprintf("%v", ca.SrcPort))
	builder.WriteString(", ")
	builder.WriteString("dst_ip=")
	builder.WriteString(ca.DstIP)
	builder.WriteString(", ")
	builder.WriteString("dst_port=")
	builder.WriteString(fmt.Sprintf("%v", ca.DstPort))
	builder.WriteString(", ")
	builder.WriteString("defence_mode=")
	builder.WriteString(fmt.Sprintf("%v", ca.DefenceMode))
	builder.WriteString(", ")
	builder.WriteString("flow_mode=")
	builder.WriteString(fmt.Sprintf("%v", ca.FlowMode))
	builder.WriteString(", ")
	builder.WriteString("tcp_ack_num=")
	builder.WriteString(ca.TCPAckNum)
	builder.WriteString(", ")
	builder.WriteString("tcp_seq_num=")
	builder.WriteString(ca.TCPSeqNum)
	builder.WriteString(", ")
	builder.WriteString("protocol=")
	builder.WriteString(fmt.Sprintf("%v", ca.Protocol))
	builder.WriteString(", ")
	builder.WriteString("defence_level=")
	builder.WriteString(fmt.Sprintf("%v", ca.DefenceLevel))
	builder.WriteString(", ")
	builder.WriteString("max_pps=")
	builder.WriteString(fmt.Sprintf("%v", ca.MaxPps))
	builder.WriteString(", ")
	builder.WriteString("max_attack_pps=")
	builder.WriteString(fmt.Sprintf("%v", ca.MaxAttackPps))
	builder.WriteString(", ")
	builder.WriteString("overlimit_pkt_count=")
	builder.WriteString(fmt.Sprintf("%v", ca.OverlimitPktCount))
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(ca.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end_time=")
	builder.WriteString(ca.EndTime.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// CloudAlerts is a parsable slice of CloudAlert.
type CloudAlerts []*CloudAlert
