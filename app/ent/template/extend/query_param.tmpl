{{/* 下一行的作用是，告诉 Intellij/GoLand 等IDE，基于 gen.Graph 类型来启用自动补全功能。 */}}
{{/* gotype: entgo.io/ent/entc/gen.Graph */}}

{{/* 该模板用于生成extend/query_param.go 文件*/}}
{{/* 实际的时间参数在运行后生成 */}}

{{ define "extend/query_param" }}

    {{/* 增加头 */}}
    // Code generated by ent, DO NOT EDIT.
    {{/* 使用extend包，防止循环导入 */}}
    package extend
    // 时间参数结构体
    {{/* 启动时会被代码生成器覆盖掉，如果有新增的时间字段，需要*/}}
    {{/*    go generate ./ent 之后再 air，才能重新 go generate ./ent，否则会提示新增的时间字段不存在*/}}
    type TimeParam struct{
    CreatedAtGte time.Time
    CreatedAtLte time.Time

    UpdatedAtGte time.Time
    UpdatedAtLte time.Time

    }
{{ end }}
