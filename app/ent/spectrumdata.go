// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// SpectrumData is the model entity for the SpectrumData schema.
type SpectrumData struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// SpectrumAlertID holds the value of the "spectrum_alert_id" field.
	SpectrumAlertID *int `json:"spectrum_alert_id,omitempty"`
	// IP holds the value of the "ip" field.
	IP string `json:"ip,omitempty"`
	// Time holds the value of the "time" field.
	Time time.Time `json:"time,omitempty"`
	// MonitorID holds the value of the "monitor_id" field.
	MonitorID int `json:"monitor_id,omitempty"`
	// DataType holds the value of the "data_type" field.
	DataType int `json:"data_type,omitempty"`
	// Bps holds the value of the "bps" field.
	Bps int64 `json:"bps,omitempty"`
	// Pps holds the value of the "pps" field.
	Pps int64 `json:"pps,omitempty"`
	// SynBps holds the value of the "syn_bps" field.
	SynBps int64 `json:"syn_bps,omitempty"`
	// SynPps holds the value of the "syn_pps" field.
	SynPps int64 `json:"syn_pps,omitempty"`
	// AckBps holds the value of the "ack_bps" field.
	AckBps int64 `json:"ack_bps,omitempty"`
	// AckPps holds the value of the "ack_pps" field.
	AckPps int64 `json:"ack_pps,omitempty"`
	// SynAckBps holds the value of the "syn_ack_bps" field.
	SynAckBps int64 `json:"syn_ack_bps,omitempty"`
	// SynAckPps holds the value of the "syn_ack_pps" field.
	SynAckPps int64 `json:"syn_ack_pps,omitempty"`
	// IcmpBps holds the value of the "icmp_bps" field.
	IcmpBps int64 `json:"icmp_bps,omitempty"`
	// IcmpPps holds the value of the "icmp_pps" field.
	IcmpPps int64 `json:"icmp_pps,omitempty"`
	// SmallPps holds the value of the "small_pps" field.
	SmallPps int64 `json:"small_pps,omitempty"`
	// NtpPps holds the value of the "ntp_pps" field.
	NtpPps int64 `json:"ntp_pps,omitempty"`
	// NtpBps holds the value of the "ntp_bps" field.
	NtpBps int64 `json:"ntp_bps,omitempty"`
	// DNSQueryPps holds the value of the "dns_query_pps" field.
	DNSQueryPps int64 `json:"dns_query_pps,omitempty"`
	// DNSQueryBps holds the value of the "dns_query_bps" field.
	DNSQueryBps int64 `json:"dns_query_bps,omitempty"`
	// DNSAnswerPps holds the value of the "dns_answer_pps" field.
	DNSAnswerPps int64 `json:"dns_answer_pps,omitempty"`
	// DNSAnswerBps holds the value of the "dns_answer_bps" field.
	DNSAnswerBps int64 `json:"dns_answer_bps,omitempty"`
	// SsdpBps holds the value of the "ssdp_bps" field.
	SsdpBps int64 `json:"ssdp_bps,omitempty"`
	// SsdpPps holds the value of the "ssdp_pps" field.
	SsdpPps int64 `json:"ssdp_pps,omitempty"`
	// UDPPps holds the value of the "udp_pps" field.
	UDPPps int64 `json:"udp_pps,omitempty"`
	// UDPBps holds the value of the "udp_bps" field.
	UDPBps int64 `json:"udp_bps,omitempty"`
	// QPS holds the value of the "qps" field.
	QPS int64 `json:"qps,omitempty"`
	// ReceiveCount holds the value of the "receive_count" field.
	ReceiveCount int `json:"receive_count,omitempty"`
	// IPType holds the value of the "ip_type" field.
	IPType int `json:"ip_type,omitempty"`
	// Monitor holds the value of the "monitor" field.
	Monitor *string `json:"monitor,omitempty"`
	// Product holds the value of the "product" field.
	Product *string `json:"product,omitempty"`
	// Host holds the value of the "host" field.
	Host *string `json:"host,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the SpectrumDataQuery when eager-loading is set.
	Edges        SpectrumDataEdges `json:"edges"`
	selectValues sql.SelectValues
}

// SpectrumDataEdges holds the relations/edges for other nodes in the graph.
type SpectrumDataEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// SpectrumAlert holds the value of the spectrum_alert edge.
	SpectrumAlert *SpectrumAlert `json:"spectrum_alert,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SpectrumDataEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// SpectrumAlertOrErr returns the SpectrumAlert value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SpectrumDataEdges) SpectrumAlertOrErr() (*SpectrumAlert, error) {
	if e.loadedTypes[1] {
		if e.SpectrumAlert == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: spectrumalert.Label}
		}
		return e.SpectrumAlert, nil
	}
	return nil, &NotLoadedError{edge: "spectrum_alert"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SpectrumData) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case spectrumdata.FieldID, spectrumdata.FieldTenantID, spectrumdata.FieldSpectrumAlertID, spectrumdata.FieldMonitorID, spectrumdata.FieldDataType, spectrumdata.FieldBps, spectrumdata.FieldPps, spectrumdata.FieldSynBps, spectrumdata.FieldSynPps, spectrumdata.FieldAckBps, spectrumdata.FieldAckPps, spectrumdata.FieldSynAckBps, spectrumdata.FieldSynAckPps, spectrumdata.FieldIcmpBps, spectrumdata.FieldIcmpPps, spectrumdata.FieldSmallPps, spectrumdata.FieldNtpPps, spectrumdata.FieldNtpBps, spectrumdata.FieldDNSQueryPps, spectrumdata.FieldDNSQueryBps, spectrumdata.FieldDNSAnswerPps, spectrumdata.FieldDNSAnswerBps, spectrumdata.FieldSsdpBps, spectrumdata.FieldSsdpPps, spectrumdata.FieldUDPPps, spectrumdata.FieldUDPBps, spectrumdata.FieldQPS, spectrumdata.FieldReceiveCount, spectrumdata.FieldIPType:
			values[i] = new(sql.NullInt64)
		case spectrumdata.FieldIP, spectrumdata.FieldMonitor, spectrumdata.FieldProduct, spectrumdata.FieldHost:
			values[i] = new(sql.NullString)
		case spectrumdata.FieldCreatedAt, spectrumdata.FieldTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SpectrumData fields.
func (sd *SpectrumData) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case spectrumdata.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			sd.ID = int(value.Int64)
		case spectrumdata.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				sd.TenantID = new(int)
				*sd.TenantID = int(value.Int64)
			}
		case spectrumdata.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				sd.CreatedAt = value.Time
			}
		case spectrumdata.FieldSpectrumAlertID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field spectrum_alert_id", values[i])
			} else if value.Valid {
				sd.SpectrumAlertID = new(int)
				*sd.SpectrumAlertID = int(value.Int64)
			}
		case spectrumdata.FieldIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ip", values[i])
			} else if value.Valid {
				sd.IP = value.String
			}
		case spectrumdata.FieldTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field time", values[i])
			} else if value.Valid {
				sd.Time = value.Time
			}
		case spectrumdata.FieldMonitorID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field monitor_id", values[i])
			} else if value.Valid {
				sd.MonitorID = int(value.Int64)
			}
		case spectrumdata.FieldDataType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field data_type", values[i])
			} else if value.Valid {
				sd.DataType = int(value.Int64)
			}
		case spectrumdata.FieldBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field bps", values[i])
			} else if value.Valid {
				sd.Bps = value.Int64
			}
		case spectrumdata.FieldPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field pps", values[i])
			} else if value.Valid {
				sd.Pps = value.Int64
			}
		case spectrumdata.FieldSynBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field syn_bps", values[i])
			} else if value.Valid {
				sd.SynBps = value.Int64
			}
		case spectrumdata.FieldSynPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field syn_pps", values[i])
			} else if value.Valid {
				sd.SynPps = value.Int64
			}
		case spectrumdata.FieldAckBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ack_bps", values[i])
			} else if value.Valid {
				sd.AckBps = value.Int64
			}
		case spectrumdata.FieldAckPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ack_pps", values[i])
			} else if value.Valid {
				sd.AckPps = value.Int64
			}
		case spectrumdata.FieldSynAckBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field syn_ack_bps", values[i])
			} else if value.Valid {
				sd.SynAckBps = value.Int64
			}
		case spectrumdata.FieldSynAckPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field syn_ack_pps", values[i])
			} else if value.Valid {
				sd.SynAckPps = value.Int64
			}
		case spectrumdata.FieldIcmpBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field icmp_bps", values[i])
			} else if value.Valid {
				sd.IcmpBps = value.Int64
			}
		case spectrumdata.FieldIcmpPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field icmp_pps", values[i])
			} else if value.Valid {
				sd.IcmpPps = value.Int64
			}
		case spectrumdata.FieldSmallPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field small_pps", values[i])
			} else if value.Valid {
				sd.SmallPps = value.Int64
			}
		case spectrumdata.FieldNtpPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ntp_pps", values[i])
			} else if value.Valid {
				sd.NtpPps = value.Int64
			}
		case spectrumdata.FieldNtpBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ntp_bps", values[i])
			} else if value.Valid {
				sd.NtpBps = value.Int64
			}
		case spectrumdata.FieldDNSQueryPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dns_query_pps", values[i])
			} else if value.Valid {
				sd.DNSQueryPps = value.Int64
			}
		case spectrumdata.FieldDNSQueryBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dns_query_bps", values[i])
			} else if value.Valid {
				sd.DNSQueryBps = value.Int64
			}
		case spectrumdata.FieldDNSAnswerPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dns_answer_pps", values[i])
			} else if value.Valid {
				sd.DNSAnswerPps = value.Int64
			}
		case spectrumdata.FieldDNSAnswerBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dns_answer_bps", values[i])
			} else if value.Valid {
				sd.DNSAnswerBps = value.Int64
			}
		case spectrumdata.FieldSsdpBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ssdp_bps", values[i])
			} else if value.Valid {
				sd.SsdpBps = value.Int64
			}
		case spectrumdata.FieldSsdpPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ssdp_pps", values[i])
			} else if value.Valid {
				sd.SsdpPps = value.Int64
			}
		case spectrumdata.FieldUDPPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field udp_pps", values[i])
			} else if value.Valid {
				sd.UDPPps = value.Int64
			}
		case spectrumdata.FieldUDPBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field udp_bps", values[i])
			} else if value.Valid {
				sd.UDPBps = value.Int64
			}
		case spectrumdata.FieldQPS:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field qps", values[i])
			} else if value.Valid {
				sd.QPS = value.Int64
			}
		case spectrumdata.FieldReceiveCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field receive_count", values[i])
			} else if value.Valid {
				sd.ReceiveCount = int(value.Int64)
			}
		case spectrumdata.FieldIPType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ip_type", values[i])
			} else if value.Valid {
				sd.IPType = int(value.Int64)
			}
		case spectrumdata.FieldMonitor:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field monitor", values[i])
			} else if value.Valid {
				sd.Monitor = new(string)
				*sd.Monitor = value.String
			}
		case spectrumdata.FieldProduct:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field product", values[i])
			} else if value.Valid {
				sd.Product = new(string)
				*sd.Product = value.String
			}
		case spectrumdata.FieldHost:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field host", values[i])
			} else if value.Valid {
				sd.Host = new(string)
				*sd.Host = value.String
			}
		default:
			sd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SpectrumData.
// This includes values selected through modifiers, order, etc.
func (sd *SpectrumData) Value(name string) (ent.Value, error) {
	return sd.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the SpectrumData entity.
func (sd *SpectrumData) QueryTenant() *TenantQuery {
	return NewSpectrumDataClient(sd.config).QueryTenant(sd)
}

// QuerySpectrumAlert queries the "spectrum_alert" edge of the SpectrumData entity.
func (sd *SpectrumData) QuerySpectrumAlert() *SpectrumAlertQuery {
	return NewSpectrumDataClient(sd.config).QuerySpectrumAlert(sd)
}

// Update returns a builder for updating this SpectrumData.
// Note that you need to call SpectrumData.Unwrap() before calling this method if this SpectrumData
// was returned from a transaction, and the transaction was committed or rolled back.
func (sd *SpectrumData) Update() *SpectrumDataUpdateOne {
	return NewSpectrumDataClient(sd.config).UpdateOne(sd)
}

// Unwrap unwraps the SpectrumData entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (sd *SpectrumData) Unwrap() *SpectrumData {
	_tx, ok := sd.config.driver.(*txDriver)
	if !ok {
		panic("ent: SpectrumData is not a transactional entity")
	}
	sd.config.driver = _tx.drv
	return sd
}

// String implements the fmt.Stringer.
func (sd *SpectrumData) String() string {
	var builder strings.Builder
	builder.WriteString("SpectrumData(")
	builder.WriteString(fmt.Sprintf("id=%v, ", sd.ID))
	if v := sd.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(sd.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := sd.SpectrumAlertID; v != nil {
		builder.WriteString("spectrum_alert_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("ip=")
	builder.WriteString(sd.IP)
	builder.WriteString(", ")
	builder.WriteString("time=")
	builder.WriteString(sd.Time.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("monitor_id=")
	builder.WriteString(fmt.Sprintf("%v", sd.MonitorID))
	builder.WriteString(", ")
	builder.WriteString("data_type=")
	builder.WriteString(fmt.Sprintf("%v", sd.DataType))
	builder.WriteString(", ")
	builder.WriteString("bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.Bps))
	builder.WriteString(", ")
	builder.WriteString("pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.Pps))
	builder.WriteString(", ")
	builder.WriteString("syn_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.SynBps))
	builder.WriteString(", ")
	builder.WriteString("syn_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.SynPps))
	builder.WriteString(", ")
	builder.WriteString("ack_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.AckBps))
	builder.WriteString(", ")
	builder.WriteString("ack_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.AckPps))
	builder.WriteString(", ")
	builder.WriteString("syn_ack_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.SynAckBps))
	builder.WriteString(", ")
	builder.WriteString("syn_ack_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.SynAckPps))
	builder.WriteString(", ")
	builder.WriteString("icmp_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.IcmpBps))
	builder.WriteString(", ")
	builder.WriteString("icmp_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.IcmpPps))
	builder.WriteString(", ")
	builder.WriteString("small_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.SmallPps))
	builder.WriteString(", ")
	builder.WriteString("ntp_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.NtpPps))
	builder.WriteString(", ")
	builder.WriteString("ntp_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.NtpBps))
	builder.WriteString(", ")
	builder.WriteString("dns_query_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.DNSQueryPps))
	builder.WriteString(", ")
	builder.WriteString("dns_query_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.DNSQueryBps))
	builder.WriteString(", ")
	builder.WriteString("dns_answer_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.DNSAnswerPps))
	builder.WriteString(", ")
	builder.WriteString("dns_answer_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.DNSAnswerBps))
	builder.WriteString(", ")
	builder.WriteString("ssdp_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.SsdpBps))
	builder.WriteString(", ")
	builder.WriteString("ssdp_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.SsdpPps))
	builder.WriteString(", ")
	builder.WriteString("udp_pps=")
	builder.WriteString(fmt.Sprintf("%v", sd.UDPPps))
	builder.WriteString(", ")
	builder.WriteString("udp_bps=")
	builder.WriteString(fmt.Sprintf("%v", sd.UDPBps))
	builder.WriteString(", ")
	builder.WriteString("qps=")
	builder.WriteString(fmt.Sprintf("%v", sd.QPS))
	builder.WriteString(", ")
	builder.WriteString("receive_count=")
	builder.WriteString(fmt.Sprintf("%v", sd.ReceiveCount))
	builder.WriteString(", ")
	builder.WriteString("ip_type=")
	builder.WriteString(fmt.Sprintf("%v", sd.IPType))
	builder.WriteString(", ")
	if v := sd.Monitor; v != nil {
		builder.WriteString("monitor=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := sd.Product; v != nil {
		builder.WriteString("product=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := sd.Host; v != nil {
		builder.WriteString("host=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// SpectrumDataSlice is a parsable slice of SpectrumData.
type SpectrumDataSlice []*SpectrumData
