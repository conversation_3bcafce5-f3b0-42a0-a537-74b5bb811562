// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// StrategyQuery is the builder for querying Strategy entities.
type StrategyQuery struct {
	config
	ctx                *QueryContext
	order              []strategy.OrderOption
	inters             []Interceptor
	predicates         []predicate.Strategy
	withTenant         *TenantQuery
	withStrategyAlerts *SpectrumAlertQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the StrategyQuery builder.
func (sq *StrategyQuery) Where(ps ...predicate.Strategy) *StrategyQuery {
	sq.predicates = append(sq.predicates, ps...)
	return sq
}

// Limit the number of records to be returned by this query.
func (sq *StrategyQuery) Limit(limit int) *StrategyQuery {
	sq.ctx.Limit = &limit
	return sq
}

// Offset to start from.
func (sq *StrategyQuery) Offset(offset int) *StrategyQuery {
	sq.ctx.Offset = &offset
	return sq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (sq *StrategyQuery) Unique(unique bool) *StrategyQuery {
	sq.ctx.Unique = &unique
	return sq
}

// Order specifies how the records should be ordered.
func (sq *StrategyQuery) Order(o ...strategy.OrderOption) *StrategyQuery {
	sq.order = append(sq.order, o...)
	return sq
}

// QueryTenant chains the current query on the "tenant" edge.
func (sq *StrategyQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: sq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(strategy.Table, strategy.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, strategy.TenantTable, strategy.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(sq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryStrategyAlerts chains the current query on the "strategy_alerts" edge.
func (sq *StrategyQuery) QueryStrategyAlerts() *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: sq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(strategy.Table, strategy.FieldID, selector),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, strategy.StrategyAlertsTable, strategy.StrategyAlertsColumn),
		)
		fromU = sqlgraph.SetNeighbors(sq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Strategy entity from the query.
// Returns a *NotFoundError when no Strategy was found.
func (sq *StrategyQuery) First(ctx context.Context) (*Strategy, error) {
	nodes, err := sq.Limit(1).All(setContextOp(ctx, sq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{strategy.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (sq *StrategyQuery) FirstX(ctx context.Context) *Strategy {
	node, err := sq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Strategy ID from the query.
// Returns a *NotFoundError when no Strategy ID was found.
func (sq *StrategyQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = sq.Limit(1).IDs(setContextOp(ctx, sq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{strategy.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (sq *StrategyQuery) FirstIDX(ctx context.Context) int {
	id, err := sq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Strategy entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Strategy entity is found.
// Returns a *NotFoundError when no Strategy entities are found.
func (sq *StrategyQuery) Only(ctx context.Context) (*Strategy, error) {
	nodes, err := sq.Limit(2).All(setContextOp(ctx, sq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{strategy.Label}
	default:
		return nil, &NotSingularError{strategy.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (sq *StrategyQuery) OnlyX(ctx context.Context) *Strategy {
	node, err := sq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Strategy ID in the query.
// Returns a *NotSingularError when more than one Strategy ID is found.
// Returns a *NotFoundError when no entities are found.
func (sq *StrategyQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = sq.Limit(2).IDs(setContextOp(ctx, sq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{strategy.Label}
	default:
		err = &NotSingularError{strategy.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (sq *StrategyQuery) OnlyIDX(ctx context.Context) int {
	id, err := sq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Strategies.
func (sq *StrategyQuery) All(ctx context.Context) ([]*Strategy, error) {
	ctx = setContextOp(ctx, sq.ctx, "All")
	if err := sq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Strategy, *StrategyQuery]()
	return withInterceptors[[]*Strategy](ctx, sq, qr, sq.inters)
}

// AllX is like All, but panics if an error occurs.
func (sq *StrategyQuery) AllX(ctx context.Context) []*Strategy {
	nodes, err := sq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Strategy IDs.
func (sq *StrategyQuery) IDs(ctx context.Context) (ids []int, err error) {
	if sq.ctx.Unique == nil && sq.path != nil {
		sq.Unique(true)
	}
	ctx = setContextOp(ctx, sq.ctx, "IDs")
	if err = sq.Select(strategy.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (sq *StrategyQuery) IDsX(ctx context.Context) []int {
	ids, err := sq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (sq *StrategyQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, sq.ctx, "Count")
	if err := sq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, sq, querierCount[*StrategyQuery](), sq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (sq *StrategyQuery) CountX(ctx context.Context) int {
	count, err := sq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (sq *StrategyQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, sq.ctx, "Exist")
	switch _, err := sq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (sq *StrategyQuery) ExistX(ctx context.Context) bool {
	exist, err := sq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the StrategyQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (sq *StrategyQuery) Clone() *StrategyQuery {
	if sq == nil {
		return nil
	}
	return &StrategyQuery{
		config:             sq.config,
		ctx:                sq.ctx.Clone(),
		order:              append([]strategy.OrderOption{}, sq.order...),
		inters:             append([]Interceptor{}, sq.inters...),
		predicates:         append([]predicate.Strategy{}, sq.predicates...),
		withTenant:         sq.withTenant.Clone(),
		withStrategyAlerts: sq.withStrategyAlerts.Clone(),
		// clone intermediate query.
		sql:  sq.sql.Clone(),
		path: sq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (sq *StrategyQuery) WithTenant(opts ...func(*TenantQuery)) *StrategyQuery {
	query := (&TenantClient{config: sq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sq.withTenant = query
	return sq
}

// WithStrategyAlerts tells the query-builder to eager-load the nodes that are connected to
// the "strategy_alerts" edge. The optional arguments are used to configure the query builder of the edge.
func (sq *StrategyQuery) WithStrategyAlerts(opts ...func(*SpectrumAlertQuery)) *StrategyQuery {
	query := (&SpectrumAlertClient{config: sq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sq.withStrategyAlerts = query
	return sq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Strategy.Query().
//		GroupBy(strategy.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (sq *StrategyQuery) GroupBy(field string, fields ...string) *StrategyGroupBy {
	sq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &StrategyGroupBy{build: sq}
	grbuild.flds = &sq.ctx.Fields
	grbuild.label = strategy.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.Strategy.Query().
//		Select(strategy.FieldTenantID).
//		Scan(ctx, &v)
func (sq *StrategyQuery) Select(fields ...string) *StrategySelect {
	sq.ctx.Fields = append(sq.ctx.Fields, fields...)
	sbuild := &StrategySelect{StrategyQuery: sq}
	sbuild.label = strategy.Label
	sbuild.flds, sbuild.scan = &sq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a StrategySelect configured with the given aggregations.
func (sq *StrategyQuery) Aggregate(fns ...AggregateFunc) *StrategySelect {
	return sq.Select().Aggregate(fns...)
}

func (sq *StrategyQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range sq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, sq); err != nil {
				return err
			}
		}
	}
	for _, f := range sq.ctx.Fields {
		if !strategy.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if sq.path != nil {
		prev, err := sq.path(ctx)
		if err != nil {
			return err
		}
		sq.sql = prev
	}
	if strategy.Policy == nil {
		return errors.New("ent: uninitialized strategy.Policy (forgotten import ent/runtime?)")
	}
	if err := strategy.Policy.EvalQuery(ctx, sq); err != nil {
		return err
	}
	return nil
}

func (sq *StrategyQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Strategy, error) {
	var (
		nodes       = []*Strategy{}
		_spec       = sq.querySpec()
		loadedTypes = [2]bool{
			sq.withTenant != nil,
			sq.withStrategyAlerts != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Strategy).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Strategy{config: sq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, sq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := sq.withTenant; query != nil {
		if err := sq.loadTenant(ctx, query, nodes, nil,
			func(n *Strategy, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := sq.withStrategyAlerts; query != nil {
		if err := sq.loadStrategyAlerts(ctx, query, nodes,
			func(n *Strategy) { n.Edges.StrategyAlerts = []*SpectrumAlert{} },
			func(n *Strategy, e *SpectrumAlert) { n.Edges.StrategyAlerts = append(n.Edges.StrategyAlerts, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (sq *StrategyQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*Strategy, init func(*Strategy), assign func(*Strategy, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Strategy)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (sq *StrategyQuery) loadStrategyAlerts(ctx context.Context, query *SpectrumAlertQuery, nodes []*Strategy, init func(*Strategy), assign func(*Strategy, *SpectrumAlert)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Strategy)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(spectrumalert.FieldStrategyID)
	}
	query.Where(predicate.SpectrumAlert(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(strategy.StrategyAlertsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.StrategyID
		if fk == nil {
			return fmt.Errorf(`foreign-key "strategy_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "strategy_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (sq *StrategyQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := sq.querySpec()
	_spec.Node.Columns = sq.ctx.Fields
	if len(sq.ctx.Fields) > 0 {
		_spec.Unique = sq.ctx.Unique != nil && *sq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, sq.driver, _spec)
}

func (sq *StrategyQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(strategy.Table, strategy.Columns, sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt))
	_spec.From = sq.sql
	if unique := sq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if sq.path != nil {
		_spec.Unique = true
	}
	if fields := sq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, strategy.FieldID)
		for i := range fields {
			if fields[i] != strategy.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if sq.withTenant != nil {
			_spec.Node.AddColumnOnce(strategy.FieldTenantID)
		}
	}
	if ps := sq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := sq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := sq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := sq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (sq *StrategyQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(sq.driver.Dialect())
	t1 := builder.Table(strategy.Table)
	columns := sq.ctx.Fields
	if len(columns) == 0 {
		columns = strategy.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if sq.sql != nil {
		selector = sq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if sq.ctx.Unique != nil && *sq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range sq.predicates {
		p(selector)
	}
	for _, p := range sq.order {
		p(selector)
	}
	if offset := sq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := sq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// StrategyGroupBy is the group-by builder for Strategy entities.
type StrategyGroupBy struct {
	selector
	build *StrategyQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (sgb *StrategyGroupBy) Aggregate(fns ...AggregateFunc) *StrategyGroupBy {
	sgb.fns = append(sgb.fns, fns...)
	return sgb
}

// Scan applies the selector query and scans the result into the given value.
func (sgb *StrategyGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sgb.build.ctx, "GroupBy")
	if err := sgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*StrategyQuery, *StrategyGroupBy](ctx, sgb.build, sgb, sgb.build.inters, v)
}

func (sgb *StrategyGroupBy) sqlScan(ctx context.Context, root *StrategyQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(sgb.fns))
	for _, fn := range sgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*sgb.flds)+len(sgb.fns))
		for _, f := range *sgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*sgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// StrategySelect is the builder for selecting fields of Strategy entities.
type StrategySelect struct {
	*StrategyQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ss *StrategySelect) Aggregate(fns ...AggregateFunc) *StrategySelect {
	ss.fns = append(ss.fns, fns...)
	return ss
}

// Scan applies the selector query and scans the result into the given value.
func (ss *StrategySelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ss.ctx, "Select")
	if err := ss.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*StrategyQuery, *StrategySelect](ctx, ss.StrategyQuery, ss, ss.inters, v)
}

func (ss *StrategySelect) sqlScan(ctx context.Context, root *StrategyQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ss.fns))
	for _, fn := range ss.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ss.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ss.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
