// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudAttackDataDelete is the builder for deleting a CloudAttackData entity.
type CloudAttackDataDelete struct {
	config
	hooks    []Hook
	mutation *CloudAttackDataMutation
}

// Where appends a list predicates to the CloudAttackDataDelete builder.
func (cadd *CloudAttackDataDelete) Where(ps ...predicate.CloudAttackData) *CloudAttackDataDelete {
	cadd.mutation.Where(ps...)
	return cadd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (cadd *CloudAttackDataDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, cadd.sqlExec, cadd.mutation, cadd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (cadd *CloudAttackDataDelete) ExecX(ctx context.Context) int {
	n, err := cadd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (cadd *CloudAttackDataDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(cloudattackdata.Table, sqlgraph.NewFieldSpec(cloudattackdata.FieldID, field.TypeInt))
	if ps := cadd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, cadd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	cadd.mutation.done = true
	return affected, err
}

// CloudAttackDataDeleteOne is the builder for deleting a single CloudAttackData entity.
type CloudAttackDataDeleteOne struct {
	cadd *CloudAttackDataDelete
}

// Where appends a list predicates to the CloudAttackDataDelete builder.
func (caddo *CloudAttackDataDeleteOne) Where(ps ...predicate.CloudAttackData) *CloudAttackDataDeleteOne {
	caddo.cadd.mutation.Where(ps...)
	return caddo
}

// Exec executes the deletion query.
func (caddo *CloudAttackDataDeleteOne) Exec(ctx context.Context) error {
	n, err := caddo.cadd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{cloudattackdata.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (caddo *CloudAttackDataDeleteOne) ExecX(ctx context.Context) {
	if err := caddo.Exec(ctx); err != nil {
		panic(err)
	}
}
