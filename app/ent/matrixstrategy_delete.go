// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixStrategyDelete is the builder for deleting a MatrixStrategy entity.
type MatrixStrategyDelete struct {
	config
	hooks    []Hook
	mutation *MatrixStrategyMutation
}

// Where appends a list predicates to the MatrixStrategyDelete builder.
func (msd *MatrixStrategyDelete) Where(ps ...predicate.MatrixStrategy) *MatrixStrategyDelete {
	msd.mutation.Where(ps...)
	return msd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (msd *MatrixStrategyDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, msd.sqlExec, msd.mutation, msd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (msd *MatrixStrategyDelete) ExecX(ctx context.Context) int {
	n, err := msd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (msd *MatrixStrategyDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(matrixstrategy.Table, sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt))
	if ps := msd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, msd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	msd.mutation.done = true
	return affected, err
}

// MatrixStrategyDeleteOne is the builder for deleting a single MatrixStrategy entity.
type MatrixStrategyDeleteOne struct {
	msd *MatrixStrategyDelete
}

// Where appends a list predicates to the MatrixStrategyDelete builder.
func (msdo *MatrixStrategyDeleteOne) Where(ps ...predicate.MatrixStrategy) *MatrixStrategyDeleteOne {
	msdo.msd.mutation.Where(ps...)
	return msdo
}

// Exec executes the deletion query.
func (msdo *MatrixStrategyDeleteOne) Exec(ctx context.Context) error {
	n, err := msdo.msd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{matrixstrategy.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (msdo *MatrixStrategyDeleteOne) ExecX(ctx context.Context) {
	if err := msdo.Exec(ctx); err != nil {
		panic(err)
	}
}
