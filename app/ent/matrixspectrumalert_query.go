// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumAlertQuery is the builder for querying MatrixSpectrumAlert entities.
type MatrixSpectrumAlertQuery struct {
	config
	ctx                     *QueryContext
	order                   []matrixspectrumalert.OrderOption
	inters                  []Interceptor
	predicates              []predicate.MatrixSpectrumAlert
	withTenant              *TenantQuery
	withMatrixSpectrumDatas *MatrixSpectrumDataQuery
	withMatrixStrategy      *MatrixStrategyQuery
	withWofangTicket        *WofangQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the MatrixSpectrumAlertQuery builder.
func (msaq *MatrixSpectrumAlertQuery) Where(ps ...predicate.MatrixSpectrumAlert) *MatrixSpectrumAlertQuery {
	msaq.predicates = append(msaq.predicates, ps...)
	return msaq
}

// Limit the number of records to be returned by this query.
func (msaq *MatrixSpectrumAlertQuery) Limit(limit int) *MatrixSpectrumAlertQuery {
	msaq.ctx.Limit = &limit
	return msaq
}

// Offset to start from.
func (msaq *MatrixSpectrumAlertQuery) Offset(offset int) *MatrixSpectrumAlertQuery {
	msaq.ctx.Offset = &offset
	return msaq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (msaq *MatrixSpectrumAlertQuery) Unique(unique bool) *MatrixSpectrumAlertQuery {
	msaq.ctx.Unique = &unique
	return msaq
}

// Order specifies how the records should be ordered.
func (msaq *MatrixSpectrumAlertQuery) Order(o ...matrixspectrumalert.OrderOption) *MatrixSpectrumAlertQuery {
	msaq.order = append(msaq.order, o...)
	return msaq
}

// QueryTenant chains the current query on the "tenant" edge.
func (msaq *MatrixSpectrumAlertQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: msaq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := msaq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := msaq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumalert.Table, matrixspectrumalert.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, matrixspectrumalert.TenantTable, matrixspectrumalert.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(msaq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryMatrixSpectrumDatas chains the current query on the "matrix_spectrum_datas" edge.
func (msaq *MatrixSpectrumAlertQuery) QueryMatrixSpectrumDatas() *MatrixSpectrumDataQuery {
	query := (&MatrixSpectrumDataClient{config: msaq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := msaq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := msaq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumalert.Table, matrixspectrumalert.FieldID, selector),
			sqlgraph.To(matrixspectrumdata.Table, matrixspectrumdata.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, matrixspectrumalert.MatrixSpectrumDatasTable, matrixspectrumalert.MatrixSpectrumDatasColumn),
		)
		fromU = sqlgraph.SetNeighbors(msaq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryMatrixStrategy chains the current query on the "matrix_strategy" edge.
func (msaq *MatrixSpectrumAlertQuery) QueryMatrixStrategy() *MatrixStrategyQuery {
	query := (&MatrixStrategyClient{config: msaq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := msaq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := msaq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumalert.Table, matrixspectrumalert.FieldID, selector),
			sqlgraph.To(matrixstrategy.Table, matrixstrategy.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, matrixspectrumalert.MatrixStrategyTable, matrixspectrumalert.MatrixStrategyColumn),
		)
		fromU = sqlgraph.SetNeighbors(msaq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryWofangTicket chains the current query on the "wofang_ticket" edge.
func (msaq *MatrixSpectrumAlertQuery) QueryWofangTicket() *WofangQuery {
	query := (&WofangClient{config: msaq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := msaq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := msaq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumalert.Table, matrixspectrumalert.FieldID, selector),
			sqlgraph.To(wofang.Table, wofang.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, matrixspectrumalert.WofangTicketTable, matrixspectrumalert.WofangTicketColumn),
		)
		fromU = sqlgraph.SetNeighbors(msaq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first MatrixSpectrumAlert entity from the query.
// Returns a *NotFoundError when no MatrixSpectrumAlert was found.
func (msaq *MatrixSpectrumAlertQuery) First(ctx context.Context) (*MatrixSpectrumAlert, error) {
	nodes, err := msaq.Limit(1).All(setContextOp(ctx, msaq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{matrixspectrumalert.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (msaq *MatrixSpectrumAlertQuery) FirstX(ctx context.Context) *MatrixSpectrumAlert {
	node, err := msaq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first MatrixSpectrumAlert ID from the query.
// Returns a *NotFoundError when no MatrixSpectrumAlert ID was found.
func (msaq *MatrixSpectrumAlertQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = msaq.Limit(1).IDs(setContextOp(ctx, msaq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{matrixspectrumalert.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (msaq *MatrixSpectrumAlertQuery) FirstIDX(ctx context.Context) int {
	id, err := msaq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single MatrixSpectrumAlert entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one MatrixSpectrumAlert entity is found.
// Returns a *NotFoundError when no MatrixSpectrumAlert entities are found.
func (msaq *MatrixSpectrumAlertQuery) Only(ctx context.Context) (*MatrixSpectrumAlert, error) {
	nodes, err := msaq.Limit(2).All(setContextOp(ctx, msaq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{matrixspectrumalert.Label}
	default:
		return nil, &NotSingularError{matrixspectrumalert.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (msaq *MatrixSpectrumAlertQuery) OnlyX(ctx context.Context) *MatrixSpectrumAlert {
	node, err := msaq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only MatrixSpectrumAlert ID in the query.
// Returns a *NotSingularError when more than one MatrixSpectrumAlert ID is found.
// Returns a *NotFoundError when no entities are found.
func (msaq *MatrixSpectrumAlertQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = msaq.Limit(2).IDs(setContextOp(ctx, msaq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{matrixspectrumalert.Label}
	default:
		err = &NotSingularError{matrixspectrumalert.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (msaq *MatrixSpectrumAlertQuery) OnlyIDX(ctx context.Context) int {
	id, err := msaq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of MatrixSpectrumAlerts.
func (msaq *MatrixSpectrumAlertQuery) All(ctx context.Context) ([]*MatrixSpectrumAlert, error) {
	ctx = setContextOp(ctx, msaq.ctx, "All")
	if err := msaq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*MatrixSpectrumAlert, *MatrixSpectrumAlertQuery]()
	return withInterceptors[[]*MatrixSpectrumAlert](ctx, msaq, qr, msaq.inters)
}

// AllX is like All, but panics if an error occurs.
func (msaq *MatrixSpectrumAlertQuery) AllX(ctx context.Context) []*MatrixSpectrumAlert {
	nodes, err := msaq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of MatrixSpectrumAlert IDs.
func (msaq *MatrixSpectrumAlertQuery) IDs(ctx context.Context) (ids []int, err error) {
	if msaq.ctx.Unique == nil && msaq.path != nil {
		msaq.Unique(true)
	}
	ctx = setContextOp(ctx, msaq.ctx, "IDs")
	if err = msaq.Select(matrixspectrumalert.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (msaq *MatrixSpectrumAlertQuery) IDsX(ctx context.Context) []int {
	ids, err := msaq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (msaq *MatrixSpectrumAlertQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, msaq.ctx, "Count")
	if err := msaq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, msaq, querierCount[*MatrixSpectrumAlertQuery](), msaq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (msaq *MatrixSpectrumAlertQuery) CountX(ctx context.Context) int {
	count, err := msaq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (msaq *MatrixSpectrumAlertQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, msaq.ctx, "Exist")
	switch _, err := msaq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (msaq *MatrixSpectrumAlertQuery) ExistX(ctx context.Context) bool {
	exist, err := msaq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the MatrixSpectrumAlertQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (msaq *MatrixSpectrumAlertQuery) Clone() *MatrixSpectrumAlertQuery {
	if msaq == nil {
		return nil
	}
	return &MatrixSpectrumAlertQuery{
		config:                  msaq.config,
		ctx:                     msaq.ctx.Clone(),
		order:                   append([]matrixspectrumalert.OrderOption{}, msaq.order...),
		inters:                  append([]Interceptor{}, msaq.inters...),
		predicates:              append([]predicate.MatrixSpectrumAlert{}, msaq.predicates...),
		withTenant:              msaq.withTenant.Clone(),
		withMatrixSpectrumDatas: msaq.withMatrixSpectrumDatas.Clone(),
		withMatrixStrategy:      msaq.withMatrixStrategy.Clone(),
		withWofangTicket:        msaq.withWofangTicket.Clone(),
		// clone intermediate query.
		sql:  msaq.sql.Clone(),
		path: msaq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (msaq *MatrixSpectrumAlertQuery) WithTenant(opts ...func(*TenantQuery)) *MatrixSpectrumAlertQuery {
	query := (&TenantClient{config: msaq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	msaq.withTenant = query
	return msaq
}

// WithMatrixSpectrumDatas tells the query-builder to eager-load the nodes that are connected to
// the "matrix_spectrum_datas" edge. The optional arguments are used to configure the query builder of the edge.
func (msaq *MatrixSpectrumAlertQuery) WithMatrixSpectrumDatas(opts ...func(*MatrixSpectrumDataQuery)) *MatrixSpectrumAlertQuery {
	query := (&MatrixSpectrumDataClient{config: msaq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	msaq.withMatrixSpectrumDatas = query
	return msaq
}

// WithMatrixStrategy tells the query-builder to eager-load the nodes that are connected to
// the "matrix_strategy" edge. The optional arguments are used to configure the query builder of the edge.
func (msaq *MatrixSpectrumAlertQuery) WithMatrixStrategy(opts ...func(*MatrixStrategyQuery)) *MatrixSpectrumAlertQuery {
	query := (&MatrixStrategyClient{config: msaq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	msaq.withMatrixStrategy = query
	return msaq
}

// WithWofangTicket tells the query-builder to eager-load the nodes that are connected to
// the "wofang_ticket" edge. The optional arguments are used to configure the query builder of the edge.
func (msaq *MatrixSpectrumAlertQuery) WithWofangTicket(opts ...func(*WofangQuery)) *MatrixSpectrumAlertQuery {
	query := (&WofangClient{config: msaq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	msaq.withWofangTicket = query
	return msaq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.MatrixSpectrumAlert.Query().
//		GroupBy(matrixspectrumalert.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (msaq *MatrixSpectrumAlertQuery) GroupBy(field string, fields ...string) *MatrixSpectrumAlertGroupBy {
	msaq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &MatrixSpectrumAlertGroupBy{build: msaq}
	grbuild.flds = &msaq.ctx.Fields
	grbuild.label = matrixspectrumalert.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.MatrixSpectrumAlert.Query().
//		Select(matrixspectrumalert.FieldTenantID).
//		Scan(ctx, &v)
func (msaq *MatrixSpectrumAlertQuery) Select(fields ...string) *MatrixSpectrumAlertSelect {
	msaq.ctx.Fields = append(msaq.ctx.Fields, fields...)
	sbuild := &MatrixSpectrumAlertSelect{MatrixSpectrumAlertQuery: msaq}
	sbuild.label = matrixspectrumalert.Label
	sbuild.flds, sbuild.scan = &msaq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a MatrixSpectrumAlertSelect configured with the given aggregations.
func (msaq *MatrixSpectrumAlertQuery) Aggregate(fns ...AggregateFunc) *MatrixSpectrumAlertSelect {
	return msaq.Select().Aggregate(fns...)
}

func (msaq *MatrixSpectrumAlertQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range msaq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, msaq); err != nil {
				return err
			}
		}
	}
	for _, f := range msaq.ctx.Fields {
		if !matrixspectrumalert.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if msaq.path != nil {
		prev, err := msaq.path(ctx)
		if err != nil {
			return err
		}
		msaq.sql = prev
	}
	if matrixspectrumalert.Policy == nil {
		return errors.New("ent: uninitialized matrixspectrumalert.Policy (forgotten import ent/runtime?)")
	}
	if err := matrixspectrumalert.Policy.EvalQuery(ctx, msaq); err != nil {
		return err
	}
	return nil
}

func (msaq *MatrixSpectrumAlertQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*MatrixSpectrumAlert, error) {
	var (
		nodes       = []*MatrixSpectrumAlert{}
		_spec       = msaq.querySpec()
		loadedTypes = [4]bool{
			msaq.withTenant != nil,
			msaq.withMatrixSpectrumDatas != nil,
			msaq.withMatrixStrategy != nil,
			msaq.withWofangTicket != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*MatrixSpectrumAlert).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &MatrixSpectrumAlert{config: msaq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, msaq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := msaq.withTenant; query != nil {
		if err := msaq.loadTenant(ctx, query, nodes, nil,
			func(n *MatrixSpectrumAlert, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := msaq.withMatrixSpectrumDatas; query != nil {
		if err := msaq.loadMatrixSpectrumDatas(ctx, query, nodes,
			func(n *MatrixSpectrumAlert) { n.Edges.MatrixSpectrumDatas = []*MatrixSpectrumData{} },
			func(n *MatrixSpectrumAlert, e *MatrixSpectrumData) {
				n.Edges.MatrixSpectrumDatas = append(n.Edges.MatrixSpectrumDatas, e)
			}); err != nil {
			return nil, err
		}
	}
	if query := msaq.withMatrixStrategy; query != nil {
		if err := msaq.loadMatrixStrategy(ctx, query, nodes, nil,
			func(n *MatrixSpectrumAlert, e *MatrixStrategy) { n.Edges.MatrixStrategy = e }); err != nil {
			return nil, err
		}
	}
	if query := msaq.withWofangTicket; query != nil {
		if err := msaq.loadWofangTicket(ctx, query, nodes, nil,
			func(n *MatrixSpectrumAlert, e *Wofang) { n.Edges.WofangTicket = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (msaq *MatrixSpectrumAlertQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*MatrixSpectrumAlert, init func(*MatrixSpectrumAlert), assign func(*MatrixSpectrumAlert, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MatrixSpectrumAlert)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (msaq *MatrixSpectrumAlertQuery) loadMatrixSpectrumDatas(ctx context.Context, query *MatrixSpectrumDataQuery, nodes []*MatrixSpectrumAlert, init func(*MatrixSpectrumAlert), assign func(*MatrixSpectrumAlert, *MatrixSpectrumData)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*MatrixSpectrumAlert)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(matrixspectrumdata.FieldMatrixSpectrumAlertID)
	}
	query.Where(predicate.MatrixSpectrumData(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(matrixspectrumalert.MatrixSpectrumDatasColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.MatrixSpectrumAlertID
		if fk == nil {
			return fmt.Errorf(`foreign-key "matrix_spectrum_alert_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "matrix_spectrum_alert_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (msaq *MatrixSpectrumAlertQuery) loadMatrixStrategy(ctx context.Context, query *MatrixStrategyQuery, nodes []*MatrixSpectrumAlert, init func(*MatrixSpectrumAlert), assign func(*MatrixSpectrumAlert, *MatrixStrategy)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MatrixSpectrumAlert)
	for i := range nodes {
		if nodes[i].MatrixStrategyID == nil {
			continue
		}
		fk := *nodes[i].MatrixStrategyID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(matrixstrategy.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "matrix_strategy_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (msaq *MatrixSpectrumAlertQuery) loadWofangTicket(ctx context.Context, query *WofangQuery, nodes []*MatrixSpectrumAlert, init func(*MatrixSpectrumAlert), assign func(*MatrixSpectrumAlert, *Wofang)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MatrixSpectrumAlert)
	for i := range nodes {
		if nodes[i].WofangID == nil {
			continue
		}
		fk := *nodes[i].WofangID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(wofang.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "wofang_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (msaq *MatrixSpectrumAlertQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := msaq.querySpec()
	_spec.Node.Columns = msaq.ctx.Fields
	if len(msaq.ctx.Fields) > 0 {
		_spec.Unique = msaq.ctx.Unique != nil && *msaq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, msaq.driver, _spec)
}

func (msaq *MatrixSpectrumAlertQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(matrixspectrumalert.Table, matrixspectrumalert.Columns, sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt))
	_spec.From = msaq.sql
	if unique := msaq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if msaq.path != nil {
		_spec.Unique = true
	}
	if fields := msaq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, matrixspectrumalert.FieldID)
		for i := range fields {
			if fields[i] != matrixspectrumalert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if msaq.withTenant != nil {
			_spec.Node.AddColumnOnce(matrixspectrumalert.FieldTenantID)
		}
		if msaq.withMatrixStrategy != nil {
			_spec.Node.AddColumnOnce(matrixspectrumalert.FieldMatrixStrategyID)
		}
		if msaq.withWofangTicket != nil {
			_spec.Node.AddColumnOnce(matrixspectrumalert.FieldWofangID)
		}
	}
	if ps := msaq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := msaq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := msaq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := msaq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (msaq *MatrixSpectrumAlertQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(msaq.driver.Dialect())
	t1 := builder.Table(matrixspectrumalert.Table)
	columns := msaq.ctx.Fields
	if len(columns) == 0 {
		columns = matrixspectrumalert.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if msaq.sql != nil {
		selector = msaq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if msaq.ctx.Unique != nil && *msaq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range msaq.predicates {
		p(selector)
	}
	for _, p := range msaq.order {
		p(selector)
	}
	if offset := msaq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := msaq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// MatrixSpectrumAlertGroupBy is the group-by builder for MatrixSpectrumAlert entities.
type MatrixSpectrumAlertGroupBy struct {
	selector
	build *MatrixSpectrumAlertQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (msagb *MatrixSpectrumAlertGroupBy) Aggregate(fns ...AggregateFunc) *MatrixSpectrumAlertGroupBy {
	msagb.fns = append(msagb.fns, fns...)
	return msagb
}

// Scan applies the selector query and scans the result into the given value.
func (msagb *MatrixSpectrumAlertGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, msagb.build.ctx, "GroupBy")
	if err := msagb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MatrixSpectrumAlertQuery, *MatrixSpectrumAlertGroupBy](ctx, msagb.build, msagb, msagb.build.inters, v)
}

func (msagb *MatrixSpectrumAlertGroupBy) sqlScan(ctx context.Context, root *MatrixSpectrumAlertQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(msagb.fns))
	for _, fn := range msagb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*msagb.flds)+len(msagb.fns))
		for _, f := range *msagb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*msagb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := msagb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// MatrixSpectrumAlertSelect is the builder for selecting fields of MatrixSpectrumAlert entities.
type MatrixSpectrumAlertSelect struct {
	*MatrixSpectrumAlertQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (msas *MatrixSpectrumAlertSelect) Aggregate(fns ...AggregateFunc) *MatrixSpectrumAlertSelect {
	msas.fns = append(msas.fns, fns...)
	return msas
}

// Scan applies the selector query and scans the result into the given value.
func (msas *MatrixSpectrumAlertSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, msas.ctx, "Select")
	if err := msas.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MatrixSpectrumAlertQuery, *MatrixSpectrumAlertSelect](ctx, msas.MatrixSpectrumAlertQuery, msas, msas.inters, v)
}

func (msas *MatrixSpectrumAlertSelect) sqlScan(ctx context.Context, root *MatrixSpectrumAlertQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(msas.fns))
	for _, fn := range msas.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*msas.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := msas.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
