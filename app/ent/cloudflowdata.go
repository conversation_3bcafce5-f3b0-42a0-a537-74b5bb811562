// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// CloudFlowData is the model entity for the CloudFlowData schema.
type CloudFlowData struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// CloudAlertID holds the value of the "cloud_alert_id" field.
	CloudAlertID *int `json:"cloud_alert_id,omitempty"`
	// 来源IP
	SrcIP string `json:"src_ip,omitempty" query:"src_ip,omitempty"`
	// 来源端口
	SrcPort int `json:"src_port,omitempty" query:"src_port,omitempty"`
	// 被攻击IP
	DstIP string `json:"dst_ip,omitempty" query:"dst_ip,omitempty"`
	// 被攻击端口
	DstPort int `json:"dst_port,omitempty" query:"dst_port,omitempty"`
	// 4层协议：6tcp，17udp
	Protocol int `json:"protocol,omitempty"`
	// 从攻击开始到攻击结束，最大的pps
	MaxAttackPps int64 `json:"max_attack_pps,omitempty" query:"max_attack_pps,omitempty"`
	// 总pps>1000的次数
	FlowOverMaxPpsCount int `json:"flow_over_max_pps_count,omitempty" query:"flow_over_max_pps_count,omitempty"`
	// 攻击开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 上一个报文到达的时间
	EndTime time.Time `json:"end_time,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the CloudFlowDataQuery when eager-loading is set.
	Edges        CloudFlowDataEdges `json:"edges"`
	selectValues sql.SelectValues
}

// CloudFlowDataEdges holds the relations/edges for other nodes in the graph.
type CloudFlowDataEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// CloudAlert holds the value of the cloud_alert edge.
	CloudAlert *CloudAlert `json:"cloud_alert,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CloudFlowDataEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// CloudAlertOrErr returns the CloudAlert value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CloudFlowDataEdges) CloudAlertOrErr() (*CloudAlert, error) {
	if e.loadedTypes[1] {
		if e.CloudAlert == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: cloudalert.Label}
		}
		return e.CloudAlert, nil
	}
	return nil, &NotLoadedError{edge: "cloud_alert"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CloudFlowData) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case cloudflowdata.FieldID, cloudflowdata.FieldTenantID, cloudflowdata.FieldCloudAlertID, cloudflowdata.FieldSrcPort, cloudflowdata.FieldDstPort, cloudflowdata.FieldProtocol, cloudflowdata.FieldMaxAttackPps, cloudflowdata.FieldFlowOverMaxPpsCount:
			values[i] = new(sql.NullInt64)
		case cloudflowdata.FieldRemark, cloudflowdata.FieldSrcIP, cloudflowdata.FieldDstIP:
			values[i] = new(sql.NullString)
		case cloudflowdata.FieldCreatedAt, cloudflowdata.FieldUpdatedAt, cloudflowdata.FieldStartTime, cloudflowdata.FieldEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CloudFlowData fields.
func (cfd *CloudFlowData) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case cloudflowdata.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			cfd.ID = int(value.Int64)
		case cloudflowdata.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				cfd.TenantID = new(int)
				*cfd.TenantID = int(value.Int64)
			}
		case cloudflowdata.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				cfd.CreatedAt = value.Time
			}
		case cloudflowdata.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				cfd.UpdatedAt = value.Time
			}
		case cloudflowdata.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				cfd.Remark = new(string)
				*cfd.Remark = value.String
			}
		case cloudflowdata.FieldCloudAlertID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field cloud_alert_id", values[i])
			} else if value.Valid {
				cfd.CloudAlertID = new(int)
				*cfd.CloudAlertID = int(value.Int64)
			}
		case cloudflowdata.FieldSrcIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field src_ip", values[i])
			} else if value.Valid {
				cfd.SrcIP = value.String
			}
		case cloudflowdata.FieldSrcPort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field src_port", values[i])
			} else if value.Valid {
				cfd.SrcPort = int(value.Int64)
			}
		case cloudflowdata.FieldDstIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field dst_ip", values[i])
			} else if value.Valid {
				cfd.DstIP = value.String
			}
		case cloudflowdata.FieldDstPort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dst_port", values[i])
			} else if value.Valid {
				cfd.DstPort = int(value.Int64)
			}
		case cloudflowdata.FieldProtocol:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field protocol", values[i])
			} else if value.Valid {
				cfd.Protocol = int(value.Int64)
			}
		case cloudflowdata.FieldMaxAttackPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_attack_pps", values[i])
			} else if value.Valid {
				cfd.MaxAttackPps = value.Int64
			}
		case cloudflowdata.FieldFlowOverMaxPpsCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field flow_over_max_pps_count", values[i])
			} else if value.Valid {
				cfd.FlowOverMaxPpsCount = int(value.Int64)
			}
		case cloudflowdata.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				cfd.StartTime = value.Time
			}
		case cloudflowdata.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				cfd.EndTime = value.Time
			}
		default:
			cfd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CloudFlowData.
// This includes values selected through modifiers, order, etc.
func (cfd *CloudFlowData) Value(name string) (ent.Value, error) {
	return cfd.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the CloudFlowData entity.
func (cfd *CloudFlowData) QueryTenant() *TenantQuery {
	return NewCloudFlowDataClient(cfd.config).QueryTenant(cfd)
}

// QueryCloudAlert queries the "cloud_alert" edge of the CloudFlowData entity.
func (cfd *CloudFlowData) QueryCloudAlert() *CloudAlertQuery {
	return NewCloudFlowDataClient(cfd.config).QueryCloudAlert(cfd)
}

// Update returns a builder for updating this CloudFlowData.
// Note that you need to call CloudFlowData.Unwrap() before calling this method if this CloudFlowData
// was returned from a transaction, and the transaction was committed or rolled back.
func (cfd *CloudFlowData) Update() *CloudFlowDataUpdateOne {
	return NewCloudFlowDataClient(cfd.config).UpdateOne(cfd)
}

// Unwrap unwraps the CloudFlowData entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cfd *CloudFlowData) Unwrap() *CloudFlowData {
	_tx, ok := cfd.config.driver.(*txDriver)
	if !ok {
		panic("ent: CloudFlowData is not a transactional entity")
	}
	cfd.config.driver = _tx.drv
	return cfd
}

// String implements the fmt.Stringer.
func (cfd *CloudFlowData) String() string {
	var builder strings.Builder
	builder.WriteString("CloudFlowData(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cfd.ID))
	if v := cfd.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(cfd.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(cfd.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := cfd.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := cfd.CloudAlertID; v != nil {
		builder.WriteString("cloud_alert_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("src_ip=")
	builder.WriteString(cfd.SrcIP)
	builder.WriteString(", ")
	builder.WriteString("src_port=")
	builder.WriteString(fmt.Sprintf("%v", cfd.SrcPort))
	builder.WriteString(", ")
	builder.WriteString("dst_ip=")
	builder.WriteString(cfd.DstIP)
	builder.WriteString(", ")
	builder.WriteString("dst_port=")
	builder.WriteString(fmt.Sprintf("%v", cfd.DstPort))
	builder.WriteString(", ")
	builder.WriteString("protocol=")
	builder.WriteString(fmt.Sprintf("%v", cfd.Protocol))
	builder.WriteString(", ")
	builder.WriteString("max_attack_pps=")
	builder.WriteString(fmt.Sprintf("%v", cfd.MaxAttackPps))
	builder.WriteString(", ")
	builder.WriteString("flow_over_max_pps_count=")
	builder.WriteString(fmt.Sprintf("%v", cfd.FlowOverMaxPpsCount))
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(cfd.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end_time=")
	builder.WriteString(cfd.EndTime.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// CloudFlowDataSlice is a parsable slice of CloudFlowData.
type CloudFlowDataSlice []*CloudFlowData
