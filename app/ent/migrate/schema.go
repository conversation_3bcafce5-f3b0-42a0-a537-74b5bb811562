// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// CasbinRulesColumns holds the columns for the "casbin_rules" table.
	CasbinRulesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "ptype", Type: field.TypeString},
		{Name: "v0", Type: field.TypeString},
		{Name: "v1", Type: field.TypeString},
		{Name: "v2", Type: field.TypeString},
		{Name: "v3", Type: field.TypeString},
		{Name: "v4", Type: field.TypeString},
		{Name: "v5", Type: field.TypeString},
	}
	// CasbinRulesTable holds the schema information for the "casbin_rules" table.
	CasbinRulesTable = &schema.Table{
		Name:       "casbin_rules",
		Columns:    <PERSON><PERSON>binRulesColumns,
		PrimaryKey: []*schema.Column{CasbinRulesColumns[0]},
	}
	// CleanDataColumns holds the columns for the "clean_data" table.
	CleanDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "ip", Type: field.TypeString},
		{Name: "time", Type: field.TypeTime},
		{Name: "in_bps", Type: field.TypeInt64},
		{Name: "out_bps", Type: field.TypeInt64},
		{Name: "in_pps", Type: field.TypeInt64},
		{Name: "out_pps", Type: field.TypeInt64},
		{Name: "in_ack_pps", Type: field.TypeInt64},
		{Name: "out_ack_pps", Type: field.TypeInt64},
		{Name: "in_ack_bps", Type: field.TypeInt64},
		{Name: "out_ack_bps", Type: field.TypeInt64},
		{Name: "in_syn_pps", Type: field.TypeInt64},
		{Name: "out_syn_pps", Type: field.TypeInt64},
		{Name: "in_udp_pps", Type: field.TypeInt64},
		{Name: "out_udp_pps", Type: field.TypeInt64},
		{Name: "in_udp_bps", Type: field.TypeInt64},
		{Name: "out_udp_bps", Type: field.TypeInt64},
		{Name: "in_icmp_pps", Type: field.TypeInt64},
		{Name: "in_icmp_bps", Type: field.TypeInt64},
		{Name: "out_icmp_bps", Type: field.TypeInt64},
		{Name: "out_icmp_pps", Type: field.TypeInt64},
		{Name: "in_dns_pps", Type: field.TypeInt64},
		{Name: "out_dns_pps", Type: field.TypeInt64},
		{Name: "in_dns_bps", Type: field.TypeInt64},
		{Name: "out_dns_bps", Type: field.TypeInt64},
		{Name: "c_filter_id", Type: field.TypeInt},
		{Name: "attack_flags", Type: field.TypeInt},
		{Name: "count", Type: field.TypeInt},
		{Name: "ip_type", Type: field.TypeInt},
		{Name: "c_filter", Type: field.TypeString, Nullable: true},
		{Name: "host", Type: field.TypeString, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
		{Name: "spectrum_alert_id", Type: field.TypeInt, Nullable: true},
	}
	// CleanDataTable holds the schema information for the "clean_data" table.
	CleanDataTable = &schema.Table{
		Name:       "clean_data",
		Columns:    CleanDataColumns,
		PrimaryKey: []*schema.Column{CleanDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "clean_data_tenants_tenant",
				Columns:    []*schema.Column{CleanDataColumns[32]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "clean_data_spectrum_alerts_clean_datas",
				Columns:    []*schema.Column{CleanDataColumns[33]},
				RefColumns: []*schema.Column{SpectrumAlertsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// CloudAlertsColumns holds the columns for the "cloud_alerts" table.
	CloudAlertsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "src_ip", Type: field.TypeString},
		{Name: "src_port", Type: field.TypeInt},
		{Name: "dst_ip", Type: field.TypeString},
		{Name: "dst_port", Type: field.TypeInt},
		{Name: "defence_mode", Type: field.TypeInt},
		{Name: "flow_mode", Type: field.TypeInt},
		{Name: "tcp_ack_num", Type: field.TypeString},
		{Name: "tcp_seq_num", Type: field.TypeString},
		{Name: "protocol", Type: field.TypeInt},
		{Name: "defence_level", Type: field.TypeInt},
		{Name: "max_pps", Type: field.TypeInt64},
		{Name: "max_attack_pps", Type: field.TypeInt64},
		{Name: "overlimit_pkt_count", Type: field.TypeInt},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// CloudAlertsTable holds the schema information for the "cloud_alerts" table.
	CloudAlertsTable = &schema.Table{
		Name:       "cloud_alerts",
		Columns:    CloudAlertsColumns,
		PrimaryKey: []*schema.Column{CloudAlertsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "cloud_alerts_tenants_tenant",
				Columns:    []*schema.Column{CloudAlertsColumns[19]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// CloudAttackDataColumns holds the columns for the "cloud_attack_data" table.
	CloudAttackDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "src_ip", Type: field.TypeString},
		{Name: "src_port", Type: field.TypeInt},
		{Name: "dst_ip", Type: field.TypeString},
		{Name: "dst_port", Type: field.TypeInt},
		{Name: "protocol", Type: field.TypeInt},
		{Name: "current_attack_pps", Type: field.TypeInt64},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// CloudAttackDataTable holds the schema information for the "cloud_attack_data" table.
	CloudAttackDataTable = &schema.Table{
		Name:       "cloud_attack_data",
		Columns:    CloudAttackDataColumns,
		PrimaryKey: []*schema.Column{CloudAttackDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "cloud_attack_data_tenants_tenant",
				Columns:    []*schema.Column{CloudAttackDataColumns[12]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// CloudFlowDataColumns holds the columns for the "cloud_flow_data" table.
	CloudFlowDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "src_ip", Type: field.TypeString},
		{Name: "src_port", Type: field.TypeInt},
		{Name: "dst_ip", Type: field.TypeString},
		{Name: "dst_port", Type: field.TypeInt},
		{Name: "protocol", Type: field.TypeInt},
		{Name: "max_attack_pps", Type: field.TypeInt64},
		{Name: "flow_over_max_pps_count", Type: field.TypeInt},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "cloud_alert_id", Type: field.TypeInt, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// CloudFlowDataTable holds the schema information for the "cloud_flow_data" table.
	CloudFlowDataTable = &schema.Table{
		Name:       "cloud_flow_data",
		Columns:    CloudFlowDataColumns,
		PrimaryKey: []*schema.Column{CloudFlowDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "cloud_flow_data_cloud_alerts_cloudflow_datas",
				Columns:    []*schema.Column{CloudFlowDataColumns[13]},
				RefColumns: []*schema.Column{CloudAlertsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "cloud_flow_data_tenants_tenant",
				Columns:    []*schema.Column{CloudFlowDataColumns[14]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// DataSyncsColumns holds the columns for the "data_syncs" table.
	DataSyncsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "pre_data_list", Type: field.TypeJSON, Nullable: true},
		{Name: "data_list", Type: field.TypeJSON, Nullable: true},
		{Name: "data_type", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
	}
	// DataSyncsTable holds the schema information for the "data_syncs" table.
	DataSyncsTable = &schema.Table{
		Name:       "data_syncs",
		Columns:    DataSyncsColumns,
		PrimaryKey: []*schema.Column{DataSyncsColumns[0]},
	}
	// GroupsColumns holds the columns for the "groups" table.
	GroupsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "name", Type: field.TypeString},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// GroupsTable holds the schema information for the "groups" table.
	GroupsTable = &schema.Table{
		Name:       "groups",
		Columns:    GroupsColumns,
		PrimaryKey: []*schema.Column{GroupsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "groups_tenants_tenant",
				Columns:    []*schema.Column{GroupsColumns[2]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// MatrixSpectrumAlertsColumns holds the columns for the "matrix_spectrum_alerts" table.
	MatrixSpectrumAlertsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "ip_list", Type: field.TypeJSON, Nullable: true},
		{Name: "region", Type: field.TypeString},
		{Name: "net_type", Type: field.TypeString},
		{Name: "isp", Type: field.TypeString},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "attack_type", Type: field.TypeString},
		{Name: "bps", Type: field.TypeInt64},
		{Name: "attack_info", Type: field.TypeJSON, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
		{Name: "matrix_strategy_id", Type: field.TypeInt, Nullable: true},
		{Name: "wofang_id", Type: field.TypeInt, Nullable: true},
	}
	// MatrixSpectrumAlertsTable holds the schema information for the "matrix_spectrum_alerts" table.
	MatrixSpectrumAlertsTable = &schema.Table{
		Name:       "matrix_spectrum_alerts",
		Columns:    MatrixSpectrumAlertsColumns,
		PrimaryKey: []*schema.Column{MatrixSpectrumAlertsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "matrix_spectrum_alerts_tenants_tenant",
				Columns:    []*schema.Column{MatrixSpectrumAlertsColumns[13]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "matrix_spectrum_alerts_matrix_strategies_matrix_strategy_alerts",
				Columns:    []*schema.Column{MatrixSpectrumAlertsColumns[14]},
				RefColumns: []*schema.Column{MatrixStrategiesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "matrix_spectrum_alerts_wofangs_matrix_spectrum_alerts",
				Columns:    []*schema.Column{MatrixSpectrumAlertsColumns[15]},
				RefColumns: []*schema.Column{WofangsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// MatrixSpectrumDataColumns holds the columns for the "matrix_spectrum_data" table.
	MatrixSpectrumDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "region", Type: field.TypeString},
		{Name: "net_type", Type: field.TypeString},
		{Name: "isp", Type: field.TypeString},
		{Name: "bps", Type: field.TypeInt64},
		{Name: "time", Type: field.TypeTime},
		{Name: "matrix_spectrum_alert_id", Type: field.TypeInt, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// MatrixSpectrumDataTable holds the schema information for the "matrix_spectrum_data" table.
	MatrixSpectrumDataTable = &schema.Table{
		Name:       "matrix_spectrum_data",
		Columns:    MatrixSpectrumDataColumns,
		PrimaryKey: []*schema.Column{MatrixSpectrumDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "matrix_spectrum_data_matrix_spectrum_alerts_matrix_spectrum_datas",
				Columns:    []*schema.Column{MatrixSpectrumDataColumns[8]},
				RefColumns: []*schema.Column{MatrixSpectrumAlertsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "matrix_spectrum_data_tenants_tenant",
				Columns:    []*schema.Column{MatrixSpectrumDataColumns[9]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// MatrixStrategiesColumns holds the columns for the "matrix_strategies" table.
	MatrixStrategiesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "name", Type: field.TypeString},
		{Name: "region", Type: field.TypeString},
		{Name: "net_type", Type: field.TypeString},
		{Name: "isp", Type: field.TypeString},
		{Name: "monitor_bps", Type: field.TypeInt64},
		{Name: "drag_bps", Type: field.TypeInt64},
		{Name: "drag_type", Type: field.TypeInt},
	}
	// MatrixStrategiesTable holds the schema information for the "matrix_strategies" table.
	MatrixStrategiesTable = &schema.Table{
		Name:       "matrix_strategies",
		Columns:    MatrixStrategiesColumns,
		PrimaryKey: []*schema.Column{MatrixStrategiesColumns[0]},
	}
	// NotifiesColumns holds the columns for the "notifies" table.
	NotifiesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "name", Type: field.TypeString},
		{Name: "popo", Type: field.TypeBool},
		{Name: "email", Type: field.TypeBool},
		{Name: "sms", Type: field.TypeBool},
		{Name: "phone", Type: field.TypeBool},
		{Name: "popo_groups", Type: field.TypeJSON},
		{Name: "emails", Type: field.TypeJSON},
		{Name: "phones", Type: field.TypeJSON},
		{Name: "ip_whitelists", Type: field.TypeJSON},
		{Name: "system", Type: field.TypeBool},
		{Name: "enabled", Type: field.TypeBool},
		{Name: "sa_notify_popo", Type: field.TypeBool},
		{Name: "sa_notify_email", Type: field.TypeBool},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// NotifiesTable holds the schema information for the "notifies" table.
	NotifiesTable = &schema.Table{
		Name:       "notifies",
		Columns:    NotifiesColumns,
		PrimaryKey: []*schema.Column{NotifiesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "notifies_tenants_tenant",
				Columns:    []*schema.Column{NotifiesColumns[17]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// ProtectGroupsColumns holds the columns for the "protect_groups" table.
	ProtectGroupsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "group_name", Type: field.TypeString},
		{Name: "group_id", Type: field.TypeInt64},
		{Name: "type", Type: field.TypeInt},
		{Name: "ip_list", Type: field.TypeJSON, Nullable: true},
		{Name: "expand_ip", Type: field.TypeString, Nullable: true, Size: 4294967295},
		{Name: "monitor_info", Type: field.TypeJSON, Nullable: true},
		{Name: "drag_info", Type: field.TypeJSON, Nullable: true},
		{Name: "nds4_config", Type: field.TypeJSON, Nullable: true},
		{Name: "nds6_config", Type: field.TypeJSON, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// ProtectGroupsTable holds the schema information for the "protect_groups" table.
	ProtectGroupsTable = &schema.Table{
		Name:       "protect_groups",
		Columns:    ProtectGroupsColumns,
		PrimaryKey: []*schema.Column{ProtectGroupsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "protect_groups_tenants_tenant",
				Columns:    []*schema.Column{ProtectGroupsColumns[13]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SkylineDosColumns holds the columns for the "skyline_dos" table.
	SkylineDosColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "region", Type: field.TypeString},
		{Name: "resource", Type: field.TypeString},
		{Name: "resource_type", Type: field.TypeString},
		{Name: "vector_types", Type: field.TypeJSON, Nullable: true},
		{Name: "status", Type: field.TypeString},
		{Name: "attack_id", Type: field.TypeString},
		{Name: "attack_counters", Type: field.TypeJSON, Nullable: true},
		{Name: "project", Type: field.TypeString},
		{Name: "duration_time", Type: field.TypeInt64},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// SkylineDosTable holds the schema information for the "skyline_dos" table.
	SkylineDosTable = &schema.Table{
		Name:       "skyline_dos",
		Columns:    SkylineDosColumns,
		PrimaryKey: []*schema.Column{SkylineDosColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "skyline_dos_tenants_tenant",
				Columns:    []*schema.Column{SkylineDosColumns[15]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SocGroupTicketsColumns holds the columns for the "soc_group_tickets" table.
	SocGroupTicketsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "name", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "description", Type: field.TypeString, Size: 1000},
		{Name: "follow_list", Type: field.TypeJSON, Nullable: true},
		{Name: "department_id", Type: field.TypeInt},
		{Name: "ip_list", Type: field.TypeJSON, Nullable: true},
		{Name: "min_bandwidth", Type: field.TypeFloat32},
		{Name: "divert_type", Type: field.TypeInt},
		{Name: "op_type", Type: field.TypeInt},
		{Name: "op_time", Type: field.TypeTime, Nullable: true},
		{Name: "config_type", Type: field.TypeInt},
		{Name: "config_args", Type: field.TypeString, Size: 1000},
		{Name: "product_name", Type: field.TypeString},
		{Name: "product_code", Type: field.TypeString, Nullable: true},
		{Name: "contact_list", Type: field.TypeJSON, Nullable: true},
		{Name: "group_ticket_id", Type: field.TypeInt, Nullable: true},
		{Name: "error_info", Type: field.TypeString, Nullable: true, Size: 1000},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
		{Name: "create_user_id", Type: field.TypeInt, Nullable: true},
	}
	// SocGroupTicketsTable holds the schema information for the "soc_group_tickets" table.
	SocGroupTicketsTable = &schema.Table{
		Name:       "soc_group_tickets",
		Columns:    SocGroupTicketsColumns,
		PrimaryKey: []*schema.Column{SocGroupTicketsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "soc_group_tickets_tenants_tenant",
				Columns:    []*schema.Column{SocGroupTicketsColumns[21]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "soc_group_tickets_users_user",
				Columns:    []*schema.Column{SocGroupTicketsColumns[22]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SpectrumAlertsColumns holds the columns for the "spectrum_alerts" table.
	SpectrumAlertsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "protect_status", Type: field.TypeJSON, Nullable: true},
		{Name: "ip", Type: field.TypeString},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "attack_type", Type: field.TypeString},
		{Name: "max_pps", Type: field.TypeInt64},
		{Name: "max_bps", Type: field.TypeInt64},
		{Name: "attack_info", Type: field.TypeJSON, Nullable: true},
		{Name: "isp_code", Type: field.TypeInt},
		{Name: "protect_group_id", Type: field.TypeInt, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
		{Name: "strategy_id", Type: field.TypeInt, Nullable: true},
		{Name: "wofang_id", Type: field.TypeInt, Nullable: true},
	}
	// SpectrumAlertsTable holds the schema information for the "spectrum_alerts" table.
	SpectrumAlertsTable = &schema.Table{
		Name:       "spectrum_alerts",
		Columns:    SpectrumAlertsColumns,
		PrimaryKey: []*schema.Column{SpectrumAlertsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "spectrum_alerts_protect_groups_spectrum_alerts",
				Columns:    []*schema.Column{SpectrumAlertsColumns[13]},
				RefColumns: []*schema.Column{ProtectGroupsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "spectrum_alerts_tenants_tenant",
				Columns:    []*schema.Column{SpectrumAlertsColumns[14]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "spectrum_alerts_strategies_strategy_alerts",
				Columns:    []*schema.Column{SpectrumAlertsColumns[15]},
				RefColumns: []*schema.Column{StrategiesColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "spectrum_alerts_wofangs_spectrum_alerts",
				Columns:    []*schema.Column{SpectrumAlertsColumns[16]},
				RefColumns: []*schema.Column{WofangsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SpectrumDataColumns holds the columns for the "spectrum_data" table.
	SpectrumDataColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "ip", Type: field.TypeString},
		{Name: "time", Type: field.TypeTime},
		{Name: "monitor_id", Type: field.TypeInt},
		{Name: "data_type", Type: field.TypeInt},
		{Name: "bps", Type: field.TypeInt64},
		{Name: "pps", Type: field.TypeInt64},
		{Name: "syn_bps", Type: field.TypeInt64},
		{Name: "syn_pps", Type: field.TypeInt64},
		{Name: "ack_bps", Type: field.TypeInt64},
		{Name: "ack_pps", Type: field.TypeInt64},
		{Name: "syn_ack_bps", Type: field.TypeInt64},
		{Name: "syn_ack_pps", Type: field.TypeInt64},
		{Name: "icmp_bps", Type: field.TypeInt64},
		{Name: "icmp_pps", Type: field.TypeInt64},
		{Name: "small_pps", Type: field.TypeInt64},
		{Name: "ntp_pps", Type: field.TypeInt64},
		{Name: "ntp_bps", Type: field.TypeInt64},
		{Name: "dns_query_pps", Type: field.TypeInt64},
		{Name: "dns_query_bps", Type: field.TypeInt64},
		{Name: "dns_answer_pps", Type: field.TypeInt64},
		{Name: "dns_answer_bps", Type: field.TypeInt64},
		{Name: "ssdp_bps", Type: field.TypeInt64},
		{Name: "ssdp_pps", Type: field.TypeInt64},
		{Name: "udp_pps", Type: field.TypeInt64},
		{Name: "udp_bps", Type: field.TypeInt64},
		{Name: "qps", Type: field.TypeInt64},
		{Name: "receive_count", Type: field.TypeInt},
		{Name: "ip_type", Type: field.TypeInt},
		{Name: "monitor", Type: field.TypeString, Nullable: true},
		{Name: "product", Type: field.TypeString, Nullable: true},
		{Name: "host", Type: field.TypeString, Nullable: true},
		{Name: "spectrum_alert_id", Type: field.TypeInt, Nullable: true},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// SpectrumDataTable holds the schema information for the "spectrum_data" table.
	SpectrumDataTable = &schema.Table{
		Name:       "spectrum_data",
		Columns:    SpectrumDataColumns,
		PrimaryKey: []*schema.Column{SpectrumDataColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "spectrum_data_spectrum_alerts_spectrum_datas",
				Columns:    []*schema.Column{SpectrumDataColumns[33]},
				RefColumns: []*schema.Column{SpectrumAlertsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "spectrum_data_tenants_tenant",
				Columns:    []*schema.Column{SpectrumDataColumns[34]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// StrategiesColumns holds the columns for the "strategies" table.
	StrategiesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "name", Type: field.TypeString},
		{Name: "type", Type: field.TypeInt},
		{Name: "enabled", Type: field.TypeBool},
		{Name: "system", Type: field.TypeBool},
		{Name: "bps", Type: field.TypeInt64},
		{Name: "pps", Type: field.TypeInt64},
		{Name: "bps_count", Type: field.TypeInt},
		{Name: "pps_count", Type: field.TypeInt},
		{Name: "isp_code", Type: field.TypeInt},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// StrategiesTable holds the schema information for the "strategies" table.
	StrategiesTable = &schema.Table{
		Name:       "strategies",
		Columns:    StrategiesColumns,
		PrimaryKey: []*schema.Column{StrategiesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "strategies_tenants_tenant",
				Columns:    []*schema.Column{StrategiesColumns[13]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SystemApisColumns holds the columns for the "system_apis" table.
	SystemApisColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "name", Type: field.TypeString},
		{Name: "path", Type: field.TypeString},
		{Name: "http_method", Type: field.TypeString},
		{Name: "roles", Type: field.TypeJSON},
		{Name: "public", Type: field.TypeBool},
		{Name: "sa", Type: field.TypeBool},
	}
	// SystemApisTable holds the schema information for the "system_apis" table.
	SystemApisTable = &schema.Table{
		Name:       "system_apis",
		Columns:    SystemApisColumns,
		PrimaryKey: []*schema.Column{SystemApisColumns[0]},
	}
	// SystemConfigsColumns holds the columns for the "system_configs" table.
	SystemConfigsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "wofang_test_ip", Type: field.TypeString},
		{Name: "notify_phones", Type: field.TypeJSON},
		{Name: "notify_emails", Type: field.TypeJSON},
		{Name: "notify_scenes", Type: field.TypeJSON},
		{Name: "ip_whitelists", Type: field.TypeJSON},
	}
	// SystemConfigsTable holds the schema information for the "system_configs" table.
	SystemConfigsTable = &schema.Table{
		Name:       "system_configs",
		Columns:    SystemConfigsColumns,
		PrimaryKey: []*schema.Column{SystemConfigsColumns[0]},
	}
	// TenantsColumns holds the columns for the "tenants" table.
	TenantsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "name", Type: field.TypeString},
		{Name: "code", Type: field.TypeString, Unique: true},
		{Name: "offline", Type: field.TypeBool},
		{Name: "isdefend", Type: field.TypeBool},
	}
	// TenantsTable holds the schema information for the "tenants" table.
	TenantsTable = &schema.Table{
		Name:       "tenants",
		Columns:    TenantsColumns,
		PrimaryKey: []*schema.Column{TenantsColumns[0]},
	}
	// UsersColumns holds the columns for the "users" table.
	UsersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "valid", Type: field.TypeBool, Default: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString, Unique: true},
		{Name: "password", Type: field.TypeString},
		{Name: "super_admin", Type: field.TypeBool, Default: false},
		{Name: "update_auth", Type: field.TypeBool, Default: false},
	}
	// UsersTable holds the schema information for the "users" table.
	UsersTable = &schema.Table{
		Name:       "users",
		Columns:    UsersColumns,
		PrimaryKey: []*schema.Column{UsersColumns[0]},
	}
	// UserOperationLogsColumns holds the columns for the "user_operation_logs" table.
	UserOperationLogsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "username", Type: field.TypeString},
		{Name: "method", Type: field.TypeString},
		{Name: "request_id", Type: field.TypeString},
		{Name: "uri", Type: field.TypeString, Size: 500},
		{Name: "request_body", Type: field.TypeString, Size: 2147483647},
		{Name: "project", Type: field.TypeString},
	}
	// UserOperationLogsTable holds the schema information for the "user_operation_logs" table.
	UserOperationLogsTable = &schema.Table{
		Name:       "user_operation_logs",
		Columns:    UserOperationLogsColumns,
		PrimaryKey: []*schema.Column{UserOperationLogsColumns[0]},
	}
	// WofangsColumns holds the columns for the "wofangs" table.
	WofangsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "name", Type: field.TypeString},
		{Name: "ip", Type: field.TypeString},
		{Name: "type", Type: field.TypeString},
		{Name: "un_drag_second", Type: field.TypeInt},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "error_info", Type: field.TypeString, Size: 500},
		{Name: "status", Type: field.TypeString},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
		{Name: "create_user_id", Type: field.TypeInt, Nullable: true},
	}
	// WofangsTable holds the schema information for the "wofangs" table.
	WofangsTable = &schema.Table{
		Name:       "wofangs",
		Columns:    WofangsColumns,
		PrimaryKey: []*schema.Column{WofangsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wofangs_tenants_tenant",
				Columns:    []*schema.Column{WofangsColumns[11]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "wofangs_users_user",
				Columns:    []*schema.Column{WofangsColumns[12]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// WofangAlertsColumns holds the columns for the "wofang_alerts" table.
	WofangAlertsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "remark", Type: field.TypeString, Nullable: true, Size: 500},
		{Name: "attack_status", Type: field.TypeInt},
		{Name: "attack_type", Type: field.TypeJSON, Nullable: true},
		{Name: "device_ip", Type: field.TypeString},
		{Name: "zone_ip", Type: field.TypeString},
		{Name: "attack_id", Type: field.TypeInt},
		{Name: "start_time", Type: field.TypeTime},
		{Name: "end_time", Type: field.TypeTime, Nullable: true},
		{Name: "max_drop_bps", Type: field.TypeInt64},
		{Name: "max_in_bps", Type: field.TypeInt64},
		{Name: "tenant_id", Type: field.TypeInt, Nullable: true},
	}
	// WofangAlertsTable holds the schema information for the "wofang_alerts" table.
	WofangAlertsTable = &schema.Table{
		Name:       "wofang_alerts",
		Columns:    WofangAlertsColumns,
		PrimaryKey: []*schema.Column{WofangAlertsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "wofang_alerts_tenants_tenant",
				Columns:    []*schema.Column{WofangAlertsColumns[13]},
				RefColumns: []*schema.Column{TenantsColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// UserGroupsColumns holds the columns for the "user_groups" table.
	UserGroupsColumns = []*schema.Column{
		{Name: "user_id", Type: field.TypeInt},
		{Name: "group_id", Type: field.TypeInt},
	}
	// UserGroupsTable holds the schema information for the "user_groups" table.
	UserGroupsTable = &schema.Table{
		Name:       "user_groups",
		Columns:    UserGroupsColumns,
		PrimaryKey: []*schema.Column{UserGroupsColumns[0], UserGroupsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "user_groups_user_id",
				Columns:    []*schema.Column{UserGroupsColumns[0]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "user_groups_group_id",
				Columns:    []*schema.Column{UserGroupsColumns[1]},
				RefColumns: []*schema.Column{GroupsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		CasbinRulesTable,
		CleanDataTable,
		CloudAlertsTable,
		CloudAttackDataTable,
		CloudFlowDataTable,
		DataSyncsTable,
		GroupsTable,
		MatrixSpectrumAlertsTable,
		MatrixSpectrumDataTable,
		MatrixStrategiesTable,
		NotifiesTable,
		ProtectGroupsTable,
		SkylineDosTable,
		SocGroupTicketsTable,
		SpectrumAlertsTable,
		SpectrumDataTable,
		StrategiesTable,
		SystemApisTable,
		SystemConfigsTable,
		TenantsTable,
		UsersTable,
		UserOperationLogsTable,
		WofangsTable,
		WofangAlertsTable,
		UserGroupsTable,
	}
)

func init() {
	CleanDataTable.ForeignKeys[0].RefTable = TenantsTable
	CleanDataTable.ForeignKeys[1].RefTable = SpectrumAlertsTable
	CloudAlertsTable.ForeignKeys[0].RefTable = TenantsTable
	CloudAttackDataTable.ForeignKeys[0].RefTable = TenantsTable
	CloudFlowDataTable.ForeignKeys[0].RefTable = CloudAlertsTable
	CloudFlowDataTable.ForeignKeys[1].RefTable = TenantsTable
	GroupsTable.ForeignKeys[0].RefTable = TenantsTable
	MatrixSpectrumAlertsTable.ForeignKeys[0].RefTable = TenantsTable
	MatrixSpectrumAlertsTable.ForeignKeys[1].RefTable = MatrixStrategiesTable
	MatrixSpectrumAlertsTable.ForeignKeys[2].RefTable = WofangsTable
	MatrixSpectrumDataTable.ForeignKeys[0].RefTable = MatrixSpectrumAlertsTable
	MatrixSpectrumDataTable.ForeignKeys[1].RefTable = TenantsTable
	NotifiesTable.ForeignKeys[0].RefTable = TenantsTable
	ProtectGroupsTable.ForeignKeys[0].RefTable = TenantsTable
	SkylineDosTable.ForeignKeys[0].RefTable = TenantsTable
	SocGroupTicketsTable.ForeignKeys[0].RefTable = TenantsTable
	SocGroupTicketsTable.ForeignKeys[1].RefTable = UsersTable
	SpectrumAlertsTable.ForeignKeys[0].RefTable = ProtectGroupsTable
	SpectrumAlertsTable.ForeignKeys[1].RefTable = TenantsTable
	SpectrumAlertsTable.ForeignKeys[2].RefTable = StrategiesTable
	SpectrumAlertsTable.ForeignKeys[3].RefTable = WofangsTable
	SpectrumDataTable.ForeignKeys[0].RefTable = SpectrumAlertsTable
	SpectrumDataTable.ForeignKeys[1].RefTable = TenantsTable
	StrategiesTable.ForeignKeys[0].RefTable = TenantsTable
	WofangsTable.ForeignKeys[0].RefTable = TenantsTable
	WofangsTable.ForeignKeys[1].RefTable = UsersTable
	WofangAlertsTable.ForeignKeys[0].RefTable = TenantsTable
	UserGroupsTable.ForeignKeys[0].RefTable = UsersTable
	UserGroupsTable.ForeignKeys[1].RefTable = GroupsTable
}
