// Code generated by ent, DO NOT EDIT.

package systemconfig

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the systemconfig type in the database.
	Label = "system_config"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldWofangTestIP holds the string denoting the wofang_test_ip field in the database.
	FieldWofangTestIP = "wofang_test_ip"
	// FieldNotifyPhones holds the string denoting the notify_phones field in the database.
	FieldNotifyPhones = "notify_phones"
	// FieldNotifyEmails holds the string denoting the notify_emails field in the database.
	FieldNotifyEmails = "notify_emails"
	// FieldNotifyScenes holds the string denoting the notify_scenes field in the database.
	FieldNotifyScenes = "notify_scenes"
	// FieldIPWhitelists holds the string denoting the ip_whitelists field in the database.
	FieldIPWhitelists = "ip_whitelists"
	// Table holds the table name of the systemconfig in the database.
	Table = "system_configs"
)

// Columns holds all SQL columns for systemconfig fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldWofangTestIP,
	FieldNotifyPhones,
	FieldNotifyEmails,
	FieldNotifyScenes,
	FieldIPWhitelists,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the SystemConfig queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByWofangTestIP orders the results by the wofang_test_ip field.
func ByWofangTestIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWofangTestIP, opts...).ToFunc()
}
