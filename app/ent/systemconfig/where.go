// Code generated by ent, DO NOT EDIT.

package systemconfig

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldRemark, v))
}

// WofangTestIP applies equality check predicate on the "wofang_test_ip" field. It's identical to WofangTestIPEQ.
func WofangTestIP(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldWofangTestIP, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldContainsFold(FieldRemark, v))
}

// WofangTestIPEQ applies the EQ predicate on the "wofang_test_ip" field.
func WofangTestIPEQ(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEQ(FieldWofangTestIP, v))
}

// WofangTestIPNEQ applies the NEQ predicate on the "wofang_test_ip" field.
func WofangTestIPNEQ(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNEQ(FieldWofangTestIP, v))
}

// WofangTestIPIn applies the In predicate on the "wofang_test_ip" field.
func WofangTestIPIn(vs ...string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldIn(FieldWofangTestIP, vs...))
}

// WofangTestIPNotIn applies the NotIn predicate on the "wofang_test_ip" field.
func WofangTestIPNotIn(vs ...string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldNotIn(FieldWofangTestIP, vs...))
}

// WofangTestIPGT applies the GT predicate on the "wofang_test_ip" field.
func WofangTestIPGT(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGT(FieldWofangTestIP, v))
}

// WofangTestIPGTE applies the GTE predicate on the "wofang_test_ip" field.
func WofangTestIPGTE(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldGTE(FieldWofangTestIP, v))
}

// WofangTestIPLT applies the LT predicate on the "wofang_test_ip" field.
func WofangTestIPLT(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLT(FieldWofangTestIP, v))
}

// WofangTestIPLTE applies the LTE predicate on the "wofang_test_ip" field.
func WofangTestIPLTE(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldLTE(FieldWofangTestIP, v))
}

// WofangTestIPContains applies the Contains predicate on the "wofang_test_ip" field.
func WofangTestIPContains(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldContains(FieldWofangTestIP, v))
}

// WofangTestIPHasPrefix applies the HasPrefix predicate on the "wofang_test_ip" field.
func WofangTestIPHasPrefix(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldHasPrefix(FieldWofangTestIP, v))
}

// WofangTestIPHasSuffix applies the HasSuffix predicate on the "wofang_test_ip" field.
func WofangTestIPHasSuffix(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldHasSuffix(FieldWofangTestIP, v))
}

// WofangTestIPEqualFold applies the EqualFold predicate on the "wofang_test_ip" field.
func WofangTestIPEqualFold(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldEqualFold(FieldWofangTestIP, v))
}

// WofangTestIPContainsFold applies the ContainsFold predicate on the "wofang_test_ip" field.
func WofangTestIPContainsFold(v string) predicate.SystemConfig {
	return predicate.SystemConfig(sql.FieldContainsFold(FieldWofangTestIP, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SystemConfig) predicate.SystemConfig {
	return predicate.SystemConfig(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SystemConfig) predicate.SystemConfig {
	return predicate.SystemConfig(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SystemConfig) predicate.SystemConfig {
	return predicate.SystemConfig(sql.NotPredicates(p))
}
