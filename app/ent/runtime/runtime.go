// Code generated by ent, DO NOT EDIT.

package runtime

import (
	"context"
	"meta/app/ent/casbinrule"
	"meta/app/ent/cleandata"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/datasync"
	"meta/app/ent/group"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/notify"
	"meta/app/ent/protectgroup"
	"meta/app/ent/schema"
	"meta/app/ent/skylinedos"
	"meta/app/ent/socgroupticket"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/strategy"
	"meta/app/ent/systemapi"
	"meta/app/ent/systemconfig"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/ent/useroperationlog"
	"meta/app/ent/wofang"
	"meta/app/ent/wofangalert"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/privacy"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	casbinruleMixin := schema.CasbinRule{}.Mixin()
	casbinrule.Policy = privacy.NewPolicies(casbinruleMixin[0], schema.CasbinRule{})
	casbinrule.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := casbinrule.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	casbinruleFields := schema.CasbinRule{}.Fields()
	_ = casbinruleFields
	// casbinruleDescType is the schema descriptor for type field.
	casbinruleDescType := casbinruleFields[0].Descriptor()
	// casbinrule.TypeValidator is a validator for the "type" field. It is called by the builders before save.
	casbinrule.TypeValidator = casbinruleDescType.Validators[0].(func(string) error)
	// casbinruleDescSub is the schema descriptor for sub field.
	casbinruleDescSub := casbinruleFields[1].Descriptor()
	// casbinrule.SubValidator is a validator for the "sub" field. It is called by the builders before save.
	casbinrule.SubValidator = casbinruleDescSub.Validators[0].(func(string) error)
	// casbinruleDescDom is the schema descriptor for dom field.
	casbinruleDescDom := casbinruleFields[2].Descriptor()
	// casbinrule.DomValidator is a validator for the "dom" field. It is called by the builders before save.
	casbinrule.DomValidator = casbinruleDescDom.Validators[0].(func(string) error)
	// casbinruleDescObj is the schema descriptor for obj field.
	casbinruleDescObj := casbinruleFields[3].Descriptor()
	// casbinrule.ObjValidator is a validator for the "obj" field. It is called by the builders before save.
	casbinrule.ObjValidator = casbinruleDescObj.Validators[0].(func(string) error)
	cleandataMixin := schema.CleanData{}.Mixin()
	cleandata.Policy = privacy.NewPolicies(cleandataMixin[0], cleandataMixin[1], schema.CleanData{})
	cleandata.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := cleandata.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	cleandataMixinFields2 := cleandataMixin[2].Fields()
	_ = cleandataMixinFields2
	cleandataFields := schema.CleanData{}.Fields()
	_ = cleandataFields
	// cleandataDescCreatedAt is the schema descriptor for created_at field.
	cleandataDescCreatedAt := cleandataMixinFields2[0].Descriptor()
	// cleandata.DefaultCreatedAt holds the default value on creation for the created_at field.
	cleandata.DefaultCreatedAt = cleandataDescCreatedAt.Default.(func() time.Time)
	cloudalertMixin := schema.CloudAlert{}.Mixin()
	cloudalert.Policy = privacy.NewPolicies(cloudalertMixin[0], cloudalertMixin[1], schema.CloudAlert{})
	cloudalert.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := cloudalert.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	cloudalertMixinFields2 := cloudalertMixin[2].Fields()
	_ = cloudalertMixinFields2
	cloudalertMixinFields3 := cloudalertMixin[3].Fields()
	_ = cloudalertMixinFields3
	cloudalertFields := schema.CloudAlert{}.Fields()
	_ = cloudalertFields
	// cloudalertDescCreatedAt is the schema descriptor for created_at field.
	cloudalertDescCreatedAt := cloudalertMixinFields2[0].Descriptor()
	// cloudalert.DefaultCreatedAt holds the default value on creation for the created_at field.
	cloudalert.DefaultCreatedAt = cloudalertDescCreatedAt.Default.(func() time.Time)
	// cloudalertDescUpdatedAt is the schema descriptor for updated_at field.
	cloudalertDescUpdatedAt := cloudalertMixinFields2[1].Descriptor()
	// cloudalert.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	cloudalert.DefaultUpdatedAt = cloudalertDescUpdatedAt.Default.(func() time.Time)
	// cloudalert.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	cloudalert.UpdateDefaultUpdatedAt = cloudalertDescUpdatedAt.UpdateDefault.(func() time.Time)
	// cloudalertDescRemark is the schema descriptor for remark field.
	cloudalertDescRemark := cloudalertMixinFields3[0].Descriptor()
	// cloudalert.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	cloudalert.RemarkValidator = cloudalertDescRemark.Validators[0].(func(string) error)
	cloudattackdataMixin := schema.CloudAttackData{}.Mixin()
	cloudattackdata.Policy = privacy.NewPolicies(cloudattackdataMixin[0], cloudattackdataMixin[1], schema.CloudAttackData{})
	cloudattackdata.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := cloudattackdata.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	cloudattackdataMixinFields2 := cloudattackdataMixin[2].Fields()
	_ = cloudattackdataMixinFields2
	cloudattackdataMixinFields3 := cloudattackdataMixin[3].Fields()
	_ = cloudattackdataMixinFields3
	cloudattackdataFields := schema.CloudAttackData{}.Fields()
	_ = cloudattackdataFields
	// cloudattackdataDescCreatedAt is the schema descriptor for created_at field.
	cloudattackdataDescCreatedAt := cloudattackdataMixinFields2[0].Descriptor()
	// cloudattackdata.DefaultCreatedAt holds the default value on creation for the created_at field.
	cloudattackdata.DefaultCreatedAt = cloudattackdataDescCreatedAt.Default.(func() time.Time)
	// cloudattackdataDescUpdatedAt is the schema descriptor for updated_at field.
	cloudattackdataDescUpdatedAt := cloudattackdataMixinFields2[1].Descriptor()
	// cloudattackdata.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	cloudattackdata.DefaultUpdatedAt = cloudattackdataDescUpdatedAt.Default.(func() time.Time)
	// cloudattackdata.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	cloudattackdata.UpdateDefaultUpdatedAt = cloudattackdataDescUpdatedAt.UpdateDefault.(func() time.Time)
	// cloudattackdataDescRemark is the schema descriptor for remark field.
	cloudattackdataDescRemark := cloudattackdataMixinFields3[0].Descriptor()
	// cloudattackdata.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	cloudattackdata.RemarkValidator = cloudattackdataDescRemark.Validators[0].(func(string) error)
	cloudflowdataMixin := schema.CloudFlowData{}.Mixin()
	cloudflowdata.Policy = privacy.NewPolicies(cloudflowdataMixin[0], cloudflowdataMixin[1], schema.CloudFlowData{})
	cloudflowdata.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := cloudflowdata.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	cloudflowdataMixinFields2 := cloudflowdataMixin[2].Fields()
	_ = cloudflowdataMixinFields2
	cloudflowdataMixinFields3 := cloudflowdataMixin[3].Fields()
	_ = cloudflowdataMixinFields3
	cloudflowdataFields := schema.CloudFlowData{}.Fields()
	_ = cloudflowdataFields
	// cloudflowdataDescCreatedAt is the schema descriptor for created_at field.
	cloudflowdataDescCreatedAt := cloudflowdataMixinFields2[0].Descriptor()
	// cloudflowdata.DefaultCreatedAt holds the default value on creation for the created_at field.
	cloudflowdata.DefaultCreatedAt = cloudflowdataDescCreatedAt.Default.(func() time.Time)
	// cloudflowdataDescUpdatedAt is the schema descriptor for updated_at field.
	cloudflowdataDescUpdatedAt := cloudflowdataMixinFields2[1].Descriptor()
	// cloudflowdata.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	cloudflowdata.DefaultUpdatedAt = cloudflowdataDescUpdatedAt.Default.(func() time.Time)
	// cloudflowdata.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	cloudflowdata.UpdateDefaultUpdatedAt = cloudflowdataDescUpdatedAt.UpdateDefault.(func() time.Time)
	// cloudflowdataDescRemark is the schema descriptor for remark field.
	cloudflowdataDescRemark := cloudflowdataMixinFields3[0].Descriptor()
	// cloudflowdata.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	cloudflowdata.RemarkValidator = cloudflowdataDescRemark.Validators[0].(func(string) error)
	datasyncMixin := schema.DataSync{}.Mixin()
	datasync.Policy = privacy.NewPolicies(datasyncMixin[0], schema.DataSync{})
	datasync.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := datasync.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	datasyncMixinFields1 := datasyncMixin[1].Fields()
	_ = datasyncMixinFields1
	datasyncMixinFields2 := datasyncMixin[2].Fields()
	_ = datasyncMixinFields2
	datasyncFields := schema.DataSync{}.Fields()
	_ = datasyncFields
	// datasyncDescCreatedAt is the schema descriptor for created_at field.
	datasyncDescCreatedAt := datasyncMixinFields1[0].Descriptor()
	// datasync.DefaultCreatedAt holds the default value on creation for the created_at field.
	datasync.DefaultCreatedAt = datasyncDescCreatedAt.Default.(func() time.Time)
	// datasyncDescUpdatedAt is the schema descriptor for updated_at field.
	datasyncDescUpdatedAt := datasyncMixinFields1[1].Descriptor()
	// datasync.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	datasync.DefaultUpdatedAt = datasyncDescUpdatedAt.Default.(func() time.Time)
	// datasync.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	datasync.UpdateDefaultUpdatedAt = datasyncDescUpdatedAt.UpdateDefault.(func() time.Time)
	// datasyncDescRemark is the schema descriptor for remark field.
	datasyncDescRemark := datasyncMixinFields2[0].Descriptor()
	// datasync.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	datasync.RemarkValidator = datasyncDescRemark.Validators[0].(func(string) error)
	groupMixin := schema.Group{}.Mixin()
	group.Policy = privacy.NewPolicies(groupMixin[0], groupMixin[1], schema.Group{})
	group.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := group.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	matrixspectrumalertMixin := schema.MatrixSpectrumAlert{}.Mixin()
	matrixspectrumalert.Policy = privacy.NewPolicies(matrixspectrumalertMixin[0], matrixspectrumalertMixin[1], schema.MatrixSpectrumAlert{})
	matrixspectrumalert.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := matrixspectrumalert.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	matrixspectrumalertMixinFields2 := matrixspectrumalertMixin[2].Fields()
	_ = matrixspectrumalertMixinFields2
	matrixspectrumalertMixinFields3 := matrixspectrumalertMixin[3].Fields()
	_ = matrixspectrumalertMixinFields3
	matrixspectrumalertFields := schema.MatrixSpectrumAlert{}.Fields()
	_ = matrixspectrumalertFields
	// matrixspectrumalertDescCreatedAt is the schema descriptor for created_at field.
	matrixspectrumalertDescCreatedAt := matrixspectrumalertMixinFields2[0].Descriptor()
	// matrixspectrumalert.DefaultCreatedAt holds the default value on creation for the created_at field.
	matrixspectrumalert.DefaultCreatedAt = matrixspectrumalertDescCreatedAt.Default.(func() time.Time)
	// matrixspectrumalertDescUpdatedAt is the schema descriptor for updated_at field.
	matrixspectrumalertDescUpdatedAt := matrixspectrumalertMixinFields2[1].Descriptor()
	// matrixspectrumalert.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	matrixspectrumalert.DefaultUpdatedAt = matrixspectrumalertDescUpdatedAt.Default.(func() time.Time)
	// matrixspectrumalert.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	matrixspectrumalert.UpdateDefaultUpdatedAt = matrixspectrumalertDescUpdatedAt.UpdateDefault.(func() time.Time)
	// matrixspectrumalertDescRemark is the schema descriptor for remark field.
	matrixspectrumalertDescRemark := matrixspectrumalertMixinFields3[0].Descriptor()
	// matrixspectrumalert.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	matrixspectrumalert.RemarkValidator = matrixspectrumalertDescRemark.Validators[0].(func(string) error)
	matrixspectrumdataMixin := schema.MatrixSpectrumData{}.Mixin()
	matrixspectrumdata.Policy = privacy.NewPolicies(matrixspectrumdataMixin[0], matrixspectrumdataMixin[1], schema.MatrixSpectrumData{})
	matrixspectrumdata.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := matrixspectrumdata.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	matrixspectrumdataMixinFields2 := matrixspectrumdataMixin[2].Fields()
	_ = matrixspectrumdataMixinFields2
	matrixspectrumdataFields := schema.MatrixSpectrumData{}.Fields()
	_ = matrixspectrumdataFields
	// matrixspectrumdataDescCreatedAt is the schema descriptor for created_at field.
	matrixspectrumdataDescCreatedAt := matrixspectrumdataMixinFields2[0].Descriptor()
	// matrixspectrumdata.DefaultCreatedAt holds the default value on creation for the created_at field.
	matrixspectrumdata.DefaultCreatedAt = matrixspectrumdataDescCreatedAt.Default.(func() time.Time)
	// matrixspectrumdataDescUpdatedAt is the schema descriptor for updated_at field.
	matrixspectrumdataDescUpdatedAt := matrixspectrumdataMixinFields2[1].Descriptor()
	// matrixspectrumdata.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	matrixspectrumdata.DefaultUpdatedAt = matrixspectrumdataDescUpdatedAt.Default.(func() time.Time)
	// matrixspectrumdata.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	matrixspectrumdata.UpdateDefaultUpdatedAt = matrixspectrumdataDescUpdatedAt.UpdateDefault.(func() time.Time)
	matrixstrategyMixin := schema.MatrixStrategy{}.Mixin()
	matrixstrategy.Policy = privacy.NewPolicies(matrixstrategyMixin[0], schema.MatrixStrategy{})
	matrixstrategy.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := matrixstrategy.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	matrixstrategyMixinFields1 := matrixstrategyMixin[1].Fields()
	_ = matrixstrategyMixinFields1
	matrixstrategyMixinFields2 := matrixstrategyMixin[2].Fields()
	_ = matrixstrategyMixinFields2
	matrixstrategyFields := schema.MatrixStrategy{}.Fields()
	_ = matrixstrategyFields
	// matrixstrategyDescCreatedAt is the schema descriptor for created_at field.
	matrixstrategyDescCreatedAt := matrixstrategyMixinFields1[0].Descriptor()
	// matrixstrategy.DefaultCreatedAt holds the default value on creation for the created_at field.
	matrixstrategy.DefaultCreatedAt = matrixstrategyDescCreatedAt.Default.(func() time.Time)
	// matrixstrategyDescUpdatedAt is the schema descriptor for updated_at field.
	matrixstrategyDescUpdatedAt := matrixstrategyMixinFields1[1].Descriptor()
	// matrixstrategy.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	matrixstrategy.DefaultUpdatedAt = matrixstrategyDescUpdatedAt.Default.(func() time.Time)
	// matrixstrategy.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	matrixstrategy.UpdateDefaultUpdatedAt = matrixstrategyDescUpdatedAt.UpdateDefault.(func() time.Time)
	// matrixstrategyDescRemark is the schema descriptor for remark field.
	matrixstrategyDescRemark := matrixstrategyMixinFields2[0].Descriptor()
	// matrixstrategy.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	matrixstrategy.RemarkValidator = matrixstrategyDescRemark.Validators[0].(func(string) error)
	// matrixstrategyDescName is the schema descriptor for name field.
	matrixstrategyDescName := matrixstrategyFields[0].Descriptor()
	// matrixstrategy.NameValidator is a validator for the "name" field. It is called by the builders before save.
	matrixstrategy.NameValidator = matrixstrategyDescName.Validators[0].(func(string) error)
	notifyMixin := schema.Notify{}.Mixin()
	notify.Policy = privacy.NewPolicies(notifyMixin[0], notifyMixin[2], schema.Notify{})
	notify.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := notify.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	notifyMixinFields1 := notifyMixin[1].Fields()
	_ = notifyMixinFields1
	notifyMixinFields3 := notifyMixin[3].Fields()
	_ = notifyMixinFields3
	notifyFields := schema.Notify{}.Fields()
	_ = notifyFields
	// notifyDescCreatedAt is the schema descriptor for created_at field.
	notifyDescCreatedAt := notifyMixinFields1[0].Descriptor()
	// notify.DefaultCreatedAt holds the default value on creation for the created_at field.
	notify.DefaultCreatedAt = notifyDescCreatedAt.Default.(func() time.Time)
	// notifyDescUpdatedAt is the schema descriptor for updated_at field.
	notifyDescUpdatedAt := notifyMixinFields1[1].Descriptor()
	// notify.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	notify.DefaultUpdatedAt = notifyDescUpdatedAt.Default.(func() time.Time)
	// notify.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	notify.UpdateDefaultUpdatedAt = notifyDescUpdatedAt.UpdateDefault.(func() time.Time)
	// notifyDescRemark is the schema descriptor for remark field.
	notifyDescRemark := notifyMixinFields3[0].Descriptor()
	// notify.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	notify.RemarkValidator = notifyDescRemark.Validators[0].(func(string) error)
	protectgroupMixin := schema.ProtectGroup{}.Mixin()
	protectgroup.Policy = privacy.NewPolicies(protectgroupMixin[0], protectgroupMixin[1], schema.ProtectGroup{})
	protectgroup.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := protectgroup.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	protectgroupMixinFields2 := protectgroupMixin[2].Fields()
	_ = protectgroupMixinFields2
	protectgroupMixinFields3 := protectgroupMixin[3].Fields()
	_ = protectgroupMixinFields3
	protectgroupFields := schema.ProtectGroup{}.Fields()
	_ = protectgroupFields
	// protectgroupDescCreatedAt is the schema descriptor for created_at field.
	protectgroupDescCreatedAt := protectgroupMixinFields2[0].Descriptor()
	// protectgroup.DefaultCreatedAt holds the default value on creation for the created_at field.
	protectgroup.DefaultCreatedAt = protectgroupDescCreatedAt.Default.(func() time.Time)
	// protectgroupDescUpdatedAt is the schema descriptor for updated_at field.
	protectgroupDescUpdatedAt := protectgroupMixinFields2[1].Descriptor()
	// protectgroup.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	protectgroup.DefaultUpdatedAt = protectgroupDescUpdatedAt.Default.(func() time.Time)
	// protectgroup.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	protectgroup.UpdateDefaultUpdatedAt = protectgroupDescUpdatedAt.UpdateDefault.(func() time.Time)
	// protectgroupDescRemark is the schema descriptor for remark field.
	protectgroupDescRemark := protectgroupMixinFields3[0].Descriptor()
	// protectgroup.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	protectgroup.RemarkValidator = protectgroupDescRemark.Validators[0].(func(string) error)
	// protectgroupDescExpandIP is the schema descriptor for expand_ip field.
	protectgroupDescExpandIP := protectgroupFields[4].Descriptor()
	// protectgroup.ExpandIPValidator is a validator for the "expand_ip" field. It is called by the builders before save.
	protectgroup.ExpandIPValidator = protectgroupDescExpandIP.Validators[0].(func(string) error)
	skylinedosMixin := schema.SkylineDos{}.Mixin()
	skylinedos.Policy = privacy.NewPolicies(skylinedosMixin[0], skylinedosMixin[1], schema.SkylineDos{})
	skylinedos.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := skylinedos.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	skylinedosMixinFields2 := skylinedosMixin[2].Fields()
	_ = skylinedosMixinFields2
	skylinedosMixinFields3 := skylinedosMixin[3].Fields()
	_ = skylinedosMixinFields3
	skylinedosFields := schema.SkylineDos{}.Fields()
	_ = skylinedosFields
	// skylinedosDescCreatedAt is the schema descriptor for created_at field.
	skylinedosDescCreatedAt := skylinedosMixinFields2[0].Descriptor()
	// skylinedos.DefaultCreatedAt holds the default value on creation for the created_at field.
	skylinedos.DefaultCreatedAt = skylinedosDescCreatedAt.Default.(func() time.Time)
	// skylinedosDescUpdatedAt is the schema descriptor for updated_at field.
	skylinedosDescUpdatedAt := skylinedosMixinFields2[1].Descriptor()
	// skylinedos.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	skylinedos.DefaultUpdatedAt = skylinedosDescUpdatedAt.Default.(func() time.Time)
	// skylinedos.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	skylinedos.UpdateDefaultUpdatedAt = skylinedosDescUpdatedAt.UpdateDefault.(func() time.Time)
	// skylinedosDescRemark is the schema descriptor for remark field.
	skylinedosDescRemark := skylinedosMixinFields3[0].Descriptor()
	// skylinedos.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	skylinedos.RemarkValidator = skylinedosDescRemark.Validators[0].(func(string) error)
	socgroupticketMixin := schema.SocGroupTicket{}.Mixin()
	socgroupticket.Policy = privacy.NewPolicies(socgroupticketMixin[0], socgroupticketMixin[2], schema.SocGroupTicket{})
	socgroupticket.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := socgroupticket.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	socgroupticketMixinFields1 := socgroupticketMixin[1].Fields()
	_ = socgroupticketMixinFields1
	socgroupticketMixinFields3 := socgroupticketMixin[3].Fields()
	_ = socgroupticketMixinFields3
	socgroupticketFields := schema.SocGroupTicket{}.Fields()
	_ = socgroupticketFields
	// socgroupticketDescCreatedAt is the schema descriptor for created_at field.
	socgroupticketDescCreatedAt := socgroupticketMixinFields1[0].Descriptor()
	// socgroupticket.DefaultCreatedAt holds the default value on creation for the created_at field.
	socgroupticket.DefaultCreatedAt = socgroupticketDescCreatedAt.Default.(func() time.Time)
	// socgroupticketDescUpdatedAt is the schema descriptor for updated_at field.
	socgroupticketDescUpdatedAt := socgroupticketMixinFields1[1].Descriptor()
	// socgroupticket.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	socgroupticket.DefaultUpdatedAt = socgroupticketDescUpdatedAt.Default.(func() time.Time)
	// socgroupticket.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	socgroupticket.UpdateDefaultUpdatedAt = socgroupticketDescUpdatedAt.UpdateDefault.(func() time.Time)
	// socgroupticketDescRemark is the schema descriptor for remark field.
	socgroupticketDescRemark := socgroupticketMixinFields3[0].Descriptor()
	// socgroupticket.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	socgroupticket.RemarkValidator = socgroupticketDescRemark.Validators[0].(func(string) error)
	// socgroupticketDescDescription is the schema descriptor for description field.
	socgroupticketDescDescription := socgroupticketFields[2].Descriptor()
	// socgroupticket.DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	socgroupticket.DescriptionValidator = socgroupticketDescDescription.Validators[0].(func(string) error)
	// socgroupticketDescConfigArgs is the schema descriptor for config_args field.
	socgroupticketDescConfigArgs := socgroupticketFields[11].Descriptor()
	// socgroupticket.ConfigArgsValidator is a validator for the "config_args" field. It is called by the builders before save.
	socgroupticket.ConfigArgsValidator = socgroupticketDescConfigArgs.Validators[0].(func(string) error)
	// socgroupticketDescErrorInfo is the schema descriptor for error_info field.
	socgroupticketDescErrorInfo := socgroupticketFields[16].Descriptor()
	// socgroupticket.ErrorInfoValidator is a validator for the "error_info" field. It is called by the builders before save.
	socgroupticket.ErrorInfoValidator = socgroupticketDescErrorInfo.Validators[0].(func(string) error)
	spectrumalertMixin := schema.SpectrumAlert{}.Mixin()
	spectrumalert.Policy = privacy.NewPolicies(spectrumalertMixin[0], spectrumalertMixin[1], schema.SpectrumAlert{})
	spectrumalert.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := spectrumalert.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	spectrumalertMixinFields2 := spectrumalertMixin[2].Fields()
	_ = spectrumalertMixinFields2
	spectrumalertMixinFields3 := spectrumalertMixin[3].Fields()
	_ = spectrumalertMixinFields3
	spectrumalertFields := schema.SpectrumAlert{}.Fields()
	_ = spectrumalertFields
	// spectrumalertDescCreatedAt is the schema descriptor for created_at field.
	spectrumalertDescCreatedAt := spectrumalertMixinFields2[0].Descriptor()
	// spectrumalert.DefaultCreatedAt holds the default value on creation for the created_at field.
	spectrumalert.DefaultCreatedAt = spectrumalertDescCreatedAt.Default.(func() time.Time)
	// spectrumalertDescUpdatedAt is the schema descriptor for updated_at field.
	spectrumalertDescUpdatedAt := spectrumalertMixinFields2[1].Descriptor()
	// spectrumalert.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	spectrumalert.DefaultUpdatedAt = spectrumalertDescUpdatedAt.Default.(func() time.Time)
	// spectrumalert.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	spectrumalert.UpdateDefaultUpdatedAt = spectrumalertDescUpdatedAt.UpdateDefault.(func() time.Time)
	// spectrumalertDescRemark is the schema descriptor for remark field.
	spectrumalertDescRemark := spectrumalertMixinFields3[0].Descriptor()
	// spectrumalert.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	spectrumalert.RemarkValidator = spectrumalertDescRemark.Validators[0].(func(string) error)
	spectrumdataMixin := schema.SpectrumData{}.Mixin()
	spectrumdata.Policy = privacy.NewPolicies(spectrumdataMixin[0], spectrumdataMixin[1], schema.SpectrumData{})
	spectrumdata.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := spectrumdata.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	spectrumdataMixinFields2 := spectrumdataMixin[2].Fields()
	_ = spectrumdataMixinFields2
	spectrumdataFields := schema.SpectrumData{}.Fields()
	_ = spectrumdataFields
	// spectrumdataDescCreatedAt is the schema descriptor for created_at field.
	spectrumdataDescCreatedAt := spectrumdataMixinFields2[0].Descriptor()
	// spectrumdata.DefaultCreatedAt holds the default value on creation for the created_at field.
	spectrumdata.DefaultCreatedAt = spectrumdataDescCreatedAt.Default.(func() time.Time)
	strategyMixin := schema.Strategy{}.Mixin()
	strategy.Policy = privacy.NewPolicies(strategyMixin[0], strategyMixin[1], schema.Strategy{})
	strategy.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := strategy.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	strategyMixinFields2 := strategyMixin[2].Fields()
	_ = strategyMixinFields2
	strategyMixinFields3 := strategyMixin[3].Fields()
	_ = strategyMixinFields3
	strategyFields := schema.Strategy{}.Fields()
	_ = strategyFields
	// strategyDescCreatedAt is the schema descriptor for created_at field.
	strategyDescCreatedAt := strategyMixinFields2[0].Descriptor()
	// strategy.DefaultCreatedAt holds the default value on creation for the created_at field.
	strategy.DefaultCreatedAt = strategyDescCreatedAt.Default.(func() time.Time)
	// strategyDescUpdatedAt is the schema descriptor for updated_at field.
	strategyDescUpdatedAt := strategyMixinFields2[1].Descriptor()
	// strategy.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	strategy.DefaultUpdatedAt = strategyDescUpdatedAt.Default.(func() time.Time)
	// strategy.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	strategy.UpdateDefaultUpdatedAt = strategyDescUpdatedAt.UpdateDefault.(func() time.Time)
	// strategyDescRemark is the schema descriptor for remark field.
	strategyDescRemark := strategyMixinFields3[0].Descriptor()
	// strategy.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	strategy.RemarkValidator = strategyDescRemark.Validators[0].(func(string) error)
	systemapiMixin := schema.SystemApi{}.Mixin()
	systemapi.Policy = privacy.NewPolicies(systemapiMixin[0], schema.SystemApi{})
	systemapi.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := systemapi.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	systemapiMixinFields1 := systemapiMixin[1].Fields()
	_ = systemapiMixinFields1
	systemapiMixinFields2 := systemapiMixin[2].Fields()
	_ = systemapiMixinFields2
	systemapiFields := schema.SystemApi{}.Fields()
	_ = systemapiFields
	// systemapiDescCreatedAt is the schema descriptor for created_at field.
	systemapiDescCreatedAt := systemapiMixinFields1[0].Descriptor()
	// systemapi.DefaultCreatedAt holds the default value on creation for the created_at field.
	systemapi.DefaultCreatedAt = systemapiDescCreatedAt.Default.(func() time.Time)
	// systemapiDescUpdatedAt is the schema descriptor for updated_at field.
	systemapiDescUpdatedAt := systemapiMixinFields1[1].Descriptor()
	// systemapi.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	systemapi.DefaultUpdatedAt = systemapiDescUpdatedAt.Default.(func() time.Time)
	// systemapi.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	systemapi.UpdateDefaultUpdatedAt = systemapiDescUpdatedAt.UpdateDefault.(func() time.Time)
	// systemapiDescRemark is the schema descriptor for remark field.
	systemapiDescRemark := systemapiMixinFields2[0].Descriptor()
	// systemapi.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	systemapi.RemarkValidator = systemapiDescRemark.Validators[0].(func(string) error)
	systemconfigMixin := schema.SystemConfig{}.Mixin()
	systemconfig.Policy = privacy.NewPolicies(systemconfigMixin[0], schema.SystemConfig{})
	systemconfig.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := systemconfig.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	systemconfigMixinFields1 := systemconfigMixin[1].Fields()
	_ = systemconfigMixinFields1
	systemconfigMixinFields2 := systemconfigMixin[2].Fields()
	_ = systemconfigMixinFields2
	systemconfigFields := schema.SystemConfig{}.Fields()
	_ = systemconfigFields
	// systemconfigDescCreatedAt is the schema descriptor for created_at field.
	systemconfigDescCreatedAt := systemconfigMixinFields1[0].Descriptor()
	// systemconfig.DefaultCreatedAt holds the default value on creation for the created_at field.
	systemconfig.DefaultCreatedAt = systemconfigDescCreatedAt.Default.(func() time.Time)
	// systemconfigDescUpdatedAt is the schema descriptor for updated_at field.
	systemconfigDescUpdatedAt := systemconfigMixinFields1[1].Descriptor()
	// systemconfig.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	systemconfig.DefaultUpdatedAt = systemconfigDescUpdatedAt.Default.(func() time.Time)
	// systemconfig.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	systemconfig.UpdateDefaultUpdatedAt = systemconfigDescUpdatedAt.UpdateDefault.(func() time.Time)
	// systemconfigDescRemark is the schema descriptor for remark field.
	systemconfigDescRemark := systemconfigMixinFields2[0].Descriptor()
	// systemconfig.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	systemconfig.RemarkValidator = systemconfigDescRemark.Validators[0].(func(string) error)
	tenantMixin := schema.Tenant{}.Mixin()
	tenant.Policy = privacy.NewPolicies(tenantMixin[0], schema.Tenant{})
	tenant.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := tenant.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	tenantFields := schema.Tenant{}.Fields()
	_ = tenantFields
	// tenantDescName is the schema descriptor for name field.
	tenantDescName := tenantFields[0].Descriptor()
	// tenant.NameValidator is a validator for the "name" field. It is called by the builders before save.
	tenant.NameValidator = tenantDescName.Validators[0].(func(string) error)
	// tenantDescCode is the schema descriptor for code field.
	tenantDescCode := tenantFields[1].Descriptor()
	// tenant.CodeValidator is a validator for the "code" field. It is called by the builders before save.
	tenant.CodeValidator = tenantDescCode.Validators[0].(func(string) error)
	userMixin := schema.User{}.Mixin()
	userMixinFields0 := userMixin[0].Fields()
	_ = userMixinFields0
	userMixinFields1 := userMixin[1].Fields()
	_ = userMixinFields1
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescValid is the schema descriptor for valid field.
	userDescValid := userMixinFields0[0].Descriptor()
	// user.DefaultValid holds the default value on creation for the valid field.
	user.DefaultValid = userDescValid.Default.(bool)
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userMixinFields1[0].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(func() time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userMixinFields1[1].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(func() time.Time)
	// user.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	user.UpdateDefaultUpdatedAt = userDescUpdatedAt.UpdateDefault.(func() time.Time)
	// userDescName is the schema descriptor for name field.
	userDescName := userFields[0].Descriptor()
	// user.NameValidator is a validator for the "name" field. It is called by the builders before save.
	user.NameValidator = userDescName.Validators[0].(func(string) error)
	// userDescPassword is the schema descriptor for password field.
	userDescPassword := userFields[1].Descriptor()
	// user.PasswordValidator is a validator for the "password" field. It is called by the builders before save.
	user.PasswordValidator = userDescPassword.Validators[0].(func(string) error)
	// userDescSuperAdmin is the schema descriptor for super_admin field.
	userDescSuperAdmin := userFields[2].Descriptor()
	// user.DefaultSuperAdmin holds the default value on creation for the super_admin field.
	user.DefaultSuperAdmin = userDescSuperAdmin.Default.(bool)
	// userDescUpdateAuth is the schema descriptor for update_auth field.
	userDescUpdateAuth := userFields[3].Descriptor()
	// user.DefaultUpdateAuth holds the default value on creation for the update_auth field.
	user.DefaultUpdateAuth = userDescUpdateAuth.Default.(bool)
	useroperationlogMixin := schema.UserOperationLog{}.Mixin()
	useroperationlogMixinFields0 := useroperationlogMixin[0].Fields()
	_ = useroperationlogMixinFields0
	useroperationlogMixinFields1 := useroperationlogMixin[1].Fields()
	_ = useroperationlogMixinFields1
	useroperationlogFields := schema.UserOperationLog{}.Fields()
	_ = useroperationlogFields
	// useroperationlogDescRemark is the schema descriptor for remark field.
	useroperationlogDescRemark := useroperationlogMixinFields0[0].Descriptor()
	// useroperationlog.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	useroperationlog.RemarkValidator = useroperationlogDescRemark.Validators[0].(func(string) error)
	// useroperationlogDescCreatedAt is the schema descriptor for created_at field.
	useroperationlogDescCreatedAt := useroperationlogMixinFields1[0].Descriptor()
	// useroperationlog.DefaultCreatedAt holds the default value on creation for the created_at field.
	useroperationlog.DefaultCreatedAt = useroperationlogDescCreatedAt.Default.(func() time.Time)
	// useroperationlogDescUpdatedAt is the schema descriptor for updated_at field.
	useroperationlogDescUpdatedAt := useroperationlogMixinFields1[1].Descriptor()
	// useroperationlog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	useroperationlog.DefaultUpdatedAt = useroperationlogDescUpdatedAt.Default.(func() time.Time)
	// useroperationlog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	useroperationlog.UpdateDefaultUpdatedAt = useroperationlogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// useroperationlogDescURI is the schema descriptor for uri field.
	useroperationlogDescURI := useroperationlogFields[3].Descriptor()
	// useroperationlog.URIValidator is a validator for the "uri" field. It is called by the builders before save.
	useroperationlog.URIValidator = useroperationlogDescURI.Validators[0].(func(string) error)
	wofangMixin := schema.Wofang{}.Mixin()
	wofang.Policy = privacy.NewPolicies(wofangMixin[0], wofangMixin[2], schema.Wofang{})
	wofang.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := wofang.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	wofangMixinFields1 := wofangMixin[1].Fields()
	_ = wofangMixinFields1
	wofangMixinFields3 := wofangMixin[3].Fields()
	_ = wofangMixinFields3
	wofangFields := schema.Wofang{}.Fields()
	_ = wofangFields
	// wofangDescCreatedAt is the schema descriptor for created_at field.
	wofangDescCreatedAt := wofangMixinFields1[0].Descriptor()
	// wofang.DefaultCreatedAt holds the default value on creation for the created_at field.
	wofang.DefaultCreatedAt = wofangDescCreatedAt.Default.(func() time.Time)
	// wofangDescUpdatedAt is the schema descriptor for updated_at field.
	wofangDescUpdatedAt := wofangMixinFields1[1].Descriptor()
	// wofang.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wofang.DefaultUpdatedAt = wofangDescUpdatedAt.Default.(func() time.Time)
	// wofang.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wofang.UpdateDefaultUpdatedAt = wofangDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wofangDescRemark is the schema descriptor for remark field.
	wofangDescRemark := wofangMixinFields3[0].Descriptor()
	// wofang.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	wofang.RemarkValidator = wofangDescRemark.Validators[0].(func(string) error)
	// wofangDescStartTime is the schema descriptor for start_time field.
	wofangDescStartTime := wofangFields[4].Descriptor()
	// wofang.DefaultStartTime holds the default value on creation for the start_time field.
	wofang.DefaultStartTime = wofangDescStartTime.Default.(func() time.Time)
	// wofangDescErrorInfo is the schema descriptor for error_info field.
	wofangDescErrorInfo := wofangFields[5].Descriptor()
	// wofang.ErrorInfoValidator is a validator for the "error_info" field. It is called by the builders before save.
	wofang.ErrorInfoValidator = wofangDescErrorInfo.Validators[0].(func(string) error)
	wofangalertMixin := schema.WofangAlert{}.Mixin()
	wofangalert.Policy = privacy.NewPolicies(wofangalertMixin[0], wofangalertMixin[1], schema.WofangAlert{})
	wofangalert.Hooks[0] = func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if err := wofangalert.Policy.EvalMutation(ctx, m); err != nil {
				return nil, err
			}
			return next.Mutate(ctx, m)
		})
	}
	wofangalertMixinFields2 := wofangalertMixin[2].Fields()
	_ = wofangalertMixinFields2
	wofangalertMixinFields3 := wofangalertMixin[3].Fields()
	_ = wofangalertMixinFields3
	wofangalertFields := schema.WofangAlert{}.Fields()
	_ = wofangalertFields
	// wofangalertDescCreatedAt is the schema descriptor for created_at field.
	wofangalertDescCreatedAt := wofangalertMixinFields2[0].Descriptor()
	// wofangalert.DefaultCreatedAt holds the default value on creation for the created_at field.
	wofangalert.DefaultCreatedAt = wofangalertDescCreatedAt.Default.(func() time.Time)
	// wofangalertDescUpdatedAt is the schema descriptor for updated_at field.
	wofangalertDescUpdatedAt := wofangalertMixinFields2[1].Descriptor()
	// wofangalert.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	wofangalert.DefaultUpdatedAt = wofangalertDescUpdatedAt.Default.(func() time.Time)
	// wofangalert.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	wofangalert.UpdateDefaultUpdatedAt = wofangalertDescUpdatedAt.UpdateDefault.(func() time.Time)
	// wofangalertDescRemark is the schema descriptor for remark field.
	wofangalertDescRemark := wofangalertMixinFields3[0].Descriptor()
	// wofangalert.RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	wofangalert.RemarkValidator = wofangalertDescRemark.Validators[0].(func(string) error)
}

const (
	Version = "v0.12.5"                                         // Version of ent codegen.
	Sum     = "h1:KREM5E4CSoej4zeGa88Ou/gfturAnpUv0mzAjch1sj4=" // Sum of ent codegen.
)
