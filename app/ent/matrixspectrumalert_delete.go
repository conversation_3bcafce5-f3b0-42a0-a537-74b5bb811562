// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumAlertDelete is the builder for deleting a MatrixSpectrumAlert entity.
type MatrixSpectrumAlertDelete struct {
	config
	hooks    []Hook
	mutation *MatrixSpectrumAlertMutation
}

// Where appends a list predicates to the MatrixSpectrumAlertDelete builder.
func (msad *MatrixSpectrumAlertDelete) Where(ps ...predicate.MatrixSpectrumAlert) *MatrixSpectrumAlertDelete {
	msad.mutation.Where(ps...)
	return msad
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (msad *MatrixSpectrumAlertDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, msad.sqlExec, msad.mutation, msad.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (msad *MatrixSpectrumAlertDelete) ExecX(ctx context.Context) int {
	n, err := msad.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (msad *MatrixSpectrumAlertDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(matrixspectrumalert.Table, sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt))
	if ps := msad.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, msad.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	msad.mutation.done = true
	return affected, err
}

// MatrixSpectrumAlertDeleteOne is the builder for deleting a single MatrixSpectrumAlert entity.
type MatrixSpectrumAlertDeleteOne struct {
	msad *MatrixSpectrumAlertDelete
}

// Where appends a list predicates to the MatrixSpectrumAlertDelete builder.
func (msado *MatrixSpectrumAlertDeleteOne) Where(ps ...predicate.MatrixSpectrumAlert) *MatrixSpectrumAlertDeleteOne {
	msado.msad.mutation.Where(ps...)
	return msado
}

// Exec executes the deletion query.
func (msado *MatrixSpectrumAlertDeleteOne) Exec(ctx context.Context) error {
	n, err := msado.msad.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{matrixspectrumalert.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (msado *MatrixSpectrumAlertDeleteOne) ExecX(ctx context.Context) {
	if err := msado.Exec(ctx); err != nil {
		panic(err)
	}
}
