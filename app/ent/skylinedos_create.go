// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/skylinedos"
	"meta/app/ent/tenant"
	"meta/app/entity/netease/gamecloud"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SkylineDosCreate is the builder for creating a SkylineDos entity.
type SkylineDosCreate struct {
	config
	mutation *SkylineDosMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (sdc *SkylineDosCreate) SetTenantID(i int) *SkylineDosCreate {
	sdc.mutation.SetTenantID(i)
	return sdc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sdc *SkylineDosCreate) SetNillableTenantID(i *int) *SkylineDosCreate {
	if i != nil {
		sdc.SetTenantID(*i)
	}
	return sdc
}

// SetCreatedAt sets the "created_at" field.
func (sdc *SkylineDosCreate) SetCreatedAt(t time.Time) *SkylineDosCreate {
	sdc.mutation.SetCreatedAt(t)
	return sdc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sdc *SkylineDosCreate) SetNillableCreatedAt(t *time.Time) *SkylineDosCreate {
	if t != nil {
		sdc.SetCreatedAt(*t)
	}
	return sdc
}

// SetUpdatedAt sets the "updated_at" field.
func (sdc *SkylineDosCreate) SetUpdatedAt(t time.Time) *SkylineDosCreate {
	sdc.mutation.SetUpdatedAt(t)
	return sdc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sdc *SkylineDosCreate) SetNillableUpdatedAt(t *time.Time) *SkylineDosCreate {
	if t != nil {
		sdc.SetUpdatedAt(*t)
	}
	return sdc
}

// SetRemark sets the "remark" field.
func (sdc *SkylineDosCreate) SetRemark(s string) *SkylineDosCreate {
	sdc.mutation.SetRemark(s)
	return sdc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sdc *SkylineDosCreate) SetNillableRemark(s *string) *SkylineDosCreate {
	if s != nil {
		sdc.SetRemark(*s)
	}
	return sdc
}

// SetStartTime sets the "start_time" field.
func (sdc *SkylineDosCreate) SetStartTime(t time.Time) *SkylineDosCreate {
	sdc.mutation.SetStartTime(t)
	return sdc
}

// SetEndTime sets the "end_time" field.
func (sdc *SkylineDosCreate) SetEndTime(t time.Time) *SkylineDosCreate {
	sdc.mutation.SetEndTime(t)
	return sdc
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (sdc *SkylineDosCreate) SetNillableEndTime(t *time.Time) *SkylineDosCreate {
	if t != nil {
		sdc.SetEndTime(*t)
	}
	return sdc
}

// SetRegion sets the "region" field.
func (sdc *SkylineDosCreate) SetRegion(s string) *SkylineDosCreate {
	sdc.mutation.SetRegion(s)
	return sdc
}

// SetResource sets the "resource" field.
func (sdc *SkylineDosCreate) SetResource(s string) *SkylineDosCreate {
	sdc.mutation.SetResource(s)
	return sdc
}

// SetResourceType sets the "resource_type" field.
func (sdc *SkylineDosCreate) SetResourceType(s string) *SkylineDosCreate {
	sdc.mutation.SetResourceType(s)
	return sdc
}

// SetVectorTypes sets the "vector_types" field.
func (sdc *SkylineDosCreate) SetVectorTypes(s *[]string) *SkylineDosCreate {
	sdc.mutation.SetVectorTypes(s)
	return sdc
}

// SetStatus sets the "status" field.
func (sdc *SkylineDosCreate) SetStatus(s string) *SkylineDosCreate {
	sdc.mutation.SetStatus(s)
	return sdc
}

// SetAttackID sets the "attack_id" field.
func (sdc *SkylineDosCreate) SetAttackID(s string) *SkylineDosCreate {
	sdc.mutation.SetAttackID(s)
	return sdc
}

// SetAttackCounters sets the "attack_counters" field.
func (sdc *SkylineDosCreate) SetAttackCounters(gc *[]gamecloud.AttackCounter) *SkylineDosCreate {
	sdc.mutation.SetAttackCounters(gc)
	return sdc
}

// SetProject sets the "project" field.
func (sdc *SkylineDosCreate) SetProject(s string) *SkylineDosCreate {
	sdc.mutation.SetProject(s)
	return sdc
}

// SetDurationTime sets the "duration_time" field.
func (sdc *SkylineDosCreate) SetDurationTime(i int64) *SkylineDosCreate {
	sdc.mutation.SetDurationTime(i)
	return sdc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sdc *SkylineDosCreate) SetTenant(t *Tenant) *SkylineDosCreate {
	return sdc.SetTenantID(t.ID)
}

// Mutation returns the SkylineDosMutation object of the builder.
func (sdc *SkylineDosCreate) Mutation() *SkylineDosMutation {
	return sdc.mutation
}

// Save creates the SkylineDos in the database.
func (sdc *SkylineDosCreate) Save(ctx context.Context) (*SkylineDos, error) {
	if err := sdc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sdc.sqlSave, sdc.mutation, sdc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sdc *SkylineDosCreate) SaveX(ctx context.Context) *SkylineDos {
	v, err := sdc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sdc *SkylineDosCreate) Exec(ctx context.Context) error {
	_, err := sdc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sdc *SkylineDosCreate) ExecX(ctx context.Context) {
	if err := sdc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sdc *SkylineDosCreate) defaults() error {
	if _, ok := sdc.mutation.CreatedAt(); !ok {
		if skylinedos.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized skylinedos.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := skylinedos.DefaultCreatedAt()
		sdc.mutation.SetCreatedAt(v)
	}
	if _, ok := sdc.mutation.UpdatedAt(); !ok {
		if skylinedos.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized skylinedos.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := skylinedos.DefaultUpdatedAt()
		sdc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sdc *SkylineDosCreate) check() error {
	if _, ok := sdc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "SkylineDos.created_at"`)}
	}
	if _, ok := sdc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "SkylineDos.updated_at"`)}
	}
	if v, ok := sdc.mutation.Remark(); ok {
		if err := skylinedos.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SkylineDos.remark": %w`, err)}
		}
	}
	if _, ok := sdc.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "SkylineDos.start_time"`)}
	}
	if _, ok := sdc.mutation.Region(); !ok {
		return &ValidationError{Name: "region", err: errors.New(`ent: missing required field "SkylineDos.region"`)}
	}
	if _, ok := sdc.mutation.Resource(); !ok {
		return &ValidationError{Name: "resource", err: errors.New(`ent: missing required field "SkylineDos.resource"`)}
	}
	if _, ok := sdc.mutation.ResourceType(); !ok {
		return &ValidationError{Name: "resource_type", err: errors.New(`ent: missing required field "SkylineDos.resource_type"`)}
	}
	if _, ok := sdc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "SkylineDos.status"`)}
	}
	if _, ok := sdc.mutation.AttackID(); !ok {
		return &ValidationError{Name: "attack_id", err: errors.New(`ent: missing required field "SkylineDos.attack_id"`)}
	}
	if _, ok := sdc.mutation.Project(); !ok {
		return &ValidationError{Name: "project", err: errors.New(`ent: missing required field "SkylineDos.project"`)}
	}
	if _, ok := sdc.mutation.DurationTime(); !ok {
		return &ValidationError{Name: "duration_time", err: errors.New(`ent: missing required field "SkylineDos.duration_time"`)}
	}
	return nil
}

func (sdc *SkylineDosCreate) sqlSave(ctx context.Context) (*SkylineDos, error) {
	if err := sdc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sdc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sdc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sdc.mutation.id = &_node.ID
	sdc.mutation.done = true
	return _node, nil
}

func (sdc *SkylineDosCreate) createSpec() (*SkylineDos, *sqlgraph.CreateSpec) {
	var (
		_node = &SkylineDos{config: sdc.config}
		_spec = sqlgraph.NewCreateSpec(skylinedos.Table, sqlgraph.NewFieldSpec(skylinedos.FieldID, field.TypeInt))
	)
	if value, ok := sdc.mutation.CreatedAt(); ok {
		_spec.SetField(skylinedos.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sdc.mutation.UpdatedAt(); ok {
		_spec.SetField(skylinedos.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := sdc.mutation.Remark(); ok {
		_spec.SetField(skylinedos.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := sdc.mutation.StartTime(); ok {
		_spec.SetField(skylinedos.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := sdc.mutation.EndTime(); ok {
		_spec.SetField(skylinedos.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if value, ok := sdc.mutation.Region(); ok {
		_spec.SetField(skylinedos.FieldRegion, field.TypeString, value)
		_node.Region = value
	}
	if value, ok := sdc.mutation.Resource(); ok {
		_spec.SetField(skylinedos.FieldResource, field.TypeString, value)
		_node.Resource = value
	}
	if value, ok := sdc.mutation.ResourceType(); ok {
		_spec.SetField(skylinedos.FieldResourceType, field.TypeString, value)
		_node.ResourceType = value
	}
	if value, ok := sdc.mutation.VectorTypes(); ok {
		_spec.SetField(skylinedos.FieldVectorTypes, field.TypeJSON, value)
		_node.VectorTypes = value
	}
	if value, ok := sdc.mutation.Status(); ok {
		_spec.SetField(skylinedos.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := sdc.mutation.AttackID(); ok {
		_spec.SetField(skylinedos.FieldAttackID, field.TypeString, value)
		_node.AttackID = value
	}
	if value, ok := sdc.mutation.AttackCounters(); ok {
		_spec.SetField(skylinedos.FieldAttackCounters, field.TypeJSON, value)
		_node.AttackCounters = value
	}
	if value, ok := sdc.mutation.Project(); ok {
		_spec.SetField(skylinedos.FieldProject, field.TypeString, value)
		_node.Project = value
	}
	if value, ok := sdc.mutation.DurationTime(); ok {
		_spec.SetField(skylinedos.FieldDurationTime, field.TypeInt64, value)
		_node.DurationTime = value
	}
	if nodes := sdc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   skylinedos.TenantTable,
			Columns: []string{skylinedos.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// SkylineDosCreateBulk is the builder for creating many SkylineDos entities in bulk.
type SkylineDosCreateBulk struct {
	config
	err      error
	builders []*SkylineDosCreate
}

// Save creates the SkylineDos entities in the database.
func (sdcb *SkylineDosCreateBulk) Save(ctx context.Context) ([]*SkylineDos, error) {
	if sdcb.err != nil {
		return nil, sdcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(sdcb.builders))
	nodes := make([]*SkylineDos, len(sdcb.builders))
	mutators := make([]Mutator, len(sdcb.builders))
	for i := range sdcb.builders {
		func(i int, root context.Context) {
			builder := sdcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SkylineDosMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, sdcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, sdcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, sdcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (sdcb *SkylineDosCreateBulk) SaveX(ctx context.Context) []*SkylineDos {
	v, err := sdcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sdcb *SkylineDosCreateBulk) Exec(ctx context.Context) error {
	_, err := sdcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sdcb *SkylineDosCreateBulk) ExecX(ctx context.Context) {
	if err := sdcb.Exec(ctx); err != nil {
		panic(err)
	}
}
