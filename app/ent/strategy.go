// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Strategy is the model entity for the Strategy schema.
type Strategy struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// 牵引防护类型
	Type int `json:"type,omitempty"`
	// 是否启用
	Enabled bool `json:"enabled,omitempty"`
	// 系统策略，项目未配置自定义策略情况下，使用系统策略
	System bool `json:"system,omitempty"`
	// 总bps大小阈值
	Bps int64 `json:"bps,omitempty"`
	// 总pps大小阈值
	Pps int64 `json:"pps,omitempty"`
	// bps次数阈值
	BpsCount int `json:"bps_count,omitempty"`
	// pps次数阈值
	PpsCount int `json:"pps_count,omitempty"`
	// ip运营商类型
	IspCode int `json:"isp_code,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the StrategyQuery when eager-loading is set.
	Edges        StrategyEdges `json:"edges"`
	selectValues sql.SelectValues
}

// StrategyEdges holds the relations/edges for other nodes in the graph.
type StrategyEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// StrategyAlerts holds the value of the strategy_alerts edge.
	StrategyAlerts []*SpectrumAlert `json:"strategy_alerts,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e StrategyEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// StrategyAlertsOrErr returns the StrategyAlerts value or an error if the edge
// was not loaded in eager-loading.
func (e StrategyEdges) StrategyAlertsOrErr() ([]*SpectrumAlert, error) {
	if e.loadedTypes[1] {
		return e.StrategyAlerts, nil
	}
	return nil, &NotLoadedError{edge: "strategy_alerts"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Strategy) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case strategy.FieldEnabled, strategy.FieldSystem:
			values[i] = new(sql.NullBool)
		case strategy.FieldID, strategy.FieldTenantID, strategy.FieldType, strategy.FieldBps, strategy.FieldPps, strategy.FieldBpsCount, strategy.FieldPpsCount, strategy.FieldIspCode:
			values[i] = new(sql.NullInt64)
		case strategy.FieldRemark, strategy.FieldName:
			values[i] = new(sql.NullString)
		case strategy.FieldCreatedAt, strategy.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Strategy fields.
func (s *Strategy) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case strategy.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			s.ID = int(value.Int64)
		case strategy.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				s.TenantID = new(int)
				*s.TenantID = int(value.Int64)
			}
		case strategy.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				s.CreatedAt = value.Time
			}
		case strategy.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				s.UpdatedAt = value.Time
			}
		case strategy.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				s.Remark = new(string)
				*s.Remark = value.String
			}
		case strategy.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				s.Name = value.String
			}
		case strategy.FieldType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				s.Type = int(value.Int64)
			}
		case strategy.FieldEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field enabled", values[i])
			} else if value.Valid {
				s.Enabled = value.Bool
			}
		case strategy.FieldSystem:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field system", values[i])
			} else if value.Valid {
				s.System = value.Bool
			}
		case strategy.FieldBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field bps", values[i])
			} else if value.Valid {
				s.Bps = value.Int64
			}
		case strategy.FieldPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field pps", values[i])
			} else if value.Valid {
				s.Pps = value.Int64
			}
		case strategy.FieldBpsCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field bps_count", values[i])
			} else if value.Valid {
				s.BpsCount = int(value.Int64)
			}
		case strategy.FieldPpsCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field pps_count", values[i])
			} else if value.Valid {
				s.PpsCount = int(value.Int64)
			}
		case strategy.FieldIspCode:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field isp_code", values[i])
			} else if value.Valid {
				s.IspCode = int(value.Int64)
			}
		default:
			s.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Strategy.
// This includes values selected through modifiers, order, etc.
func (s *Strategy) Value(name string) (ent.Value, error) {
	return s.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the Strategy entity.
func (s *Strategy) QueryTenant() *TenantQuery {
	return NewStrategyClient(s.config).QueryTenant(s)
}

// QueryStrategyAlerts queries the "strategy_alerts" edge of the Strategy entity.
func (s *Strategy) QueryStrategyAlerts() *SpectrumAlertQuery {
	return NewStrategyClient(s.config).QueryStrategyAlerts(s)
}

// Update returns a builder for updating this Strategy.
// Note that you need to call Strategy.Unwrap() before calling this method if this Strategy
// was returned from a transaction, and the transaction was committed or rolled back.
func (s *Strategy) Update() *StrategyUpdateOne {
	return NewStrategyClient(s.config).UpdateOne(s)
}

// Unwrap unwraps the Strategy entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (s *Strategy) Unwrap() *Strategy {
	_tx, ok := s.config.driver.(*txDriver)
	if !ok {
		panic("ent: Strategy is not a transactional entity")
	}
	s.config.driver = _tx.drv
	return s
}

// String implements the fmt.Stringer.
func (s *Strategy) String() string {
	var builder strings.Builder
	builder.WriteString("Strategy(")
	builder.WriteString(fmt.Sprintf("id=%v, ", s.ID))
	if v := s.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(s.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(s.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := s.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(s.Name)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", s.Type))
	builder.WriteString(", ")
	builder.WriteString("enabled=")
	builder.WriteString(fmt.Sprintf("%v", s.Enabled))
	builder.WriteString(", ")
	builder.WriteString("system=")
	builder.WriteString(fmt.Sprintf("%v", s.System))
	builder.WriteString(", ")
	builder.WriteString("bps=")
	builder.WriteString(fmt.Sprintf("%v", s.Bps))
	builder.WriteString(", ")
	builder.WriteString("pps=")
	builder.WriteString(fmt.Sprintf("%v", s.Pps))
	builder.WriteString(", ")
	builder.WriteString("bps_count=")
	builder.WriteString(fmt.Sprintf("%v", s.BpsCount))
	builder.WriteString(", ")
	builder.WriteString("pps_count=")
	builder.WriteString(fmt.Sprintf("%v", s.PpsCount))
	builder.WriteString(", ")
	builder.WriteString("isp_code=")
	builder.WriteString(fmt.Sprintf("%v", s.IspCode))
	builder.WriteByte(')')
	return builder.String()
}

// Strategies is a parsable slice of Strategy.
type Strategies []*Strategy
