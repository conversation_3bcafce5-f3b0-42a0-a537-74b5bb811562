// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/matrixstrategy"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// MatrixStrategy is the model entity for the MatrixStrategy schema.
type MatrixStrategy struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// Region holds the value of the "region" field.
	Region string `json:"region,omitempty"`
	// NetType holds the value of the "net_type" field.
	NetType string `json:"net_type,omitempty"`
	// Isp holds the value of the "isp" field.
	Isp string `json:"isp,omitempty"`
	// MonitorBps holds the value of the "monitor_bps" field.
	MonitorBps int64 `json:"monitor_bps,omitempty"`
	// DragBps holds the value of the "drag_bps" field.
	DragBps int64 `json:"drag_bps,omitempty"`
	// DragType holds the value of the "drag_type" field.
	DragType int `json:"drag_type,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the MatrixStrategyQuery when eager-loading is set.
	Edges        MatrixStrategyEdges `json:"edges"`
	selectValues sql.SelectValues
}

// MatrixStrategyEdges holds the relations/edges for other nodes in the graph.
type MatrixStrategyEdges struct {
	// MatrixStrategyAlerts holds the value of the matrix_strategy_alerts edge.
	MatrixStrategyAlerts []*MatrixSpectrumAlert `json:"matrix_strategy_alerts,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// MatrixStrategyAlertsOrErr returns the MatrixStrategyAlerts value or an error if the edge
// was not loaded in eager-loading.
func (e MatrixStrategyEdges) MatrixStrategyAlertsOrErr() ([]*MatrixSpectrumAlert, error) {
	if e.loadedTypes[0] {
		return e.MatrixStrategyAlerts, nil
	}
	return nil, &NotLoadedError{edge: "matrix_strategy_alerts"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*MatrixStrategy) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case matrixstrategy.FieldID, matrixstrategy.FieldMonitorBps, matrixstrategy.FieldDragBps, matrixstrategy.FieldDragType:
			values[i] = new(sql.NullInt64)
		case matrixstrategy.FieldRemark, matrixstrategy.FieldName, matrixstrategy.FieldRegion, matrixstrategy.FieldNetType, matrixstrategy.FieldIsp:
			values[i] = new(sql.NullString)
		case matrixstrategy.FieldCreatedAt, matrixstrategy.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the MatrixStrategy fields.
func (ms *MatrixStrategy) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case matrixstrategy.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			ms.ID = int(value.Int64)
		case matrixstrategy.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ms.CreatedAt = value.Time
			}
		case matrixstrategy.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ms.UpdatedAt = value.Time
			}
		case matrixstrategy.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				ms.Remark = new(string)
				*ms.Remark = value.String
			}
		case matrixstrategy.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				ms.Name = value.String
			}
		case matrixstrategy.FieldRegion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field region", values[i])
			} else if value.Valid {
				ms.Region = value.String
			}
		case matrixstrategy.FieldNetType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field net_type", values[i])
			} else if value.Valid {
				ms.NetType = value.String
			}
		case matrixstrategy.FieldIsp:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field isp", values[i])
			} else if value.Valid {
				ms.Isp = value.String
			}
		case matrixstrategy.FieldMonitorBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field monitor_bps", values[i])
			} else if value.Valid {
				ms.MonitorBps = value.Int64
			}
		case matrixstrategy.FieldDragBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field drag_bps", values[i])
			} else if value.Valid {
				ms.DragBps = value.Int64
			}
		case matrixstrategy.FieldDragType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field drag_type", values[i])
			} else if value.Valid {
				ms.DragType = int(value.Int64)
			}
		default:
			ms.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the MatrixStrategy.
// This includes values selected through modifiers, order, etc.
func (ms *MatrixStrategy) Value(name string) (ent.Value, error) {
	return ms.selectValues.Get(name)
}

// QueryMatrixStrategyAlerts queries the "matrix_strategy_alerts" edge of the MatrixStrategy entity.
func (ms *MatrixStrategy) QueryMatrixStrategyAlerts() *MatrixSpectrumAlertQuery {
	return NewMatrixStrategyClient(ms.config).QueryMatrixStrategyAlerts(ms)
}

// Update returns a builder for updating this MatrixStrategy.
// Note that you need to call MatrixStrategy.Unwrap() before calling this method if this MatrixStrategy
// was returned from a transaction, and the transaction was committed or rolled back.
func (ms *MatrixStrategy) Update() *MatrixStrategyUpdateOne {
	return NewMatrixStrategyClient(ms.config).UpdateOne(ms)
}

// Unwrap unwraps the MatrixStrategy entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ms *MatrixStrategy) Unwrap() *MatrixStrategy {
	_tx, ok := ms.config.driver.(*txDriver)
	if !ok {
		panic("ent: MatrixStrategy is not a transactional entity")
	}
	ms.config.driver = _tx.drv
	return ms
}

// String implements the fmt.Stringer.
func (ms *MatrixStrategy) String() string {
	var builder strings.Builder
	builder.WriteString("MatrixStrategy(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ms.ID))
	builder.WriteString("created_at=")
	builder.WriteString(ms.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ms.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := ms.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(ms.Name)
	builder.WriteString(", ")
	builder.WriteString("region=")
	builder.WriteString(ms.Region)
	builder.WriteString(", ")
	builder.WriteString("net_type=")
	builder.WriteString(ms.NetType)
	builder.WriteString(", ")
	builder.WriteString("isp=")
	builder.WriteString(ms.Isp)
	builder.WriteString(", ")
	builder.WriteString("monitor_bps=")
	builder.WriteString(fmt.Sprintf("%v", ms.MonitorBps))
	builder.WriteString(", ")
	builder.WriteString("drag_bps=")
	builder.WriteString(fmt.Sprintf("%v", ms.DragBps))
	builder.WriteString(", ")
	builder.WriteString("drag_type=")
	builder.WriteString(fmt.Sprintf("%v", ms.DragType))
	builder.WriteByte(')')
	return builder.String()
}

// MatrixStrategies is a parsable slice of MatrixStrategy.
type MatrixStrategies []*MatrixStrategy
