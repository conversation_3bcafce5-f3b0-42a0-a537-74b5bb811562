// Code generated by ent, DO NOT EDIT.

package matrixspectrumalert

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldRemark, v))
}

// WofangID applies equality check predicate on the "wofang_id" field. It's identical to WofangIDEQ.
func WofangID(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldWofangID, v))
}

// MatrixStrategyID applies equality check predicate on the "matrix_strategy_id" field. It's identical to MatrixStrategyIDEQ.
func MatrixStrategyID(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldMatrixStrategyID, v))
}

// Region applies equality check predicate on the "region" field. It's identical to RegionEQ.
func Region(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldRegion, v))
}

// NetType applies equality check predicate on the "net_type" field. It's identical to NetTypeEQ.
func NetType(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldNetType, v))
}

// Isp applies equality check predicate on the "isp" field. It's identical to IspEQ.
func Isp(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldIsp, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldStartTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldEndTime, v))
}

// AttackType applies equality check predicate on the "attack_type" field. It's identical to AttackTypeEQ.
func AttackType(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldAttackType, v))
}

// Bps applies equality check predicate on the "bps" field. It's identical to BpsEQ.
func Bps(v int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldBps, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContainsFold(FieldRemark, v))
}

// WofangIDEQ applies the EQ predicate on the "wofang_id" field.
func WofangIDEQ(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldWofangID, v))
}

// WofangIDNEQ applies the NEQ predicate on the "wofang_id" field.
func WofangIDNEQ(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldWofangID, v))
}

// WofangIDIn applies the In predicate on the "wofang_id" field.
func WofangIDIn(vs ...int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldWofangID, vs...))
}

// WofangIDNotIn applies the NotIn predicate on the "wofang_id" field.
func WofangIDNotIn(vs ...int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldWofangID, vs...))
}

// WofangIDIsNil applies the IsNil predicate on the "wofang_id" field.
func WofangIDIsNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIsNull(FieldWofangID))
}

// WofangIDNotNil applies the NotNil predicate on the "wofang_id" field.
func WofangIDNotNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotNull(FieldWofangID))
}

// MatrixStrategyIDEQ applies the EQ predicate on the "matrix_strategy_id" field.
func MatrixStrategyIDEQ(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldMatrixStrategyID, v))
}

// MatrixStrategyIDNEQ applies the NEQ predicate on the "matrix_strategy_id" field.
func MatrixStrategyIDNEQ(v int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldMatrixStrategyID, v))
}

// MatrixStrategyIDIn applies the In predicate on the "matrix_strategy_id" field.
func MatrixStrategyIDIn(vs ...int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldMatrixStrategyID, vs...))
}

// MatrixStrategyIDNotIn applies the NotIn predicate on the "matrix_strategy_id" field.
func MatrixStrategyIDNotIn(vs ...int) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldMatrixStrategyID, vs...))
}

// MatrixStrategyIDIsNil applies the IsNil predicate on the "matrix_strategy_id" field.
func MatrixStrategyIDIsNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIsNull(FieldMatrixStrategyID))
}

// MatrixStrategyIDNotNil applies the NotNil predicate on the "matrix_strategy_id" field.
func MatrixStrategyIDNotNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotNull(FieldMatrixStrategyID))
}

// IPListIsNil applies the IsNil predicate on the "ip_list" field.
func IPListIsNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIsNull(FieldIPList))
}

// IPListNotNil applies the NotNil predicate on the "ip_list" field.
func IPListNotNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotNull(FieldIPList))
}

// RegionEQ applies the EQ predicate on the "region" field.
func RegionEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldRegion, v))
}

// RegionNEQ applies the NEQ predicate on the "region" field.
func RegionNEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldRegion, v))
}

// RegionIn applies the In predicate on the "region" field.
func RegionIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldRegion, vs...))
}

// RegionNotIn applies the NotIn predicate on the "region" field.
func RegionNotIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldRegion, vs...))
}

// RegionGT applies the GT predicate on the "region" field.
func RegionGT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldRegion, v))
}

// RegionGTE applies the GTE predicate on the "region" field.
func RegionGTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldRegion, v))
}

// RegionLT applies the LT predicate on the "region" field.
func RegionLT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldRegion, v))
}

// RegionLTE applies the LTE predicate on the "region" field.
func RegionLTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldRegion, v))
}

// RegionContains applies the Contains predicate on the "region" field.
func RegionContains(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContains(FieldRegion, v))
}

// RegionHasPrefix applies the HasPrefix predicate on the "region" field.
func RegionHasPrefix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasPrefix(FieldRegion, v))
}

// RegionHasSuffix applies the HasSuffix predicate on the "region" field.
func RegionHasSuffix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasSuffix(FieldRegion, v))
}

// RegionEqualFold applies the EqualFold predicate on the "region" field.
func RegionEqualFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEqualFold(FieldRegion, v))
}

// RegionContainsFold applies the ContainsFold predicate on the "region" field.
func RegionContainsFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContainsFold(FieldRegion, v))
}

// NetTypeEQ applies the EQ predicate on the "net_type" field.
func NetTypeEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldNetType, v))
}

// NetTypeNEQ applies the NEQ predicate on the "net_type" field.
func NetTypeNEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldNetType, v))
}

// NetTypeIn applies the In predicate on the "net_type" field.
func NetTypeIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldNetType, vs...))
}

// NetTypeNotIn applies the NotIn predicate on the "net_type" field.
func NetTypeNotIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldNetType, vs...))
}

// NetTypeGT applies the GT predicate on the "net_type" field.
func NetTypeGT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldNetType, v))
}

// NetTypeGTE applies the GTE predicate on the "net_type" field.
func NetTypeGTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldNetType, v))
}

// NetTypeLT applies the LT predicate on the "net_type" field.
func NetTypeLT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldNetType, v))
}

// NetTypeLTE applies the LTE predicate on the "net_type" field.
func NetTypeLTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldNetType, v))
}

// NetTypeContains applies the Contains predicate on the "net_type" field.
func NetTypeContains(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContains(FieldNetType, v))
}

// NetTypeHasPrefix applies the HasPrefix predicate on the "net_type" field.
func NetTypeHasPrefix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasPrefix(FieldNetType, v))
}

// NetTypeHasSuffix applies the HasSuffix predicate on the "net_type" field.
func NetTypeHasSuffix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasSuffix(FieldNetType, v))
}

// NetTypeEqualFold applies the EqualFold predicate on the "net_type" field.
func NetTypeEqualFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEqualFold(FieldNetType, v))
}

// NetTypeContainsFold applies the ContainsFold predicate on the "net_type" field.
func NetTypeContainsFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContainsFold(FieldNetType, v))
}

// IspEQ applies the EQ predicate on the "isp" field.
func IspEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldIsp, v))
}

// IspNEQ applies the NEQ predicate on the "isp" field.
func IspNEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldIsp, v))
}

// IspIn applies the In predicate on the "isp" field.
func IspIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldIsp, vs...))
}

// IspNotIn applies the NotIn predicate on the "isp" field.
func IspNotIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldIsp, vs...))
}

// IspGT applies the GT predicate on the "isp" field.
func IspGT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldIsp, v))
}

// IspGTE applies the GTE predicate on the "isp" field.
func IspGTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldIsp, v))
}

// IspLT applies the LT predicate on the "isp" field.
func IspLT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldIsp, v))
}

// IspLTE applies the LTE predicate on the "isp" field.
func IspLTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldIsp, v))
}

// IspContains applies the Contains predicate on the "isp" field.
func IspContains(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContains(FieldIsp, v))
}

// IspHasPrefix applies the HasPrefix predicate on the "isp" field.
func IspHasPrefix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasPrefix(FieldIsp, v))
}

// IspHasSuffix applies the HasSuffix predicate on the "isp" field.
func IspHasSuffix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasSuffix(FieldIsp, v))
}

// IspEqualFold applies the EqualFold predicate on the "isp" field.
func IspEqualFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEqualFold(FieldIsp, v))
}

// IspContainsFold applies the ContainsFold predicate on the "isp" field.
func IspContainsFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContainsFold(FieldIsp, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldStartTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotNull(FieldEndTime))
}

// AttackTypeEQ applies the EQ predicate on the "attack_type" field.
func AttackTypeEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldAttackType, v))
}

// AttackTypeNEQ applies the NEQ predicate on the "attack_type" field.
func AttackTypeNEQ(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldAttackType, v))
}

// AttackTypeIn applies the In predicate on the "attack_type" field.
func AttackTypeIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldAttackType, vs...))
}

// AttackTypeNotIn applies the NotIn predicate on the "attack_type" field.
func AttackTypeNotIn(vs ...string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldAttackType, vs...))
}

// AttackTypeGT applies the GT predicate on the "attack_type" field.
func AttackTypeGT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldAttackType, v))
}

// AttackTypeGTE applies the GTE predicate on the "attack_type" field.
func AttackTypeGTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldAttackType, v))
}

// AttackTypeLT applies the LT predicate on the "attack_type" field.
func AttackTypeLT(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldAttackType, v))
}

// AttackTypeLTE applies the LTE predicate on the "attack_type" field.
func AttackTypeLTE(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldAttackType, v))
}

// AttackTypeContains applies the Contains predicate on the "attack_type" field.
func AttackTypeContains(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContains(FieldAttackType, v))
}

// AttackTypeHasPrefix applies the HasPrefix predicate on the "attack_type" field.
func AttackTypeHasPrefix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasPrefix(FieldAttackType, v))
}

// AttackTypeHasSuffix applies the HasSuffix predicate on the "attack_type" field.
func AttackTypeHasSuffix(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldHasSuffix(FieldAttackType, v))
}

// AttackTypeEqualFold applies the EqualFold predicate on the "attack_type" field.
func AttackTypeEqualFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEqualFold(FieldAttackType, v))
}

// AttackTypeContainsFold applies the ContainsFold predicate on the "attack_type" field.
func AttackTypeContainsFold(v string) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldContainsFold(FieldAttackType, v))
}

// BpsEQ applies the EQ predicate on the "bps" field.
func BpsEQ(v int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldEQ(FieldBps, v))
}

// BpsNEQ applies the NEQ predicate on the "bps" field.
func BpsNEQ(v int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNEQ(FieldBps, v))
}

// BpsIn applies the In predicate on the "bps" field.
func BpsIn(vs ...int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIn(FieldBps, vs...))
}

// BpsNotIn applies the NotIn predicate on the "bps" field.
func BpsNotIn(vs ...int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotIn(FieldBps, vs...))
}

// BpsGT applies the GT predicate on the "bps" field.
func BpsGT(v int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGT(FieldBps, v))
}

// BpsGTE applies the GTE predicate on the "bps" field.
func BpsGTE(v int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldGTE(FieldBps, v))
}

// BpsLT applies the LT predicate on the "bps" field.
func BpsLT(v int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLT(FieldBps, v))
}

// BpsLTE applies the LTE predicate on the "bps" field.
func BpsLTE(v int64) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldLTE(FieldBps, v))
}

// AttackInfoIsNil applies the IsNil predicate on the "attack_info" field.
func AttackInfoIsNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldIsNull(FieldAttackInfo))
}

// AttackInfoNotNil applies the NotNil predicate on the "attack_info" field.
func AttackInfoNotNil() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.FieldNotNull(FieldAttackInfo))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMatrixSpectrumDatas applies the HasEdge predicate on the "matrix_spectrum_datas" edge.
func HasMatrixSpectrumDatas() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, MatrixSpectrumDatasTable, MatrixSpectrumDatasColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMatrixSpectrumDatasWith applies the HasEdge predicate on the "matrix_spectrum_datas" edge with a given conditions (other predicates).
func HasMatrixSpectrumDatasWith(preds ...predicate.MatrixSpectrumData) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		step := newMatrixSpectrumDatasStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMatrixStrategy applies the HasEdge predicate on the "matrix_strategy" edge.
func HasMatrixStrategy() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, MatrixStrategyTable, MatrixStrategyColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMatrixStrategyWith applies the HasEdge predicate on the "matrix_strategy" edge with a given conditions (other predicates).
func HasMatrixStrategyWith(preds ...predicate.MatrixStrategy) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		step := newMatrixStrategyStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasWofangTicket applies the HasEdge predicate on the "wofang_ticket" edge.
func HasWofangTicket() predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, WofangTicketTable, WofangTicketColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasWofangTicketWith applies the HasEdge predicate on the "wofang_ticket" edge with a given conditions (other predicates).
func HasWofangTicketWith(preds ...predicate.Wofang) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		step := newWofangTicketStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.MatrixSpectrumAlert) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.MatrixSpectrumAlert) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.MatrixSpectrumAlert) predicate.MatrixSpectrumAlert {
	return predicate.MatrixSpectrumAlert(sql.NotPredicates(p))
}
