// Code generated by ent, DO NOT EDIT.

package matrixspectrumalert

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the matrixspectrumalert type in the database.
	Label = "matrix_spectrum_alert"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldWofangID holds the string denoting the wofang_id field in the database.
	FieldWofangID = "wofang_id"
	// FieldMatrixStrategyID holds the string denoting the matrix_strategy_id field in the database.
	FieldMatrixStrategyID = "matrix_strategy_id"
	// FieldIPList holds the string denoting the ip_list field in the database.
	FieldIPList = "ip_list"
	// FieldRegion holds the string denoting the region field in the database.
	FieldRegion = "region"
	// FieldNetType holds the string denoting the net_type field in the database.
	FieldNetType = "net_type"
	// FieldIsp holds the string denoting the isp field in the database.
	FieldIsp = "isp"
	// FieldStartTime holds the string denoting the start_time field in the database.
	FieldStartTime = "start_time"
	// FieldEndTime holds the string denoting the end_time field in the database.
	FieldEndTime = "end_time"
	// FieldAttackType holds the string denoting the attack_type field in the database.
	FieldAttackType = "attack_type"
	// FieldBps holds the string denoting the bps field in the database.
	FieldBps = "bps"
	// FieldAttackInfo holds the string denoting the attack_info field in the database.
	FieldAttackInfo = "attack_info"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeMatrixSpectrumDatas holds the string denoting the matrix_spectrum_datas edge name in mutations.
	EdgeMatrixSpectrumDatas = "matrix_spectrum_datas"
	// EdgeMatrixStrategy holds the string denoting the matrix_strategy edge name in mutations.
	EdgeMatrixStrategy = "matrix_strategy"
	// EdgeWofangTicket holds the string denoting the wofang_ticket edge name in mutations.
	EdgeWofangTicket = "wofang_ticket"
	// Table holds the table name of the matrixspectrumalert in the database.
	Table = "matrix_spectrum_alerts"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "matrix_spectrum_alerts"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// MatrixSpectrumDatasTable is the table that holds the matrix_spectrum_datas relation/edge.
	MatrixSpectrumDatasTable = "matrix_spectrum_data"
	// MatrixSpectrumDatasInverseTable is the table name for the MatrixSpectrumData entity.
	// It exists in this package in order to avoid circular dependency with the "matrixspectrumdata" package.
	MatrixSpectrumDatasInverseTable = "matrix_spectrum_data"
	// MatrixSpectrumDatasColumn is the table column denoting the matrix_spectrum_datas relation/edge.
	MatrixSpectrumDatasColumn = "matrix_spectrum_alert_id"
	// MatrixStrategyTable is the table that holds the matrix_strategy relation/edge.
	MatrixStrategyTable = "matrix_spectrum_alerts"
	// MatrixStrategyInverseTable is the table name for the MatrixStrategy entity.
	// It exists in this package in order to avoid circular dependency with the "matrixstrategy" package.
	MatrixStrategyInverseTable = "matrix_strategies"
	// MatrixStrategyColumn is the table column denoting the matrix_strategy relation/edge.
	MatrixStrategyColumn = "matrix_strategy_id"
	// WofangTicketTable is the table that holds the wofang_ticket relation/edge.
	WofangTicketTable = "matrix_spectrum_alerts"
	// WofangTicketInverseTable is the table name for the Wofang entity.
	// It exists in this package in order to avoid circular dependency with the "wofang" package.
	WofangTicketInverseTable = "wofangs"
	// WofangTicketColumn is the table column denoting the wofang_ticket relation/edge.
	WofangTicketColumn = "wofang_id"
)

// Columns holds all SQL columns for matrixspectrumalert fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldWofangID,
	FieldMatrixStrategyID,
	FieldIPList,
	FieldRegion,
	FieldNetType,
	FieldIsp,
	FieldStartTime,
	FieldEndTime,
	FieldAttackType,
	FieldBps,
	FieldAttackInfo,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the MatrixSpectrumAlert queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByWofangID orders the results by the wofang_id field.
func ByWofangID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWofangID, opts...).ToFunc()
}

// ByMatrixStrategyID orders the results by the matrix_strategy_id field.
func ByMatrixStrategyID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMatrixStrategyID, opts...).ToFunc()
}

// ByRegion orders the results by the region field.
func ByRegion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRegion, opts...).ToFunc()
}

// ByNetType orders the results by the net_type field.
func ByNetType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNetType, opts...).ToFunc()
}

// ByIsp orders the results by the isp field.
func ByIsp(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsp, opts...).ToFunc()
}

// ByStartTime orders the results by the start_time field.
func ByStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartTime, opts...).ToFunc()
}

// ByEndTime orders the results by the end_time field.
func ByEndTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEndTime, opts...).ToFunc()
}

// ByAttackType orders the results by the attack_type field.
func ByAttackType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAttackType, opts...).ToFunc()
}

// ByBps orders the results by the bps field.
func ByBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBps, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// ByMatrixSpectrumDatasCount orders the results by matrix_spectrum_datas count.
func ByMatrixSpectrumDatasCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMatrixSpectrumDatasStep(), opts...)
	}
}

// ByMatrixSpectrumDatas orders the results by matrix_spectrum_datas terms.
func ByMatrixSpectrumDatas(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMatrixSpectrumDatasStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByMatrixStrategyField orders the results by matrix_strategy field.
func ByMatrixStrategyField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMatrixStrategyStep(), sql.OrderByField(field, opts...))
	}
}

// ByWofangTicketField orders the results by wofang_ticket field.
func ByWofangTicketField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newWofangTicketStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newMatrixSpectrumDatasStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MatrixSpectrumDatasInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, MatrixSpectrumDatasTable, MatrixSpectrumDatasColumn),
	)
}
func newMatrixStrategyStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MatrixStrategyInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, MatrixStrategyTable, MatrixStrategyColumn),
	)
}
func newWofangTicketStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(WofangTicketInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, WofangTicketTable, WofangTicketColumn),
	)
}
