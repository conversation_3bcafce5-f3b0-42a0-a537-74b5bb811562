// Code generated by ent, DO NOT EDIT.

package cloudalert

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldRemark, v))
}

// SrcIP applies equality check predicate on the "src_ip" field. It's identical to SrcIPEQ.
func SrcIP(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldSrcIP, v))
}

// SrcPort applies equality check predicate on the "src_port" field. It's identical to SrcPortEQ.
func SrcPort(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldSrcPort, v))
}

// DstIP applies equality check predicate on the "dst_ip" field. It's identical to DstIPEQ.
func DstIP(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldDstIP, v))
}

// DstPort applies equality check predicate on the "dst_port" field. It's identical to DstPortEQ.
func DstPort(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldDstPort, v))
}

// DefenceMode applies equality check predicate on the "defence_mode" field. It's identical to DefenceModeEQ.
func DefenceMode(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldDefenceMode, v))
}

// FlowMode applies equality check predicate on the "flow_mode" field. It's identical to FlowModeEQ.
func FlowMode(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldFlowMode, v))
}

// TCPAckNum applies equality check predicate on the "tcp_ack_num" field. It's identical to TCPAckNumEQ.
func TCPAckNum(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldTCPAckNum, v))
}

// TCPSeqNum applies equality check predicate on the "tcp_seq_num" field. It's identical to TCPSeqNumEQ.
func TCPSeqNum(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldTCPSeqNum, v))
}

// Protocol applies equality check predicate on the "protocol" field. It's identical to ProtocolEQ.
func Protocol(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldProtocol, v))
}

// DefenceLevel applies equality check predicate on the "defence_level" field. It's identical to DefenceLevelEQ.
func DefenceLevel(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldDefenceLevel, v))
}

// MaxPps applies equality check predicate on the "max_pps" field. It's identical to MaxPpsEQ.
func MaxPps(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldMaxPps, v))
}

// MaxAttackPps applies equality check predicate on the "max_attack_pps" field. It's identical to MaxAttackPpsEQ.
func MaxAttackPps(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldMaxAttackPps, v))
}

// OverlimitPktCount applies equality check predicate on the "overlimit_pkt_count" field. It's identical to OverlimitPktCountEQ.
func OverlimitPktCount(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldOverlimitPktCount, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldStartTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldEndTime, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContainsFold(FieldRemark, v))
}

// SrcIPEQ applies the EQ predicate on the "src_ip" field.
func SrcIPEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldSrcIP, v))
}

// SrcIPNEQ applies the NEQ predicate on the "src_ip" field.
func SrcIPNEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldSrcIP, v))
}

// SrcIPIn applies the In predicate on the "src_ip" field.
func SrcIPIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldSrcIP, vs...))
}

// SrcIPNotIn applies the NotIn predicate on the "src_ip" field.
func SrcIPNotIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldSrcIP, vs...))
}

// SrcIPGT applies the GT predicate on the "src_ip" field.
func SrcIPGT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldSrcIP, v))
}

// SrcIPGTE applies the GTE predicate on the "src_ip" field.
func SrcIPGTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldSrcIP, v))
}

// SrcIPLT applies the LT predicate on the "src_ip" field.
func SrcIPLT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldSrcIP, v))
}

// SrcIPLTE applies the LTE predicate on the "src_ip" field.
func SrcIPLTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldSrcIP, v))
}

// SrcIPContains applies the Contains predicate on the "src_ip" field.
func SrcIPContains(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContains(FieldSrcIP, v))
}

// SrcIPHasPrefix applies the HasPrefix predicate on the "src_ip" field.
func SrcIPHasPrefix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasPrefix(FieldSrcIP, v))
}

// SrcIPHasSuffix applies the HasSuffix predicate on the "src_ip" field.
func SrcIPHasSuffix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasSuffix(FieldSrcIP, v))
}

// SrcIPEqualFold applies the EqualFold predicate on the "src_ip" field.
func SrcIPEqualFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEqualFold(FieldSrcIP, v))
}

// SrcIPContainsFold applies the ContainsFold predicate on the "src_ip" field.
func SrcIPContainsFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContainsFold(FieldSrcIP, v))
}

// SrcPortEQ applies the EQ predicate on the "src_port" field.
func SrcPortEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldSrcPort, v))
}

// SrcPortNEQ applies the NEQ predicate on the "src_port" field.
func SrcPortNEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldSrcPort, v))
}

// SrcPortIn applies the In predicate on the "src_port" field.
func SrcPortIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldSrcPort, vs...))
}

// SrcPortNotIn applies the NotIn predicate on the "src_port" field.
func SrcPortNotIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldSrcPort, vs...))
}

// SrcPortGT applies the GT predicate on the "src_port" field.
func SrcPortGT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldSrcPort, v))
}

// SrcPortGTE applies the GTE predicate on the "src_port" field.
func SrcPortGTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldSrcPort, v))
}

// SrcPortLT applies the LT predicate on the "src_port" field.
func SrcPortLT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldSrcPort, v))
}

// SrcPortLTE applies the LTE predicate on the "src_port" field.
func SrcPortLTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldSrcPort, v))
}

// DstIPEQ applies the EQ predicate on the "dst_ip" field.
func DstIPEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldDstIP, v))
}

// DstIPNEQ applies the NEQ predicate on the "dst_ip" field.
func DstIPNEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldDstIP, v))
}

// DstIPIn applies the In predicate on the "dst_ip" field.
func DstIPIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldDstIP, vs...))
}

// DstIPNotIn applies the NotIn predicate on the "dst_ip" field.
func DstIPNotIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldDstIP, vs...))
}

// DstIPGT applies the GT predicate on the "dst_ip" field.
func DstIPGT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldDstIP, v))
}

// DstIPGTE applies the GTE predicate on the "dst_ip" field.
func DstIPGTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldDstIP, v))
}

// DstIPLT applies the LT predicate on the "dst_ip" field.
func DstIPLT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldDstIP, v))
}

// DstIPLTE applies the LTE predicate on the "dst_ip" field.
func DstIPLTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldDstIP, v))
}

// DstIPContains applies the Contains predicate on the "dst_ip" field.
func DstIPContains(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContains(FieldDstIP, v))
}

// DstIPHasPrefix applies the HasPrefix predicate on the "dst_ip" field.
func DstIPHasPrefix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasPrefix(FieldDstIP, v))
}

// DstIPHasSuffix applies the HasSuffix predicate on the "dst_ip" field.
func DstIPHasSuffix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasSuffix(FieldDstIP, v))
}

// DstIPEqualFold applies the EqualFold predicate on the "dst_ip" field.
func DstIPEqualFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEqualFold(FieldDstIP, v))
}

// DstIPContainsFold applies the ContainsFold predicate on the "dst_ip" field.
func DstIPContainsFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContainsFold(FieldDstIP, v))
}

// DstPortEQ applies the EQ predicate on the "dst_port" field.
func DstPortEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldDstPort, v))
}

// DstPortNEQ applies the NEQ predicate on the "dst_port" field.
func DstPortNEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldDstPort, v))
}

// DstPortIn applies the In predicate on the "dst_port" field.
func DstPortIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldDstPort, vs...))
}

// DstPortNotIn applies the NotIn predicate on the "dst_port" field.
func DstPortNotIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldDstPort, vs...))
}

// DstPortGT applies the GT predicate on the "dst_port" field.
func DstPortGT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldDstPort, v))
}

// DstPortGTE applies the GTE predicate on the "dst_port" field.
func DstPortGTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldDstPort, v))
}

// DstPortLT applies the LT predicate on the "dst_port" field.
func DstPortLT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldDstPort, v))
}

// DstPortLTE applies the LTE predicate on the "dst_port" field.
func DstPortLTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldDstPort, v))
}

// DefenceModeEQ applies the EQ predicate on the "defence_mode" field.
func DefenceModeEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldDefenceMode, v))
}

// DefenceModeNEQ applies the NEQ predicate on the "defence_mode" field.
func DefenceModeNEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldDefenceMode, v))
}

// DefenceModeIn applies the In predicate on the "defence_mode" field.
func DefenceModeIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldDefenceMode, vs...))
}

// DefenceModeNotIn applies the NotIn predicate on the "defence_mode" field.
func DefenceModeNotIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldDefenceMode, vs...))
}

// DefenceModeGT applies the GT predicate on the "defence_mode" field.
func DefenceModeGT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldDefenceMode, v))
}

// DefenceModeGTE applies the GTE predicate on the "defence_mode" field.
func DefenceModeGTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldDefenceMode, v))
}

// DefenceModeLT applies the LT predicate on the "defence_mode" field.
func DefenceModeLT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldDefenceMode, v))
}

// DefenceModeLTE applies the LTE predicate on the "defence_mode" field.
func DefenceModeLTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldDefenceMode, v))
}

// FlowModeEQ applies the EQ predicate on the "flow_mode" field.
func FlowModeEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldFlowMode, v))
}

// FlowModeNEQ applies the NEQ predicate on the "flow_mode" field.
func FlowModeNEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldFlowMode, v))
}

// FlowModeIn applies the In predicate on the "flow_mode" field.
func FlowModeIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldFlowMode, vs...))
}

// FlowModeNotIn applies the NotIn predicate on the "flow_mode" field.
func FlowModeNotIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldFlowMode, vs...))
}

// FlowModeGT applies the GT predicate on the "flow_mode" field.
func FlowModeGT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldFlowMode, v))
}

// FlowModeGTE applies the GTE predicate on the "flow_mode" field.
func FlowModeGTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldFlowMode, v))
}

// FlowModeLT applies the LT predicate on the "flow_mode" field.
func FlowModeLT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldFlowMode, v))
}

// FlowModeLTE applies the LTE predicate on the "flow_mode" field.
func FlowModeLTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldFlowMode, v))
}

// TCPAckNumEQ applies the EQ predicate on the "tcp_ack_num" field.
func TCPAckNumEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldTCPAckNum, v))
}

// TCPAckNumNEQ applies the NEQ predicate on the "tcp_ack_num" field.
func TCPAckNumNEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldTCPAckNum, v))
}

// TCPAckNumIn applies the In predicate on the "tcp_ack_num" field.
func TCPAckNumIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldTCPAckNum, vs...))
}

// TCPAckNumNotIn applies the NotIn predicate on the "tcp_ack_num" field.
func TCPAckNumNotIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldTCPAckNum, vs...))
}

// TCPAckNumGT applies the GT predicate on the "tcp_ack_num" field.
func TCPAckNumGT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldTCPAckNum, v))
}

// TCPAckNumGTE applies the GTE predicate on the "tcp_ack_num" field.
func TCPAckNumGTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldTCPAckNum, v))
}

// TCPAckNumLT applies the LT predicate on the "tcp_ack_num" field.
func TCPAckNumLT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldTCPAckNum, v))
}

// TCPAckNumLTE applies the LTE predicate on the "tcp_ack_num" field.
func TCPAckNumLTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldTCPAckNum, v))
}

// TCPAckNumContains applies the Contains predicate on the "tcp_ack_num" field.
func TCPAckNumContains(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContains(FieldTCPAckNum, v))
}

// TCPAckNumHasPrefix applies the HasPrefix predicate on the "tcp_ack_num" field.
func TCPAckNumHasPrefix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasPrefix(FieldTCPAckNum, v))
}

// TCPAckNumHasSuffix applies the HasSuffix predicate on the "tcp_ack_num" field.
func TCPAckNumHasSuffix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasSuffix(FieldTCPAckNum, v))
}

// TCPAckNumEqualFold applies the EqualFold predicate on the "tcp_ack_num" field.
func TCPAckNumEqualFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEqualFold(FieldTCPAckNum, v))
}

// TCPAckNumContainsFold applies the ContainsFold predicate on the "tcp_ack_num" field.
func TCPAckNumContainsFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContainsFold(FieldTCPAckNum, v))
}

// TCPSeqNumEQ applies the EQ predicate on the "tcp_seq_num" field.
func TCPSeqNumEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldTCPSeqNum, v))
}

// TCPSeqNumNEQ applies the NEQ predicate on the "tcp_seq_num" field.
func TCPSeqNumNEQ(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldTCPSeqNum, v))
}

// TCPSeqNumIn applies the In predicate on the "tcp_seq_num" field.
func TCPSeqNumIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldTCPSeqNum, vs...))
}

// TCPSeqNumNotIn applies the NotIn predicate on the "tcp_seq_num" field.
func TCPSeqNumNotIn(vs ...string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldTCPSeqNum, vs...))
}

// TCPSeqNumGT applies the GT predicate on the "tcp_seq_num" field.
func TCPSeqNumGT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldTCPSeqNum, v))
}

// TCPSeqNumGTE applies the GTE predicate on the "tcp_seq_num" field.
func TCPSeqNumGTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldTCPSeqNum, v))
}

// TCPSeqNumLT applies the LT predicate on the "tcp_seq_num" field.
func TCPSeqNumLT(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldTCPSeqNum, v))
}

// TCPSeqNumLTE applies the LTE predicate on the "tcp_seq_num" field.
func TCPSeqNumLTE(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldTCPSeqNum, v))
}

// TCPSeqNumContains applies the Contains predicate on the "tcp_seq_num" field.
func TCPSeqNumContains(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContains(FieldTCPSeqNum, v))
}

// TCPSeqNumHasPrefix applies the HasPrefix predicate on the "tcp_seq_num" field.
func TCPSeqNumHasPrefix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasPrefix(FieldTCPSeqNum, v))
}

// TCPSeqNumHasSuffix applies the HasSuffix predicate on the "tcp_seq_num" field.
func TCPSeqNumHasSuffix(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldHasSuffix(FieldTCPSeqNum, v))
}

// TCPSeqNumEqualFold applies the EqualFold predicate on the "tcp_seq_num" field.
func TCPSeqNumEqualFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEqualFold(FieldTCPSeqNum, v))
}

// TCPSeqNumContainsFold applies the ContainsFold predicate on the "tcp_seq_num" field.
func TCPSeqNumContainsFold(v string) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldContainsFold(FieldTCPSeqNum, v))
}

// ProtocolEQ applies the EQ predicate on the "protocol" field.
func ProtocolEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldProtocol, v))
}

// ProtocolNEQ applies the NEQ predicate on the "protocol" field.
func ProtocolNEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldProtocol, v))
}

// ProtocolIn applies the In predicate on the "protocol" field.
func ProtocolIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldProtocol, vs...))
}

// ProtocolNotIn applies the NotIn predicate on the "protocol" field.
func ProtocolNotIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldProtocol, vs...))
}

// ProtocolGT applies the GT predicate on the "protocol" field.
func ProtocolGT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldProtocol, v))
}

// ProtocolGTE applies the GTE predicate on the "protocol" field.
func ProtocolGTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldProtocol, v))
}

// ProtocolLT applies the LT predicate on the "protocol" field.
func ProtocolLT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldProtocol, v))
}

// ProtocolLTE applies the LTE predicate on the "protocol" field.
func ProtocolLTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldProtocol, v))
}

// DefenceLevelEQ applies the EQ predicate on the "defence_level" field.
func DefenceLevelEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldDefenceLevel, v))
}

// DefenceLevelNEQ applies the NEQ predicate on the "defence_level" field.
func DefenceLevelNEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldDefenceLevel, v))
}

// DefenceLevelIn applies the In predicate on the "defence_level" field.
func DefenceLevelIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldDefenceLevel, vs...))
}

// DefenceLevelNotIn applies the NotIn predicate on the "defence_level" field.
func DefenceLevelNotIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldDefenceLevel, vs...))
}

// DefenceLevelGT applies the GT predicate on the "defence_level" field.
func DefenceLevelGT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldDefenceLevel, v))
}

// DefenceLevelGTE applies the GTE predicate on the "defence_level" field.
func DefenceLevelGTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldDefenceLevel, v))
}

// DefenceLevelLT applies the LT predicate on the "defence_level" field.
func DefenceLevelLT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldDefenceLevel, v))
}

// DefenceLevelLTE applies the LTE predicate on the "defence_level" field.
func DefenceLevelLTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldDefenceLevel, v))
}

// MaxPpsEQ applies the EQ predicate on the "max_pps" field.
func MaxPpsEQ(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldMaxPps, v))
}

// MaxPpsNEQ applies the NEQ predicate on the "max_pps" field.
func MaxPpsNEQ(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldMaxPps, v))
}

// MaxPpsIn applies the In predicate on the "max_pps" field.
func MaxPpsIn(vs ...int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldMaxPps, vs...))
}

// MaxPpsNotIn applies the NotIn predicate on the "max_pps" field.
func MaxPpsNotIn(vs ...int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldMaxPps, vs...))
}

// MaxPpsGT applies the GT predicate on the "max_pps" field.
func MaxPpsGT(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldMaxPps, v))
}

// MaxPpsGTE applies the GTE predicate on the "max_pps" field.
func MaxPpsGTE(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldMaxPps, v))
}

// MaxPpsLT applies the LT predicate on the "max_pps" field.
func MaxPpsLT(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldMaxPps, v))
}

// MaxPpsLTE applies the LTE predicate on the "max_pps" field.
func MaxPpsLTE(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldMaxPps, v))
}

// MaxAttackPpsEQ applies the EQ predicate on the "max_attack_pps" field.
func MaxAttackPpsEQ(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldMaxAttackPps, v))
}

// MaxAttackPpsNEQ applies the NEQ predicate on the "max_attack_pps" field.
func MaxAttackPpsNEQ(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldMaxAttackPps, v))
}

// MaxAttackPpsIn applies the In predicate on the "max_attack_pps" field.
func MaxAttackPpsIn(vs ...int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldMaxAttackPps, vs...))
}

// MaxAttackPpsNotIn applies the NotIn predicate on the "max_attack_pps" field.
func MaxAttackPpsNotIn(vs ...int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldMaxAttackPps, vs...))
}

// MaxAttackPpsGT applies the GT predicate on the "max_attack_pps" field.
func MaxAttackPpsGT(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldMaxAttackPps, v))
}

// MaxAttackPpsGTE applies the GTE predicate on the "max_attack_pps" field.
func MaxAttackPpsGTE(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldMaxAttackPps, v))
}

// MaxAttackPpsLT applies the LT predicate on the "max_attack_pps" field.
func MaxAttackPpsLT(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldMaxAttackPps, v))
}

// MaxAttackPpsLTE applies the LTE predicate on the "max_attack_pps" field.
func MaxAttackPpsLTE(v int64) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldMaxAttackPps, v))
}

// OverlimitPktCountEQ applies the EQ predicate on the "overlimit_pkt_count" field.
func OverlimitPktCountEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldOverlimitPktCount, v))
}

// OverlimitPktCountNEQ applies the NEQ predicate on the "overlimit_pkt_count" field.
func OverlimitPktCountNEQ(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldOverlimitPktCount, v))
}

// OverlimitPktCountIn applies the In predicate on the "overlimit_pkt_count" field.
func OverlimitPktCountIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldOverlimitPktCount, vs...))
}

// OverlimitPktCountNotIn applies the NotIn predicate on the "overlimit_pkt_count" field.
func OverlimitPktCountNotIn(vs ...int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldOverlimitPktCount, vs...))
}

// OverlimitPktCountGT applies the GT predicate on the "overlimit_pkt_count" field.
func OverlimitPktCountGT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldOverlimitPktCount, v))
}

// OverlimitPktCountGTE applies the GTE predicate on the "overlimit_pkt_count" field.
func OverlimitPktCountGTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldOverlimitPktCount, v))
}

// OverlimitPktCountLT applies the LT predicate on the "overlimit_pkt_count" field.
func OverlimitPktCountLT(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldOverlimitPktCount, v))
}

// OverlimitPktCountLTE applies the LTE predicate on the "overlimit_pkt_count" field.
func OverlimitPktCountLTE(v int) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldOverlimitPktCount, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldStartTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.CloudAlert {
	return predicate.CloudAlert(sql.FieldNotNull(FieldEndTime))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.CloudAlert {
	return predicate.CloudAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.CloudAlert {
	return predicate.CloudAlert(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCloudflowDatas applies the HasEdge predicate on the "cloudflow_datas" edge.
func HasCloudflowDatas() predicate.CloudAlert {
	return predicate.CloudAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, CloudflowDatasTable, CloudflowDatasColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCloudflowDatasWith applies the HasEdge predicate on the "cloudflow_datas" edge with a given conditions (other predicates).
func HasCloudflowDatasWith(preds ...predicate.CloudFlowData) predicate.CloudAlert {
	return predicate.CloudAlert(func(s *sql.Selector) {
		step := newCloudflowDatasStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.CloudAlert) predicate.CloudAlert {
	return predicate.CloudAlert(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.CloudAlert) predicate.CloudAlert {
	return predicate.CloudAlert(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.CloudAlert) predicate.CloudAlert {
	return predicate.CloudAlert(sql.NotPredicates(p))
}
