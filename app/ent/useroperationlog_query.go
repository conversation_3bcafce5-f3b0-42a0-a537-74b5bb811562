// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"meta/app/ent/predicate"
	"meta/app/ent/useroperationlog"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserOperationLogQuery is the builder for querying UserOperationLog entities.
type UserOperationLogQuery struct {
	config
	ctx        *QueryContext
	order      []useroperationlog.OrderOption
	inters     []Interceptor
	predicates []predicate.UserOperationLog
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the UserOperationLogQuery builder.
func (uolq *UserOperationLogQuery) Where(ps ...predicate.UserOperationLog) *UserOperationLogQuery {
	uolq.predicates = append(uolq.predicates, ps...)
	return uolq
}

// Limit the number of records to be returned by this query.
func (uolq *UserOperationLogQuery) Limit(limit int) *UserOperationLogQuery {
	uolq.ctx.Limit = &limit
	return uolq
}

// Offset to start from.
func (uolq *UserOperationLogQuery) Offset(offset int) *UserOperationLogQuery {
	uolq.ctx.Offset = &offset
	return uolq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (uolq *UserOperationLogQuery) Unique(unique bool) *UserOperationLogQuery {
	uolq.ctx.Unique = &unique
	return uolq
}

// Order specifies how the records should be ordered.
func (uolq *UserOperationLogQuery) Order(o ...useroperationlog.OrderOption) *UserOperationLogQuery {
	uolq.order = append(uolq.order, o...)
	return uolq
}

// First returns the first UserOperationLog entity from the query.
// Returns a *NotFoundError when no UserOperationLog was found.
func (uolq *UserOperationLogQuery) First(ctx context.Context) (*UserOperationLog, error) {
	nodes, err := uolq.Limit(1).All(setContextOp(ctx, uolq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{useroperationlog.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (uolq *UserOperationLogQuery) FirstX(ctx context.Context) *UserOperationLog {
	node, err := uolq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first UserOperationLog ID from the query.
// Returns a *NotFoundError when no UserOperationLog ID was found.
func (uolq *UserOperationLogQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = uolq.Limit(1).IDs(setContextOp(ctx, uolq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{useroperationlog.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (uolq *UserOperationLogQuery) FirstIDX(ctx context.Context) int {
	id, err := uolq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single UserOperationLog entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one UserOperationLog entity is found.
// Returns a *NotFoundError when no UserOperationLog entities are found.
func (uolq *UserOperationLogQuery) Only(ctx context.Context) (*UserOperationLog, error) {
	nodes, err := uolq.Limit(2).All(setContextOp(ctx, uolq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{useroperationlog.Label}
	default:
		return nil, &NotSingularError{useroperationlog.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (uolq *UserOperationLogQuery) OnlyX(ctx context.Context) *UserOperationLog {
	node, err := uolq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only UserOperationLog ID in the query.
// Returns a *NotSingularError when more than one UserOperationLog ID is found.
// Returns a *NotFoundError when no entities are found.
func (uolq *UserOperationLogQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = uolq.Limit(2).IDs(setContextOp(ctx, uolq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{useroperationlog.Label}
	default:
		err = &NotSingularError{useroperationlog.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (uolq *UserOperationLogQuery) OnlyIDX(ctx context.Context) int {
	id, err := uolq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of UserOperationLogs.
func (uolq *UserOperationLogQuery) All(ctx context.Context) ([]*UserOperationLog, error) {
	ctx = setContextOp(ctx, uolq.ctx, "All")
	if err := uolq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*UserOperationLog, *UserOperationLogQuery]()
	return withInterceptors[[]*UserOperationLog](ctx, uolq, qr, uolq.inters)
}

// AllX is like All, but panics if an error occurs.
func (uolq *UserOperationLogQuery) AllX(ctx context.Context) []*UserOperationLog {
	nodes, err := uolq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of UserOperationLog IDs.
func (uolq *UserOperationLogQuery) IDs(ctx context.Context) (ids []int, err error) {
	if uolq.ctx.Unique == nil && uolq.path != nil {
		uolq.Unique(true)
	}
	ctx = setContextOp(ctx, uolq.ctx, "IDs")
	if err = uolq.Select(useroperationlog.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (uolq *UserOperationLogQuery) IDsX(ctx context.Context) []int {
	ids, err := uolq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (uolq *UserOperationLogQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, uolq.ctx, "Count")
	if err := uolq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, uolq, querierCount[*UserOperationLogQuery](), uolq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (uolq *UserOperationLogQuery) CountX(ctx context.Context) int {
	count, err := uolq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (uolq *UserOperationLogQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, uolq.ctx, "Exist")
	switch _, err := uolq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (uolq *UserOperationLogQuery) ExistX(ctx context.Context) bool {
	exist, err := uolq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the UserOperationLogQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (uolq *UserOperationLogQuery) Clone() *UserOperationLogQuery {
	if uolq == nil {
		return nil
	}
	return &UserOperationLogQuery{
		config:     uolq.config,
		ctx:        uolq.ctx.Clone(),
		order:      append([]useroperationlog.OrderOption{}, uolq.order...),
		inters:     append([]Interceptor{}, uolq.inters...),
		predicates: append([]predicate.UserOperationLog{}, uolq.predicates...),
		// clone intermediate query.
		sql:  uolq.sql.Clone(),
		path: uolq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Remark string `json:"remark,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.UserOperationLog.Query().
//		GroupBy(useroperationlog.FieldRemark).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (uolq *UserOperationLogQuery) GroupBy(field string, fields ...string) *UserOperationLogGroupBy {
	uolq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &UserOperationLogGroupBy{build: uolq}
	grbuild.flds = &uolq.ctx.Fields
	grbuild.label = useroperationlog.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Remark string `json:"remark,omitempty"`
//	}
//
//	client.UserOperationLog.Query().
//		Select(useroperationlog.FieldRemark).
//		Scan(ctx, &v)
func (uolq *UserOperationLogQuery) Select(fields ...string) *UserOperationLogSelect {
	uolq.ctx.Fields = append(uolq.ctx.Fields, fields...)
	sbuild := &UserOperationLogSelect{UserOperationLogQuery: uolq}
	sbuild.label = useroperationlog.Label
	sbuild.flds, sbuild.scan = &uolq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a UserOperationLogSelect configured with the given aggregations.
func (uolq *UserOperationLogQuery) Aggregate(fns ...AggregateFunc) *UserOperationLogSelect {
	return uolq.Select().Aggregate(fns...)
}

func (uolq *UserOperationLogQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range uolq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, uolq); err != nil {
				return err
			}
		}
	}
	for _, f := range uolq.ctx.Fields {
		if !useroperationlog.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if uolq.path != nil {
		prev, err := uolq.path(ctx)
		if err != nil {
			return err
		}
		uolq.sql = prev
	}
	return nil
}

func (uolq *UserOperationLogQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*UserOperationLog, error) {
	var (
		nodes = []*UserOperationLog{}
		_spec = uolq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*UserOperationLog).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &UserOperationLog{config: uolq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, uolq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (uolq *UserOperationLogQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := uolq.querySpec()
	_spec.Node.Columns = uolq.ctx.Fields
	if len(uolq.ctx.Fields) > 0 {
		_spec.Unique = uolq.ctx.Unique != nil && *uolq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, uolq.driver, _spec)
}

func (uolq *UserOperationLogQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(useroperationlog.Table, useroperationlog.Columns, sqlgraph.NewFieldSpec(useroperationlog.FieldID, field.TypeInt))
	_spec.From = uolq.sql
	if unique := uolq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if uolq.path != nil {
		_spec.Unique = true
	}
	if fields := uolq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, useroperationlog.FieldID)
		for i := range fields {
			if fields[i] != useroperationlog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := uolq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := uolq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := uolq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := uolq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (uolq *UserOperationLogQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(uolq.driver.Dialect())
	t1 := builder.Table(useroperationlog.Table)
	columns := uolq.ctx.Fields
	if len(columns) == 0 {
		columns = useroperationlog.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if uolq.sql != nil {
		selector = uolq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if uolq.ctx.Unique != nil && *uolq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range uolq.predicates {
		p(selector)
	}
	for _, p := range uolq.order {
		p(selector)
	}
	if offset := uolq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := uolq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// UserOperationLogGroupBy is the group-by builder for UserOperationLog entities.
type UserOperationLogGroupBy struct {
	selector
	build *UserOperationLogQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (uolgb *UserOperationLogGroupBy) Aggregate(fns ...AggregateFunc) *UserOperationLogGroupBy {
	uolgb.fns = append(uolgb.fns, fns...)
	return uolgb
}

// Scan applies the selector query and scans the result into the given value.
func (uolgb *UserOperationLogGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, uolgb.build.ctx, "GroupBy")
	if err := uolgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserOperationLogQuery, *UserOperationLogGroupBy](ctx, uolgb.build, uolgb, uolgb.build.inters, v)
}

func (uolgb *UserOperationLogGroupBy) sqlScan(ctx context.Context, root *UserOperationLogQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(uolgb.fns))
	for _, fn := range uolgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*uolgb.flds)+len(uolgb.fns))
		for _, f := range *uolgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*uolgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := uolgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// UserOperationLogSelect is the builder for selecting fields of UserOperationLog entities.
type UserOperationLogSelect struct {
	*UserOperationLogQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (uols *UserOperationLogSelect) Aggregate(fns ...AggregateFunc) *UserOperationLogSelect {
	uols.fns = append(uols.fns, fns...)
	return uols
}

// Scan applies the selector query and scans the result into the given value.
func (uols *UserOperationLogSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, uols.ctx, "Select")
	if err := uols.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserOperationLogQuery, *UserOperationLogSelect](ctx, uols.UserOperationLogQuery, uols, uols.inters, v)
}

func (uols *UserOperationLogSelect) sqlScan(ctx context.Context, root *UserOperationLogQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(uols.fns))
	for _, fn := range uols.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*uols.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := uols.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
