// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixstrategy"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixStrategyCreate is the builder for creating a MatrixStrategy entity.
type MatrixStrategyCreate struct {
	config
	mutation *MatrixStrategyMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (msc *MatrixStrategyCreate) SetCreatedAt(t time.Time) *MatrixStrategyCreate {
	msc.mutation.SetCreatedAt(t)
	return msc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (msc *MatrixStrategyCreate) SetNillableCreatedAt(t *time.Time) *MatrixStrategyCreate {
	if t != nil {
		msc.SetCreatedAt(*t)
	}
	return msc
}

// SetUpdatedAt sets the "updated_at" field.
func (msc *MatrixStrategyCreate) SetUpdatedAt(t time.Time) *MatrixStrategyCreate {
	msc.mutation.SetUpdatedAt(t)
	return msc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (msc *MatrixStrategyCreate) SetNillableUpdatedAt(t *time.Time) *MatrixStrategyCreate {
	if t != nil {
		msc.SetUpdatedAt(*t)
	}
	return msc
}

// SetRemark sets the "remark" field.
func (msc *MatrixStrategyCreate) SetRemark(s string) *MatrixStrategyCreate {
	msc.mutation.SetRemark(s)
	return msc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (msc *MatrixStrategyCreate) SetNillableRemark(s *string) *MatrixStrategyCreate {
	if s != nil {
		msc.SetRemark(*s)
	}
	return msc
}

// SetName sets the "name" field.
func (msc *MatrixStrategyCreate) SetName(s string) *MatrixStrategyCreate {
	msc.mutation.SetName(s)
	return msc
}

// SetRegion sets the "region" field.
func (msc *MatrixStrategyCreate) SetRegion(s string) *MatrixStrategyCreate {
	msc.mutation.SetRegion(s)
	return msc
}

// SetNetType sets the "net_type" field.
func (msc *MatrixStrategyCreate) SetNetType(s string) *MatrixStrategyCreate {
	msc.mutation.SetNetType(s)
	return msc
}

// SetIsp sets the "isp" field.
func (msc *MatrixStrategyCreate) SetIsp(s string) *MatrixStrategyCreate {
	msc.mutation.SetIsp(s)
	return msc
}

// SetMonitorBps sets the "monitor_bps" field.
func (msc *MatrixStrategyCreate) SetMonitorBps(i int64) *MatrixStrategyCreate {
	msc.mutation.SetMonitorBps(i)
	return msc
}

// SetDragBps sets the "drag_bps" field.
func (msc *MatrixStrategyCreate) SetDragBps(i int64) *MatrixStrategyCreate {
	msc.mutation.SetDragBps(i)
	return msc
}

// SetDragType sets the "drag_type" field.
func (msc *MatrixStrategyCreate) SetDragType(i int) *MatrixStrategyCreate {
	msc.mutation.SetDragType(i)
	return msc
}

// AddMatrixStrategyAlertIDs adds the "matrix_strategy_alerts" edge to the MatrixSpectrumAlert entity by IDs.
func (msc *MatrixStrategyCreate) AddMatrixStrategyAlertIDs(ids ...int) *MatrixStrategyCreate {
	msc.mutation.AddMatrixStrategyAlertIDs(ids...)
	return msc
}

// AddMatrixStrategyAlerts adds the "matrix_strategy_alerts" edges to the MatrixSpectrumAlert entity.
func (msc *MatrixStrategyCreate) AddMatrixStrategyAlerts(m ...*MatrixSpectrumAlert) *MatrixStrategyCreate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msc.AddMatrixStrategyAlertIDs(ids...)
}

// Mutation returns the MatrixStrategyMutation object of the builder.
func (msc *MatrixStrategyCreate) Mutation() *MatrixStrategyMutation {
	return msc.mutation
}

// Save creates the MatrixStrategy in the database.
func (msc *MatrixStrategyCreate) Save(ctx context.Context) (*MatrixStrategy, error) {
	if err := msc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, msc.sqlSave, msc.mutation, msc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (msc *MatrixStrategyCreate) SaveX(ctx context.Context) *MatrixStrategy {
	v, err := msc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (msc *MatrixStrategyCreate) Exec(ctx context.Context) error {
	_, err := msc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msc *MatrixStrategyCreate) ExecX(ctx context.Context) {
	if err := msc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msc *MatrixStrategyCreate) defaults() error {
	if _, ok := msc.mutation.CreatedAt(); !ok {
		if matrixstrategy.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixstrategy.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := matrixstrategy.DefaultCreatedAt()
		msc.mutation.SetCreatedAt(v)
	}
	if _, ok := msc.mutation.UpdatedAt(); !ok {
		if matrixstrategy.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixstrategy.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixstrategy.DefaultUpdatedAt()
		msc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (msc *MatrixStrategyCreate) check() error {
	if _, ok := msc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "MatrixStrategy.created_at"`)}
	}
	if _, ok := msc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "MatrixStrategy.updated_at"`)}
	}
	if v, ok := msc.mutation.Remark(); ok {
		if err := matrixstrategy.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "MatrixStrategy.remark": %w`, err)}
		}
	}
	if _, ok := msc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "MatrixStrategy.name"`)}
	}
	if v, ok := msc.mutation.Name(); ok {
		if err := matrixstrategy.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "MatrixStrategy.name": %w`, err)}
		}
	}
	if _, ok := msc.mutation.Region(); !ok {
		return &ValidationError{Name: "region", err: errors.New(`ent: missing required field "MatrixStrategy.region"`)}
	}
	if _, ok := msc.mutation.NetType(); !ok {
		return &ValidationError{Name: "net_type", err: errors.New(`ent: missing required field "MatrixStrategy.net_type"`)}
	}
	if _, ok := msc.mutation.Isp(); !ok {
		return &ValidationError{Name: "isp", err: errors.New(`ent: missing required field "MatrixStrategy.isp"`)}
	}
	if _, ok := msc.mutation.MonitorBps(); !ok {
		return &ValidationError{Name: "monitor_bps", err: errors.New(`ent: missing required field "MatrixStrategy.monitor_bps"`)}
	}
	if _, ok := msc.mutation.DragBps(); !ok {
		return &ValidationError{Name: "drag_bps", err: errors.New(`ent: missing required field "MatrixStrategy.drag_bps"`)}
	}
	if _, ok := msc.mutation.DragType(); !ok {
		return &ValidationError{Name: "drag_type", err: errors.New(`ent: missing required field "MatrixStrategy.drag_type"`)}
	}
	return nil
}

func (msc *MatrixStrategyCreate) sqlSave(ctx context.Context) (*MatrixStrategy, error) {
	if err := msc.check(); err != nil {
		return nil, err
	}
	_node, _spec := msc.createSpec()
	if err := sqlgraph.CreateNode(ctx, msc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	msc.mutation.id = &_node.ID
	msc.mutation.done = true
	return _node, nil
}

func (msc *MatrixStrategyCreate) createSpec() (*MatrixStrategy, *sqlgraph.CreateSpec) {
	var (
		_node = &MatrixStrategy{config: msc.config}
		_spec = sqlgraph.NewCreateSpec(matrixstrategy.Table, sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt))
	)
	if value, ok := msc.mutation.CreatedAt(); ok {
		_spec.SetField(matrixstrategy.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := msc.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixstrategy.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := msc.mutation.Remark(); ok {
		_spec.SetField(matrixstrategy.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := msc.mutation.Name(); ok {
		_spec.SetField(matrixstrategy.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := msc.mutation.Region(); ok {
		_spec.SetField(matrixstrategy.FieldRegion, field.TypeString, value)
		_node.Region = value
	}
	if value, ok := msc.mutation.NetType(); ok {
		_spec.SetField(matrixstrategy.FieldNetType, field.TypeString, value)
		_node.NetType = value
	}
	if value, ok := msc.mutation.Isp(); ok {
		_spec.SetField(matrixstrategy.FieldIsp, field.TypeString, value)
		_node.Isp = value
	}
	if value, ok := msc.mutation.MonitorBps(); ok {
		_spec.SetField(matrixstrategy.FieldMonitorBps, field.TypeInt64, value)
		_node.MonitorBps = value
	}
	if value, ok := msc.mutation.DragBps(); ok {
		_spec.SetField(matrixstrategy.FieldDragBps, field.TypeInt64, value)
		_node.DragBps = value
	}
	if value, ok := msc.mutation.DragType(); ok {
		_spec.SetField(matrixstrategy.FieldDragType, field.TypeInt, value)
		_node.DragType = value
	}
	if nodes := msc.mutation.MatrixStrategyAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixstrategy.MatrixStrategyAlertsTable,
			Columns: []string{matrixstrategy.MatrixStrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// MatrixStrategyCreateBulk is the builder for creating many MatrixStrategy entities in bulk.
type MatrixStrategyCreateBulk struct {
	config
	err      error
	builders []*MatrixStrategyCreate
}

// Save creates the MatrixStrategy entities in the database.
func (mscb *MatrixStrategyCreateBulk) Save(ctx context.Context) ([]*MatrixStrategy, error) {
	if mscb.err != nil {
		return nil, mscb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(mscb.builders))
	nodes := make([]*MatrixStrategy, len(mscb.builders))
	mutators := make([]Mutator, len(mscb.builders))
	for i := range mscb.builders {
		func(i int, root context.Context) {
			builder := mscb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*MatrixStrategyMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, mscb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, mscb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, mscb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (mscb *MatrixStrategyCreateBulk) SaveX(ctx context.Context) []*MatrixStrategy {
	v, err := mscb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mscb *MatrixStrategyCreateBulk) Exec(ctx context.Context) error {
	_, err := mscb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mscb *MatrixStrategyCreateBulk) ExecX(ctx context.Context) {
	if err := mscb.Exec(ctx); err != nil {
		panic(err)
	}
}
