// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/strategy"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// StrategyDelete is the builder for deleting a Strategy entity.
type StrategyDelete struct {
	config
	hooks    []Hook
	mutation *StrategyMutation
}

// Where appends a list predicates to the StrategyDelete builder.
func (sd *StrategyDelete) Where(ps ...predicate.Strategy) *StrategyDelete {
	sd.mutation.Where(ps...)
	return sd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (sd *StrategyDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, sd.sqlExec, sd.mutation, sd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (sd *StrategyDelete) ExecX(ctx context.Context) int {
	n, err := sd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (sd *StrategyDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(strategy.Table, sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt))
	if ps := sd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, sd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	sd.mutation.done = true
	return affected, err
}

// StrategyDeleteOne is the builder for deleting a single Strategy entity.
type StrategyDeleteOne struct {
	sd *StrategyDelete
}

// Where appends a list predicates to the StrategyDelete builder.
func (sdo *StrategyDeleteOne) Where(ps ...predicate.Strategy) *StrategyDeleteOne {
	sdo.sd.mutation.Where(ps...)
	return sdo
}

// Exec executes the deletion query.
func (sdo *StrategyDeleteOne) Exec(ctx context.Context) error {
	n, err := sdo.sd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{strategy.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (sdo *StrategyDeleteOne) ExecX(ctx context.Context) {
	if err := sdo.Exec(ctx); err != nil {
		panic(err)
	}
}
