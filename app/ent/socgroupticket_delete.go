// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/socgroupticket"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SocGroupTicketDelete is the builder for deleting a SocGroupTicket entity.
type SocGroupTicketDelete struct {
	config
	hooks    []Hook
	mutation *SocGroupTicketMutation
}

// Where appends a list predicates to the SocGroupTicketDelete builder.
func (sgtd *SocGroupTicketDelete) Where(ps ...predicate.SocGroupTicket) *SocGroupTicketDelete {
	sgtd.mutation.Where(ps...)
	return sgtd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (sgtd *SocGroupTicketDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, sgtd.sqlExec, sgtd.mutation, sgtd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (sgtd *SocGroupTicketDelete) ExecX(ctx context.Context) int {
	n, err := sgtd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (sgtd *SocGroupTicketDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(socgroupticket.Table, sqlgraph.NewFieldSpec(socgroupticket.FieldID, field.TypeInt))
	if ps := sgtd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, sgtd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	sgtd.mutation.done = true
	return affected, err
}

// SocGroupTicketDeleteOne is the builder for deleting a single SocGroupTicket entity.
type SocGroupTicketDeleteOne struct {
	sgtd *SocGroupTicketDelete
}

// Where appends a list predicates to the SocGroupTicketDelete builder.
func (sgtdo *SocGroupTicketDeleteOne) Where(ps ...predicate.SocGroupTicket) *SocGroupTicketDeleteOne {
	sgtdo.sgtd.mutation.Where(ps...)
	return sgtdo
}

// Exec executes the deletion query.
func (sgtdo *SocGroupTicketDeleteOne) Exec(ctx context.Context) error {
	n, err := sgtdo.sgtd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{socgroupticket.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (sgtdo *SocGroupTicketDeleteOne) ExecX(ctx context.Context) {
	if err := sgtdo.Exec(ctx); err != nil {
		panic(err)
	}
}
