// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/protectgroup"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"
	"meta/app/entity/netease"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// SpectrumAlert is the model entity for the SpectrumAlert schema.
type SpectrumAlert struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// ProtectGroupID holds the value of the "protect_group_id" field.
	ProtectGroupID *int `json:"protect_group_id,omitempty"`
	// StrategyID holds the value of the "strategy_id" field.
	StrategyID *int `json:"strategy_id,omitempty"`
	// WofangID holds the value of the "wofang_id" field.
	WofangID *int `json:"wofang_id,omitempty"`
	// ProtectStatus holds the value of the "protect_status" field.
	ProtectStatus *[]int `json:"protect_status,omitempty" query:"protect_status,omitempty"`
	// 被攻击IP
	IP string `json:"ip,omitempty"`
	// 攻击开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 攻击结束时间
	EndTime time.Time `json:"end_time,omitempty"`
	// 攻击类型
	AttackType string `json:"attack_type,omitempty" query:"attack_type"`
	// 告警PPS
	MaxPps int64 `json:"max_pps,omitempty"`
	// 告警BPS
	MaxBps int64 `json:"max_bps,omitempty"`
	// AttackInfo holds the value of the "attack_info" field.
	AttackInfo netease.AttackInfo `json:"attack_info,omitempty"`
	// IspCode holds the value of the "isp_code" field.
	IspCode int `json:"isp_code,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the SpectrumAlertQuery when eager-loading is set.
	Edges        SpectrumAlertEdges `json:"edges"`
	selectValues sql.SelectValues
}

// SpectrumAlertEdges holds the relations/edges for other nodes in the graph.
type SpectrumAlertEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// SpectrumDatas holds the value of the spectrum_datas edge.
	SpectrumDatas []*SpectrumData `json:"spectrum_datas,omitempty"`
	// CleanDatas holds the value of the clean_datas edge.
	CleanDatas []*CleanData `json:"clean_datas,omitempty"`
	// ProtectGroup holds the value of the protect_group edge.
	ProtectGroup *ProtectGroup `json:"protect_group,omitempty"`
	// Strategy holds the value of the strategy edge.
	Strategy *Strategy `json:"strategy,omitempty"`
	// WofangTicket holds the value of the wofang_ticket edge.
	WofangTicket *Wofang `json:"wofang_ticket,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [6]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SpectrumAlertEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// SpectrumDatasOrErr returns the SpectrumDatas value or an error if the edge
// was not loaded in eager-loading.
func (e SpectrumAlertEdges) SpectrumDatasOrErr() ([]*SpectrumData, error) {
	if e.loadedTypes[1] {
		return e.SpectrumDatas, nil
	}
	return nil, &NotLoadedError{edge: "spectrum_datas"}
}

// CleanDatasOrErr returns the CleanDatas value or an error if the edge
// was not loaded in eager-loading.
func (e SpectrumAlertEdges) CleanDatasOrErr() ([]*CleanData, error) {
	if e.loadedTypes[2] {
		return e.CleanDatas, nil
	}
	return nil, &NotLoadedError{edge: "clean_datas"}
}

// ProtectGroupOrErr returns the ProtectGroup value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SpectrumAlertEdges) ProtectGroupOrErr() (*ProtectGroup, error) {
	if e.loadedTypes[3] {
		if e.ProtectGroup == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: protectgroup.Label}
		}
		return e.ProtectGroup, nil
	}
	return nil, &NotLoadedError{edge: "protect_group"}
}

// StrategyOrErr returns the Strategy value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SpectrumAlertEdges) StrategyOrErr() (*Strategy, error) {
	if e.loadedTypes[4] {
		if e.Strategy == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: strategy.Label}
		}
		return e.Strategy, nil
	}
	return nil, &NotLoadedError{edge: "strategy"}
}

// WofangTicketOrErr returns the WofangTicket value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SpectrumAlertEdges) WofangTicketOrErr() (*Wofang, error) {
	if e.loadedTypes[5] {
		if e.WofangTicket == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: wofang.Label}
		}
		return e.WofangTicket, nil
	}
	return nil, &NotLoadedError{edge: "wofang_ticket"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SpectrumAlert) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case spectrumalert.FieldProtectStatus, spectrumalert.FieldAttackInfo:
			values[i] = new([]byte)
		case spectrumalert.FieldID, spectrumalert.FieldTenantID, spectrumalert.FieldProtectGroupID, spectrumalert.FieldStrategyID, spectrumalert.FieldWofangID, spectrumalert.FieldMaxPps, spectrumalert.FieldMaxBps, spectrumalert.FieldIspCode:
			values[i] = new(sql.NullInt64)
		case spectrumalert.FieldRemark, spectrumalert.FieldIP, spectrumalert.FieldAttackType:
			values[i] = new(sql.NullString)
		case spectrumalert.FieldCreatedAt, spectrumalert.FieldUpdatedAt, spectrumalert.FieldStartTime, spectrumalert.FieldEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SpectrumAlert fields.
func (sa *SpectrumAlert) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case spectrumalert.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			sa.ID = int(value.Int64)
		case spectrumalert.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				sa.TenantID = new(int)
				*sa.TenantID = int(value.Int64)
			}
		case spectrumalert.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				sa.CreatedAt = value.Time
			}
		case spectrumalert.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				sa.UpdatedAt = value.Time
			}
		case spectrumalert.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				sa.Remark = new(string)
				*sa.Remark = value.String
			}
		case spectrumalert.FieldProtectGroupID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field protect_group_id", values[i])
			} else if value.Valid {
				sa.ProtectGroupID = new(int)
				*sa.ProtectGroupID = int(value.Int64)
			}
		case spectrumalert.FieldStrategyID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field strategy_id", values[i])
			} else if value.Valid {
				sa.StrategyID = new(int)
				*sa.StrategyID = int(value.Int64)
			}
		case spectrumalert.FieldWofangID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field wofang_id", values[i])
			} else if value.Valid {
				sa.WofangID = new(int)
				*sa.WofangID = int(value.Int64)
			}
		case spectrumalert.FieldProtectStatus:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field protect_status", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sa.ProtectStatus); err != nil {
					return fmt.Errorf("unmarshal field protect_status: %w", err)
				}
			}
		case spectrumalert.FieldIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ip", values[i])
			} else if value.Valid {
				sa.IP = value.String
			}
		case spectrumalert.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				sa.StartTime = value.Time
			}
		case spectrumalert.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				sa.EndTime = value.Time
			}
		case spectrumalert.FieldAttackType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field attack_type", values[i])
			} else if value.Valid {
				sa.AttackType = value.String
			}
		case spectrumalert.FieldMaxPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_pps", values[i])
			} else if value.Valid {
				sa.MaxPps = value.Int64
			}
		case spectrumalert.FieldMaxBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_bps", values[i])
			} else if value.Valid {
				sa.MaxBps = value.Int64
			}
		case spectrumalert.FieldAttackInfo:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field attack_info", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sa.AttackInfo); err != nil {
					return fmt.Errorf("unmarshal field attack_info: %w", err)
				}
			}
		case spectrumalert.FieldIspCode:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field isp_code", values[i])
			} else if value.Valid {
				sa.IspCode = int(value.Int64)
			}
		default:
			sa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SpectrumAlert.
// This includes values selected through modifiers, order, etc.
func (sa *SpectrumAlert) Value(name string) (ent.Value, error) {
	return sa.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the SpectrumAlert entity.
func (sa *SpectrumAlert) QueryTenant() *TenantQuery {
	return NewSpectrumAlertClient(sa.config).QueryTenant(sa)
}

// QuerySpectrumDatas queries the "spectrum_datas" edge of the SpectrumAlert entity.
func (sa *SpectrumAlert) QuerySpectrumDatas() *SpectrumDataQuery {
	return NewSpectrumAlertClient(sa.config).QuerySpectrumDatas(sa)
}

// QueryCleanDatas queries the "clean_datas" edge of the SpectrumAlert entity.
func (sa *SpectrumAlert) QueryCleanDatas() *CleanDataQuery {
	return NewSpectrumAlertClient(sa.config).QueryCleanDatas(sa)
}

// QueryProtectGroup queries the "protect_group" edge of the SpectrumAlert entity.
func (sa *SpectrumAlert) QueryProtectGroup() *ProtectGroupQuery {
	return NewSpectrumAlertClient(sa.config).QueryProtectGroup(sa)
}

// QueryStrategy queries the "strategy" edge of the SpectrumAlert entity.
func (sa *SpectrumAlert) QueryStrategy() *StrategyQuery {
	return NewSpectrumAlertClient(sa.config).QueryStrategy(sa)
}

// QueryWofangTicket queries the "wofang_ticket" edge of the SpectrumAlert entity.
func (sa *SpectrumAlert) QueryWofangTicket() *WofangQuery {
	return NewSpectrumAlertClient(sa.config).QueryWofangTicket(sa)
}

// Update returns a builder for updating this SpectrumAlert.
// Note that you need to call SpectrumAlert.Unwrap() before calling this method if this SpectrumAlert
// was returned from a transaction, and the transaction was committed or rolled back.
func (sa *SpectrumAlert) Update() *SpectrumAlertUpdateOne {
	return NewSpectrumAlertClient(sa.config).UpdateOne(sa)
}

// Unwrap unwraps the SpectrumAlert entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (sa *SpectrumAlert) Unwrap() *SpectrumAlert {
	_tx, ok := sa.config.driver.(*txDriver)
	if !ok {
		panic("ent: SpectrumAlert is not a transactional entity")
	}
	sa.config.driver = _tx.drv
	return sa
}

// String implements the fmt.Stringer.
func (sa *SpectrumAlert) String() string {
	var builder strings.Builder
	builder.WriteString("SpectrumAlert(")
	builder.WriteString(fmt.Sprintf("id=%v, ", sa.ID))
	if v := sa.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(sa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(sa.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := sa.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := sa.ProtectGroupID; v != nil {
		builder.WriteString("protect_group_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := sa.StrategyID; v != nil {
		builder.WriteString("strategy_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := sa.WofangID; v != nil {
		builder.WriteString("wofang_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("protect_status=")
	builder.WriteString(fmt.Sprintf("%v", sa.ProtectStatus))
	builder.WriteString(", ")
	builder.WriteString("ip=")
	builder.WriteString(sa.IP)
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(sa.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end_time=")
	builder.WriteString(sa.EndTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("attack_type=")
	builder.WriteString(sa.AttackType)
	builder.WriteString(", ")
	builder.WriteString("max_pps=")
	builder.WriteString(fmt.Sprintf("%v", sa.MaxPps))
	builder.WriteString(", ")
	builder.WriteString("max_bps=")
	builder.WriteString(fmt.Sprintf("%v", sa.MaxBps))
	builder.WriteString(", ")
	builder.WriteString("attack_info=")
	builder.WriteString(fmt.Sprintf("%v", sa.AttackInfo))
	builder.WriteString(", ")
	builder.WriteString("isp_code=")
	builder.WriteString(fmt.Sprintf("%v", sa.IspCode))
	builder.WriteByte(')')
	return builder.String()
}

// SpectrumAlerts is a parsable slice of SpectrumAlert.
type SpectrumAlerts []*SpectrumAlert
