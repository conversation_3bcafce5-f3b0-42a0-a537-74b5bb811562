// Code generated by ent, DO NOT EDIT.

package matrixspectrumdata

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldUpdatedAt, v))
}

// MatrixSpectrumAlertID applies equality check predicate on the "matrix_spectrum_alert_id" field. It's identical to MatrixSpectrumAlertIDEQ.
func MatrixSpectrumAlertID(v int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldMatrixSpectrumAlertID, v))
}

// Region applies equality check predicate on the "region" field. It's identical to RegionEQ.
func Region(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldRegion, v))
}

// NetType applies equality check predicate on the "net_type" field. It's identical to NetTypeEQ.
func NetType(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldNetType, v))
}

// Isp applies equality check predicate on the "isp" field. It's identical to IspEQ.
func Isp(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldIsp, v))
}

// Bps applies equality check predicate on the "bps" field. It's identical to BpsEQ.
func Bps(v int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldBps, v))
}

// Time applies equality check predicate on the "time" field. It's identical to TimeEQ.
func Time(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldTime, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLTE(FieldUpdatedAt, v))
}

// MatrixSpectrumAlertIDEQ applies the EQ predicate on the "matrix_spectrum_alert_id" field.
func MatrixSpectrumAlertIDEQ(v int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldMatrixSpectrumAlertID, v))
}

// MatrixSpectrumAlertIDNEQ applies the NEQ predicate on the "matrix_spectrum_alert_id" field.
func MatrixSpectrumAlertIDNEQ(v int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldMatrixSpectrumAlertID, v))
}

// MatrixSpectrumAlertIDIn applies the In predicate on the "matrix_spectrum_alert_id" field.
func MatrixSpectrumAlertIDIn(vs ...int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldMatrixSpectrumAlertID, vs...))
}

// MatrixSpectrumAlertIDNotIn applies the NotIn predicate on the "matrix_spectrum_alert_id" field.
func MatrixSpectrumAlertIDNotIn(vs ...int) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldMatrixSpectrumAlertID, vs...))
}

// MatrixSpectrumAlertIDIsNil applies the IsNil predicate on the "matrix_spectrum_alert_id" field.
func MatrixSpectrumAlertIDIsNil() predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIsNull(FieldMatrixSpectrumAlertID))
}

// MatrixSpectrumAlertIDNotNil applies the NotNil predicate on the "matrix_spectrum_alert_id" field.
func MatrixSpectrumAlertIDNotNil() predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotNull(FieldMatrixSpectrumAlertID))
}

// RegionEQ applies the EQ predicate on the "region" field.
func RegionEQ(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldRegion, v))
}

// RegionNEQ applies the NEQ predicate on the "region" field.
func RegionNEQ(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldRegion, v))
}

// RegionIn applies the In predicate on the "region" field.
func RegionIn(vs ...string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldRegion, vs...))
}

// RegionNotIn applies the NotIn predicate on the "region" field.
func RegionNotIn(vs ...string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldRegion, vs...))
}

// RegionGT applies the GT predicate on the "region" field.
func RegionGT(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGT(FieldRegion, v))
}

// RegionGTE applies the GTE predicate on the "region" field.
func RegionGTE(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGTE(FieldRegion, v))
}

// RegionLT applies the LT predicate on the "region" field.
func RegionLT(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLT(FieldRegion, v))
}

// RegionLTE applies the LTE predicate on the "region" field.
func RegionLTE(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLTE(FieldRegion, v))
}

// RegionContains applies the Contains predicate on the "region" field.
func RegionContains(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldContains(FieldRegion, v))
}

// RegionHasPrefix applies the HasPrefix predicate on the "region" field.
func RegionHasPrefix(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldHasPrefix(FieldRegion, v))
}

// RegionHasSuffix applies the HasSuffix predicate on the "region" field.
func RegionHasSuffix(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldHasSuffix(FieldRegion, v))
}

// RegionEqualFold applies the EqualFold predicate on the "region" field.
func RegionEqualFold(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEqualFold(FieldRegion, v))
}

// RegionContainsFold applies the ContainsFold predicate on the "region" field.
func RegionContainsFold(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldContainsFold(FieldRegion, v))
}

// NetTypeEQ applies the EQ predicate on the "net_type" field.
func NetTypeEQ(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldNetType, v))
}

// NetTypeNEQ applies the NEQ predicate on the "net_type" field.
func NetTypeNEQ(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldNetType, v))
}

// NetTypeIn applies the In predicate on the "net_type" field.
func NetTypeIn(vs ...string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldNetType, vs...))
}

// NetTypeNotIn applies the NotIn predicate on the "net_type" field.
func NetTypeNotIn(vs ...string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldNetType, vs...))
}

// NetTypeGT applies the GT predicate on the "net_type" field.
func NetTypeGT(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGT(FieldNetType, v))
}

// NetTypeGTE applies the GTE predicate on the "net_type" field.
func NetTypeGTE(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGTE(FieldNetType, v))
}

// NetTypeLT applies the LT predicate on the "net_type" field.
func NetTypeLT(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLT(FieldNetType, v))
}

// NetTypeLTE applies the LTE predicate on the "net_type" field.
func NetTypeLTE(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLTE(FieldNetType, v))
}

// NetTypeContains applies the Contains predicate on the "net_type" field.
func NetTypeContains(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldContains(FieldNetType, v))
}

// NetTypeHasPrefix applies the HasPrefix predicate on the "net_type" field.
func NetTypeHasPrefix(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldHasPrefix(FieldNetType, v))
}

// NetTypeHasSuffix applies the HasSuffix predicate on the "net_type" field.
func NetTypeHasSuffix(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldHasSuffix(FieldNetType, v))
}

// NetTypeEqualFold applies the EqualFold predicate on the "net_type" field.
func NetTypeEqualFold(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEqualFold(FieldNetType, v))
}

// NetTypeContainsFold applies the ContainsFold predicate on the "net_type" field.
func NetTypeContainsFold(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldContainsFold(FieldNetType, v))
}

// IspEQ applies the EQ predicate on the "isp" field.
func IspEQ(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldIsp, v))
}

// IspNEQ applies the NEQ predicate on the "isp" field.
func IspNEQ(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldIsp, v))
}

// IspIn applies the In predicate on the "isp" field.
func IspIn(vs ...string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldIsp, vs...))
}

// IspNotIn applies the NotIn predicate on the "isp" field.
func IspNotIn(vs ...string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldIsp, vs...))
}

// IspGT applies the GT predicate on the "isp" field.
func IspGT(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGT(FieldIsp, v))
}

// IspGTE applies the GTE predicate on the "isp" field.
func IspGTE(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGTE(FieldIsp, v))
}

// IspLT applies the LT predicate on the "isp" field.
func IspLT(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLT(FieldIsp, v))
}

// IspLTE applies the LTE predicate on the "isp" field.
func IspLTE(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLTE(FieldIsp, v))
}

// IspContains applies the Contains predicate on the "isp" field.
func IspContains(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldContains(FieldIsp, v))
}

// IspHasPrefix applies the HasPrefix predicate on the "isp" field.
func IspHasPrefix(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldHasPrefix(FieldIsp, v))
}

// IspHasSuffix applies the HasSuffix predicate on the "isp" field.
func IspHasSuffix(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldHasSuffix(FieldIsp, v))
}

// IspEqualFold applies the EqualFold predicate on the "isp" field.
func IspEqualFold(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEqualFold(FieldIsp, v))
}

// IspContainsFold applies the ContainsFold predicate on the "isp" field.
func IspContainsFold(v string) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldContainsFold(FieldIsp, v))
}

// BpsEQ applies the EQ predicate on the "bps" field.
func BpsEQ(v int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldBps, v))
}

// BpsNEQ applies the NEQ predicate on the "bps" field.
func BpsNEQ(v int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldBps, v))
}

// BpsIn applies the In predicate on the "bps" field.
func BpsIn(vs ...int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldBps, vs...))
}

// BpsNotIn applies the NotIn predicate on the "bps" field.
func BpsNotIn(vs ...int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldBps, vs...))
}

// BpsGT applies the GT predicate on the "bps" field.
func BpsGT(v int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGT(FieldBps, v))
}

// BpsGTE applies the GTE predicate on the "bps" field.
func BpsGTE(v int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGTE(FieldBps, v))
}

// BpsLT applies the LT predicate on the "bps" field.
func BpsLT(v int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLT(FieldBps, v))
}

// BpsLTE applies the LTE predicate on the "bps" field.
func BpsLTE(v int64) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLTE(FieldBps, v))
}

// TimeEQ applies the EQ predicate on the "time" field.
func TimeEQ(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldEQ(FieldTime, v))
}

// TimeNEQ applies the NEQ predicate on the "time" field.
func TimeNEQ(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNEQ(FieldTime, v))
}

// TimeIn applies the In predicate on the "time" field.
func TimeIn(vs ...time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldIn(FieldTime, vs...))
}

// TimeNotIn applies the NotIn predicate on the "time" field.
func TimeNotIn(vs ...time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldNotIn(FieldTime, vs...))
}

// TimeGT applies the GT predicate on the "time" field.
func TimeGT(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGT(FieldTime, v))
}

// TimeGTE applies the GTE predicate on the "time" field.
func TimeGTE(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldGTE(FieldTime, v))
}

// TimeLT applies the LT predicate on the "time" field.
func TimeLT(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLT(FieldTime, v))
}

// TimeLTE applies the LTE predicate on the "time" field.
func TimeLTE(v time.Time) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.FieldLTE(FieldTime, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMatrixSpectrumAlert applies the HasEdge predicate on the "matrix_spectrum_alert" edge.
func HasMatrixSpectrumAlert() predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, MatrixSpectrumAlertTable, MatrixSpectrumAlertColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMatrixSpectrumAlertWith applies the HasEdge predicate on the "matrix_spectrum_alert" edge with a given conditions (other predicates).
func HasMatrixSpectrumAlertWith(preds ...predicate.MatrixSpectrumAlert) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(func(s *sql.Selector) {
		step := newMatrixSpectrumAlertStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.MatrixSpectrumData) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.MatrixSpectrumData) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.MatrixSpectrumData) predicate.MatrixSpectrumData {
	return predicate.MatrixSpectrumData(sql.NotPredicates(p))
}
