// Code generated by ent, DO NOT EDIT.

package matrixspectrumdata

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the matrixspectrumdata type in the database.
	Label = "matrix_spectrum_data"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldMatrixSpectrumAlertID holds the string denoting the matrix_spectrum_alert_id field in the database.
	FieldMatrixSpectrumAlertID = "matrix_spectrum_alert_id"
	// FieldRegion holds the string denoting the region field in the database.
	FieldRegion = "region"
	// FieldNetType holds the string denoting the net_type field in the database.
	FieldNetType = "net_type"
	// FieldIsp holds the string denoting the isp field in the database.
	FieldIsp = "isp"
	// FieldBps holds the string denoting the bps field in the database.
	FieldBps = "bps"
	// FieldTime holds the string denoting the time field in the database.
	FieldTime = "time"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeMatrixSpectrumAlert holds the string denoting the matrix_spectrum_alert edge name in mutations.
	EdgeMatrixSpectrumAlert = "matrix_spectrum_alert"
	// Table holds the table name of the matrixspectrumdata in the database.
	Table = "matrix_spectrum_data"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "matrix_spectrum_data"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// MatrixSpectrumAlertTable is the table that holds the matrix_spectrum_alert relation/edge.
	MatrixSpectrumAlertTable = "matrix_spectrum_data"
	// MatrixSpectrumAlertInverseTable is the table name for the MatrixSpectrumAlert entity.
	// It exists in this package in order to avoid circular dependency with the "matrixspectrumalert" package.
	MatrixSpectrumAlertInverseTable = "matrix_spectrum_alerts"
	// MatrixSpectrumAlertColumn is the table column denoting the matrix_spectrum_alert relation/edge.
	MatrixSpectrumAlertColumn = "matrix_spectrum_alert_id"
)

// Columns holds all SQL columns for matrixspectrumdata fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldMatrixSpectrumAlertID,
	FieldRegion,
	FieldNetType,
	FieldIsp,
	FieldBps,
	FieldTime,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the MatrixSpectrumData queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByMatrixSpectrumAlertID orders the results by the matrix_spectrum_alert_id field.
func ByMatrixSpectrumAlertID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMatrixSpectrumAlertID, opts...).ToFunc()
}

// ByRegion orders the results by the region field.
func ByRegion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRegion, opts...).ToFunc()
}

// ByNetType orders the results by the net_type field.
func ByNetType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNetType, opts...).ToFunc()
}

// ByIsp orders the results by the isp field.
func ByIsp(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsp, opts...).ToFunc()
}

// ByBps orders the results by the bps field.
func ByBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBps, opts...).ToFunc()
}

// ByTime orders the results by the time field.
func ByTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTime, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// ByMatrixSpectrumAlertField orders the results by matrix_spectrum_alert field.
func ByMatrixSpectrumAlertField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMatrixSpectrumAlertStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newMatrixSpectrumAlertStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MatrixSpectrumAlertInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, MatrixSpectrumAlertTable, MatrixSpectrumAlertColumn),
	)
}
