// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/socgroupticket"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/entity/netease/socgroup"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// SocGroupTicket is the model entity for the SocGroupTicket schema.
type SocGroupTicket struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// 类型
	Type string `json:"type,omitempty"`
	// 描述
	Description string `json:"description,omitempty"`
	// 工单跟踪者id列表
	FollowList *[]int `json:"follow_list,omitempty"`
	// 部门id
	DepartmentID int `json:"department_id,omitempty"`
	// 牵引IP列表
	IPList *[]string `json:"ip_list,omitempty"`
	// 牵引IP列表中最小的物理带宽或压测带宽(非业务流量)，保留两位小数，单位Gbps
	MinBandwidth float32 `json:"min_bandwidth,omitempty"`
	// 清洗方式, 1表示清洗上线，2表示清洗下线 3调优
	DivertType int `json:"divert_type,omitempty"`
	// 操作方式，1表示自动，2表示手动
	OpType int `json:"op_type,omitempty"`
	// 操作时间
	OpTime time.Time `json:"op_time,omitempty"`
	// 参数配置类型，1表示默认，2表示自定义  仅[清洗上线] 有效
	ConfigType int `json:"config_type,omitempty"`
	// 参数配置  仅[清洗上线和清洗调优且configType=2(自定义防护参数)] 有效
	ConfigArgs string `json:"config_args,omitempty"`
	// 产品中文名
	ProductName string `json:"product_name,omitempty"`
	// 产品代号
	ProductCode string `json:"product_code,omitempty"`
	// 紧急联系人列表
	ContactList *[]socgroup.User `json:"contact_list,omitempty"`
	// 集团工单编号
	GroupTicketID int `json:"group_ticket_id,omitempty"`
	// 错误信息
	ErrorInfo string `json:"error_info,omitempty"`
	// 创建用户Id，可选
	CreateUserID *int `json:"create_user_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the SocGroupTicketQuery when eager-loading is set.
	Edges        SocGroupTicketEdges `json:"edges"`
	selectValues sql.SelectValues
}

// SocGroupTicketEdges holds the relations/edges for other nodes in the graph.
type SocGroupTicketEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SocGroupTicketEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SocGroupTicketEdges) UserOrErr() (*User, error) {
	if e.loadedTypes[1] {
		if e.User == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: user.Label}
		}
		return e.User, nil
	}
	return nil, &NotLoadedError{edge: "user"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SocGroupTicket) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case socgroupticket.FieldFollowList, socgroupticket.FieldIPList, socgroupticket.FieldContactList:
			values[i] = new([]byte)
		case socgroupticket.FieldMinBandwidth:
			values[i] = new(sql.NullFloat64)
		case socgroupticket.FieldID, socgroupticket.FieldTenantID, socgroupticket.FieldDepartmentID, socgroupticket.FieldDivertType, socgroupticket.FieldOpType, socgroupticket.FieldConfigType, socgroupticket.FieldGroupTicketID, socgroupticket.FieldCreateUserID:
			values[i] = new(sql.NullInt64)
		case socgroupticket.FieldRemark, socgroupticket.FieldName, socgroupticket.FieldType, socgroupticket.FieldDescription, socgroupticket.FieldConfigArgs, socgroupticket.FieldProductName, socgroupticket.FieldProductCode, socgroupticket.FieldErrorInfo:
			values[i] = new(sql.NullString)
		case socgroupticket.FieldCreatedAt, socgroupticket.FieldUpdatedAt, socgroupticket.FieldOpTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SocGroupTicket fields.
func (sgt *SocGroupTicket) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case socgroupticket.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			sgt.ID = int(value.Int64)
		case socgroupticket.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				sgt.CreatedAt = value.Time
			}
		case socgroupticket.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				sgt.UpdatedAt = value.Time
			}
		case socgroupticket.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				sgt.TenantID = new(int)
				*sgt.TenantID = int(value.Int64)
			}
		case socgroupticket.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				sgt.Remark = new(string)
				*sgt.Remark = value.String
			}
		case socgroupticket.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				sgt.Name = value.String
			}
		case socgroupticket.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				sgt.Type = value.String
			}
		case socgroupticket.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				sgt.Description = value.String
			}
		case socgroupticket.FieldFollowList:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field follow_list", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sgt.FollowList); err != nil {
					return fmt.Errorf("unmarshal field follow_list: %w", err)
				}
			}
		case socgroupticket.FieldDepartmentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field department_id", values[i])
			} else if value.Valid {
				sgt.DepartmentID = int(value.Int64)
			}
		case socgroupticket.FieldIPList:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field ip_list", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sgt.IPList); err != nil {
					return fmt.Errorf("unmarshal field ip_list: %w", err)
				}
			}
		case socgroupticket.FieldMinBandwidth:
			if value, ok := values[i].(*sql.NullFloat64); !ok {
				return fmt.Errorf("unexpected type %T for field min_bandwidth", values[i])
			} else if value.Valid {
				sgt.MinBandwidth = float32(value.Float64)
			}
		case socgroupticket.FieldDivertType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field divert_type", values[i])
			} else if value.Valid {
				sgt.DivertType = int(value.Int64)
			}
		case socgroupticket.FieldOpType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field op_type", values[i])
			} else if value.Valid {
				sgt.OpType = int(value.Int64)
			}
		case socgroupticket.FieldOpTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field op_time", values[i])
			} else if value.Valid {
				sgt.OpTime = value.Time
			}
		case socgroupticket.FieldConfigType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field config_type", values[i])
			} else if value.Valid {
				sgt.ConfigType = int(value.Int64)
			}
		case socgroupticket.FieldConfigArgs:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field config_args", values[i])
			} else if value.Valid {
				sgt.ConfigArgs = value.String
			}
		case socgroupticket.FieldProductName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field product_name", values[i])
			} else if value.Valid {
				sgt.ProductName = value.String
			}
		case socgroupticket.FieldProductCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field product_code", values[i])
			} else if value.Valid {
				sgt.ProductCode = value.String
			}
		case socgroupticket.FieldContactList:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field contact_list", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sgt.ContactList); err != nil {
					return fmt.Errorf("unmarshal field contact_list: %w", err)
				}
			}
		case socgroupticket.FieldGroupTicketID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field group_ticket_id", values[i])
			} else if value.Valid {
				sgt.GroupTicketID = int(value.Int64)
			}
		case socgroupticket.FieldErrorInfo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field error_info", values[i])
			} else if value.Valid {
				sgt.ErrorInfo = value.String
			}
		case socgroupticket.FieldCreateUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field create_user_id", values[i])
			} else if value.Valid {
				sgt.CreateUserID = new(int)
				*sgt.CreateUserID = int(value.Int64)
			}
		default:
			sgt.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SocGroupTicket.
// This includes values selected through modifiers, order, etc.
func (sgt *SocGroupTicket) Value(name string) (ent.Value, error) {
	return sgt.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the SocGroupTicket entity.
func (sgt *SocGroupTicket) QueryTenant() *TenantQuery {
	return NewSocGroupTicketClient(sgt.config).QueryTenant(sgt)
}

// QueryUser queries the "user" edge of the SocGroupTicket entity.
func (sgt *SocGroupTicket) QueryUser() *UserQuery {
	return NewSocGroupTicketClient(sgt.config).QueryUser(sgt)
}

// Update returns a builder for updating this SocGroupTicket.
// Note that you need to call SocGroupTicket.Unwrap() before calling this method if this SocGroupTicket
// was returned from a transaction, and the transaction was committed or rolled back.
func (sgt *SocGroupTicket) Update() *SocGroupTicketUpdateOne {
	return NewSocGroupTicketClient(sgt.config).UpdateOne(sgt)
}

// Unwrap unwraps the SocGroupTicket entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (sgt *SocGroupTicket) Unwrap() *SocGroupTicket {
	_tx, ok := sgt.config.driver.(*txDriver)
	if !ok {
		panic("ent: SocGroupTicket is not a transactional entity")
	}
	sgt.config.driver = _tx.drv
	return sgt
}

// String implements the fmt.Stringer.
func (sgt *SocGroupTicket) String() string {
	var builder strings.Builder
	builder.WriteString("SocGroupTicket(")
	builder.WriteString(fmt.Sprintf("id=%v, ", sgt.ID))
	builder.WriteString("created_at=")
	builder.WriteString(sgt.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(sgt.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := sgt.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := sgt.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(sgt.Name)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(sgt.Type)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(sgt.Description)
	builder.WriteString(", ")
	builder.WriteString("follow_list=")
	builder.WriteString(fmt.Sprintf("%v", sgt.FollowList))
	builder.WriteString(", ")
	builder.WriteString("department_id=")
	builder.WriteString(fmt.Sprintf("%v", sgt.DepartmentID))
	builder.WriteString(", ")
	builder.WriteString("ip_list=")
	builder.WriteString(fmt.Sprintf("%v", sgt.IPList))
	builder.WriteString(", ")
	builder.WriteString("min_bandwidth=")
	builder.WriteString(fmt.Sprintf("%v", sgt.MinBandwidth))
	builder.WriteString(", ")
	builder.WriteString("divert_type=")
	builder.WriteString(fmt.Sprintf("%v", sgt.DivertType))
	builder.WriteString(", ")
	builder.WriteString("op_type=")
	builder.WriteString(fmt.Sprintf("%v", sgt.OpType))
	builder.WriteString(", ")
	builder.WriteString("op_time=")
	builder.WriteString(sgt.OpTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("config_type=")
	builder.WriteString(fmt.Sprintf("%v", sgt.ConfigType))
	builder.WriteString(", ")
	builder.WriteString("config_args=")
	builder.WriteString(sgt.ConfigArgs)
	builder.WriteString(", ")
	builder.WriteString("product_name=")
	builder.WriteString(sgt.ProductName)
	builder.WriteString(", ")
	builder.WriteString("product_code=")
	builder.WriteString(sgt.ProductCode)
	builder.WriteString(", ")
	builder.WriteString("contact_list=")
	builder.WriteString(fmt.Sprintf("%v", sgt.ContactList))
	builder.WriteString(", ")
	builder.WriteString("group_ticket_id=")
	builder.WriteString(fmt.Sprintf("%v", sgt.GroupTicketID))
	builder.WriteString(", ")
	builder.WriteString("error_info=")
	builder.WriteString(sgt.ErrorInfo)
	builder.WriteString(", ")
	if v := sgt.CreateUserID; v != nil {
		builder.WriteString("create_user_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteByte(')')
	return builder.String()
}

// SocGroupTickets is a parsable slice of SocGroupTicket.
type SocGroupTickets []*SocGroupTicket
