// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/notify"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Notify is the model entity for the Notify schema.
type Notify struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// true，通过popo对emails进行通知
	Popo bool `json:"popo,omitempty"`
	// true，通过邮件对emails进行通知
	Email bool `json:"email,omitempty"`
	// true，通过短信对phones进行通知
	Sms bool `json:"sms,omitempty"`
	// true，通过电话对phones进行通知
	Phone bool `json:"phone,omitempty"`
	// 通过popo群进行通知
	PopoGroups *[]string `json:"popo_groups,omitempty"`
	// 邮件列表
	Emails *[]string `json:"emails,omitempty"`
	// 电话列表
	Phones *[]string `json:"phones,omitempty"`
	// 通知IP白名单，告警IP在白名单中将不再通知；项目白名单 > 系统设置白名单
	IPWhitelists *[]string `json:"ip_whitelists,omitempty"`
	// true，系统配置
	System bool `json:"system,omitempty"`
	// 是否启用
	Enabled bool `json:"enabled,omitempty"`
	// true，通过auth获取sa列表，并通过popo进行通知；仅非系统配置生效
	SaNotifyPopo bool `json:"sa_notify_popo,omitempty"`
	// true，通过auth获取sa列表，并通过邮件进行通知；仅非系统配置生效
	SaNotifyEmail bool `json:"sa_notify_email,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the NotifyQuery when eager-loading is set.
	Edges        NotifyEdges `json:"edges"`
	selectValues sql.SelectValues
}

// NotifyEdges holds the relations/edges for other nodes in the graph.
type NotifyEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e NotifyEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Notify) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case notify.FieldPopoGroups, notify.FieldEmails, notify.FieldPhones, notify.FieldIPWhitelists:
			values[i] = new([]byte)
		case notify.FieldPopo, notify.FieldEmail, notify.FieldSms, notify.FieldPhone, notify.FieldSystem, notify.FieldEnabled, notify.FieldSaNotifyPopo, notify.FieldSaNotifyEmail:
			values[i] = new(sql.NullBool)
		case notify.FieldID, notify.FieldTenantID:
			values[i] = new(sql.NullInt64)
		case notify.FieldRemark, notify.FieldName:
			values[i] = new(sql.NullString)
		case notify.FieldCreatedAt, notify.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Notify fields.
func (n *Notify) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case notify.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			n.ID = int(value.Int64)
		case notify.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				n.CreatedAt = value.Time
			}
		case notify.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				n.UpdatedAt = value.Time
			}
		case notify.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				n.TenantID = new(int)
				*n.TenantID = int(value.Int64)
			}
		case notify.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				n.Remark = new(string)
				*n.Remark = value.String
			}
		case notify.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				n.Name = value.String
			}
		case notify.FieldPopo:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field popo", values[i])
			} else if value.Valid {
				n.Popo = value.Bool
			}
		case notify.FieldEmail:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field email", values[i])
			} else if value.Valid {
				n.Email = value.Bool
			}
		case notify.FieldSms:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field sms", values[i])
			} else if value.Valid {
				n.Sms = value.Bool
			}
		case notify.FieldPhone:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field phone", values[i])
			} else if value.Valid {
				n.Phone = value.Bool
			}
		case notify.FieldPopoGroups:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field popo_groups", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.PopoGroups); err != nil {
					return fmt.Errorf("unmarshal field popo_groups: %w", err)
				}
			}
		case notify.FieldEmails:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field emails", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.Emails); err != nil {
					return fmt.Errorf("unmarshal field emails: %w", err)
				}
			}
		case notify.FieldPhones:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field phones", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.Phones); err != nil {
					return fmt.Errorf("unmarshal field phones: %w", err)
				}
			}
		case notify.FieldIPWhitelists:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field ip_whitelists", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &n.IPWhitelists); err != nil {
					return fmt.Errorf("unmarshal field ip_whitelists: %w", err)
				}
			}
		case notify.FieldSystem:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field system", values[i])
			} else if value.Valid {
				n.System = value.Bool
			}
		case notify.FieldEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field enabled", values[i])
			} else if value.Valid {
				n.Enabled = value.Bool
			}
		case notify.FieldSaNotifyPopo:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field sa_notify_popo", values[i])
			} else if value.Valid {
				n.SaNotifyPopo = value.Bool
			}
		case notify.FieldSaNotifyEmail:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field sa_notify_email", values[i])
			} else if value.Valid {
				n.SaNotifyEmail = value.Bool
			}
		default:
			n.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Notify.
// This includes values selected through modifiers, order, etc.
func (n *Notify) Value(name string) (ent.Value, error) {
	return n.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the Notify entity.
func (n *Notify) QueryTenant() *TenantQuery {
	return NewNotifyClient(n.config).QueryTenant(n)
}

// Update returns a builder for updating this Notify.
// Note that you need to call Notify.Unwrap() before calling this method if this Notify
// was returned from a transaction, and the transaction was committed or rolled back.
func (n *Notify) Update() *NotifyUpdateOne {
	return NewNotifyClient(n.config).UpdateOne(n)
}

// Unwrap unwraps the Notify entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (n *Notify) Unwrap() *Notify {
	_tx, ok := n.config.driver.(*txDriver)
	if !ok {
		panic("ent: Notify is not a transactional entity")
	}
	n.config.driver = _tx.drv
	return n
}

// String implements the fmt.Stringer.
func (n *Notify) String() string {
	var builder strings.Builder
	builder.WriteString("Notify(")
	builder.WriteString(fmt.Sprintf("id=%v, ", n.ID))
	builder.WriteString("created_at=")
	builder.WriteString(n.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(n.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := n.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := n.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(n.Name)
	builder.WriteString(", ")
	builder.WriteString("popo=")
	builder.WriteString(fmt.Sprintf("%v", n.Popo))
	builder.WriteString(", ")
	builder.WriteString("email=")
	builder.WriteString(fmt.Sprintf("%v", n.Email))
	builder.WriteString(", ")
	builder.WriteString("sms=")
	builder.WriteString(fmt.Sprintf("%v", n.Sms))
	builder.WriteString(", ")
	builder.WriteString("phone=")
	builder.WriteString(fmt.Sprintf("%v", n.Phone))
	builder.WriteString(", ")
	builder.WriteString("popo_groups=")
	builder.WriteString(fmt.Sprintf("%v", n.PopoGroups))
	builder.WriteString(", ")
	builder.WriteString("emails=")
	builder.WriteString(fmt.Sprintf("%v", n.Emails))
	builder.WriteString(", ")
	builder.WriteString("phones=")
	builder.WriteString(fmt.Sprintf("%v", n.Phones))
	builder.WriteString(", ")
	builder.WriteString("ip_whitelists=")
	builder.WriteString(fmt.Sprintf("%v", n.IPWhitelists))
	builder.WriteString(", ")
	builder.WriteString("system=")
	builder.WriteString(fmt.Sprintf("%v", n.System))
	builder.WriteString(", ")
	builder.WriteString("enabled=")
	builder.WriteString(fmt.Sprintf("%v", n.Enabled))
	builder.WriteString(", ")
	builder.WriteString("sa_notify_popo=")
	builder.WriteString(fmt.Sprintf("%v", n.SaNotifyPopo))
	builder.WriteString(", ")
	builder.WriteString("sa_notify_email=")
	builder.WriteString(fmt.Sprintf("%v", n.SaNotifyEmail))
	builder.WriteByte(')')
	return builder.String()
}

// Notifies is a parsable slice of Notify.
type Notifies []*Notify
