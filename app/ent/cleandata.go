// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/cleandata"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// CleanData is the model entity for the CleanData schema.
type CleanData struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// SpectrumAlertID holds the value of the "spectrum_alert_id" field.
	SpectrumAlertID *int `json:"spectrum_alert_id,omitempty"`
	// IP holds the value of the "ip" field.
	IP string `json:"ip,omitempty"`
	// Time holds the value of the "time" field.
	Time time.Time `json:"time,omitempty"`
	// InBps holds the value of the "in_bps" field.
	InBps int64 `json:"in_bps,omitempty"`
	// OutBps holds the value of the "out_bps" field.
	OutBps int64 `json:"out_bps,omitempty"`
	// InPps holds the value of the "in_pps" field.
	InPps int64 `json:"in_pps,omitempty"`
	// OutPps holds the value of the "out_pps" field.
	OutPps int64 `json:"out_pps,omitempty"`
	// InAckPps holds the value of the "in_ack_pps" field.
	InAckPps int64 `json:"in_ack_pps,omitempty"`
	// OutAckPps holds the value of the "out_ack_pps" field.
	OutAckPps int64 `json:"out_ack_pps,omitempty"`
	// InAckBps holds the value of the "in_ack_bps" field.
	InAckBps int64 `json:"in_ack_bps,omitempty"`
	// OutAckBps holds the value of the "out_ack_bps" field.
	OutAckBps int64 `json:"out_ack_bps,omitempty"`
	// InSynPps holds the value of the "in_syn_pps" field.
	InSynPps int64 `json:"in_syn_pps,omitempty"`
	// OutSynPps holds the value of the "out_syn_pps" field.
	OutSynPps int64 `json:"out_syn_pps,omitempty"`
	// InUDPPps holds the value of the "in_udp_pps" field.
	InUDPPps int64 `json:"in_udp_pps,omitempty"`
	// OutUDPPps holds the value of the "out_udp_pps" field.
	OutUDPPps int64 `json:"out_udp_pps,omitempty"`
	// InUDPBps holds the value of the "in_udp_bps" field.
	InUDPBps int64 `json:"in_udp_bps,omitempty"`
	// OutUDPBps holds the value of the "out_udp_bps" field.
	OutUDPBps int64 `json:"out_udp_bps,omitempty"`
	// InIcmpPps holds the value of the "in_icmp_pps" field.
	InIcmpPps int64 `json:"in_icmp_pps,omitempty"`
	// InIcmpBps holds the value of the "in_icmp_bps" field.
	InIcmpBps int64 `json:"in_icmp_bps,omitempty"`
	// OutIcmpBps holds the value of the "out_icmp_bps" field.
	OutIcmpBps int64 `json:"out_icmp_bps,omitempty"`
	// OutIcmpPps holds the value of the "out_icmp_pps" field.
	OutIcmpPps int64 `json:"out_icmp_pps,omitempty"`
	// InDNSPps holds the value of the "in_dns_pps" field.
	InDNSPps int64 `json:"in_dns_pps,omitempty"`
	// OutDNSPps holds the value of the "out_dns_pps" field.
	OutDNSPps int64 `json:"out_dns_pps,omitempty"`
	// InDNSBps holds the value of the "in_dns_bps" field.
	InDNSBps int64 `json:"in_dns_bps,omitempty"`
	// OutDNSBps holds the value of the "out_dns_bps" field.
	OutDNSBps int64 `json:"out_dns_bps,omitempty"`
	// CFilterID holds the value of the "c_filter_id" field.
	CFilterID int `json:"c_filter_id,omitempty"`
	// AttackFlags holds the value of the "attack_flags" field.
	AttackFlags int `json:"attack_flags,omitempty"`
	// Count holds the value of the "count" field.
	Count int `json:"count,omitempty"`
	// IPType holds the value of the "ip_type" field.
	IPType int `json:"ip_type,omitempty"`
	// CFilter holds the value of the "c_filter" field.
	CFilter *string `json:"c_filter,omitempty"`
	// Host holds the value of the "host" field.
	Host *string `json:"host,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the CleanDataQuery when eager-loading is set.
	Edges        CleanDataEdges `json:"edges"`
	selectValues sql.SelectValues
}

// CleanDataEdges holds the relations/edges for other nodes in the graph.
type CleanDataEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// SpectrumAlert holds the value of the spectrum_alert edge.
	SpectrumAlert *SpectrumAlert `json:"spectrum_alert,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CleanDataEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// SpectrumAlertOrErr returns the SpectrumAlert value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CleanDataEdges) SpectrumAlertOrErr() (*SpectrumAlert, error) {
	if e.loadedTypes[1] {
		if e.SpectrumAlert == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: spectrumalert.Label}
		}
		return e.SpectrumAlert, nil
	}
	return nil, &NotLoadedError{edge: "spectrum_alert"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CleanData) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case cleandata.FieldID, cleandata.FieldTenantID, cleandata.FieldSpectrumAlertID, cleandata.FieldInBps, cleandata.FieldOutBps, cleandata.FieldInPps, cleandata.FieldOutPps, cleandata.FieldInAckPps, cleandata.FieldOutAckPps, cleandata.FieldInAckBps, cleandata.FieldOutAckBps, cleandata.FieldInSynPps, cleandata.FieldOutSynPps, cleandata.FieldInUDPPps, cleandata.FieldOutUDPPps, cleandata.FieldInUDPBps, cleandata.FieldOutUDPBps, cleandata.FieldInIcmpPps, cleandata.FieldInIcmpBps, cleandata.FieldOutIcmpBps, cleandata.FieldOutIcmpPps, cleandata.FieldInDNSPps, cleandata.FieldOutDNSPps, cleandata.FieldInDNSBps, cleandata.FieldOutDNSBps, cleandata.FieldCFilterID, cleandata.FieldAttackFlags, cleandata.FieldCount, cleandata.FieldIPType:
			values[i] = new(sql.NullInt64)
		case cleandata.FieldIP, cleandata.FieldCFilter, cleandata.FieldHost:
			values[i] = new(sql.NullString)
		case cleandata.FieldCreatedAt, cleandata.FieldTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CleanData fields.
func (cd *CleanData) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case cleandata.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			cd.ID = int(value.Int64)
		case cleandata.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				cd.TenantID = new(int)
				*cd.TenantID = int(value.Int64)
			}
		case cleandata.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				cd.CreatedAt = value.Time
			}
		case cleandata.FieldSpectrumAlertID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field spectrum_alert_id", values[i])
			} else if value.Valid {
				cd.SpectrumAlertID = new(int)
				*cd.SpectrumAlertID = int(value.Int64)
			}
		case cleandata.FieldIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ip", values[i])
			} else if value.Valid {
				cd.IP = value.String
			}
		case cleandata.FieldTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field time", values[i])
			} else if value.Valid {
				cd.Time = value.Time
			}
		case cleandata.FieldInBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_bps", values[i])
			} else if value.Valid {
				cd.InBps = value.Int64
			}
		case cleandata.FieldOutBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_bps", values[i])
			} else if value.Valid {
				cd.OutBps = value.Int64
			}
		case cleandata.FieldInPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_pps", values[i])
			} else if value.Valid {
				cd.InPps = value.Int64
			}
		case cleandata.FieldOutPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_pps", values[i])
			} else if value.Valid {
				cd.OutPps = value.Int64
			}
		case cleandata.FieldInAckPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_ack_pps", values[i])
			} else if value.Valid {
				cd.InAckPps = value.Int64
			}
		case cleandata.FieldOutAckPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_ack_pps", values[i])
			} else if value.Valid {
				cd.OutAckPps = value.Int64
			}
		case cleandata.FieldInAckBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_ack_bps", values[i])
			} else if value.Valid {
				cd.InAckBps = value.Int64
			}
		case cleandata.FieldOutAckBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_ack_bps", values[i])
			} else if value.Valid {
				cd.OutAckBps = value.Int64
			}
		case cleandata.FieldInSynPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_syn_pps", values[i])
			} else if value.Valid {
				cd.InSynPps = value.Int64
			}
		case cleandata.FieldOutSynPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_syn_pps", values[i])
			} else if value.Valid {
				cd.OutSynPps = value.Int64
			}
		case cleandata.FieldInUDPPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_udp_pps", values[i])
			} else if value.Valid {
				cd.InUDPPps = value.Int64
			}
		case cleandata.FieldOutUDPPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_udp_pps", values[i])
			} else if value.Valid {
				cd.OutUDPPps = value.Int64
			}
		case cleandata.FieldInUDPBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_udp_bps", values[i])
			} else if value.Valid {
				cd.InUDPBps = value.Int64
			}
		case cleandata.FieldOutUDPBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_udp_bps", values[i])
			} else if value.Valid {
				cd.OutUDPBps = value.Int64
			}
		case cleandata.FieldInIcmpPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_icmp_pps", values[i])
			} else if value.Valid {
				cd.InIcmpPps = value.Int64
			}
		case cleandata.FieldInIcmpBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_icmp_bps", values[i])
			} else if value.Valid {
				cd.InIcmpBps = value.Int64
			}
		case cleandata.FieldOutIcmpBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_icmp_bps", values[i])
			} else if value.Valid {
				cd.OutIcmpBps = value.Int64
			}
		case cleandata.FieldOutIcmpPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_icmp_pps", values[i])
			} else if value.Valid {
				cd.OutIcmpPps = value.Int64
			}
		case cleandata.FieldInDNSPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_dns_pps", values[i])
			} else if value.Valid {
				cd.InDNSPps = value.Int64
			}
		case cleandata.FieldOutDNSPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_dns_pps", values[i])
			} else if value.Valid {
				cd.OutDNSPps = value.Int64
			}
		case cleandata.FieldInDNSBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field in_dns_bps", values[i])
			} else if value.Valid {
				cd.InDNSBps = value.Int64
			}
		case cleandata.FieldOutDNSBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field out_dns_bps", values[i])
			} else if value.Valid {
				cd.OutDNSBps = value.Int64
			}
		case cleandata.FieldCFilterID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field c_filter_id", values[i])
			} else if value.Valid {
				cd.CFilterID = int(value.Int64)
			}
		case cleandata.FieldAttackFlags:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field attack_flags", values[i])
			} else if value.Valid {
				cd.AttackFlags = int(value.Int64)
			}
		case cleandata.FieldCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field count", values[i])
			} else if value.Valid {
				cd.Count = int(value.Int64)
			}
		case cleandata.FieldIPType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ip_type", values[i])
			} else if value.Valid {
				cd.IPType = int(value.Int64)
			}
		case cleandata.FieldCFilter:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field c_filter", values[i])
			} else if value.Valid {
				cd.CFilter = new(string)
				*cd.CFilter = value.String
			}
		case cleandata.FieldHost:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field host", values[i])
			} else if value.Valid {
				cd.Host = new(string)
				*cd.Host = value.String
			}
		default:
			cd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CleanData.
// This includes values selected through modifiers, order, etc.
func (cd *CleanData) Value(name string) (ent.Value, error) {
	return cd.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the CleanData entity.
func (cd *CleanData) QueryTenant() *TenantQuery {
	return NewCleanDataClient(cd.config).QueryTenant(cd)
}

// QuerySpectrumAlert queries the "spectrum_alert" edge of the CleanData entity.
func (cd *CleanData) QuerySpectrumAlert() *SpectrumAlertQuery {
	return NewCleanDataClient(cd.config).QuerySpectrumAlert(cd)
}

// Update returns a builder for updating this CleanData.
// Note that you need to call CleanData.Unwrap() before calling this method if this CleanData
// was returned from a transaction, and the transaction was committed or rolled back.
func (cd *CleanData) Update() *CleanDataUpdateOne {
	return NewCleanDataClient(cd.config).UpdateOne(cd)
}

// Unwrap unwraps the CleanData entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cd *CleanData) Unwrap() *CleanData {
	_tx, ok := cd.config.driver.(*txDriver)
	if !ok {
		panic("ent: CleanData is not a transactional entity")
	}
	cd.config.driver = _tx.drv
	return cd
}

// String implements the fmt.Stringer.
func (cd *CleanData) String() string {
	var builder strings.Builder
	builder.WriteString("CleanData(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cd.ID))
	if v := cd.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(cd.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := cd.SpectrumAlertID; v != nil {
		builder.WriteString("spectrum_alert_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("ip=")
	builder.WriteString(cd.IP)
	builder.WriteString(", ")
	builder.WriteString("time=")
	builder.WriteString(cd.Time.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("in_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InBps))
	builder.WriteString(", ")
	builder.WriteString("out_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutBps))
	builder.WriteString(", ")
	builder.WriteString("in_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InPps))
	builder.WriteString(", ")
	builder.WriteString("out_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutPps))
	builder.WriteString(", ")
	builder.WriteString("in_ack_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InAckPps))
	builder.WriteString(", ")
	builder.WriteString("out_ack_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutAckPps))
	builder.WriteString(", ")
	builder.WriteString("in_ack_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InAckBps))
	builder.WriteString(", ")
	builder.WriteString("out_ack_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutAckBps))
	builder.WriteString(", ")
	builder.WriteString("in_syn_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InSynPps))
	builder.WriteString(", ")
	builder.WriteString("out_syn_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutSynPps))
	builder.WriteString(", ")
	builder.WriteString("in_udp_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InUDPPps))
	builder.WriteString(", ")
	builder.WriteString("out_udp_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutUDPPps))
	builder.WriteString(", ")
	builder.WriteString("in_udp_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InUDPBps))
	builder.WriteString(", ")
	builder.WriteString("out_udp_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutUDPBps))
	builder.WriteString(", ")
	builder.WriteString("in_icmp_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InIcmpPps))
	builder.WriteString(", ")
	builder.WriteString("in_icmp_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InIcmpBps))
	builder.WriteString(", ")
	builder.WriteString("out_icmp_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutIcmpBps))
	builder.WriteString(", ")
	builder.WriteString("out_icmp_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutIcmpPps))
	builder.WriteString(", ")
	builder.WriteString("in_dns_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InDNSPps))
	builder.WriteString(", ")
	builder.WriteString("out_dns_pps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutDNSPps))
	builder.WriteString(", ")
	builder.WriteString("in_dns_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.InDNSBps))
	builder.WriteString(", ")
	builder.WriteString("out_dns_bps=")
	builder.WriteString(fmt.Sprintf("%v", cd.OutDNSBps))
	builder.WriteString(", ")
	builder.WriteString("c_filter_id=")
	builder.WriteString(fmt.Sprintf("%v", cd.CFilterID))
	builder.WriteString(", ")
	builder.WriteString("attack_flags=")
	builder.WriteString(fmt.Sprintf("%v", cd.AttackFlags))
	builder.WriteString(", ")
	builder.WriteString("count=")
	builder.WriteString(fmt.Sprintf("%v", cd.Count))
	builder.WriteString(", ")
	builder.WriteString("ip_type=")
	builder.WriteString(fmt.Sprintf("%v", cd.IPType))
	builder.WriteString(", ")
	if v := cd.CFilter; v != nil {
		builder.WriteString("c_filter=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := cd.Host; v != nil {
		builder.WriteString("host=")
		builder.WriteString(*v)
	}
	builder.WriteByte(')')
	return builder.String()
}

// CleanDataSlice is a parsable slice of CleanData.
type CleanDataSlice []*CleanData
