// Code generated by ent, DO NOT EDIT.

package notify

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Notify {
	return predicate.Notify(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Notify {
	return predicate.Notify(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Notify {
	return predicate.Notify(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Notify {
	return predicate.Notify(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Notify {
	return predicate.Notify(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Notify {
	return predicate.Notify(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldUpdatedAt, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldTenantID, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldRemark, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldName, v))
}

// Popo applies equality check predicate on the "popo" field. It's identical to PopoEQ.
func Popo(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldPopo, v))
}

// Email applies equality check predicate on the "email" field. It's identical to EmailEQ.
func Email(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldEmail, v))
}

// Sms applies equality check predicate on the "sms" field. It's identical to SmsEQ.
func Sms(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldSms, v))
}

// Phone applies equality check predicate on the "phone" field. It's identical to PhoneEQ.
func Phone(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldPhone, v))
}

// System applies equality check predicate on the "system" field. It's identical to SystemEQ.
func System(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldSystem, v))
}

// Enabled applies equality check predicate on the "enabled" field. It's identical to EnabledEQ.
func Enabled(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldEnabled, v))
}

// SaNotifyPopo applies equality check predicate on the "sa_notify_popo" field. It's identical to SaNotifyPopoEQ.
func SaNotifyPopo(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldSaNotifyPopo, v))
}

// SaNotifyEmail applies equality check predicate on the "sa_notify_email" field. It's identical to SaNotifyEmailEQ.
func SaNotifyEmail(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldSaNotifyEmail, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Notify {
	return predicate.Notify(sql.FieldLTE(FieldUpdatedAt, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.Notify {
	return predicate.Notify(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.Notify {
	return predicate.Notify(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.Notify {
	return predicate.Notify(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.Notify {
	return predicate.Notify(sql.FieldNotNull(FieldTenantID))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.Notify {
	return predicate.Notify(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.Notify {
	return predicate.Notify(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.Notify {
	return predicate.Notify(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.Notify {
	return predicate.Notify(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.Notify {
	return predicate.Notify(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.Notify {
	return predicate.Notify(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.Notify {
	return predicate.Notify(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.Notify {
	return predicate.Notify(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.Notify {
	return predicate.Notify(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.Notify {
	return predicate.Notify(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.Notify {
	return predicate.Notify(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.Notify {
	return predicate.Notify(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.Notify {
	return predicate.Notify(sql.FieldContainsFold(FieldRemark, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Notify {
	return predicate.Notify(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Notify {
	return predicate.Notify(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Notify {
	return predicate.Notify(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Notify {
	return predicate.Notify(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Notify {
	return predicate.Notify(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Notify {
	return predicate.Notify(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Notify {
	return predicate.Notify(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Notify {
	return predicate.Notify(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Notify {
	return predicate.Notify(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Notify {
	return predicate.Notify(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Notify {
	return predicate.Notify(sql.FieldContainsFold(FieldName, v))
}

// PopoEQ applies the EQ predicate on the "popo" field.
func PopoEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldPopo, v))
}

// PopoNEQ applies the NEQ predicate on the "popo" field.
func PopoNEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldPopo, v))
}

// EmailEQ applies the EQ predicate on the "email" field.
func EmailEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldEmail, v))
}

// EmailNEQ applies the NEQ predicate on the "email" field.
func EmailNEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldEmail, v))
}

// SmsEQ applies the EQ predicate on the "sms" field.
func SmsEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldSms, v))
}

// SmsNEQ applies the NEQ predicate on the "sms" field.
func SmsNEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldSms, v))
}

// PhoneEQ applies the EQ predicate on the "phone" field.
func PhoneEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldPhone, v))
}

// PhoneNEQ applies the NEQ predicate on the "phone" field.
func PhoneNEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldPhone, v))
}

// SystemEQ applies the EQ predicate on the "system" field.
func SystemEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldSystem, v))
}

// SystemNEQ applies the NEQ predicate on the "system" field.
func SystemNEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldSystem, v))
}

// EnabledEQ applies the EQ predicate on the "enabled" field.
func EnabledEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldEnabled, v))
}

// EnabledNEQ applies the NEQ predicate on the "enabled" field.
func EnabledNEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldEnabled, v))
}

// SaNotifyPopoEQ applies the EQ predicate on the "sa_notify_popo" field.
func SaNotifyPopoEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldSaNotifyPopo, v))
}

// SaNotifyPopoNEQ applies the NEQ predicate on the "sa_notify_popo" field.
func SaNotifyPopoNEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldSaNotifyPopo, v))
}

// SaNotifyEmailEQ applies the EQ predicate on the "sa_notify_email" field.
func SaNotifyEmailEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldEQ(FieldSaNotifyEmail, v))
}

// SaNotifyEmailNEQ applies the NEQ predicate on the "sa_notify_email" field.
func SaNotifyEmailNEQ(v bool) predicate.Notify {
	return predicate.Notify(sql.FieldNEQ(FieldSaNotifyEmail, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.Notify {
	return predicate.Notify(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.Notify {
	return predicate.Notify(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Notify) predicate.Notify {
	return predicate.Notify(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Notify) predicate.Notify {
	return predicate.Notify(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Notify) predicate.Notify {
	return predicate.Notify(sql.NotPredicates(p))
}
