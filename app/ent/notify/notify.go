// Code generated by ent, DO NOT EDIT.

package notify

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the notify type in the database.
	Label = "notify"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldPopo holds the string denoting the popo field in the database.
	FieldPopo = "popo"
	// FieldEmail holds the string denoting the email field in the database.
	FieldEmail = "email"
	// FieldSms holds the string denoting the sms field in the database.
	FieldSms = "sms"
	// FieldPhone holds the string denoting the phone field in the database.
	FieldPhone = "phone"
	// FieldPopoGroups holds the string denoting the popo_groups field in the database.
	FieldPopoGroups = "popo_groups"
	// FieldEmails holds the string denoting the emails field in the database.
	FieldEmails = "emails"
	// FieldPhones holds the string denoting the phones field in the database.
	FieldPhones = "phones"
	// FieldIPWhitelists holds the string denoting the ip_whitelists field in the database.
	FieldIPWhitelists = "ip_whitelists"
	// FieldSystem holds the string denoting the system field in the database.
	FieldSystem = "system"
	// FieldEnabled holds the string denoting the enabled field in the database.
	FieldEnabled = "enabled"
	// FieldSaNotifyPopo holds the string denoting the sa_notify_popo field in the database.
	FieldSaNotifyPopo = "sa_notify_popo"
	// FieldSaNotifyEmail holds the string denoting the sa_notify_email field in the database.
	FieldSaNotifyEmail = "sa_notify_email"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// Table holds the table name of the notify in the database.
	Table = "notifies"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "notifies"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
)

// Columns holds all SQL columns for notify fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldTenantID,
	FieldRemark,
	FieldName,
	FieldPopo,
	FieldEmail,
	FieldSms,
	FieldPhone,
	FieldPopoGroups,
	FieldEmails,
	FieldPhones,
	FieldIPWhitelists,
	FieldSystem,
	FieldEnabled,
	FieldSaNotifyPopo,
	FieldSaNotifyEmail,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the Notify queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByPopo orders the results by the popo field.
func ByPopo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPopo, opts...).ToFunc()
}

// ByEmail orders the results by the email field.
func ByEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmail, opts...).ToFunc()
}

// BySms orders the results by the sms field.
func BySms(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSms, opts...).ToFunc()
}

// ByPhone orders the results by the phone field.
func ByPhone(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPhone, opts...).ToFunc()
}

// BySystem orders the results by the system field.
func BySystem(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSystem, opts...).ToFunc()
}

// ByEnabled orders the results by the enabled field.
func ByEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnabled, opts...).ToFunc()
}

// BySaNotifyPopo orders the results by the sa_notify_popo field.
func BySaNotifyPopo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSaNotifyPopo, opts...).ToFunc()
}

// BySaNotifyEmail orders the results by the sa_notify_email field.
func BySaNotifyEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSaNotifyEmail, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
