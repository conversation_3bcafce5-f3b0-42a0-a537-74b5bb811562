// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SpectrumDataUpdate is the builder for updating SpectrumData entities.
type SpectrumDataUpdate struct {
	config
	hooks    []Hook
	mutation *SpectrumDataMutation
}

// Where appends a list predicates to the SpectrumDataUpdate builder.
func (sdu *SpectrumDataUpdate) Where(ps ...predicate.SpectrumData) *SpectrumDataUpdate {
	sdu.mutation.Where(ps...)
	return sdu
}

// SetTenantID sets the "tenant_id" field.
func (sdu *SpectrumDataUpdate) SetTenantID(i int) *SpectrumDataUpdate {
	sdu.mutation.SetTenantID(i)
	return sdu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableTenantID(i *int) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetTenantID(*i)
	}
	return sdu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (sdu *SpectrumDataUpdate) ClearTenantID() *SpectrumDataUpdate {
	sdu.mutation.ClearTenantID()
	return sdu
}

// SetSpectrumAlertID sets the "spectrum_alert_id" field.
func (sdu *SpectrumDataUpdate) SetSpectrumAlertID(i int) *SpectrumDataUpdate {
	sdu.mutation.SetSpectrumAlertID(i)
	return sdu
}

// SetNillableSpectrumAlertID sets the "spectrum_alert_id" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableSpectrumAlertID(i *int) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetSpectrumAlertID(*i)
	}
	return sdu
}

// ClearSpectrumAlertID clears the value of the "spectrum_alert_id" field.
func (sdu *SpectrumDataUpdate) ClearSpectrumAlertID() *SpectrumDataUpdate {
	sdu.mutation.ClearSpectrumAlertID()
	return sdu
}

// SetIP sets the "ip" field.
func (sdu *SpectrumDataUpdate) SetIP(s string) *SpectrumDataUpdate {
	sdu.mutation.SetIP(s)
	return sdu
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableIP(s *string) *SpectrumDataUpdate {
	if s != nil {
		sdu.SetIP(*s)
	}
	return sdu
}

// SetTime sets the "time" field.
func (sdu *SpectrumDataUpdate) SetTime(t time.Time) *SpectrumDataUpdate {
	sdu.mutation.SetTime(t)
	return sdu
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableTime(t *time.Time) *SpectrumDataUpdate {
	if t != nil {
		sdu.SetTime(*t)
	}
	return sdu
}

// SetMonitorID sets the "monitor_id" field.
func (sdu *SpectrumDataUpdate) SetMonitorID(i int) *SpectrumDataUpdate {
	sdu.mutation.ResetMonitorID()
	sdu.mutation.SetMonitorID(i)
	return sdu
}

// SetNillableMonitorID sets the "monitor_id" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableMonitorID(i *int) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetMonitorID(*i)
	}
	return sdu
}

// AddMonitorID adds i to the "monitor_id" field.
func (sdu *SpectrumDataUpdate) AddMonitorID(i int) *SpectrumDataUpdate {
	sdu.mutation.AddMonitorID(i)
	return sdu
}

// SetDataType sets the "data_type" field.
func (sdu *SpectrumDataUpdate) SetDataType(i int) *SpectrumDataUpdate {
	sdu.mutation.ResetDataType()
	sdu.mutation.SetDataType(i)
	return sdu
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableDataType(i *int) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetDataType(*i)
	}
	return sdu
}

// AddDataType adds i to the "data_type" field.
func (sdu *SpectrumDataUpdate) AddDataType(i int) *SpectrumDataUpdate {
	sdu.mutation.AddDataType(i)
	return sdu
}

// SetBps sets the "bps" field.
func (sdu *SpectrumDataUpdate) SetBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetBps()
	sdu.mutation.SetBps(i)
	return sdu
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetBps(*i)
	}
	return sdu
}

// AddBps adds i to the "bps" field.
func (sdu *SpectrumDataUpdate) AddBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddBps(i)
	return sdu
}

// SetPps sets the "pps" field.
func (sdu *SpectrumDataUpdate) SetPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetPps()
	sdu.mutation.SetPps(i)
	return sdu
}

// SetNillablePps sets the "pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillablePps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetPps(*i)
	}
	return sdu
}

// AddPps adds i to the "pps" field.
func (sdu *SpectrumDataUpdate) AddPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddPps(i)
	return sdu
}

// SetSynBps sets the "syn_bps" field.
func (sdu *SpectrumDataUpdate) SetSynBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetSynBps()
	sdu.mutation.SetSynBps(i)
	return sdu
}

// SetNillableSynBps sets the "syn_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableSynBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetSynBps(*i)
	}
	return sdu
}

// AddSynBps adds i to the "syn_bps" field.
func (sdu *SpectrumDataUpdate) AddSynBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddSynBps(i)
	return sdu
}

// SetSynPps sets the "syn_pps" field.
func (sdu *SpectrumDataUpdate) SetSynPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetSynPps()
	sdu.mutation.SetSynPps(i)
	return sdu
}

// SetNillableSynPps sets the "syn_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableSynPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetSynPps(*i)
	}
	return sdu
}

// AddSynPps adds i to the "syn_pps" field.
func (sdu *SpectrumDataUpdate) AddSynPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddSynPps(i)
	return sdu
}

// SetAckBps sets the "ack_bps" field.
func (sdu *SpectrumDataUpdate) SetAckBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetAckBps()
	sdu.mutation.SetAckBps(i)
	return sdu
}

// SetNillableAckBps sets the "ack_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableAckBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetAckBps(*i)
	}
	return sdu
}

// AddAckBps adds i to the "ack_bps" field.
func (sdu *SpectrumDataUpdate) AddAckBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddAckBps(i)
	return sdu
}

// SetAckPps sets the "ack_pps" field.
func (sdu *SpectrumDataUpdate) SetAckPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetAckPps()
	sdu.mutation.SetAckPps(i)
	return sdu
}

// SetNillableAckPps sets the "ack_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableAckPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetAckPps(*i)
	}
	return sdu
}

// AddAckPps adds i to the "ack_pps" field.
func (sdu *SpectrumDataUpdate) AddAckPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddAckPps(i)
	return sdu
}

// SetSynAckBps sets the "syn_ack_bps" field.
func (sdu *SpectrumDataUpdate) SetSynAckBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetSynAckBps()
	sdu.mutation.SetSynAckBps(i)
	return sdu
}

// SetNillableSynAckBps sets the "syn_ack_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableSynAckBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetSynAckBps(*i)
	}
	return sdu
}

// AddSynAckBps adds i to the "syn_ack_bps" field.
func (sdu *SpectrumDataUpdate) AddSynAckBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddSynAckBps(i)
	return sdu
}

// SetSynAckPps sets the "syn_ack_pps" field.
func (sdu *SpectrumDataUpdate) SetSynAckPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetSynAckPps()
	sdu.mutation.SetSynAckPps(i)
	return sdu
}

// SetNillableSynAckPps sets the "syn_ack_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableSynAckPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetSynAckPps(*i)
	}
	return sdu
}

// AddSynAckPps adds i to the "syn_ack_pps" field.
func (sdu *SpectrumDataUpdate) AddSynAckPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddSynAckPps(i)
	return sdu
}

// SetIcmpBps sets the "icmp_bps" field.
func (sdu *SpectrumDataUpdate) SetIcmpBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetIcmpBps()
	sdu.mutation.SetIcmpBps(i)
	return sdu
}

// SetNillableIcmpBps sets the "icmp_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableIcmpBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetIcmpBps(*i)
	}
	return sdu
}

// AddIcmpBps adds i to the "icmp_bps" field.
func (sdu *SpectrumDataUpdate) AddIcmpBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddIcmpBps(i)
	return sdu
}

// SetIcmpPps sets the "icmp_pps" field.
func (sdu *SpectrumDataUpdate) SetIcmpPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetIcmpPps()
	sdu.mutation.SetIcmpPps(i)
	return sdu
}

// SetNillableIcmpPps sets the "icmp_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableIcmpPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetIcmpPps(*i)
	}
	return sdu
}

// AddIcmpPps adds i to the "icmp_pps" field.
func (sdu *SpectrumDataUpdate) AddIcmpPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddIcmpPps(i)
	return sdu
}

// SetSmallPps sets the "small_pps" field.
func (sdu *SpectrumDataUpdate) SetSmallPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetSmallPps()
	sdu.mutation.SetSmallPps(i)
	return sdu
}

// SetNillableSmallPps sets the "small_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableSmallPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetSmallPps(*i)
	}
	return sdu
}

// AddSmallPps adds i to the "small_pps" field.
func (sdu *SpectrumDataUpdate) AddSmallPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddSmallPps(i)
	return sdu
}

// SetNtpPps sets the "ntp_pps" field.
func (sdu *SpectrumDataUpdate) SetNtpPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetNtpPps()
	sdu.mutation.SetNtpPps(i)
	return sdu
}

// SetNillableNtpPps sets the "ntp_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableNtpPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetNtpPps(*i)
	}
	return sdu
}

// AddNtpPps adds i to the "ntp_pps" field.
func (sdu *SpectrumDataUpdate) AddNtpPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddNtpPps(i)
	return sdu
}

// SetNtpBps sets the "ntp_bps" field.
func (sdu *SpectrumDataUpdate) SetNtpBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetNtpBps()
	sdu.mutation.SetNtpBps(i)
	return sdu
}

// SetNillableNtpBps sets the "ntp_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableNtpBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetNtpBps(*i)
	}
	return sdu
}

// AddNtpBps adds i to the "ntp_bps" field.
func (sdu *SpectrumDataUpdate) AddNtpBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddNtpBps(i)
	return sdu
}

// SetDNSQueryPps sets the "dns_query_pps" field.
func (sdu *SpectrumDataUpdate) SetDNSQueryPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetDNSQueryPps()
	sdu.mutation.SetDNSQueryPps(i)
	return sdu
}

// SetNillableDNSQueryPps sets the "dns_query_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableDNSQueryPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetDNSQueryPps(*i)
	}
	return sdu
}

// AddDNSQueryPps adds i to the "dns_query_pps" field.
func (sdu *SpectrumDataUpdate) AddDNSQueryPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddDNSQueryPps(i)
	return sdu
}

// SetDNSQueryBps sets the "dns_query_bps" field.
func (sdu *SpectrumDataUpdate) SetDNSQueryBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetDNSQueryBps()
	sdu.mutation.SetDNSQueryBps(i)
	return sdu
}

// SetNillableDNSQueryBps sets the "dns_query_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableDNSQueryBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetDNSQueryBps(*i)
	}
	return sdu
}

// AddDNSQueryBps adds i to the "dns_query_bps" field.
func (sdu *SpectrumDataUpdate) AddDNSQueryBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddDNSQueryBps(i)
	return sdu
}

// SetDNSAnswerPps sets the "dns_answer_pps" field.
func (sdu *SpectrumDataUpdate) SetDNSAnswerPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetDNSAnswerPps()
	sdu.mutation.SetDNSAnswerPps(i)
	return sdu
}

// SetNillableDNSAnswerPps sets the "dns_answer_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableDNSAnswerPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetDNSAnswerPps(*i)
	}
	return sdu
}

// AddDNSAnswerPps adds i to the "dns_answer_pps" field.
func (sdu *SpectrumDataUpdate) AddDNSAnswerPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddDNSAnswerPps(i)
	return sdu
}

// SetDNSAnswerBps sets the "dns_answer_bps" field.
func (sdu *SpectrumDataUpdate) SetDNSAnswerBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetDNSAnswerBps()
	sdu.mutation.SetDNSAnswerBps(i)
	return sdu
}

// SetNillableDNSAnswerBps sets the "dns_answer_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableDNSAnswerBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetDNSAnswerBps(*i)
	}
	return sdu
}

// AddDNSAnswerBps adds i to the "dns_answer_bps" field.
func (sdu *SpectrumDataUpdate) AddDNSAnswerBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddDNSAnswerBps(i)
	return sdu
}

// SetSsdpBps sets the "ssdp_bps" field.
func (sdu *SpectrumDataUpdate) SetSsdpBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetSsdpBps()
	sdu.mutation.SetSsdpBps(i)
	return sdu
}

// SetNillableSsdpBps sets the "ssdp_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableSsdpBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetSsdpBps(*i)
	}
	return sdu
}

// AddSsdpBps adds i to the "ssdp_bps" field.
func (sdu *SpectrumDataUpdate) AddSsdpBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddSsdpBps(i)
	return sdu
}

// SetSsdpPps sets the "ssdp_pps" field.
func (sdu *SpectrumDataUpdate) SetSsdpPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetSsdpPps()
	sdu.mutation.SetSsdpPps(i)
	return sdu
}

// SetNillableSsdpPps sets the "ssdp_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableSsdpPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetSsdpPps(*i)
	}
	return sdu
}

// AddSsdpPps adds i to the "ssdp_pps" field.
func (sdu *SpectrumDataUpdate) AddSsdpPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddSsdpPps(i)
	return sdu
}

// SetUDPPps sets the "udp_pps" field.
func (sdu *SpectrumDataUpdate) SetUDPPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetUDPPps()
	sdu.mutation.SetUDPPps(i)
	return sdu
}

// SetNillableUDPPps sets the "udp_pps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableUDPPps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetUDPPps(*i)
	}
	return sdu
}

// AddUDPPps adds i to the "udp_pps" field.
func (sdu *SpectrumDataUpdate) AddUDPPps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddUDPPps(i)
	return sdu
}

// SetUDPBps sets the "udp_bps" field.
func (sdu *SpectrumDataUpdate) SetUDPBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetUDPBps()
	sdu.mutation.SetUDPBps(i)
	return sdu
}

// SetNillableUDPBps sets the "udp_bps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableUDPBps(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetUDPBps(*i)
	}
	return sdu
}

// AddUDPBps adds i to the "udp_bps" field.
func (sdu *SpectrumDataUpdate) AddUDPBps(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddUDPBps(i)
	return sdu
}

// SetQPS sets the "qps" field.
func (sdu *SpectrumDataUpdate) SetQPS(i int64) *SpectrumDataUpdate {
	sdu.mutation.ResetQPS()
	sdu.mutation.SetQPS(i)
	return sdu
}

// SetNillableQPS sets the "qps" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableQPS(i *int64) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetQPS(*i)
	}
	return sdu
}

// AddQPS adds i to the "qps" field.
func (sdu *SpectrumDataUpdate) AddQPS(i int64) *SpectrumDataUpdate {
	sdu.mutation.AddQPS(i)
	return sdu
}

// SetReceiveCount sets the "receive_count" field.
func (sdu *SpectrumDataUpdate) SetReceiveCount(i int) *SpectrumDataUpdate {
	sdu.mutation.ResetReceiveCount()
	sdu.mutation.SetReceiveCount(i)
	return sdu
}

// SetNillableReceiveCount sets the "receive_count" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableReceiveCount(i *int) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetReceiveCount(*i)
	}
	return sdu
}

// AddReceiveCount adds i to the "receive_count" field.
func (sdu *SpectrumDataUpdate) AddReceiveCount(i int) *SpectrumDataUpdate {
	sdu.mutation.AddReceiveCount(i)
	return sdu
}

// SetIPType sets the "ip_type" field.
func (sdu *SpectrumDataUpdate) SetIPType(i int) *SpectrumDataUpdate {
	sdu.mutation.ResetIPType()
	sdu.mutation.SetIPType(i)
	return sdu
}

// SetNillableIPType sets the "ip_type" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableIPType(i *int) *SpectrumDataUpdate {
	if i != nil {
		sdu.SetIPType(*i)
	}
	return sdu
}

// AddIPType adds i to the "ip_type" field.
func (sdu *SpectrumDataUpdate) AddIPType(i int) *SpectrumDataUpdate {
	sdu.mutation.AddIPType(i)
	return sdu
}

// SetMonitor sets the "monitor" field.
func (sdu *SpectrumDataUpdate) SetMonitor(s string) *SpectrumDataUpdate {
	sdu.mutation.SetMonitor(s)
	return sdu
}

// SetNillableMonitor sets the "monitor" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableMonitor(s *string) *SpectrumDataUpdate {
	if s != nil {
		sdu.SetMonitor(*s)
	}
	return sdu
}

// ClearMonitor clears the value of the "monitor" field.
func (sdu *SpectrumDataUpdate) ClearMonitor() *SpectrumDataUpdate {
	sdu.mutation.ClearMonitor()
	return sdu
}

// SetProduct sets the "product" field.
func (sdu *SpectrumDataUpdate) SetProduct(s string) *SpectrumDataUpdate {
	sdu.mutation.SetProduct(s)
	return sdu
}

// SetNillableProduct sets the "product" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableProduct(s *string) *SpectrumDataUpdate {
	if s != nil {
		sdu.SetProduct(*s)
	}
	return sdu
}

// ClearProduct clears the value of the "product" field.
func (sdu *SpectrumDataUpdate) ClearProduct() *SpectrumDataUpdate {
	sdu.mutation.ClearProduct()
	return sdu
}

// SetHost sets the "host" field.
func (sdu *SpectrumDataUpdate) SetHost(s string) *SpectrumDataUpdate {
	sdu.mutation.SetHost(s)
	return sdu
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (sdu *SpectrumDataUpdate) SetNillableHost(s *string) *SpectrumDataUpdate {
	if s != nil {
		sdu.SetHost(*s)
	}
	return sdu
}

// ClearHost clears the value of the "host" field.
func (sdu *SpectrumDataUpdate) ClearHost() *SpectrumDataUpdate {
	sdu.mutation.ClearHost()
	return sdu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sdu *SpectrumDataUpdate) SetTenant(t *Tenant) *SpectrumDataUpdate {
	return sdu.SetTenantID(t.ID)
}

// SetSpectrumAlert sets the "spectrum_alert" edge to the SpectrumAlert entity.
func (sdu *SpectrumDataUpdate) SetSpectrumAlert(s *SpectrumAlert) *SpectrumDataUpdate {
	return sdu.SetSpectrumAlertID(s.ID)
}

// Mutation returns the SpectrumDataMutation object of the builder.
func (sdu *SpectrumDataUpdate) Mutation() *SpectrumDataMutation {
	return sdu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (sdu *SpectrumDataUpdate) ClearTenant() *SpectrumDataUpdate {
	sdu.mutation.ClearTenant()
	return sdu
}

// ClearSpectrumAlert clears the "spectrum_alert" edge to the SpectrumAlert entity.
func (sdu *SpectrumDataUpdate) ClearSpectrumAlert() *SpectrumDataUpdate {
	sdu.mutation.ClearSpectrumAlert()
	return sdu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (sdu *SpectrumDataUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, sdu.sqlSave, sdu.mutation, sdu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sdu *SpectrumDataUpdate) SaveX(ctx context.Context) int {
	affected, err := sdu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (sdu *SpectrumDataUpdate) Exec(ctx context.Context) error {
	_, err := sdu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sdu *SpectrumDataUpdate) ExecX(ctx context.Context) {
	if err := sdu.Exec(ctx); err != nil {
		panic(err)
	}
}

func (sdu *SpectrumDataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(spectrumdata.Table, spectrumdata.Columns, sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt))
	if ps := sdu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sdu.mutation.IP(); ok {
		_spec.SetField(spectrumdata.FieldIP, field.TypeString, value)
	}
	if value, ok := sdu.mutation.Time(); ok {
		_spec.SetField(spectrumdata.FieldTime, field.TypeTime, value)
	}
	if value, ok := sdu.mutation.MonitorID(); ok {
		_spec.SetField(spectrumdata.FieldMonitorID, field.TypeInt, value)
	}
	if value, ok := sdu.mutation.AddedMonitorID(); ok {
		_spec.AddField(spectrumdata.FieldMonitorID, field.TypeInt, value)
	}
	if value, ok := sdu.mutation.DataType(); ok {
		_spec.SetField(spectrumdata.FieldDataType, field.TypeInt, value)
	}
	if value, ok := sdu.mutation.AddedDataType(); ok {
		_spec.AddField(spectrumdata.FieldDataType, field.TypeInt, value)
	}
	if value, ok := sdu.mutation.Bps(); ok {
		_spec.SetField(spectrumdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedBps(); ok {
		_spec.AddField(spectrumdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.Pps(); ok {
		_spec.SetField(spectrumdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedPps(); ok {
		_spec.AddField(spectrumdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.SynBps(); ok {
		_spec.SetField(spectrumdata.FieldSynBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedSynBps(); ok {
		_spec.AddField(spectrumdata.FieldSynBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.SynPps(); ok {
		_spec.SetField(spectrumdata.FieldSynPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedSynPps(); ok {
		_spec.AddField(spectrumdata.FieldSynPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AckBps(); ok {
		_spec.SetField(spectrumdata.FieldAckBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedAckBps(); ok {
		_spec.AddField(spectrumdata.FieldAckBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AckPps(); ok {
		_spec.SetField(spectrumdata.FieldAckPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedAckPps(); ok {
		_spec.AddField(spectrumdata.FieldAckPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.SynAckBps(); ok {
		_spec.SetField(spectrumdata.FieldSynAckBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedSynAckBps(); ok {
		_spec.AddField(spectrumdata.FieldSynAckBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.SynAckPps(); ok {
		_spec.SetField(spectrumdata.FieldSynAckPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedSynAckPps(); ok {
		_spec.AddField(spectrumdata.FieldSynAckPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.IcmpBps(); ok {
		_spec.SetField(spectrumdata.FieldIcmpBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedIcmpBps(); ok {
		_spec.AddField(spectrumdata.FieldIcmpBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.IcmpPps(); ok {
		_spec.SetField(spectrumdata.FieldIcmpPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedIcmpPps(); ok {
		_spec.AddField(spectrumdata.FieldIcmpPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.SmallPps(); ok {
		_spec.SetField(spectrumdata.FieldSmallPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedSmallPps(); ok {
		_spec.AddField(spectrumdata.FieldSmallPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.NtpPps(); ok {
		_spec.SetField(spectrumdata.FieldNtpPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedNtpPps(); ok {
		_spec.AddField(spectrumdata.FieldNtpPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.NtpBps(); ok {
		_spec.SetField(spectrumdata.FieldNtpBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedNtpBps(); ok {
		_spec.AddField(spectrumdata.FieldNtpBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.DNSQueryPps(); ok {
		_spec.SetField(spectrumdata.FieldDNSQueryPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedDNSQueryPps(); ok {
		_spec.AddField(spectrumdata.FieldDNSQueryPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.DNSQueryBps(); ok {
		_spec.SetField(spectrumdata.FieldDNSQueryBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedDNSQueryBps(); ok {
		_spec.AddField(spectrumdata.FieldDNSQueryBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.DNSAnswerPps(); ok {
		_spec.SetField(spectrumdata.FieldDNSAnswerPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedDNSAnswerPps(); ok {
		_spec.AddField(spectrumdata.FieldDNSAnswerPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.DNSAnswerBps(); ok {
		_spec.SetField(spectrumdata.FieldDNSAnswerBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedDNSAnswerBps(); ok {
		_spec.AddField(spectrumdata.FieldDNSAnswerBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.SsdpBps(); ok {
		_spec.SetField(spectrumdata.FieldSsdpBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedSsdpBps(); ok {
		_spec.AddField(spectrumdata.FieldSsdpBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.SsdpPps(); ok {
		_spec.SetField(spectrumdata.FieldSsdpPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedSsdpPps(); ok {
		_spec.AddField(spectrumdata.FieldSsdpPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.UDPPps(); ok {
		_spec.SetField(spectrumdata.FieldUDPPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedUDPPps(); ok {
		_spec.AddField(spectrumdata.FieldUDPPps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.UDPBps(); ok {
		_spec.SetField(spectrumdata.FieldUDPBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedUDPBps(); ok {
		_spec.AddField(spectrumdata.FieldUDPBps, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.QPS(); ok {
		_spec.SetField(spectrumdata.FieldQPS, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedQPS(); ok {
		_spec.AddField(spectrumdata.FieldQPS, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.ReceiveCount(); ok {
		_spec.SetField(spectrumdata.FieldReceiveCount, field.TypeInt, value)
	}
	if value, ok := sdu.mutation.AddedReceiveCount(); ok {
		_spec.AddField(spectrumdata.FieldReceiveCount, field.TypeInt, value)
	}
	if value, ok := sdu.mutation.IPType(); ok {
		_spec.SetField(spectrumdata.FieldIPType, field.TypeInt, value)
	}
	if value, ok := sdu.mutation.AddedIPType(); ok {
		_spec.AddField(spectrumdata.FieldIPType, field.TypeInt, value)
	}
	if value, ok := sdu.mutation.Monitor(); ok {
		_spec.SetField(spectrumdata.FieldMonitor, field.TypeString, value)
	}
	if sdu.mutation.MonitorCleared() {
		_spec.ClearField(spectrumdata.FieldMonitor, field.TypeString)
	}
	if value, ok := sdu.mutation.Product(); ok {
		_spec.SetField(spectrumdata.FieldProduct, field.TypeString, value)
	}
	if sdu.mutation.ProductCleared() {
		_spec.ClearField(spectrumdata.FieldProduct, field.TypeString)
	}
	if value, ok := sdu.mutation.Host(); ok {
		_spec.SetField(spectrumdata.FieldHost, field.TypeString, value)
	}
	if sdu.mutation.HostCleared() {
		_spec.ClearField(spectrumdata.FieldHost, field.TypeString)
	}
	if sdu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumdata.TenantTable,
			Columns: []string{spectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sdu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumdata.TenantTable,
			Columns: []string{spectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sdu.mutation.SpectrumAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumdata.SpectrumAlertTable,
			Columns: []string{spectrumdata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sdu.mutation.SpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumdata.SpectrumAlertTable,
			Columns: []string{spectrumdata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, sdu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{spectrumdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	sdu.mutation.done = true
	return n, nil
}

// SpectrumDataUpdateOne is the builder for updating a single SpectrumData entity.
type SpectrumDataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SpectrumDataMutation
}

// SetTenantID sets the "tenant_id" field.
func (sduo *SpectrumDataUpdateOne) SetTenantID(i int) *SpectrumDataUpdateOne {
	sduo.mutation.SetTenantID(i)
	return sduo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableTenantID(i *int) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetTenantID(*i)
	}
	return sduo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (sduo *SpectrumDataUpdateOne) ClearTenantID() *SpectrumDataUpdateOne {
	sduo.mutation.ClearTenantID()
	return sduo
}

// SetSpectrumAlertID sets the "spectrum_alert_id" field.
func (sduo *SpectrumDataUpdateOne) SetSpectrumAlertID(i int) *SpectrumDataUpdateOne {
	sduo.mutation.SetSpectrumAlertID(i)
	return sduo
}

// SetNillableSpectrumAlertID sets the "spectrum_alert_id" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableSpectrumAlertID(i *int) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetSpectrumAlertID(*i)
	}
	return sduo
}

// ClearSpectrumAlertID clears the value of the "spectrum_alert_id" field.
func (sduo *SpectrumDataUpdateOne) ClearSpectrumAlertID() *SpectrumDataUpdateOne {
	sduo.mutation.ClearSpectrumAlertID()
	return sduo
}

// SetIP sets the "ip" field.
func (sduo *SpectrumDataUpdateOne) SetIP(s string) *SpectrumDataUpdateOne {
	sduo.mutation.SetIP(s)
	return sduo
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableIP(s *string) *SpectrumDataUpdateOne {
	if s != nil {
		sduo.SetIP(*s)
	}
	return sduo
}

// SetTime sets the "time" field.
func (sduo *SpectrumDataUpdateOne) SetTime(t time.Time) *SpectrumDataUpdateOne {
	sduo.mutation.SetTime(t)
	return sduo
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableTime(t *time.Time) *SpectrumDataUpdateOne {
	if t != nil {
		sduo.SetTime(*t)
	}
	return sduo
}

// SetMonitorID sets the "monitor_id" field.
func (sduo *SpectrumDataUpdateOne) SetMonitorID(i int) *SpectrumDataUpdateOne {
	sduo.mutation.ResetMonitorID()
	sduo.mutation.SetMonitorID(i)
	return sduo
}

// SetNillableMonitorID sets the "monitor_id" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableMonitorID(i *int) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetMonitorID(*i)
	}
	return sduo
}

// AddMonitorID adds i to the "monitor_id" field.
func (sduo *SpectrumDataUpdateOne) AddMonitorID(i int) *SpectrumDataUpdateOne {
	sduo.mutation.AddMonitorID(i)
	return sduo
}

// SetDataType sets the "data_type" field.
func (sduo *SpectrumDataUpdateOne) SetDataType(i int) *SpectrumDataUpdateOne {
	sduo.mutation.ResetDataType()
	sduo.mutation.SetDataType(i)
	return sduo
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableDataType(i *int) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetDataType(*i)
	}
	return sduo
}

// AddDataType adds i to the "data_type" field.
func (sduo *SpectrumDataUpdateOne) AddDataType(i int) *SpectrumDataUpdateOne {
	sduo.mutation.AddDataType(i)
	return sduo
}

// SetBps sets the "bps" field.
func (sduo *SpectrumDataUpdateOne) SetBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetBps()
	sduo.mutation.SetBps(i)
	return sduo
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetBps(*i)
	}
	return sduo
}

// AddBps adds i to the "bps" field.
func (sduo *SpectrumDataUpdateOne) AddBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddBps(i)
	return sduo
}

// SetPps sets the "pps" field.
func (sduo *SpectrumDataUpdateOne) SetPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetPps()
	sduo.mutation.SetPps(i)
	return sduo
}

// SetNillablePps sets the "pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillablePps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetPps(*i)
	}
	return sduo
}

// AddPps adds i to the "pps" field.
func (sduo *SpectrumDataUpdateOne) AddPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddPps(i)
	return sduo
}

// SetSynBps sets the "syn_bps" field.
func (sduo *SpectrumDataUpdateOne) SetSynBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetSynBps()
	sduo.mutation.SetSynBps(i)
	return sduo
}

// SetNillableSynBps sets the "syn_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableSynBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetSynBps(*i)
	}
	return sduo
}

// AddSynBps adds i to the "syn_bps" field.
func (sduo *SpectrumDataUpdateOne) AddSynBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddSynBps(i)
	return sduo
}

// SetSynPps sets the "syn_pps" field.
func (sduo *SpectrumDataUpdateOne) SetSynPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetSynPps()
	sduo.mutation.SetSynPps(i)
	return sduo
}

// SetNillableSynPps sets the "syn_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableSynPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetSynPps(*i)
	}
	return sduo
}

// AddSynPps adds i to the "syn_pps" field.
func (sduo *SpectrumDataUpdateOne) AddSynPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddSynPps(i)
	return sduo
}

// SetAckBps sets the "ack_bps" field.
func (sduo *SpectrumDataUpdateOne) SetAckBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetAckBps()
	sduo.mutation.SetAckBps(i)
	return sduo
}

// SetNillableAckBps sets the "ack_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableAckBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetAckBps(*i)
	}
	return sduo
}

// AddAckBps adds i to the "ack_bps" field.
func (sduo *SpectrumDataUpdateOne) AddAckBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddAckBps(i)
	return sduo
}

// SetAckPps sets the "ack_pps" field.
func (sduo *SpectrumDataUpdateOne) SetAckPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetAckPps()
	sduo.mutation.SetAckPps(i)
	return sduo
}

// SetNillableAckPps sets the "ack_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableAckPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetAckPps(*i)
	}
	return sduo
}

// AddAckPps adds i to the "ack_pps" field.
func (sduo *SpectrumDataUpdateOne) AddAckPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddAckPps(i)
	return sduo
}

// SetSynAckBps sets the "syn_ack_bps" field.
func (sduo *SpectrumDataUpdateOne) SetSynAckBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetSynAckBps()
	sduo.mutation.SetSynAckBps(i)
	return sduo
}

// SetNillableSynAckBps sets the "syn_ack_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableSynAckBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetSynAckBps(*i)
	}
	return sduo
}

// AddSynAckBps adds i to the "syn_ack_bps" field.
func (sduo *SpectrumDataUpdateOne) AddSynAckBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddSynAckBps(i)
	return sduo
}

// SetSynAckPps sets the "syn_ack_pps" field.
func (sduo *SpectrumDataUpdateOne) SetSynAckPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetSynAckPps()
	sduo.mutation.SetSynAckPps(i)
	return sduo
}

// SetNillableSynAckPps sets the "syn_ack_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableSynAckPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetSynAckPps(*i)
	}
	return sduo
}

// AddSynAckPps adds i to the "syn_ack_pps" field.
func (sduo *SpectrumDataUpdateOne) AddSynAckPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddSynAckPps(i)
	return sduo
}

// SetIcmpBps sets the "icmp_bps" field.
func (sduo *SpectrumDataUpdateOne) SetIcmpBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetIcmpBps()
	sduo.mutation.SetIcmpBps(i)
	return sduo
}

// SetNillableIcmpBps sets the "icmp_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableIcmpBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetIcmpBps(*i)
	}
	return sduo
}

// AddIcmpBps adds i to the "icmp_bps" field.
func (sduo *SpectrumDataUpdateOne) AddIcmpBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddIcmpBps(i)
	return sduo
}

// SetIcmpPps sets the "icmp_pps" field.
func (sduo *SpectrumDataUpdateOne) SetIcmpPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetIcmpPps()
	sduo.mutation.SetIcmpPps(i)
	return sduo
}

// SetNillableIcmpPps sets the "icmp_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableIcmpPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetIcmpPps(*i)
	}
	return sduo
}

// AddIcmpPps adds i to the "icmp_pps" field.
func (sduo *SpectrumDataUpdateOne) AddIcmpPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddIcmpPps(i)
	return sduo
}

// SetSmallPps sets the "small_pps" field.
func (sduo *SpectrumDataUpdateOne) SetSmallPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetSmallPps()
	sduo.mutation.SetSmallPps(i)
	return sduo
}

// SetNillableSmallPps sets the "small_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableSmallPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetSmallPps(*i)
	}
	return sduo
}

// AddSmallPps adds i to the "small_pps" field.
func (sduo *SpectrumDataUpdateOne) AddSmallPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddSmallPps(i)
	return sduo
}

// SetNtpPps sets the "ntp_pps" field.
func (sduo *SpectrumDataUpdateOne) SetNtpPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetNtpPps()
	sduo.mutation.SetNtpPps(i)
	return sduo
}

// SetNillableNtpPps sets the "ntp_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableNtpPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetNtpPps(*i)
	}
	return sduo
}

// AddNtpPps adds i to the "ntp_pps" field.
func (sduo *SpectrumDataUpdateOne) AddNtpPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddNtpPps(i)
	return sduo
}

// SetNtpBps sets the "ntp_bps" field.
func (sduo *SpectrumDataUpdateOne) SetNtpBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetNtpBps()
	sduo.mutation.SetNtpBps(i)
	return sduo
}

// SetNillableNtpBps sets the "ntp_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableNtpBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetNtpBps(*i)
	}
	return sduo
}

// AddNtpBps adds i to the "ntp_bps" field.
func (sduo *SpectrumDataUpdateOne) AddNtpBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddNtpBps(i)
	return sduo
}

// SetDNSQueryPps sets the "dns_query_pps" field.
func (sduo *SpectrumDataUpdateOne) SetDNSQueryPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetDNSQueryPps()
	sduo.mutation.SetDNSQueryPps(i)
	return sduo
}

// SetNillableDNSQueryPps sets the "dns_query_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableDNSQueryPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetDNSQueryPps(*i)
	}
	return sduo
}

// AddDNSQueryPps adds i to the "dns_query_pps" field.
func (sduo *SpectrumDataUpdateOne) AddDNSQueryPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddDNSQueryPps(i)
	return sduo
}

// SetDNSQueryBps sets the "dns_query_bps" field.
func (sduo *SpectrumDataUpdateOne) SetDNSQueryBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetDNSQueryBps()
	sduo.mutation.SetDNSQueryBps(i)
	return sduo
}

// SetNillableDNSQueryBps sets the "dns_query_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableDNSQueryBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetDNSQueryBps(*i)
	}
	return sduo
}

// AddDNSQueryBps adds i to the "dns_query_bps" field.
func (sduo *SpectrumDataUpdateOne) AddDNSQueryBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddDNSQueryBps(i)
	return sduo
}

// SetDNSAnswerPps sets the "dns_answer_pps" field.
func (sduo *SpectrumDataUpdateOne) SetDNSAnswerPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetDNSAnswerPps()
	sduo.mutation.SetDNSAnswerPps(i)
	return sduo
}

// SetNillableDNSAnswerPps sets the "dns_answer_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableDNSAnswerPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetDNSAnswerPps(*i)
	}
	return sduo
}

// AddDNSAnswerPps adds i to the "dns_answer_pps" field.
func (sduo *SpectrumDataUpdateOne) AddDNSAnswerPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddDNSAnswerPps(i)
	return sduo
}

// SetDNSAnswerBps sets the "dns_answer_bps" field.
func (sduo *SpectrumDataUpdateOne) SetDNSAnswerBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetDNSAnswerBps()
	sduo.mutation.SetDNSAnswerBps(i)
	return sduo
}

// SetNillableDNSAnswerBps sets the "dns_answer_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableDNSAnswerBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetDNSAnswerBps(*i)
	}
	return sduo
}

// AddDNSAnswerBps adds i to the "dns_answer_bps" field.
func (sduo *SpectrumDataUpdateOne) AddDNSAnswerBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddDNSAnswerBps(i)
	return sduo
}

// SetSsdpBps sets the "ssdp_bps" field.
func (sduo *SpectrumDataUpdateOne) SetSsdpBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetSsdpBps()
	sduo.mutation.SetSsdpBps(i)
	return sduo
}

// SetNillableSsdpBps sets the "ssdp_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableSsdpBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetSsdpBps(*i)
	}
	return sduo
}

// AddSsdpBps adds i to the "ssdp_bps" field.
func (sduo *SpectrumDataUpdateOne) AddSsdpBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddSsdpBps(i)
	return sduo
}

// SetSsdpPps sets the "ssdp_pps" field.
func (sduo *SpectrumDataUpdateOne) SetSsdpPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetSsdpPps()
	sduo.mutation.SetSsdpPps(i)
	return sduo
}

// SetNillableSsdpPps sets the "ssdp_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableSsdpPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetSsdpPps(*i)
	}
	return sduo
}

// AddSsdpPps adds i to the "ssdp_pps" field.
func (sduo *SpectrumDataUpdateOne) AddSsdpPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddSsdpPps(i)
	return sduo
}

// SetUDPPps sets the "udp_pps" field.
func (sduo *SpectrumDataUpdateOne) SetUDPPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetUDPPps()
	sduo.mutation.SetUDPPps(i)
	return sduo
}

// SetNillableUDPPps sets the "udp_pps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableUDPPps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetUDPPps(*i)
	}
	return sduo
}

// AddUDPPps adds i to the "udp_pps" field.
func (sduo *SpectrumDataUpdateOne) AddUDPPps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddUDPPps(i)
	return sduo
}

// SetUDPBps sets the "udp_bps" field.
func (sduo *SpectrumDataUpdateOne) SetUDPBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetUDPBps()
	sduo.mutation.SetUDPBps(i)
	return sduo
}

// SetNillableUDPBps sets the "udp_bps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableUDPBps(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetUDPBps(*i)
	}
	return sduo
}

// AddUDPBps adds i to the "udp_bps" field.
func (sduo *SpectrumDataUpdateOne) AddUDPBps(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddUDPBps(i)
	return sduo
}

// SetQPS sets the "qps" field.
func (sduo *SpectrumDataUpdateOne) SetQPS(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.ResetQPS()
	sduo.mutation.SetQPS(i)
	return sduo
}

// SetNillableQPS sets the "qps" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableQPS(i *int64) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetQPS(*i)
	}
	return sduo
}

// AddQPS adds i to the "qps" field.
func (sduo *SpectrumDataUpdateOne) AddQPS(i int64) *SpectrumDataUpdateOne {
	sduo.mutation.AddQPS(i)
	return sduo
}

// SetReceiveCount sets the "receive_count" field.
func (sduo *SpectrumDataUpdateOne) SetReceiveCount(i int) *SpectrumDataUpdateOne {
	sduo.mutation.ResetReceiveCount()
	sduo.mutation.SetReceiveCount(i)
	return sduo
}

// SetNillableReceiveCount sets the "receive_count" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableReceiveCount(i *int) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetReceiveCount(*i)
	}
	return sduo
}

// AddReceiveCount adds i to the "receive_count" field.
func (sduo *SpectrumDataUpdateOne) AddReceiveCount(i int) *SpectrumDataUpdateOne {
	sduo.mutation.AddReceiveCount(i)
	return sduo
}

// SetIPType sets the "ip_type" field.
func (sduo *SpectrumDataUpdateOne) SetIPType(i int) *SpectrumDataUpdateOne {
	sduo.mutation.ResetIPType()
	sduo.mutation.SetIPType(i)
	return sduo
}

// SetNillableIPType sets the "ip_type" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableIPType(i *int) *SpectrumDataUpdateOne {
	if i != nil {
		sduo.SetIPType(*i)
	}
	return sduo
}

// AddIPType adds i to the "ip_type" field.
func (sduo *SpectrumDataUpdateOne) AddIPType(i int) *SpectrumDataUpdateOne {
	sduo.mutation.AddIPType(i)
	return sduo
}

// SetMonitor sets the "monitor" field.
func (sduo *SpectrumDataUpdateOne) SetMonitor(s string) *SpectrumDataUpdateOne {
	sduo.mutation.SetMonitor(s)
	return sduo
}

// SetNillableMonitor sets the "monitor" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableMonitor(s *string) *SpectrumDataUpdateOne {
	if s != nil {
		sduo.SetMonitor(*s)
	}
	return sduo
}

// ClearMonitor clears the value of the "monitor" field.
func (sduo *SpectrumDataUpdateOne) ClearMonitor() *SpectrumDataUpdateOne {
	sduo.mutation.ClearMonitor()
	return sduo
}

// SetProduct sets the "product" field.
func (sduo *SpectrumDataUpdateOne) SetProduct(s string) *SpectrumDataUpdateOne {
	sduo.mutation.SetProduct(s)
	return sduo
}

// SetNillableProduct sets the "product" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableProduct(s *string) *SpectrumDataUpdateOne {
	if s != nil {
		sduo.SetProduct(*s)
	}
	return sduo
}

// ClearProduct clears the value of the "product" field.
func (sduo *SpectrumDataUpdateOne) ClearProduct() *SpectrumDataUpdateOne {
	sduo.mutation.ClearProduct()
	return sduo
}

// SetHost sets the "host" field.
func (sduo *SpectrumDataUpdateOne) SetHost(s string) *SpectrumDataUpdateOne {
	sduo.mutation.SetHost(s)
	return sduo
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (sduo *SpectrumDataUpdateOne) SetNillableHost(s *string) *SpectrumDataUpdateOne {
	if s != nil {
		sduo.SetHost(*s)
	}
	return sduo
}

// ClearHost clears the value of the "host" field.
func (sduo *SpectrumDataUpdateOne) ClearHost() *SpectrumDataUpdateOne {
	sduo.mutation.ClearHost()
	return sduo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sduo *SpectrumDataUpdateOne) SetTenant(t *Tenant) *SpectrumDataUpdateOne {
	return sduo.SetTenantID(t.ID)
}

// SetSpectrumAlert sets the "spectrum_alert" edge to the SpectrumAlert entity.
func (sduo *SpectrumDataUpdateOne) SetSpectrumAlert(s *SpectrumAlert) *SpectrumDataUpdateOne {
	return sduo.SetSpectrumAlertID(s.ID)
}

// Mutation returns the SpectrumDataMutation object of the builder.
func (sduo *SpectrumDataUpdateOne) Mutation() *SpectrumDataMutation {
	return sduo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (sduo *SpectrumDataUpdateOne) ClearTenant() *SpectrumDataUpdateOne {
	sduo.mutation.ClearTenant()
	return sduo
}

// ClearSpectrumAlert clears the "spectrum_alert" edge to the SpectrumAlert entity.
func (sduo *SpectrumDataUpdateOne) ClearSpectrumAlert() *SpectrumDataUpdateOne {
	sduo.mutation.ClearSpectrumAlert()
	return sduo
}

// Where appends a list predicates to the SpectrumDataUpdate builder.
func (sduo *SpectrumDataUpdateOne) Where(ps ...predicate.SpectrumData) *SpectrumDataUpdateOne {
	sduo.mutation.Where(ps...)
	return sduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (sduo *SpectrumDataUpdateOne) Select(field string, fields ...string) *SpectrumDataUpdateOne {
	sduo.fields = append([]string{field}, fields...)
	return sduo
}

// Save executes the query and returns the updated SpectrumData entity.
func (sduo *SpectrumDataUpdateOne) Save(ctx context.Context) (*SpectrumData, error) {
	return withHooks(ctx, sduo.sqlSave, sduo.mutation, sduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sduo *SpectrumDataUpdateOne) SaveX(ctx context.Context) *SpectrumData {
	node, err := sduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (sduo *SpectrumDataUpdateOne) Exec(ctx context.Context) error {
	_, err := sduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sduo *SpectrumDataUpdateOne) ExecX(ctx context.Context) {
	if err := sduo.Exec(ctx); err != nil {
		panic(err)
	}
}

func (sduo *SpectrumDataUpdateOne) sqlSave(ctx context.Context) (_node *SpectrumData, err error) {
	_spec := sqlgraph.NewUpdateSpec(spectrumdata.Table, spectrumdata.Columns, sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt))
	id, ok := sduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SpectrumData.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := sduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, spectrumdata.FieldID)
		for _, f := range fields {
			if !spectrumdata.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != spectrumdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := sduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sduo.mutation.IP(); ok {
		_spec.SetField(spectrumdata.FieldIP, field.TypeString, value)
	}
	if value, ok := sduo.mutation.Time(); ok {
		_spec.SetField(spectrumdata.FieldTime, field.TypeTime, value)
	}
	if value, ok := sduo.mutation.MonitorID(); ok {
		_spec.SetField(spectrumdata.FieldMonitorID, field.TypeInt, value)
	}
	if value, ok := sduo.mutation.AddedMonitorID(); ok {
		_spec.AddField(spectrumdata.FieldMonitorID, field.TypeInt, value)
	}
	if value, ok := sduo.mutation.DataType(); ok {
		_spec.SetField(spectrumdata.FieldDataType, field.TypeInt, value)
	}
	if value, ok := sduo.mutation.AddedDataType(); ok {
		_spec.AddField(spectrumdata.FieldDataType, field.TypeInt, value)
	}
	if value, ok := sduo.mutation.Bps(); ok {
		_spec.SetField(spectrumdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedBps(); ok {
		_spec.AddField(spectrumdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.Pps(); ok {
		_spec.SetField(spectrumdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedPps(); ok {
		_spec.AddField(spectrumdata.FieldPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.SynBps(); ok {
		_spec.SetField(spectrumdata.FieldSynBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedSynBps(); ok {
		_spec.AddField(spectrumdata.FieldSynBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.SynPps(); ok {
		_spec.SetField(spectrumdata.FieldSynPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedSynPps(); ok {
		_spec.AddField(spectrumdata.FieldSynPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AckBps(); ok {
		_spec.SetField(spectrumdata.FieldAckBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedAckBps(); ok {
		_spec.AddField(spectrumdata.FieldAckBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AckPps(); ok {
		_spec.SetField(spectrumdata.FieldAckPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedAckPps(); ok {
		_spec.AddField(spectrumdata.FieldAckPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.SynAckBps(); ok {
		_spec.SetField(spectrumdata.FieldSynAckBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedSynAckBps(); ok {
		_spec.AddField(spectrumdata.FieldSynAckBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.SynAckPps(); ok {
		_spec.SetField(spectrumdata.FieldSynAckPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedSynAckPps(); ok {
		_spec.AddField(spectrumdata.FieldSynAckPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.IcmpBps(); ok {
		_spec.SetField(spectrumdata.FieldIcmpBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedIcmpBps(); ok {
		_spec.AddField(spectrumdata.FieldIcmpBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.IcmpPps(); ok {
		_spec.SetField(spectrumdata.FieldIcmpPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedIcmpPps(); ok {
		_spec.AddField(spectrumdata.FieldIcmpPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.SmallPps(); ok {
		_spec.SetField(spectrumdata.FieldSmallPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedSmallPps(); ok {
		_spec.AddField(spectrumdata.FieldSmallPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.NtpPps(); ok {
		_spec.SetField(spectrumdata.FieldNtpPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedNtpPps(); ok {
		_spec.AddField(spectrumdata.FieldNtpPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.NtpBps(); ok {
		_spec.SetField(spectrumdata.FieldNtpBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedNtpBps(); ok {
		_spec.AddField(spectrumdata.FieldNtpBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.DNSQueryPps(); ok {
		_spec.SetField(spectrumdata.FieldDNSQueryPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedDNSQueryPps(); ok {
		_spec.AddField(spectrumdata.FieldDNSQueryPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.DNSQueryBps(); ok {
		_spec.SetField(spectrumdata.FieldDNSQueryBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedDNSQueryBps(); ok {
		_spec.AddField(spectrumdata.FieldDNSQueryBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.DNSAnswerPps(); ok {
		_spec.SetField(spectrumdata.FieldDNSAnswerPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedDNSAnswerPps(); ok {
		_spec.AddField(spectrumdata.FieldDNSAnswerPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.DNSAnswerBps(); ok {
		_spec.SetField(spectrumdata.FieldDNSAnswerBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedDNSAnswerBps(); ok {
		_spec.AddField(spectrumdata.FieldDNSAnswerBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.SsdpBps(); ok {
		_spec.SetField(spectrumdata.FieldSsdpBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedSsdpBps(); ok {
		_spec.AddField(spectrumdata.FieldSsdpBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.SsdpPps(); ok {
		_spec.SetField(spectrumdata.FieldSsdpPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedSsdpPps(); ok {
		_spec.AddField(spectrumdata.FieldSsdpPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.UDPPps(); ok {
		_spec.SetField(spectrumdata.FieldUDPPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedUDPPps(); ok {
		_spec.AddField(spectrumdata.FieldUDPPps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.UDPBps(); ok {
		_spec.SetField(spectrumdata.FieldUDPBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedUDPBps(); ok {
		_spec.AddField(spectrumdata.FieldUDPBps, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.QPS(); ok {
		_spec.SetField(spectrumdata.FieldQPS, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedQPS(); ok {
		_spec.AddField(spectrumdata.FieldQPS, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.ReceiveCount(); ok {
		_spec.SetField(spectrumdata.FieldReceiveCount, field.TypeInt, value)
	}
	if value, ok := sduo.mutation.AddedReceiveCount(); ok {
		_spec.AddField(spectrumdata.FieldReceiveCount, field.TypeInt, value)
	}
	if value, ok := sduo.mutation.IPType(); ok {
		_spec.SetField(spectrumdata.FieldIPType, field.TypeInt, value)
	}
	if value, ok := sduo.mutation.AddedIPType(); ok {
		_spec.AddField(spectrumdata.FieldIPType, field.TypeInt, value)
	}
	if value, ok := sduo.mutation.Monitor(); ok {
		_spec.SetField(spectrumdata.FieldMonitor, field.TypeString, value)
	}
	if sduo.mutation.MonitorCleared() {
		_spec.ClearField(spectrumdata.FieldMonitor, field.TypeString)
	}
	if value, ok := sduo.mutation.Product(); ok {
		_spec.SetField(spectrumdata.FieldProduct, field.TypeString, value)
	}
	if sduo.mutation.ProductCleared() {
		_spec.ClearField(spectrumdata.FieldProduct, field.TypeString)
	}
	if value, ok := sduo.mutation.Host(); ok {
		_spec.SetField(spectrumdata.FieldHost, field.TypeString, value)
	}
	if sduo.mutation.HostCleared() {
		_spec.ClearField(spectrumdata.FieldHost, field.TypeString)
	}
	if sduo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumdata.TenantTable,
			Columns: []string{spectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sduo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumdata.TenantTable,
			Columns: []string{spectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sduo.mutation.SpectrumAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumdata.SpectrumAlertTable,
			Columns: []string{spectrumdata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sduo.mutation.SpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumdata.SpectrumAlertTable,
			Columns: []string{spectrumdata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &SpectrumData{config: sduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, sduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{spectrumdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	sduo.mutation.done = true
	return _node, nil
}
