// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudFlowDataDelete is the builder for deleting a CloudFlowData entity.
type CloudFlowDataDelete struct {
	config
	hooks    []Hook
	mutation *CloudFlowDataMutation
}

// Where appends a list predicates to the CloudFlowDataDelete builder.
func (cfdd *CloudFlowDataDelete) Where(ps ...predicate.CloudFlowData) *CloudFlowDataDelete {
	cfdd.mutation.Where(ps...)
	return cfdd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (cfdd *CloudFlowDataDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, cfdd.sqlExec, cfdd.mutation, cfdd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (cfdd *CloudFlowDataDelete) ExecX(ctx context.Context) int {
	n, err := cfdd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (cfdd *CloudFlowDataDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(cloudflowdata.Table, sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt))
	if ps := cfdd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, cfdd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	cfdd.mutation.done = true
	return affected, err
}

// CloudFlowDataDeleteOne is the builder for deleting a single CloudFlowData entity.
type CloudFlowDataDeleteOne struct {
	cfdd *CloudFlowDataDelete
}

// Where appends a list predicates to the CloudFlowDataDelete builder.
func (cfddo *CloudFlowDataDeleteOne) Where(ps ...predicate.CloudFlowData) *CloudFlowDataDeleteOne {
	cfddo.cfdd.mutation.Where(ps...)
	return cfddo
}

// Exec executes the deletion query.
func (cfddo *CloudFlowDataDeleteOne) Exec(ctx context.Context) error {
	n, err := cfddo.cfdd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{cloudflowdata.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (cfddo *CloudFlowDataDeleteOne) ExecX(ctx context.Context) {
	if err := cfddo.Exec(ctx); err != nil {
		panic(err)
	}
}
