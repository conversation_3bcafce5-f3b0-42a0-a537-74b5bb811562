// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"
	"meta/app/entity/netease"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// MatrixSpectrumAlert is the model entity for the MatrixSpectrumAlert schema.
type MatrixSpectrumAlert struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// WofangID holds the value of the "wofang_id" field.
	WofangID *int `json:"wofang_id,omitempty"`
	// MatrixStrategyID holds the value of the "matrix_strategy_id" field.
	MatrixStrategyID *int `json:"matrix_strategy_id,omitempty"`
	// IPList holds the value of the "ip_list" field.
	IPList *[]string `json:"ip_list,omitempty" query:"ip_list,omitempty"`
	// Region holds the value of the "region" field.
	Region string `json:"region,omitempty"`
	// NetType holds the value of the "net_type" field.
	NetType string `json:"net_type,omitempty" query:"net_type"`
	// Isp holds the value of the "isp" field.
	Isp string `json:"isp,omitempty"`
	// 攻击开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 攻击结束时间
	EndTime time.Time `json:"end_time,omitempty"`
	// 攻击类型
	AttackType string `json:"attack_type,omitempty"`
	// 告警BPS
	Bps int64 `json:"bps,omitempty"`
	// AttackInfo holds the value of the "attack_info" field.
	AttackInfo netease.MatrixAttackInfo `json:"attack_info,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the MatrixSpectrumAlertQuery when eager-loading is set.
	Edges        MatrixSpectrumAlertEdges `json:"edges"`
	selectValues sql.SelectValues
}

// MatrixSpectrumAlertEdges holds the relations/edges for other nodes in the graph.
type MatrixSpectrumAlertEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// MatrixSpectrumDatas holds the value of the matrix_spectrum_datas edge.
	MatrixSpectrumDatas []*MatrixSpectrumData `json:"matrix_spectrum_datas,omitempty"`
	// MatrixStrategy holds the value of the matrix_strategy edge.
	MatrixStrategy *MatrixStrategy `json:"matrix_strategy,omitempty"`
	// WofangTicket holds the value of the wofang_ticket edge.
	WofangTicket *Wofang `json:"wofang_ticket,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [4]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MatrixSpectrumAlertEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// MatrixSpectrumDatasOrErr returns the MatrixSpectrumDatas value or an error if the edge
// was not loaded in eager-loading.
func (e MatrixSpectrumAlertEdges) MatrixSpectrumDatasOrErr() ([]*MatrixSpectrumData, error) {
	if e.loadedTypes[1] {
		return e.MatrixSpectrumDatas, nil
	}
	return nil, &NotLoadedError{edge: "matrix_spectrum_datas"}
}

// MatrixStrategyOrErr returns the MatrixStrategy value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MatrixSpectrumAlertEdges) MatrixStrategyOrErr() (*MatrixStrategy, error) {
	if e.loadedTypes[2] {
		if e.MatrixStrategy == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: matrixstrategy.Label}
		}
		return e.MatrixStrategy, nil
	}
	return nil, &NotLoadedError{edge: "matrix_strategy"}
}

// WofangTicketOrErr returns the WofangTicket value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MatrixSpectrumAlertEdges) WofangTicketOrErr() (*Wofang, error) {
	if e.loadedTypes[3] {
		if e.WofangTicket == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: wofang.Label}
		}
		return e.WofangTicket, nil
	}
	return nil, &NotLoadedError{edge: "wofang_ticket"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*MatrixSpectrumAlert) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case matrixspectrumalert.FieldIPList, matrixspectrumalert.FieldAttackInfo:
			values[i] = new([]byte)
		case matrixspectrumalert.FieldID, matrixspectrumalert.FieldTenantID, matrixspectrumalert.FieldWofangID, matrixspectrumalert.FieldMatrixStrategyID, matrixspectrumalert.FieldBps:
			values[i] = new(sql.NullInt64)
		case matrixspectrumalert.FieldRemark, matrixspectrumalert.FieldRegion, matrixspectrumalert.FieldNetType, matrixspectrumalert.FieldIsp, matrixspectrumalert.FieldAttackType:
			values[i] = new(sql.NullString)
		case matrixspectrumalert.FieldCreatedAt, matrixspectrumalert.FieldUpdatedAt, matrixspectrumalert.FieldStartTime, matrixspectrumalert.FieldEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the MatrixSpectrumAlert fields.
func (msa *MatrixSpectrumAlert) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case matrixspectrumalert.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			msa.ID = int(value.Int64)
		case matrixspectrumalert.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				msa.TenantID = new(int)
				*msa.TenantID = int(value.Int64)
			}
		case matrixspectrumalert.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				msa.CreatedAt = value.Time
			}
		case matrixspectrumalert.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				msa.UpdatedAt = value.Time
			}
		case matrixspectrumalert.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				msa.Remark = new(string)
				*msa.Remark = value.String
			}
		case matrixspectrumalert.FieldWofangID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field wofang_id", values[i])
			} else if value.Valid {
				msa.WofangID = new(int)
				*msa.WofangID = int(value.Int64)
			}
		case matrixspectrumalert.FieldMatrixStrategyID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field matrix_strategy_id", values[i])
			} else if value.Valid {
				msa.MatrixStrategyID = new(int)
				*msa.MatrixStrategyID = int(value.Int64)
			}
		case matrixspectrumalert.FieldIPList:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field ip_list", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &msa.IPList); err != nil {
					return fmt.Errorf("unmarshal field ip_list: %w", err)
				}
			}
		case matrixspectrumalert.FieldRegion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field region", values[i])
			} else if value.Valid {
				msa.Region = value.String
			}
		case matrixspectrumalert.FieldNetType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field net_type", values[i])
			} else if value.Valid {
				msa.NetType = value.String
			}
		case matrixspectrumalert.FieldIsp:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field isp", values[i])
			} else if value.Valid {
				msa.Isp = value.String
			}
		case matrixspectrumalert.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				msa.StartTime = value.Time
			}
		case matrixspectrumalert.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				msa.EndTime = value.Time
			}
		case matrixspectrumalert.FieldAttackType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field attack_type", values[i])
			} else if value.Valid {
				msa.AttackType = value.String
			}
		case matrixspectrumalert.FieldBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field bps", values[i])
			} else if value.Valid {
				msa.Bps = value.Int64
			}
		case matrixspectrumalert.FieldAttackInfo:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field attack_info", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &msa.AttackInfo); err != nil {
					return fmt.Errorf("unmarshal field attack_info: %w", err)
				}
			}
		default:
			msa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the MatrixSpectrumAlert.
// This includes values selected through modifiers, order, etc.
func (msa *MatrixSpectrumAlert) Value(name string) (ent.Value, error) {
	return msa.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the MatrixSpectrumAlert entity.
func (msa *MatrixSpectrumAlert) QueryTenant() *TenantQuery {
	return NewMatrixSpectrumAlertClient(msa.config).QueryTenant(msa)
}

// QueryMatrixSpectrumDatas queries the "matrix_spectrum_datas" edge of the MatrixSpectrumAlert entity.
func (msa *MatrixSpectrumAlert) QueryMatrixSpectrumDatas() *MatrixSpectrumDataQuery {
	return NewMatrixSpectrumAlertClient(msa.config).QueryMatrixSpectrumDatas(msa)
}

// QueryMatrixStrategy queries the "matrix_strategy" edge of the MatrixSpectrumAlert entity.
func (msa *MatrixSpectrumAlert) QueryMatrixStrategy() *MatrixStrategyQuery {
	return NewMatrixSpectrumAlertClient(msa.config).QueryMatrixStrategy(msa)
}

// QueryWofangTicket queries the "wofang_ticket" edge of the MatrixSpectrumAlert entity.
func (msa *MatrixSpectrumAlert) QueryWofangTicket() *WofangQuery {
	return NewMatrixSpectrumAlertClient(msa.config).QueryWofangTicket(msa)
}

// Update returns a builder for updating this MatrixSpectrumAlert.
// Note that you need to call MatrixSpectrumAlert.Unwrap() before calling this method if this MatrixSpectrumAlert
// was returned from a transaction, and the transaction was committed or rolled back.
func (msa *MatrixSpectrumAlert) Update() *MatrixSpectrumAlertUpdateOne {
	return NewMatrixSpectrumAlertClient(msa.config).UpdateOne(msa)
}

// Unwrap unwraps the MatrixSpectrumAlert entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (msa *MatrixSpectrumAlert) Unwrap() *MatrixSpectrumAlert {
	_tx, ok := msa.config.driver.(*txDriver)
	if !ok {
		panic("ent: MatrixSpectrumAlert is not a transactional entity")
	}
	msa.config.driver = _tx.drv
	return msa
}

// String implements the fmt.Stringer.
func (msa *MatrixSpectrumAlert) String() string {
	var builder strings.Builder
	builder.WriteString("MatrixSpectrumAlert(")
	builder.WriteString(fmt.Sprintf("id=%v, ", msa.ID))
	if v := msa.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(msa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(msa.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := msa.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	if v := msa.WofangID; v != nil {
		builder.WriteString("wofang_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	if v := msa.MatrixStrategyID; v != nil {
		builder.WriteString("matrix_strategy_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("ip_list=")
	builder.WriteString(fmt.Sprintf("%v", msa.IPList))
	builder.WriteString(", ")
	builder.WriteString("region=")
	builder.WriteString(msa.Region)
	builder.WriteString(", ")
	builder.WriteString("net_type=")
	builder.WriteString(msa.NetType)
	builder.WriteString(", ")
	builder.WriteString("isp=")
	builder.WriteString(msa.Isp)
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(msa.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end_time=")
	builder.WriteString(msa.EndTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("attack_type=")
	builder.WriteString(msa.AttackType)
	builder.WriteString(", ")
	builder.WriteString("bps=")
	builder.WriteString(fmt.Sprintf("%v", msa.Bps))
	builder.WriteString(", ")
	builder.WriteString("attack_info=")
	builder.WriteString(fmt.Sprintf("%v", msa.AttackInfo))
	builder.WriteByte(')')
	return builder.String()
}

// MatrixSpectrumAlerts is a parsable slice of MatrixSpectrumAlert.
type MatrixSpectrumAlerts []*MatrixSpectrumAlert
