// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/skylinedos"
	"meta/app/ent/tenant"
	"meta/app/entity/netease/gamecloud"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// SkylineDos is the model entity for the SkylineDos schema.
type SkylineDos struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 攻击开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 攻击结束时间
	EndTime time.Time `json:"end_time,omitempty"`
	// Region holds the value of the "region" field.
	Region string `json:"region,omitempty"`
	// Resource holds the value of the "resource" field.
	Resource string `json:"resource,omitempty"`
	// ResourceType holds the value of the "resource_type" field.
	ResourceType string `json:"resource_type,omitempty" query:"resource_type,omitempty"`
	// 攻击类型
	VectorTypes *[]string `json:"vector_types,omitempty" query:"vector_types,omitempty"`
	// Status holds the value of the "status" field.
	Status string `json:"status,omitempty"`
	// AttackID holds the value of the "attack_id" field.
	AttackID string `json:"attack_id,omitempty"`
	// AttackCounters holds the value of the "attack_counters" field.
	AttackCounters *[]gamecloud.AttackCounter `json:"attack_counters,omitempty" query:"attack_counters,omitempty"`
	// Project holds the value of the "project" field.
	Project string `json:"project,omitempty"`
	// DurationTime holds the value of the "duration_time" field.
	DurationTime int64 `json:"duration_time,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the SkylineDosQuery when eager-loading is set.
	Edges        SkylineDosEdges `json:"edges"`
	selectValues sql.SelectValues
}

// SkylineDosEdges holds the relations/edges for other nodes in the graph.
type SkylineDosEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e SkylineDosEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SkylineDos) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case skylinedos.FieldVectorTypes, skylinedos.FieldAttackCounters:
			values[i] = new([]byte)
		case skylinedos.FieldID, skylinedos.FieldTenantID, skylinedos.FieldDurationTime:
			values[i] = new(sql.NullInt64)
		case skylinedos.FieldRemark, skylinedos.FieldRegion, skylinedos.FieldResource, skylinedos.FieldResourceType, skylinedos.FieldStatus, skylinedos.FieldAttackID, skylinedos.FieldProject:
			values[i] = new(sql.NullString)
		case skylinedos.FieldCreatedAt, skylinedos.FieldUpdatedAt, skylinedos.FieldStartTime, skylinedos.FieldEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SkylineDos fields.
func (sd *SkylineDos) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case skylinedos.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			sd.ID = int(value.Int64)
		case skylinedos.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				sd.TenantID = new(int)
				*sd.TenantID = int(value.Int64)
			}
		case skylinedos.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				sd.CreatedAt = value.Time
			}
		case skylinedos.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				sd.UpdatedAt = value.Time
			}
		case skylinedos.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				sd.Remark = new(string)
				*sd.Remark = value.String
			}
		case skylinedos.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				sd.StartTime = value.Time
			}
		case skylinedos.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				sd.EndTime = value.Time
			}
		case skylinedos.FieldRegion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field region", values[i])
			} else if value.Valid {
				sd.Region = value.String
			}
		case skylinedos.FieldResource:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field resource", values[i])
			} else if value.Valid {
				sd.Resource = value.String
			}
		case skylinedos.FieldResourceType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field resource_type", values[i])
			} else if value.Valid {
				sd.ResourceType = value.String
			}
		case skylinedos.FieldVectorTypes:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field vector_types", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sd.VectorTypes); err != nil {
					return fmt.Errorf("unmarshal field vector_types: %w", err)
				}
			}
		case skylinedos.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				sd.Status = value.String
			}
		case skylinedos.FieldAttackID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field attack_id", values[i])
			} else if value.Valid {
				sd.AttackID = value.String
			}
		case skylinedos.FieldAttackCounters:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field attack_counters", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sd.AttackCounters); err != nil {
					return fmt.Errorf("unmarshal field attack_counters: %w", err)
				}
			}
		case skylinedos.FieldProject:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field project", values[i])
			} else if value.Valid {
				sd.Project = value.String
			}
		case skylinedos.FieldDurationTime:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field duration_time", values[i])
			} else if value.Valid {
				sd.DurationTime = value.Int64
			}
		default:
			sd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SkylineDos.
// This includes values selected through modifiers, order, etc.
func (sd *SkylineDos) Value(name string) (ent.Value, error) {
	return sd.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the SkylineDos entity.
func (sd *SkylineDos) QueryTenant() *TenantQuery {
	return NewSkylineDosClient(sd.config).QueryTenant(sd)
}

// Update returns a builder for updating this SkylineDos.
// Note that you need to call SkylineDos.Unwrap() before calling this method if this SkylineDos
// was returned from a transaction, and the transaction was committed or rolled back.
func (sd *SkylineDos) Update() *SkylineDosUpdateOne {
	return NewSkylineDosClient(sd.config).UpdateOne(sd)
}

// Unwrap unwraps the SkylineDos entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (sd *SkylineDos) Unwrap() *SkylineDos {
	_tx, ok := sd.config.driver.(*txDriver)
	if !ok {
		panic("ent: SkylineDos is not a transactional entity")
	}
	sd.config.driver = _tx.drv
	return sd
}

// String implements the fmt.Stringer.
func (sd *SkylineDos) String() string {
	var builder strings.Builder
	builder.WriteString("SkylineDos(")
	builder.WriteString(fmt.Sprintf("id=%v, ", sd.ID))
	if v := sd.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(sd.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(sd.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := sd.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(sd.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end_time=")
	builder.WriteString(sd.EndTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("region=")
	builder.WriteString(sd.Region)
	builder.WriteString(", ")
	builder.WriteString("resource=")
	builder.WriteString(sd.Resource)
	builder.WriteString(", ")
	builder.WriteString("resource_type=")
	builder.WriteString(sd.ResourceType)
	builder.WriteString(", ")
	builder.WriteString("vector_types=")
	builder.WriteString(fmt.Sprintf("%v", sd.VectorTypes))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(sd.Status)
	builder.WriteString(", ")
	builder.WriteString("attack_id=")
	builder.WriteString(sd.AttackID)
	builder.WriteString(", ")
	builder.WriteString("attack_counters=")
	builder.WriteString(fmt.Sprintf("%v", sd.AttackCounters))
	builder.WriteString(", ")
	builder.WriteString("project=")
	builder.WriteString(sd.Project)
	builder.WriteString(", ")
	builder.WriteString("duration_time=")
	builder.WriteString(fmt.Sprintf("%v", sd.DurationTime))
	builder.WriteByte(')')
	return builder.String()
}

// SkylineDosSlice is a parsable slice of SkylineDos.
type SkylineDosSlice []*SkylineDos
