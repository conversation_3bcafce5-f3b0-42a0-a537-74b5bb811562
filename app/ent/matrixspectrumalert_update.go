// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"
	"meta/app/entity/netease"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumAlertUpdate is the builder for updating MatrixSpectrumAlert entities.
type MatrixSpectrumAlertUpdate struct {
	config
	hooks    []Hook
	mutation *MatrixSpectrumAlertMutation
}

// Where appends a list predicates to the MatrixSpectrumAlertUpdate builder.
func (msau *MatrixSpectrumAlertUpdate) Where(ps ...predicate.MatrixSpectrumAlert) *MatrixSpectrumAlertUpdate {
	msau.mutation.Where(ps...)
	return msau
}

// SetTenantID sets the "tenant_id" field.
func (msau *MatrixSpectrumAlertUpdate) SetTenantID(i int) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetTenantID(i)
	return msau
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableTenantID(i *int) *MatrixSpectrumAlertUpdate {
	if i != nil {
		msau.SetTenantID(*i)
	}
	return msau
}

// ClearTenantID clears the value of the "tenant_id" field.
func (msau *MatrixSpectrumAlertUpdate) ClearTenantID() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearTenantID()
	return msau
}

// SetUpdatedAt sets the "updated_at" field.
func (msau *MatrixSpectrumAlertUpdate) SetUpdatedAt(t time.Time) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetUpdatedAt(t)
	return msau
}

// SetRemark sets the "remark" field.
func (msau *MatrixSpectrumAlertUpdate) SetRemark(s string) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetRemark(s)
	return msau
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableRemark(s *string) *MatrixSpectrumAlertUpdate {
	if s != nil {
		msau.SetRemark(*s)
	}
	return msau
}

// ClearRemark clears the value of the "remark" field.
func (msau *MatrixSpectrumAlertUpdate) ClearRemark() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearRemark()
	return msau
}

// SetWofangID sets the "wofang_id" field.
func (msau *MatrixSpectrumAlertUpdate) SetWofangID(i int) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetWofangID(i)
	return msau
}

// SetNillableWofangID sets the "wofang_id" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableWofangID(i *int) *MatrixSpectrumAlertUpdate {
	if i != nil {
		msau.SetWofangID(*i)
	}
	return msau
}

// ClearWofangID clears the value of the "wofang_id" field.
func (msau *MatrixSpectrumAlertUpdate) ClearWofangID() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearWofangID()
	return msau
}

// SetMatrixStrategyID sets the "matrix_strategy_id" field.
func (msau *MatrixSpectrumAlertUpdate) SetMatrixStrategyID(i int) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetMatrixStrategyID(i)
	return msau
}

// SetNillableMatrixStrategyID sets the "matrix_strategy_id" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableMatrixStrategyID(i *int) *MatrixSpectrumAlertUpdate {
	if i != nil {
		msau.SetMatrixStrategyID(*i)
	}
	return msau
}

// ClearMatrixStrategyID clears the value of the "matrix_strategy_id" field.
func (msau *MatrixSpectrumAlertUpdate) ClearMatrixStrategyID() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearMatrixStrategyID()
	return msau
}

// SetIPList sets the "ip_list" field.
func (msau *MatrixSpectrumAlertUpdate) SetIPList(s *[]string) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetIPList(s)
	return msau
}

// ClearIPList clears the value of the "ip_list" field.
func (msau *MatrixSpectrumAlertUpdate) ClearIPList() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearIPList()
	return msau
}

// SetRegion sets the "region" field.
func (msau *MatrixSpectrumAlertUpdate) SetRegion(s string) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetRegion(s)
	return msau
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableRegion(s *string) *MatrixSpectrumAlertUpdate {
	if s != nil {
		msau.SetRegion(*s)
	}
	return msau
}

// SetNetType sets the "net_type" field.
func (msau *MatrixSpectrumAlertUpdate) SetNetType(s string) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetNetType(s)
	return msau
}

// SetNillableNetType sets the "net_type" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableNetType(s *string) *MatrixSpectrumAlertUpdate {
	if s != nil {
		msau.SetNetType(*s)
	}
	return msau
}

// SetIsp sets the "isp" field.
func (msau *MatrixSpectrumAlertUpdate) SetIsp(s string) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetIsp(s)
	return msau
}

// SetNillableIsp sets the "isp" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableIsp(s *string) *MatrixSpectrumAlertUpdate {
	if s != nil {
		msau.SetIsp(*s)
	}
	return msau
}

// SetStartTime sets the "start_time" field.
func (msau *MatrixSpectrumAlertUpdate) SetStartTime(t time.Time) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetStartTime(t)
	return msau
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableStartTime(t *time.Time) *MatrixSpectrumAlertUpdate {
	if t != nil {
		msau.SetStartTime(*t)
	}
	return msau
}

// SetEndTime sets the "end_time" field.
func (msau *MatrixSpectrumAlertUpdate) SetEndTime(t time.Time) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetEndTime(t)
	return msau
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableEndTime(t *time.Time) *MatrixSpectrumAlertUpdate {
	if t != nil {
		msau.SetEndTime(*t)
	}
	return msau
}

// ClearEndTime clears the value of the "end_time" field.
func (msau *MatrixSpectrumAlertUpdate) ClearEndTime() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearEndTime()
	return msau
}

// SetAttackType sets the "attack_type" field.
func (msau *MatrixSpectrumAlertUpdate) SetAttackType(s string) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetAttackType(s)
	return msau
}

// SetNillableAttackType sets the "attack_type" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableAttackType(s *string) *MatrixSpectrumAlertUpdate {
	if s != nil {
		msau.SetAttackType(*s)
	}
	return msau
}

// SetBps sets the "bps" field.
func (msau *MatrixSpectrumAlertUpdate) SetBps(i int64) *MatrixSpectrumAlertUpdate {
	msau.mutation.ResetBps()
	msau.mutation.SetBps(i)
	return msau
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableBps(i *int64) *MatrixSpectrumAlertUpdate {
	if i != nil {
		msau.SetBps(*i)
	}
	return msau
}

// AddBps adds i to the "bps" field.
func (msau *MatrixSpectrumAlertUpdate) AddBps(i int64) *MatrixSpectrumAlertUpdate {
	msau.mutation.AddBps(i)
	return msau
}

// SetAttackInfo sets the "attack_info" field.
func (msau *MatrixSpectrumAlertUpdate) SetAttackInfo(nai netease.MatrixAttackInfo) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetAttackInfo(nai)
	return msau
}

// SetNillableAttackInfo sets the "attack_info" field if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableAttackInfo(nai *netease.MatrixAttackInfo) *MatrixSpectrumAlertUpdate {
	if nai != nil {
		msau.SetAttackInfo(*nai)
	}
	return msau
}

// ClearAttackInfo clears the value of the "attack_info" field.
func (msau *MatrixSpectrumAlertUpdate) ClearAttackInfo() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearAttackInfo()
	return msau
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (msau *MatrixSpectrumAlertUpdate) SetTenant(t *Tenant) *MatrixSpectrumAlertUpdate {
	return msau.SetTenantID(t.ID)
}

// AddMatrixSpectrumDataIDs adds the "matrix_spectrum_datas" edge to the MatrixSpectrumData entity by IDs.
func (msau *MatrixSpectrumAlertUpdate) AddMatrixSpectrumDataIDs(ids ...int) *MatrixSpectrumAlertUpdate {
	msau.mutation.AddMatrixSpectrumDataIDs(ids...)
	return msau
}

// AddMatrixSpectrumDatas adds the "matrix_spectrum_datas" edges to the MatrixSpectrumData entity.
func (msau *MatrixSpectrumAlertUpdate) AddMatrixSpectrumDatas(m ...*MatrixSpectrumData) *MatrixSpectrumAlertUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msau.AddMatrixSpectrumDataIDs(ids...)
}

// SetMatrixStrategy sets the "matrix_strategy" edge to the MatrixStrategy entity.
func (msau *MatrixSpectrumAlertUpdate) SetMatrixStrategy(m *MatrixStrategy) *MatrixSpectrumAlertUpdate {
	return msau.SetMatrixStrategyID(m.ID)
}

// SetWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID.
func (msau *MatrixSpectrumAlertUpdate) SetWofangTicketID(id int) *MatrixSpectrumAlertUpdate {
	msau.mutation.SetWofangTicketID(id)
	return msau
}

// SetNillableWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID if the given value is not nil.
func (msau *MatrixSpectrumAlertUpdate) SetNillableWofangTicketID(id *int) *MatrixSpectrumAlertUpdate {
	if id != nil {
		msau = msau.SetWofangTicketID(*id)
	}
	return msau
}

// SetWofangTicket sets the "wofang_ticket" edge to the Wofang entity.
func (msau *MatrixSpectrumAlertUpdate) SetWofangTicket(w *Wofang) *MatrixSpectrumAlertUpdate {
	return msau.SetWofangTicketID(w.ID)
}

// Mutation returns the MatrixSpectrumAlertMutation object of the builder.
func (msau *MatrixSpectrumAlertUpdate) Mutation() *MatrixSpectrumAlertMutation {
	return msau.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (msau *MatrixSpectrumAlertUpdate) ClearTenant() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearTenant()
	return msau
}

// ClearMatrixSpectrumDatas clears all "matrix_spectrum_datas" edges to the MatrixSpectrumData entity.
func (msau *MatrixSpectrumAlertUpdate) ClearMatrixSpectrumDatas() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearMatrixSpectrumDatas()
	return msau
}

// RemoveMatrixSpectrumDataIDs removes the "matrix_spectrum_datas" edge to MatrixSpectrumData entities by IDs.
func (msau *MatrixSpectrumAlertUpdate) RemoveMatrixSpectrumDataIDs(ids ...int) *MatrixSpectrumAlertUpdate {
	msau.mutation.RemoveMatrixSpectrumDataIDs(ids...)
	return msau
}

// RemoveMatrixSpectrumDatas removes "matrix_spectrum_datas" edges to MatrixSpectrumData entities.
func (msau *MatrixSpectrumAlertUpdate) RemoveMatrixSpectrumDatas(m ...*MatrixSpectrumData) *MatrixSpectrumAlertUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msau.RemoveMatrixSpectrumDataIDs(ids...)
}

// ClearMatrixStrategy clears the "matrix_strategy" edge to the MatrixStrategy entity.
func (msau *MatrixSpectrumAlertUpdate) ClearMatrixStrategy() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearMatrixStrategy()
	return msau
}

// ClearWofangTicket clears the "wofang_ticket" edge to the Wofang entity.
func (msau *MatrixSpectrumAlertUpdate) ClearWofangTicket() *MatrixSpectrumAlertUpdate {
	msau.mutation.ClearWofangTicket()
	return msau
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (msau *MatrixSpectrumAlertUpdate) Save(ctx context.Context) (int, error) {
	if err := msau.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, msau.sqlSave, msau.mutation, msau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (msau *MatrixSpectrumAlertUpdate) SaveX(ctx context.Context) int {
	affected, err := msau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (msau *MatrixSpectrumAlertUpdate) Exec(ctx context.Context) error {
	_, err := msau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msau *MatrixSpectrumAlertUpdate) ExecX(ctx context.Context) {
	if err := msau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msau *MatrixSpectrumAlertUpdate) defaults() error {
	if _, ok := msau.mutation.UpdatedAt(); !ok {
		if matrixspectrumalert.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixspectrumalert.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixspectrumalert.UpdateDefaultUpdatedAt()
		msau.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (msau *MatrixSpectrumAlertUpdate) check() error {
	if v, ok := msau.mutation.Remark(); ok {
		if err := matrixspectrumalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "MatrixSpectrumAlert.remark": %w`, err)}
		}
	}
	return nil
}

func (msau *MatrixSpectrumAlertUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := msau.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(matrixspectrumalert.Table, matrixspectrumalert.Columns, sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt))
	if ps := msau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := msau.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixspectrumalert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := msau.mutation.Remark(); ok {
		_spec.SetField(matrixspectrumalert.FieldRemark, field.TypeString, value)
	}
	if msau.mutation.RemarkCleared() {
		_spec.ClearField(matrixspectrumalert.FieldRemark, field.TypeString)
	}
	if value, ok := msau.mutation.IPList(); ok {
		_spec.SetField(matrixspectrumalert.FieldIPList, field.TypeJSON, value)
	}
	if msau.mutation.IPListCleared() {
		_spec.ClearField(matrixspectrumalert.FieldIPList, field.TypeJSON)
	}
	if value, ok := msau.mutation.Region(); ok {
		_spec.SetField(matrixspectrumalert.FieldRegion, field.TypeString, value)
	}
	if value, ok := msau.mutation.NetType(); ok {
		_spec.SetField(matrixspectrumalert.FieldNetType, field.TypeString, value)
	}
	if value, ok := msau.mutation.Isp(); ok {
		_spec.SetField(matrixspectrumalert.FieldIsp, field.TypeString, value)
	}
	if value, ok := msau.mutation.StartTime(); ok {
		_spec.SetField(matrixspectrumalert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := msau.mutation.EndTime(); ok {
		_spec.SetField(matrixspectrumalert.FieldEndTime, field.TypeTime, value)
	}
	if msau.mutation.EndTimeCleared() {
		_spec.ClearField(matrixspectrumalert.FieldEndTime, field.TypeTime)
	}
	if value, ok := msau.mutation.AttackType(); ok {
		_spec.SetField(matrixspectrumalert.FieldAttackType, field.TypeString, value)
	}
	if value, ok := msau.mutation.Bps(); ok {
		_spec.SetField(matrixspectrumalert.FieldBps, field.TypeInt64, value)
	}
	if value, ok := msau.mutation.AddedBps(); ok {
		_spec.AddField(matrixspectrumalert.FieldBps, field.TypeInt64, value)
	}
	if value, ok := msau.mutation.AttackInfo(); ok {
		_spec.SetField(matrixspectrumalert.FieldAttackInfo, field.TypeJSON, value)
	}
	if msau.mutation.AttackInfoCleared() {
		_spec.ClearField(matrixspectrumalert.FieldAttackInfo, field.TypeJSON)
	}
	if msau.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumalert.TenantTable,
			Columns: []string{matrixspectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msau.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumalert.TenantTable,
			Columns: []string{matrixspectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if msau.mutation.MatrixSpectrumDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixspectrumalert.MatrixSpectrumDatasTable,
			Columns: []string{matrixspectrumalert.MatrixSpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msau.mutation.RemovedMatrixSpectrumDatasIDs(); len(nodes) > 0 && !msau.mutation.MatrixSpectrumDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixspectrumalert.MatrixSpectrumDatasTable,
			Columns: []string{matrixspectrumalert.MatrixSpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msau.mutation.MatrixSpectrumDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixspectrumalert.MatrixSpectrumDatasTable,
			Columns: []string{matrixspectrumalert.MatrixSpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if msau.mutation.MatrixStrategyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.MatrixStrategyTable,
			Columns: []string{matrixspectrumalert.MatrixStrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msau.mutation.MatrixStrategyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.MatrixStrategyTable,
			Columns: []string{matrixspectrumalert.MatrixStrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if msau.mutation.WofangTicketCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.WofangTicketTable,
			Columns: []string{matrixspectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msau.mutation.WofangTicketIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.WofangTicketTable,
			Columns: []string{matrixspectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, msau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{matrixspectrumalert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	msau.mutation.done = true
	return n, nil
}

// MatrixSpectrumAlertUpdateOne is the builder for updating a single MatrixSpectrumAlert entity.
type MatrixSpectrumAlertUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *MatrixSpectrumAlertMutation
}

// SetTenantID sets the "tenant_id" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetTenantID(i int) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetTenantID(i)
	return msauo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableTenantID(i *int) *MatrixSpectrumAlertUpdateOne {
	if i != nil {
		msauo.SetTenantID(*i)
	}
	return msauo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearTenantID() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearTenantID()
	return msauo
}

// SetUpdatedAt sets the "updated_at" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetUpdatedAt(t time.Time) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetUpdatedAt(t)
	return msauo
}

// SetRemark sets the "remark" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetRemark(s string) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetRemark(s)
	return msauo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableRemark(s *string) *MatrixSpectrumAlertUpdateOne {
	if s != nil {
		msauo.SetRemark(*s)
	}
	return msauo
}

// ClearRemark clears the value of the "remark" field.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearRemark() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearRemark()
	return msauo
}

// SetWofangID sets the "wofang_id" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetWofangID(i int) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetWofangID(i)
	return msauo
}

// SetNillableWofangID sets the "wofang_id" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableWofangID(i *int) *MatrixSpectrumAlertUpdateOne {
	if i != nil {
		msauo.SetWofangID(*i)
	}
	return msauo
}

// ClearWofangID clears the value of the "wofang_id" field.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearWofangID() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearWofangID()
	return msauo
}

// SetMatrixStrategyID sets the "matrix_strategy_id" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetMatrixStrategyID(i int) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetMatrixStrategyID(i)
	return msauo
}

// SetNillableMatrixStrategyID sets the "matrix_strategy_id" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableMatrixStrategyID(i *int) *MatrixSpectrumAlertUpdateOne {
	if i != nil {
		msauo.SetMatrixStrategyID(*i)
	}
	return msauo
}

// ClearMatrixStrategyID clears the value of the "matrix_strategy_id" field.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearMatrixStrategyID() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearMatrixStrategyID()
	return msauo
}

// SetIPList sets the "ip_list" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetIPList(s *[]string) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetIPList(s)
	return msauo
}

// ClearIPList clears the value of the "ip_list" field.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearIPList() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearIPList()
	return msauo
}

// SetRegion sets the "region" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetRegion(s string) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetRegion(s)
	return msauo
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableRegion(s *string) *MatrixSpectrumAlertUpdateOne {
	if s != nil {
		msauo.SetRegion(*s)
	}
	return msauo
}

// SetNetType sets the "net_type" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNetType(s string) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetNetType(s)
	return msauo
}

// SetNillableNetType sets the "net_type" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableNetType(s *string) *MatrixSpectrumAlertUpdateOne {
	if s != nil {
		msauo.SetNetType(*s)
	}
	return msauo
}

// SetIsp sets the "isp" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetIsp(s string) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetIsp(s)
	return msauo
}

// SetNillableIsp sets the "isp" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableIsp(s *string) *MatrixSpectrumAlertUpdateOne {
	if s != nil {
		msauo.SetIsp(*s)
	}
	return msauo
}

// SetStartTime sets the "start_time" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetStartTime(t time.Time) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetStartTime(t)
	return msauo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableStartTime(t *time.Time) *MatrixSpectrumAlertUpdateOne {
	if t != nil {
		msauo.SetStartTime(*t)
	}
	return msauo
}

// SetEndTime sets the "end_time" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetEndTime(t time.Time) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetEndTime(t)
	return msauo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableEndTime(t *time.Time) *MatrixSpectrumAlertUpdateOne {
	if t != nil {
		msauo.SetEndTime(*t)
	}
	return msauo
}

// ClearEndTime clears the value of the "end_time" field.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearEndTime() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearEndTime()
	return msauo
}

// SetAttackType sets the "attack_type" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetAttackType(s string) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetAttackType(s)
	return msauo
}

// SetNillableAttackType sets the "attack_type" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableAttackType(s *string) *MatrixSpectrumAlertUpdateOne {
	if s != nil {
		msauo.SetAttackType(*s)
	}
	return msauo
}

// SetBps sets the "bps" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetBps(i int64) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ResetBps()
	msauo.mutation.SetBps(i)
	return msauo
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableBps(i *int64) *MatrixSpectrumAlertUpdateOne {
	if i != nil {
		msauo.SetBps(*i)
	}
	return msauo
}

// AddBps adds i to the "bps" field.
func (msauo *MatrixSpectrumAlertUpdateOne) AddBps(i int64) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.AddBps(i)
	return msauo
}

// SetAttackInfo sets the "attack_info" field.
func (msauo *MatrixSpectrumAlertUpdateOne) SetAttackInfo(nai netease.MatrixAttackInfo) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetAttackInfo(nai)
	return msauo
}

// SetNillableAttackInfo sets the "attack_info" field if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableAttackInfo(nai *netease.MatrixAttackInfo) *MatrixSpectrumAlertUpdateOne {
	if nai != nil {
		msauo.SetAttackInfo(*nai)
	}
	return msauo
}

// ClearAttackInfo clears the value of the "attack_info" field.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearAttackInfo() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearAttackInfo()
	return msauo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (msauo *MatrixSpectrumAlertUpdateOne) SetTenant(t *Tenant) *MatrixSpectrumAlertUpdateOne {
	return msauo.SetTenantID(t.ID)
}

// AddMatrixSpectrumDataIDs adds the "matrix_spectrum_datas" edge to the MatrixSpectrumData entity by IDs.
func (msauo *MatrixSpectrumAlertUpdateOne) AddMatrixSpectrumDataIDs(ids ...int) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.AddMatrixSpectrumDataIDs(ids...)
	return msauo
}

// AddMatrixSpectrumDatas adds the "matrix_spectrum_datas" edges to the MatrixSpectrumData entity.
func (msauo *MatrixSpectrumAlertUpdateOne) AddMatrixSpectrumDatas(m ...*MatrixSpectrumData) *MatrixSpectrumAlertUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msauo.AddMatrixSpectrumDataIDs(ids...)
}

// SetMatrixStrategy sets the "matrix_strategy" edge to the MatrixStrategy entity.
func (msauo *MatrixSpectrumAlertUpdateOne) SetMatrixStrategy(m *MatrixStrategy) *MatrixSpectrumAlertUpdateOne {
	return msauo.SetMatrixStrategyID(m.ID)
}

// SetWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID.
func (msauo *MatrixSpectrumAlertUpdateOne) SetWofangTicketID(id int) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.SetWofangTicketID(id)
	return msauo
}

// SetNillableWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID if the given value is not nil.
func (msauo *MatrixSpectrumAlertUpdateOne) SetNillableWofangTicketID(id *int) *MatrixSpectrumAlertUpdateOne {
	if id != nil {
		msauo = msauo.SetWofangTicketID(*id)
	}
	return msauo
}

// SetWofangTicket sets the "wofang_ticket" edge to the Wofang entity.
func (msauo *MatrixSpectrumAlertUpdateOne) SetWofangTicket(w *Wofang) *MatrixSpectrumAlertUpdateOne {
	return msauo.SetWofangTicketID(w.ID)
}

// Mutation returns the MatrixSpectrumAlertMutation object of the builder.
func (msauo *MatrixSpectrumAlertUpdateOne) Mutation() *MatrixSpectrumAlertMutation {
	return msauo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearTenant() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearTenant()
	return msauo
}

// ClearMatrixSpectrumDatas clears all "matrix_spectrum_datas" edges to the MatrixSpectrumData entity.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearMatrixSpectrumDatas() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearMatrixSpectrumDatas()
	return msauo
}

// RemoveMatrixSpectrumDataIDs removes the "matrix_spectrum_datas" edge to MatrixSpectrumData entities by IDs.
func (msauo *MatrixSpectrumAlertUpdateOne) RemoveMatrixSpectrumDataIDs(ids ...int) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.RemoveMatrixSpectrumDataIDs(ids...)
	return msauo
}

// RemoveMatrixSpectrumDatas removes "matrix_spectrum_datas" edges to MatrixSpectrumData entities.
func (msauo *MatrixSpectrumAlertUpdateOne) RemoveMatrixSpectrumDatas(m ...*MatrixSpectrumData) *MatrixSpectrumAlertUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msauo.RemoveMatrixSpectrumDataIDs(ids...)
}

// ClearMatrixStrategy clears the "matrix_strategy" edge to the MatrixStrategy entity.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearMatrixStrategy() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearMatrixStrategy()
	return msauo
}

// ClearWofangTicket clears the "wofang_ticket" edge to the Wofang entity.
func (msauo *MatrixSpectrumAlertUpdateOne) ClearWofangTicket() *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.ClearWofangTicket()
	return msauo
}

// Where appends a list predicates to the MatrixSpectrumAlertUpdate builder.
func (msauo *MatrixSpectrumAlertUpdateOne) Where(ps ...predicate.MatrixSpectrumAlert) *MatrixSpectrumAlertUpdateOne {
	msauo.mutation.Where(ps...)
	return msauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (msauo *MatrixSpectrumAlertUpdateOne) Select(field string, fields ...string) *MatrixSpectrumAlertUpdateOne {
	msauo.fields = append([]string{field}, fields...)
	return msauo
}

// Save executes the query and returns the updated MatrixSpectrumAlert entity.
func (msauo *MatrixSpectrumAlertUpdateOne) Save(ctx context.Context) (*MatrixSpectrumAlert, error) {
	if err := msauo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, msauo.sqlSave, msauo.mutation, msauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (msauo *MatrixSpectrumAlertUpdateOne) SaveX(ctx context.Context) *MatrixSpectrumAlert {
	node, err := msauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (msauo *MatrixSpectrumAlertUpdateOne) Exec(ctx context.Context) error {
	_, err := msauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msauo *MatrixSpectrumAlertUpdateOne) ExecX(ctx context.Context) {
	if err := msauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msauo *MatrixSpectrumAlertUpdateOne) defaults() error {
	if _, ok := msauo.mutation.UpdatedAt(); !ok {
		if matrixspectrumalert.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixspectrumalert.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixspectrumalert.UpdateDefaultUpdatedAt()
		msauo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (msauo *MatrixSpectrumAlertUpdateOne) check() error {
	if v, ok := msauo.mutation.Remark(); ok {
		if err := matrixspectrumalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "MatrixSpectrumAlert.remark": %w`, err)}
		}
	}
	return nil
}

func (msauo *MatrixSpectrumAlertUpdateOne) sqlSave(ctx context.Context) (_node *MatrixSpectrumAlert, err error) {
	if err := msauo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(matrixspectrumalert.Table, matrixspectrumalert.Columns, sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt))
	id, ok := msauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "MatrixSpectrumAlert.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := msauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, matrixspectrumalert.FieldID)
		for _, f := range fields {
			if !matrixspectrumalert.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != matrixspectrumalert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := msauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := msauo.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixspectrumalert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := msauo.mutation.Remark(); ok {
		_spec.SetField(matrixspectrumalert.FieldRemark, field.TypeString, value)
	}
	if msauo.mutation.RemarkCleared() {
		_spec.ClearField(matrixspectrumalert.FieldRemark, field.TypeString)
	}
	if value, ok := msauo.mutation.IPList(); ok {
		_spec.SetField(matrixspectrumalert.FieldIPList, field.TypeJSON, value)
	}
	if msauo.mutation.IPListCleared() {
		_spec.ClearField(matrixspectrumalert.FieldIPList, field.TypeJSON)
	}
	if value, ok := msauo.mutation.Region(); ok {
		_spec.SetField(matrixspectrumalert.FieldRegion, field.TypeString, value)
	}
	if value, ok := msauo.mutation.NetType(); ok {
		_spec.SetField(matrixspectrumalert.FieldNetType, field.TypeString, value)
	}
	if value, ok := msauo.mutation.Isp(); ok {
		_spec.SetField(matrixspectrumalert.FieldIsp, field.TypeString, value)
	}
	if value, ok := msauo.mutation.StartTime(); ok {
		_spec.SetField(matrixspectrumalert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := msauo.mutation.EndTime(); ok {
		_spec.SetField(matrixspectrumalert.FieldEndTime, field.TypeTime, value)
	}
	if msauo.mutation.EndTimeCleared() {
		_spec.ClearField(matrixspectrumalert.FieldEndTime, field.TypeTime)
	}
	if value, ok := msauo.mutation.AttackType(); ok {
		_spec.SetField(matrixspectrumalert.FieldAttackType, field.TypeString, value)
	}
	if value, ok := msauo.mutation.Bps(); ok {
		_spec.SetField(matrixspectrumalert.FieldBps, field.TypeInt64, value)
	}
	if value, ok := msauo.mutation.AddedBps(); ok {
		_spec.AddField(matrixspectrumalert.FieldBps, field.TypeInt64, value)
	}
	if value, ok := msauo.mutation.AttackInfo(); ok {
		_spec.SetField(matrixspectrumalert.FieldAttackInfo, field.TypeJSON, value)
	}
	if msauo.mutation.AttackInfoCleared() {
		_spec.ClearField(matrixspectrumalert.FieldAttackInfo, field.TypeJSON)
	}
	if msauo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumalert.TenantTable,
			Columns: []string{matrixspectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msauo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumalert.TenantTable,
			Columns: []string{matrixspectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if msauo.mutation.MatrixSpectrumDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixspectrumalert.MatrixSpectrumDatasTable,
			Columns: []string{matrixspectrumalert.MatrixSpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msauo.mutation.RemovedMatrixSpectrumDatasIDs(); len(nodes) > 0 && !msauo.mutation.MatrixSpectrumDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixspectrumalert.MatrixSpectrumDatasTable,
			Columns: []string{matrixspectrumalert.MatrixSpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msauo.mutation.MatrixSpectrumDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixspectrumalert.MatrixSpectrumDatasTable,
			Columns: []string{matrixspectrumalert.MatrixSpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if msauo.mutation.MatrixStrategyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.MatrixStrategyTable,
			Columns: []string{matrixspectrumalert.MatrixStrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msauo.mutation.MatrixStrategyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.MatrixStrategyTable,
			Columns: []string{matrixspectrumalert.MatrixStrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if msauo.mutation.WofangTicketCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.WofangTicketTable,
			Columns: []string{matrixspectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msauo.mutation.WofangTicketIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.WofangTicketTable,
			Columns: []string{matrixspectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &MatrixSpectrumAlert{config: msauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, msauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{matrixspectrumalert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	msauo.mutation.done = true
	return _node, nil
}
