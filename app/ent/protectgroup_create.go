// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/protectgroup"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"
	"meta/app/entity/netease"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ProtectGroupCreate is the builder for creating a ProtectGroup entity.
type ProtectGroupCreate struct {
	config
	mutation *ProtectGroupMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (pgc *ProtectGroupCreate) SetTenantID(i int) *ProtectGroupCreate {
	pgc.mutation.SetTenantID(i)
	return pgc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (pgc *ProtectGroupCreate) SetNillableTenantID(i *int) *ProtectGroupCreate {
	if i != nil {
		pgc.SetTenantID(*i)
	}
	return pgc
}

// SetCreatedAt sets the "created_at" field.
func (pgc *ProtectGroupCreate) SetCreatedAt(t time.Time) *ProtectGroupCreate {
	pgc.mutation.SetCreatedAt(t)
	return pgc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pgc *ProtectGroupCreate) SetNillableCreatedAt(t *time.Time) *ProtectGroupCreate {
	if t != nil {
		pgc.SetCreatedAt(*t)
	}
	return pgc
}

// SetUpdatedAt sets the "updated_at" field.
func (pgc *ProtectGroupCreate) SetUpdatedAt(t time.Time) *ProtectGroupCreate {
	pgc.mutation.SetUpdatedAt(t)
	return pgc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pgc *ProtectGroupCreate) SetNillableUpdatedAt(t *time.Time) *ProtectGroupCreate {
	if t != nil {
		pgc.SetUpdatedAt(*t)
	}
	return pgc
}

// SetRemark sets the "remark" field.
func (pgc *ProtectGroupCreate) SetRemark(s string) *ProtectGroupCreate {
	pgc.mutation.SetRemark(s)
	return pgc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (pgc *ProtectGroupCreate) SetNillableRemark(s *string) *ProtectGroupCreate {
	if s != nil {
		pgc.SetRemark(*s)
	}
	return pgc
}

// SetGroupName sets the "group_name" field.
func (pgc *ProtectGroupCreate) SetGroupName(s string) *ProtectGroupCreate {
	pgc.mutation.SetGroupName(s)
	return pgc
}

// SetGroupID sets the "group_id" field.
func (pgc *ProtectGroupCreate) SetGroupID(i int64) *ProtectGroupCreate {
	pgc.mutation.SetGroupID(i)
	return pgc
}

// SetType sets the "type" field.
func (pgc *ProtectGroupCreate) SetType(i int) *ProtectGroupCreate {
	pgc.mutation.SetType(i)
	return pgc
}

// SetIPList sets the "ip_list" field.
func (pgc *ProtectGroupCreate) SetIPList(s *[]string) *ProtectGroupCreate {
	pgc.mutation.SetIPList(s)
	return pgc
}

// SetExpandIP sets the "expand_ip" field.
func (pgc *ProtectGroupCreate) SetExpandIP(s string) *ProtectGroupCreate {
	pgc.mutation.SetExpandIP(s)
	return pgc
}

// SetNillableExpandIP sets the "expand_ip" field if the given value is not nil.
func (pgc *ProtectGroupCreate) SetNillableExpandIP(s *string) *ProtectGroupCreate {
	if s != nil {
		pgc.SetExpandIP(*s)
	}
	return pgc
}

// SetMonitorInfo sets the "monitor_info" field.
func (pgc *ProtectGroupCreate) SetMonitorInfo(ni *netease.MonitorInfo) *ProtectGroupCreate {
	pgc.mutation.SetMonitorInfo(ni)
	return pgc
}

// SetDragInfo sets the "drag_info" field.
func (pgc *ProtectGroupCreate) SetDragInfo(ni *netease.DragInfo) *ProtectGroupCreate {
	pgc.mutation.SetDragInfo(ni)
	return pgc
}

// SetNds4Config sets the "nds4_config" field.
func (pgc *ProtectGroupCreate) SetNds4Config(n *netease.Nds4Config) *ProtectGroupCreate {
	pgc.mutation.SetNds4Config(n)
	return pgc
}

// SetNds6Config sets the "nds6_config" field.
func (pgc *ProtectGroupCreate) SetNds6Config(n *netease.Nds6Config) *ProtectGroupCreate {
	pgc.mutation.SetNds6Config(n)
	return pgc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (pgc *ProtectGroupCreate) SetTenant(t *Tenant) *ProtectGroupCreate {
	return pgc.SetTenantID(t.ID)
}

// AddSpectrumAlertIDs adds the "spectrum_alerts" edge to the SpectrumAlert entity by IDs.
func (pgc *ProtectGroupCreate) AddSpectrumAlertIDs(ids ...int) *ProtectGroupCreate {
	pgc.mutation.AddSpectrumAlertIDs(ids...)
	return pgc
}

// AddSpectrumAlerts adds the "spectrum_alerts" edges to the SpectrumAlert entity.
func (pgc *ProtectGroupCreate) AddSpectrumAlerts(s ...*SpectrumAlert) *ProtectGroupCreate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return pgc.AddSpectrumAlertIDs(ids...)
}

// Mutation returns the ProtectGroupMutation object of the builder.
func (pgc *ProtectGroupCreate) Mutation() *ProtectGroupMutation {
	return pgc.mutation
}

// Save creates the ProtectGroup in the database.
func (pgc *ProtectGroupCreate) Save(ctx context.Context) (*ProtectGroup, error) {
	if err := pgc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, pgc.sqlSave, pgc.mutation, pgc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pgc *ProtectGroupCreate) SaveX(ctx context.Context) *ProtectGroup {
	v, err := pgc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pgc *ProtectGroupCreate) Exec(ctx context.Context) error {
	_, err := pgc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pgc *ProtectGroupCreate) ExecX(ctx context.Context) {
	if err := pgc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pgc *ProtectGroupCreate) defaults() error {
	if _, ok := pgc.mutation.CreatedAt(); !ok {
		if protectgroup.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized protectgroup.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := protectgroup.DefaultCreatedAt()
		pgc.mutation.SetCreatedAt(v)
	}
	if _, ok := pgc.mutation.UpdatedAt(); !ok {
		if protectgroup.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized protectgroup.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := protectgroup.DefaultUpdatedAt()
		pgc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (pgc *ProtectGroupCreate) check() error {
	if _, ok := pgc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "ProtectGroup.created_at"`)}
	}
	if _, ok := pgc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "ProtectGroup.updated_at"`)}
	}
	if v, ok := pgc.mutation.Remark(); ok {
		if err := protectgroup.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "ProtectGroup.remark": %w`, err)}
		}
	}
	if _, ok := pgc.mutation.GroupName(); !ok {
		return &ValidationError{Name: "group_name", err: errors.New(`ent: missing required field "ProtectGroup.group_name"`)}
	}
	if _, ok := pgc.mutation.GroupID(); !ok {
		return &ValidationError{Name: "group_id", err: errors.New(`ent: missing required field "ProtectGroup.group_id"`)}
	}
	if _, ok := pgc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "ProtectGroup.type"`)}
	}
	if v, ok := pgc.mutation.ExpandIP(); ok {
		if err := protectgroup.ExpandIPValidator(v); err != nil {
			return &ValidationError{Name: "expand_ip", err: fmt.Errorf(`ent: validator failed for field "ProtectGroup.expand_ip": %w`, err)}
		}
	}
	return nil
}

func (pgc *ProtectGroupCreate) sqlSave(ctx context.Context) (*ProtectGroup, error) {
	if err := pgc.check(); err != nil {
		return nil, err
	}
	_node, _spec := pgc.createSpec()
	if err := sqlgraph.CreateNode(ctx, pgc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	pgc.mutation.id = &_node.ID
	pgc.mutation.done = true
	return _node, nil
}

func (pgc *ProtectGroupCreate) createSpec() (*ProtectGroup, *sqlgraph.CreateSpec) {
	var (
		_node = &ProtectGroup{config: pgc.config}
		_spec = sqlgraph.NewCreateSpec(protectgroup.Table, sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt))
	)
	if value, ok := pgc.mutation.CreatedAt(); ok {
		_spec.SetField(protectgroup.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pgc.mutation.UpdatedAt(); ok {
		_spec.SetField(protectgroup.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := pgc.mutation.Remark(); ok {
		_spec.SetField(protectgroup.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := pgc.mutation.GroupName(); ok {
		_spec.SetField(protectgroup.FieldGroupName, field.TypeString, value)
		_node.GroupName = value
	}
	if value, ok := pgc.mutation.GroupID(); ok {
		_spec.SetField(protectgroup.FieldGroupID, field.TypeInt64, value)
		_node.GroupID = value
	}
	if value, ok := pgc.mutation.GetType(); ok {
		_spec.SetField(protectgroup.FieldType, field.TypeInt, value)
		_node.Type = value
	}
	if value, ok := pgc.mutation.IPList(); ok {
		_spec.SetField(protectgroup.FieldIPList, field.TypeJSON, value)
		_node.IPList = value
	}
	if value, ok := pgc.mutation.ExpandIP(); ok {
		_spec.SetField(protectgroup.FieldExpandIP, field.TypeString, value)
		_node.ExpandIP = value
	}
	if value, ok := pgc.mutation.MonitorInfo(); ok {
		_spec.SetField(protectgroup.FieldMonitorInfo, field.TypeJSON, value)
		_node.MonitorInfo = value
	}
	if value, ok := pgc.mutation.DragInfo(); ok {
		_spec.SetField(protectgroup.FieldDragInfo, field.TypeJSON, value)
		_node.DragInfo = value
	}
	if value, ok := pgc.mutation.Nds4Config(); ok {
		_spec.SetField(protectgroup.FieldNds4Config, field.TypeJSON, value)
		_node.Nds4Config = value
	}
	if value, ok := pgc.mutation.Nds6Config(); ok {
		_spec.SetField(protectgroup.FieldNds6Config, field.TypeJSON, value)
		_node.Nds6Config = value
	}
	if nodes := pgc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   protectgroup.TenantTable,
			Columns: []string{protectgroup.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pgc.mutation.SpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   protectgroup.SpectrumAlertsTable,
			Columns: []string{protectgroup.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// ProtectGroupCreateBulk is the builder for creating many ProtectGroup entities in bulk.
type ProtectGroupCreateBulk struct {
	config
	err      error
	builders []*ProtectGroupCreate
}

// Save creates the ProtectGroup entities in the database.
func (pgcb *ProtectGroupCreateBulk) Save(ctx context.Context) ([]*ProtectGroup, error) {
	if pgcb.err != nil {
		return nil, pgcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pgcb.builders))
	nodes := make([]*ProtectGroup, len(pgcb.builders))
	mutators := make([]Mutator, len(pgcb.builders))
	for i := range pgcb.builders {
		func(i int, root context.Context) {
			builder := pgcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ProtectGroupMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pgcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pgcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pgcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pgcb *ProtectGroupCreateBulk) SaveX(ctx context.Context) []*ProtectGroup {
	v, err := pgcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pgcb *ProtectGroupCreateBulk) Exec(ctx context.Context) error {
	_, err := pgcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pgcb *ProtectGroupCreateBulk) ExecX(ctx context.Context) {
	if err := pgcb.Exec(ctx); err != nil {
		panic(err)
	}
}
