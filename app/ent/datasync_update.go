// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/datasync"
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DataSyncUpdate is the builder for updating DataSync entities.
type DataSyncUpdate struct {
	config
	hooks    []Hook
	mutation *DataSyncMutation
}

// Where appends a list predicates to the DataSyncUpdate builder.
func (dsu *DataSyncUpdate) Where(ps ...predicate.DataSync) *DataSyncUpdate {
	dsu.mutation.Where(ps...)
	return dsu
}

// SetUpdatedAt sets the "updated_at" field.
func (dsu *DataSyncUpdate) SetUpdatedAt(t time.Time) *DataSyncUpdate {
	dsu.mutation.SetUpdatedAt(t)
	return dsu
}

// SetRemark sets the "remark" field.
func (dsu *DataSyncUpdate) SetRemark(s string) *DataSyncUpdate {
	dsu.mutation.SetRemark(s)
	return dsu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (dsu *DataSyncUpdate) SetNillableRemark(s *string) *DataSyncUpdate {
	if s != nil {
		dsu.SetRemark(*s)
	}
	return dsu
}

// ClearRemark clears the value of the "remark" field.
func (dsu *DataSyncUpdate) ClearRemark() *DataSyncUpdate {
	dsu.mutation.ClearRemark()
	return dsu
}

// SetPreDataList sets the "pre_data_list" field.
func (dsu *DataSyncUpdate) SetPreDataList(s *[]string) *DataSyncUpdate {
	dsu.mutation.SetPreDataList(s)
	return dsu
}

// ClearPreDataList clears the value of the "pre_data_list" field.
func (dsu *DataSyncUpdate) ClearPreDataList() *DataSyncUpdate {
	dsu.mutation.ClearPreDataList()
	return dsu
}

// SetDataList sets the "data_list" field.
func (dsu *DataSyncUpdate) SetDataList(s *[]string) *DataSyncUpdate {
	dsu.mutation.SetDataList(s)
	return dsu
}

// ClearDataList clears the value of the "data_list" field.
func (dsu *DataSyncUpdate) ClearDataList() *DataSyncUpdate {
	dsu.mutation.ClearDataList()
	return dsu
}

// SetDataType sets the "data_type" field.
func (dsu *DataSyncUpdate) SetDataType(s string) *DataSyncUpdate {
	dsu.mutation.SetDataType(s)
	return dsu
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (dsu *DataSyncUpdate) SetNillableDataType(s *string) *DataSyncUpdate {
	if s != nil {
		dsu.SetDataType(*s)
	}
	return dsu
}

// SetType sets the "type" field.
func (dsu *DataSyncUpdate) SetType(s string) *DataSyncUpdate {
	dsu.mutation.SetType(s)
	return dsu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (dsu *DataSyncUpdate) SetNillableType(s *string) *DataSyncUpdate {
	if s != nil {
		dsu.SetType(*s)
	}
	return dsu
}

// Mutation returns the DataSyncMutation object of the builder.
func (dsu *DataSyncUpdate) Mutation() *DataSyncMutation {
	return dsu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (dsu *DataSyncUpdate) Save(ctx context.Context) (int, error) {
	if err := dsu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, dsu.sqlSave, dsu.mutation, dsu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (dsu *DataSyncUpdate) SaveX(ctx context.Context) int {
	affected, err := dsu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (dsu *DataSyncUpdate) Exec(ctx context.Context) error {
	_, err := dsu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dsu *DataSyncUpdate) ExecX(ctx context.Context) {
	if err := dsu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dsu *DataSyncUpdate) defaults() error {
	if _, ok := dsu.mutation.UpdatedAt(); !ok {
		if datasync.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized datasync.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := datasync.UpdateDefaultUpdatedAt()
		dsu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (dsu *DataSyncUpdate) check() error {
	if v, ok := dsu.mutation.Remark(); ok {
		if err := datasync.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "DataSync.remark": %w`, err)}
		}
	}
	return nil
}

func (dsu *DataSyncUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := dsu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(datasync.Table, datasync.Columns, sqlgraph.NewFieldSpec(datasync.FieldID, field.TypeInt))
	if ps := dsu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := dsu.mutation.UpdatedAt(); ok {
		_spec.SetField(datasync.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := dsu.mutation.Remark(); ok {
		_spec.SetField(datasync.FieldRemark, field.TypeString, value)
	}
	if dsu.mutation.RemarkCleared() {
		_spec.ClearField(datasync.FieldRemark, field.TypeString)
	}
	if value, ok := dsu.mutation.PreDataList(); ok {
		_spec.SetField(datasync.FieldPreDataList, field.TypeJSON, value)
	}
	if dsu.mutation.PreDataListCleared() {
		_spec.ClearField(datasync.FieldPreDataList, field.TypeJSON)
	}
	if value, ok := dsu.mutation.DataList(); ok {
		_spec.SetField(datasync.FieldDataList, field.TypeJSON, value)
	}
	if dsu.mutation.DataListCleared() {
		_spec.ClearField(datasync.FieldDataList, field.TypeJSON)
	}
	if value, ok := dsu.mutation.DataType(); ok {
		_spec.SetField(datasync.FieldDataType, field.TypeString, value)
	}
	if value, ok := dsu.mutation.GetType(); ok {
		_spec.SetField(datasync.FieldType, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, dsu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{datasync.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	dsu.mutation.done = true
	return n, nil
}

// DataSyncUpdateOne is the builder for updating a single DataSync entity.
type DataSyncUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *DataSyncMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (dsuo *DataSyncUpdateOne) SetUpdatedAt(t time.Time) *DataSyncUpdateOne {
	dsuo.mutation.SetUpdatedAt(t)
	return dsuo
}

// SetRemark sets the "remark" field.
func (dsuo *DataSyncUpdateOne) SetRemark(s string) *DataSyncUpdateOne {
	dsuo.mutation.SetRemark(s)
	return dsuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (dsuo *DataSyncUpdateOne) SetNillableRemark(s *string) *DataSyncUpdateOne {
	if s != nil {
		dsuo.SetRemark(*s)
	}
	return dsuo
}

// ClearRemark clears the value of the "remark" field.
func (dsuo *DataSyncUpdateOne) ClearRemark() *DataSyncUpdateOne {
	dsuo.mutation.ClearRemark()
	return dsuo
}

// SetPreDataList sets the "pre_data_list" field.
func (dsuo *DataSyncUpdateOne) SetPreDataList(s *[]string) *DataSyncUpdateOne {
	dsuo.mutation.SetPreDataList(s)
	return dsuo
}

// ClearPreDataList clears the value of the "pre_data_list" field.
func (dsuo *DataSyncUpdateOne) ClearPreDataList() *DataSyncUpdateOne {
	dsuo.mutation.ClearPreDataList()
	return dsuo
}

// SetDataList sets the "data_list" field.
func (dsuo *DataSyncUpdateOne) SetDataList(s *[]string) *DataSyncUpdateOne {
	dsuo.mutation.SetDataList(s)
	return dsuo
}

// ClearDataList clears the value of the "data_list" field.
func (dsuo *DataSyncUpdateOne) ClearDataList() *DataSyncUpdateOne {
	dsuo.mutation.ClearDataList()
	return dsuo
}

// SetDataType sets the "data_type" field.
func (dsuo *DataSyncUpdateOne) SetDataType(s string) *DataSyncUpdateOne {
	dsuo.mutation.SetDataType(s)
	return dsuo
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (dsuo *DataSyncUpdateOne) SetNillableDataType(s *string) *DataSyncUpdateOne {
	if s != nil {
		dsuo.SetDataType(*s)
	}
	return dsuo
}

// SetType sets the "type" field.
func (dsuo *DataSyncUpdateOne) SetType(s string) *DataSyncUpdateOne {
	dsuo.mutation.SetType(s)
	return dsuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (dsuo *DataSyncUpdateOne) SetNillableType(s *string) *DataSyncUpdateOne {
	if s != nil {
		dsuo.SetType(*s)
	}
	return dsuo
}

// Mutation returns the DataSyncMutation object of the builder.
func (dsuo *DataSyncUpdateOne) Mutation() *DataSyncMutation {
	return dsuo.mutation
}

// Where appends a list predicates to the DataSyncUpdate builder.
func (dsuo *DataSyncUpdateOne) Where(ps ...predicate.DataSync) *DataSyncUpdateOne {
	dsuo.mutation.Where(ps...)
	return dsuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (dsuo *DataSyncUpdateOne) Select(field string, fields ...string) *DataSyncUpdateOne {
	dsuo.fields = append([]string{field}, fields...)
	return dsuo
}

// Save executes the query and returns the updated DataSync entity.
func (dsuo *DataSyncUpdateOne) Save(ctx context.Context) (*DataSync, error) {
	if err := dsuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, dsuo.sqlSave, dsuo.mutation, dsuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (dsuo *DataSyncUpdateOne) SaveX(ctx context.Context) *DataSync {
	node, err := dsuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (dsuo *DataSyncUpdateOne) Exec(ctx context.Context) error {
	_, err := dsuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dsuo *DataSyncUpdateOne) ExecX(ctx context.Context) {
	if err := dsuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dsuo *DataSyncUpdateOne) defaults() error {
	if _, ok := dsuo.mutation.UpdatedAt(); !ok {
		if datasync.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized datasync.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := datasync.UpdateDefaultUpdatedAt()
		dsuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (dsuo *DataSyncUpdateOne) check() error {
	if v, ok := dsuo.mutation.Remark(); ok {
		if err := datasync.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "DataSync.remark": %w`, err)}
		}
	}
	return nil
}

func (dsuo *DataSyncUpdateOne) sqlSave(ctx context.Context) (_node *DataSync, err error) {
	if err := dsuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(datasync.Table, datasync.Columns, sqlgraph.NewFieldSpec(datasync.FieldID, field.TypeInt))
	id, ok := dsuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DataSync.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := dsuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, datasync.FieldID)
		for _, f := range fields {
			if !datasync.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != datasync.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := dsuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := dsuo.mutation.UpdatedAt(); ok {
		_spec.SetField(datasync.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := dsuo.mutation.Remark(); ok {
		_spec.SetField(datasync.FieldRemark, field.TypeString, value)
	}
	if dsuo.mutation.RemarkCleared() {
		_spec.ClearField(datasync.FieldRemark, field.TypeString)
	}
	if value, ok := dsuo.mutation.PreDataList(); ok {
		_spec.SetField(datasync.FieldPreDataList, field.TypeJSON, value)
	}
	if dsuo.mutation.PreDataListCleared() {
		_spec.ClearField(datasync.FieldPreDataList, field.TypeJSON)
	}
	if value, ok := dsuo.mutation.DataList(); ok {
		_spec.SetField(datasync.FieldDataList, field.TypeJSON, value)
	}
	if dsuo.mutation.DataListCleared() {
		_spec.ClearField(datasync.FieldDataList, field.TypeJSON)
	}
	if value, ok := dsuo.mutation.DataType(); ok {
		_spec.SetField(datasync.FieldDataType, field.TypeString, value)
	}
	if value, ok := dsuo.mutation.GetType(); ok {
		_spec.SetField(datasync.FieldType, field.TypeString, value)
	}
	_node = &DataSync{config: dsuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, dsuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{datasync.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	dsuo.mutation.done = true
	return _node, nil
}
