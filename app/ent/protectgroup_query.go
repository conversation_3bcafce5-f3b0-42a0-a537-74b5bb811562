// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/predicate"
	"meta/app/ent/protectgroup"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ProtectGroupQuery is the builder for querying ProtectGroup entities.
type ProtectGroupQuery struct {
	config
	ctx                *QueryContext
	order              []protectgroup.OrderOption
	inters             []Interceptor
	predicates         []predicate.ProtectGroup
	withTenant         *TenantQuery
	withSpectrumAlerts *SpectrumAlertQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ProtectGroupQuery builder.
func (pgq *ProtectGroupQuery) Where(ps ...predicate.ProtectGroup) *ProtectGroupQuery {
	pgq.predicates = append(pgq.predicates, ps...)
	return pgq
}

// Limit the number of records to be returned by this query.
func (pgq *ProtectGroupQuery) Limit(limit int) *ProtectGroupQuery {
	pgq.ctx.Limit = &limit
	return pgq
}

// Offset to start from.
func (pgq *ProtectGroupQuery) Offset(offset int) *ProtectGroupQuery {
	pgq.ctx.Offset = &offset
	return pgq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (pgq *ProtectGroupQuery) Unique(unique bool) *ProtectGroupQuery {
	pgq.ctx.Unique = &unique
	return pgq
}

// Order specifies how the records should be ordered.
func (pgq *ProtectGroupQuery) Order(o ...protectgroup.OrderOption) *ProtectGroupQuery {
	pgq.order = append(pgq.order, o...)
	return pgq
}

// QueryTenant chains the current query on the "tenant" edge.
func (pgq *ProtectGroupQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: pgq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pgq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pgq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(protectgroup.Table, protectgroup.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, protectgroup.TenantTable, protectgroup.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(pgq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QuerySpectrumAlerts chains the current query on the "spectrum_alerts" edge.
func (pgq *ProtectGroupQuery) QuerySpectrumAlerts() *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: pgq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := pgq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := pgq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(protectgroup.Table, protectgroup.FieldID, selector),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, protectgroup.SpectrumAlertsTable, protectgroup.SpectrumAlertsColumn),
		)
		fromU = sqlgraph.SetNeighbors(pgq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first ProtectGroup entity from the query.
// Returns a *NotFoundError when no ProtectGroup was found.
func (pgq *ProtectGroupQuery) First(ctx context.Context) (*ProtectGroup, error) {
	nodes, err := pgq.Limit(1).All(setContextOp(ctx, pgq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{protectgroup.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (pgq *ProtectGroupQuery) FirstX(ctx context.Context) *ProtectGroup {
	node, err := pgq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first ProtectGroup ID from the query.
// Returns a *NotFoundError when no ProtectGroup ID was found.
func (pgq *ProtectGroupQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = pgq.Limit(1).IDs(setContextOp(ctx, pgq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{protectgroup.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (pgq *ProtectGroupQuery) FirstIDX(ctx context.Context) int {
	id, err := pgq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single ProtectGroup entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one ProtectGroup entity is found.
// Returns a *NotFoundError when no ProtectGroup entities are found.
func (pgq *ProtectGroupQuery) Only(ctx context.Context) (*ProtectGroup, error) {
	nodes, err := pgq.Limit(2).All(setContextOp(ctx, pgq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{protectgroup.Label}
	default:
		return nil, &NotSingularError{protectgroup.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (pgq *ProtectGroupQuery) OnlyX(ctx context.Context) *ProtectGroup {
	node, err := pgq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only ProtectGroup ID in the query.
// Returns a *NotSingularError when more than one ProtectGroup ID is found.
// Returns a *NotFoundError when no entities are found.
func (pgq *ProtectGroupQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = pgq.Limit(2).IDs(setContextOp(ctx, pgq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{protectgroup.Label}
	default:
		err = &NotSingularError{protectgroup.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (pgq *ProtectGroupQuery) OnlyIDX(ctx context.Context) int {
	id, err := pgq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of ProtectGroups.
func (pgq *ProtectGroupQuery) All(ctx context.Context) ([]*ProtectGroup, error) {
	ctx = setContextOp(ctx, pgq.ctx, "All")
	if err := pgq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*ProtectGroup, *ProtectGroupQuery]()
	return withInterceptors[[]*ProtectGroup](ctx, pgq, qr, pgq.inters)
}

// AllX is like All, but panics if an error occurs.
func (pgq *ProtectGroupQuery) AllX(ctx context.Context) []*ProtectGroup {
	nodes, err := pgq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of ProtectGroup IDs.
func (pgq *ProtectGroupQuery) IDs(ctx context.Context) (ids []int, err error) {
	if pgq.ctx.Unique == nil && pgq.path != nil {
		pgq.Unique(true)
	}
	ctx = setContextOp(ctx, pgq.ctx, "IDs")
	if err = pgq.Select(protectgroup.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (pgq *ProtectGroupQuery) IDsX(ctx context.Context) []int {
	ids, err := pgq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (pgq *ProtectGroupQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, pgq.ctx, "Count")
	if err := pgq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, pgq, querierCount[*ProtectGroupQuery](), pgq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (pgq *ProtectGroupQuery) CountX(ctx context.Context) int {
	count, err := pgq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (pgq *ProtectGroupQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, pgq.ctx, "Exist")
	switch _, err := pgq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (pgq *ProtectGroupQuery) ExistX(ctx context.Context) bool {
	exist, err := pgq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ProtectGroupQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (pgq *ProtectGroupQuery) Clone() *ProtectGroupQuery {
	if pgq == nil {
		return nil
	}
	return &ProtectGroupQuery{
		config:             pgq.config,
		ctx:                pgq.ctx.Clone(),
		order:              append([]protectgroup.OrderOption{}, pgq.order...),
		inters:             append([]Interceptor{}, pgq.inters...),
		predicates:         append([]predicate.ProtectGroup{}, pgq.predicates...),
		withTenant:         pgq.withTenant.Clone(),
		withSpectrumAlerts: pgq.withSpectrumAlerts.Clone(),
		// clone intermediate query.
		sql:  pgq.sql.Clone(),
		path: pgq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (pgq *ProtectGroupQuery) WithTenant(opts ...func(*TenantQuery)) *ProtectGroupQuery {
	query := (&TenantClient{config: pgq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pgq.withTenant = query
	return pgq
}

// WithSpectrumAlerts tells the query-builder to eager-load the nodes that are connected to
// the "spectrum_alerts" edge. The optional arguments are used to configure the query builder of the edge.
func (pgq *ProtectGroupQuery) WithSpectrumAlerts(opts ...func(*SpectrumAlertQuery)) *ProtectGroupQuery {
	query := (&SpectrumAlertClient{config: pgq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	pgq.withSpectrumAlerts = query
	return pgq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.ProtectGroup.Query().
//		GroupBy(protectgroup.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (pgq *ProtectGroupQuery) GroupBy(field string, fields ...string) *ProtectGroupGroupBy {
	pgq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ProtectGroupGroupBy{build: pgq}
	grbuild.flds = &pgq.ctx.Fields
	grbuild.label = protectgroup.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.ProtectGroup.Query().
//		Select(protectgroup.FieldTenantID).
//		Scan(ctx, &v)
func (pgq *ProtectGroupQuery) Select(fields ...string) *ProtectGroupSelect {
	pgq.ctx.Fields = append(pgq.ctx.Fields, fields...)
	sbuild := &ProtectGroupSelect{ProtectGroupQuery: pgq}
	sbuild.label = protectgroup.Label
	sbuild.flds, sbuild.scan = &pgq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ProtectGroupSelect configured with the given aggregations.
func (pgq *ProtectGroupQuery) Aggregate(fns ...AggregateFunc) *ProtectGroupSelect {
	return pgq.Select().Aggregate(fns...)
}

func (pgq *ProtectGroupQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range pgq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, pgq); err != nil {
				return err
			}
		}
	}
	for _, f := range pgq.ctx.Fields {
		if !protectgroup.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if pgq.path != nil {
		prev, err := pgq.path(ctx)
		if err != nil {
			return err
		}
		pgq.sql = prev
	}
	if protectgroup.Policy == nil {
		return errors.New("ent: uninitialized protectgroup.Policy (forgotten import ent/runtime?)")
	}
	if err := protectgroup.Policy.EvalQuery(ctx, pgq); err != nil {
		return err
	}
	return nil
}

func (pgq *ProtectGroupQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*ProtectGroup, error) {
	var (
		nodes       = []*ProtectGroup{}
		_spec       = pgq.querySpec()
		loadedTypes = [2]bool{
			pgq.withTenant != nil,
			pgq.withSpectrumAlerts != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*ProtectGroup).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &ProtectGroup{config: pgq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, pgq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := pgq.withTenant; query != nil {
		if err := pgq.loadTenant(ctx, query, nodes, nil,
			func(n *ProtectGroup, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := pgq.withSpectrumAlerts; query != nil {
		if err := pgq.loadSpectrumAlerts(ctx, query, nodes,
			func(n *ProtectGroup) { n.Edges.SpectrumAlerts = []*SpectrumAlert{} },
			func(n *ProtectGroup, e *SpectrumAlert) { n.Edges.SpectrumAlerts = append(n.Edges.SpectrumAlerts, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (pgq *ProtectGroupQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*ProtectGroup, init func(*ProtectGroup), assign func(*ProtectGroup, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*ProtectGroup)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (pgq *ProtectGroupQuery) loadSpectrumAlerts(ctx context.Context, query *SpectrumAlertQuery, nodes []*ProtectGroup, init func(*ProtectGroup), assign func(*ProtectGroup, *SpectrumAlert)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*ProtectGroup)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(spectrumalert.FieldProtectGroupID)
	}
	query.Where(predicate.SpectrumAlert(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(protectgroup.SpectrumAlertsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.ProtectGroupID
		if fk == nil {
			return fmt.Errorf(`foreign-key "protect_group_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "protect_group_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (pgq *ProtectGroupQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := pgq.querySpec()
	_spec.Node.Columns = pgq.ctx.Fields
	if len(pgq.ctx.Fields) > 0 {
		_spec.Unique = pgq.ctx.Unique != nil && *pgq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, pgq.driver, _spec)
}

func (pgq *ProtectGroupQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(protectgroup.Table, protectgroup.Columns, sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt))
	_spec.From = pgq.sql
	if unique := pgq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if pgq.path != nil {
		_spec.Unique = true
	}
	if fields := pgq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, protectgroup.FieldID)
		for i := range fields {
			if fields[i] != protectgroup.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if pgq.withTenant != nil {
			_spec.Node.AddColumnOnce(protectgroup.FieldTenantID)
		}
	}
	if ps := pgq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := pgq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := pgq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := pgq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (pgq *ProtectGroupQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(pgq.driver.Dialect())
	t1 := builder.Table(protectgroup.Table)
	columns := pgq.ctx.Fields
	if len(columns) == 0 {
		columns = protectgroup.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if pgq.sql != nil {
		selector = pgq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if pgq.ctx.Unique != nil && *pgq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range pgq.predicates {
		p(selector)
	}
	for _, p := range pgq.order {
		p(selector)
	}
	if offset := pgq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := pgq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ProtectGroupGroupBy is the group-by builder for ProtectGroup entities.
type ProtectGroupGroupBy struct {
	selector
	build *ProtectGroupQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (pggb *ProtectGroupGroupBy) Aggregate(fns ...AggregateFunc) *ProtectGroupGroupBy {
	pggb.fns = append(pggb.fns, fns...)
	return pggb
}

// Scan applies the selector query and scans the result into the given value.
func (pggb *ProtectGroupGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, pggb.build.ctx, "GroupBy")
	if err := pggb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ProtectGroupQuery, *ProtectGroupGroupBy](ctx, pggb.build, pggb, pggb.build.inters, v)
}

func (pggb *ProtectGroupGroupBy) sqlScan(ctx context.Context, root *ProtectGroupQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(pggb.fns))
	for _, fn := range pggb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*pggb.flds)+len(pggb.fns))
		for _, f := range *pggb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*pggb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := pggb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ProtectGroupSelect is the builder for selecting fields of ProtectGroup entities.
type ProtectGroupSelect struct {
	*ProtectGroupQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (pgs *ProtectGroupSelect) Aggregate(fns ...AggregateFunc) *ProtectGroupSelect {
	pgs.fns = append(pgs.fns, fns...)
	return pgs
}

// Scan applies the selector query and scans the result into the given value.
func (pgs *ProtectGroupSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, pgs.ctx, "Select")
	if err := pgs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ProtectGroupQuery, *ProtectGroupSelect](ctx, pgs.ProtectGroupQuery, pgs, pgs.inters, v)
}

func (pgs *ProtectGroupSelect) sqlScan(ctx context.Context, root *ProtectGroupQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(pgs.fns))
	for _, fn := range pgs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*pgs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := pgs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
