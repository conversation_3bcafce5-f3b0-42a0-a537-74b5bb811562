// Code generated by ent, DO NOT EDIT.

package protectgroup

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the protectgroup type in the database.
	Label = "protect_group"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldGroupName holds the string denoting the group_name field in the database.
	FieldGroupName = "group_name"
	// FieldGroupID holds the string denoting the group_id field in the database.
	FieldGroupID = "group_id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldIPList holds the string denoting the ip_list field in the database.
	FieldIPList = "ip_list"
	// FieldExpandIP holds the string denoting the expand_ip field in the database.
	FieldExpandIP = "expand_ip"
	// FieldMonitorInfo holds the string denoting the monitor_info field in the database.
	FieldMonitorInfo = "monitor_info"
	// FieldDragInfo holds the string denoting the drag_info field in the database.
	FieldDragInfo = "drag_info"
	// FieldNds4Config holds the string denoting the nds4_config field in the database.
	FieldNds4Config = "nds4_config"
	// FieldNds6Config holds the string denoting the nds6_config field in the database.
	FieldNds6Config = "nds6_config"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeSpectrumAlerts holds the string denoting the spectrum_alerts edge name in mutations.
	EdgeSpectrumAlerts = "spectrum_alerts"
	// Table holds the table name of the protectgroup in the database.
	Table = "protect_groups"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "protect_groups"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// SpectrumAlertsTable is the table that holds the spectrum_alerts relation/edge.
	SpectrumAlertsTable = "spectrum_alerts"
	// SpectrumAlertsInverseTable is the table name for the SpectrumAlert entity.
	// It exists in this package in order to avoid circular dependency with the "spectrumalert" package.
	SpectrumAlertsInverseTable = "spectrum_alerts"
	// SpectrumAlertsColumn is the table column denoting the spectrum_alerts relation/edge.
	SpectrumAlertsColumn = "protect_group_id"
)

// Columns holds all SQL columns for protectgroup fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldGroupName,
	FieldGroupID,
	FieldType,
	FieldIPList,
	FieldExpandIP,
	FieldMonitorInfo,
	FieldDragInfo,
	FieldNds4Config,
	FieldNds6Config,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
	// ExpandIPValidator is a validator for the "expand_ip" field. It is called by the builders before save.
	ExpandIPValidator func(string) error
)

// OrderOption defines the ordering options for the ProtectGroup queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByGroupName orders the results by the group_name field.
func ByGroupName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGroupName, opts...).ToFunc()
}

// ByGroupID orders the results by the group_id field.
func ByGroupID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGroupID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByExpandIP orders the results by the expand_ip field.
func ByExpandIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExpandIP, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// BySpectrumAlertsCount orders the results by spectrum_alerts count.
func BySpectrumAlertsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newSpectrumAlertsStep(), opts...)
	}
}

// BySpectrumAlerts orders the results by spectrum_alerts terms.
func BySpectrumAlerts(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSpectrumAlertsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newSpectrumAlertsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SpectrumAlertsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, SpectrumAlertsTable, SpectrumAlertsColumn),
	)
}
