// Code generated by ent, DO NOT EDIT.

package protectgroup

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldRemark, v))
}

// GroupName applies equality check predicate on the "group_name" field. It's identical to GroupNameEQ.
func GroupName(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldGroupName, v))
}

// GroupID applies equality check predicate on the "group_id" field. It's identical to GroupIDEQ.
func GroupID(v int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldGroupID, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldType, v))
}

// ExpandIP applies equality check predicate on the "expand_ip" field. It's identical to ExpandIPEQ.
func ExpandIP(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldExpandIP, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldContainsFold(FieldRemark, v))
}

// GroupNameEQ applies the EQ predicate on the "group_name" field.
func GroupNameEQ(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldGroupName, v))
}

// GroupNameNEQ applies the NEQ predicate on the "group_name" field.
func GroupNameNEQ(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldGroupName, v))
}

// GroupNameIn applies the In predicate on the "group_name" field.
func GroupNameIn(vs ...string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldGroupName, vs...))
}

// GroupNameNotIn applies the NotIn predicate on the "group_name" field.
func GroupNameNotIn(vs ...string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldGroupName, vs...))
}

// GroupNameGT applies the GT predicate on the "group_name" field.
func GroupNameGT(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGT(FieldGroupName, v))
}

// GroupNameGTE applies the GTE predicate on the "group_name" field.
func GroupNameGTE(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGTE(FieldGroupName, v))
}

// GroupNameLT applies the LT predicate on the "group_name" field.
func GroupNameLT(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLT(FieldGroupName, v))
}

// GroupNameLTE applies the LTE predicate on the "group_name" field.
func GroupNameLTE(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLTE(FieldGroupName, v))
}

// GroupNameContains applies the Contains predicate on the "group_name" field.
func GroupNameContains(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldContains(FieldGroupName, v))
}

// GroupNameHasPrefix applies the HasPrefix predicate on the "group_name" field.
func GroupNameHasPrefix(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldHasPrefix(FieldGroupName, v))
}

// GroupNameHasSuffix applies the HasSuffix predicate on the "group_name" field.
func GroupNameHasSuffix(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldHasSuffix(FieldGroupName, v))
}

// GroupNameEqualFold applies the EqualFold predicate on the "group_name" field.
func GroupNameEqualFold(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEqualFold(FieldGroupName, v))
}

// GroupNameContainsFold applies the ContainsFold predicate on the "group_name" field.
func GroupNameContainsFold(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldContainsFold(FieldGroupName, v))
}

// GroupIDEQ applies the EQ predicate on the "group_id" field.
func GroupIDEQ(v int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldGroupID, v))
}

// GroupIDNEQ applies the NEQ predicate on the "group_id" field.
func GroupIDNEQ(v int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldGroupID, v))
}

// GroupIDIn applies the In predicate on the "group_id" field.
func GroupIDIn(vs ...int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldGroupID, vs...))
}

// GroupIDNotIn applies the NotIn predicate on the "group_id" field.
func GroupIDNotIn(vs ...int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldGroupID, vs...))
}

// GroupIDGT applies the GT predicate on the "group_id" field.
func GroupIDGT(v int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGT(FieldGroupID, v))
}

// GroupIDGTE applies the GTE predicate on the "group_id" field.
func GroupIDGTE(v int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGTE(FieldGroupID, v))
}

// GroupIDLT applies the LT predicate on the "group_id" field.
func GroupIDLT(v int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLT(FieldGroupID, v))
}

// GroupIDLTE applies the LTE predicate on the "group_id" field.
func GroupIDLTE(v int64) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLTE(FieldGroupID, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v int) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLTE(FieldType, v))
}

// IPListIsNil applies the IsNil predicate on the "ip_list" field.
func IPListIsNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIsNull(FieldIPList))
}

// IPListNotNil applies the NotNil predicate on the "ip_list" field.
func IPListNotNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotNull(FieldIPList))
}

// ExpandIPEQ applies the EQ predicate on the "expand_ip" field.
func ExpandIPEQ(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEQ(FieldExpandIP, v))
}

// ExpandIPNEQ applies the NEQ predicate on the "expand_ip" field.
func ExpandIPNEQ(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNEQ(FieldExpandIP, v))
}

// ExpandIPIn applies the In predicate on the "expand_ip" field.
func ExpandIPIn(vs ...string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIn(FieldExpandIP, vs...))
}

// ExpandIPNotIn applies the NotIn predicate on the "expand_ip" field.
func ExpandIPNotIn(vs ...string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotIn(FieldExpandIP, vs...))
}

// ExpandIPGT applies the GT predicate on the "expand_ip" field.
func ExpandIPGT(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGT(FieldExpandIP, v))
}

// ExpandIPGTE applies the GTE predicate on the "expand_ip" field.
func ExpandIPGTE(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldGTE(FieldExpandIP, v))
}

// ExpandIPLT applies the LT predicate on the "expand_ip" field.
func ExpandIPLT(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLT(FieldExpandIP, v))
}

// ExpandIPLTE applies the LTE predicate on the "expand_ip" field.
func ExpandIPLTE(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldLTE(FieldExpandIP, v))
}

// ExpandIPContains applies the Contains predicate on the "expand_ip" field.
func ExpandIPContains(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldContains(FieldExpandIP, v))
}

// ExpandIPHasPrefix applies the HasPrefix predicate on the "expand_ip" field.
func ExpandIPHasPrefix(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldHasPrefix(FieldExpandIP, v))
}

// ExpandIPHasSuffix applies the HasSuffix predicate on the "expand_ip" field.
func ExpandIPHasSuffix(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldHasSuffix(FieldExpandIP, v))
}

// ExpandIPIsNil applies the IsNil predicate on the "expand_ip" field.
func ExpandIPIsNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIsNull(FieldExpandIP))
}

// ExpandIPNotNil applies the NotNil predicate on the "expand_ip" field.
func ExpandIPNotNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotNull(FieldExpandIP))
}

// ExpandIPEqualFold applies the EqualFold predicate on the "expand_ip" field.
func ExpandIPEqualFold(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldEqualFold(FieldExpandIP, v))
}

// ExpandIPContainsFold applies the ContainsFold predicate on the "expand_ip" field.
func ExpandIPContainsFold(v string) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldContainsFold(FieldExpandIP, v))
}

// MonitorInfoIsNil applies the IsNil predicate on the "monitor_info" field.
func MonitorInfoIsNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIsNull(FieldMonitorInfo))
}

// MonitorInfoNotNil applies the NotNil predicate on the "monitor_info" field.
func MonitorInfoNotNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotNull(FieldMonitorInfo))
}

// DragInfoIsNil applies the IsNil predicate on the "drag_info" field.
func DragInfoIsNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIsNull(FieldDragInfo))
}

// DragInfoNotNil applies the NotNil predicate on the "drag_info" field.
func DragInfoNotNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotNull(FieldDragInfo))
}

// Nds4ConfigIsNil applies the IsNil predicate on the "nds4_config" field.
func Nds4ConfigIsNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIsNull(FieldNds4Config))
}

// Nds4ConfigNotNil applies the NotNil predicate on the "nds4_config" field.
func Nds4ConfigNotNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotNull(FieldNds4Config))
}

// Nds6ConfigIsNil applies the IsNil predicate on the "nds6_config" field.
func Nds6ConfigIsNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldIsNull(FieldNds6Config))
}

// Nds6ConfigNotNil applies the NotNil predicate on the "nds6_config" field.
func Nds6ConfigNotNil() predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.FieldNotNull(FieldNds6Config))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.ProtectGroup {
	return predicate.ProtectGroup(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.ProtectGroup {
	return predicate.ProtectGroup(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasSpectrumAlerts applies the HasEdge predicate on the "spectrum_alerts" edge.
func HasSpectrumAlerts() predicate.ProtectGroup {
	return predicate.ProtectGroup(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, SpectrumAlertsTable, SpectrumAlertsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSpectrumAlertsWith applies the HasEdge predicate on the "spectrum_alerts" edge with a given conditions (other predicates).
func HasSpectrumAlertsWith(preds ...predicate.SpectrumAlert) predicate.ProtectGroup {
	return predicate.ProtectGroup(func(s *sql.Selector) {
		step := newSpectrumAlertsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ProtectGroup) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ProtectGroup) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ProtectGroup) predicate.ProtectGroup {
	return predicate.ProtectGroup(sql.NotPredicates(p))
}
