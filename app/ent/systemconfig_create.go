// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/systemconfig"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SystemConfigCreate is the builder for creating a SystemConfig entity.
type SystemConfigCreate struct {
	config
	mutation *SystemConfigMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (scc *SystemConfigCreate) SetCreatedAt(t time.Time) *SystemConfigCreate {
	scc.mutation.SetCreatedAt(t)
	return scc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (scc *SystemConfigCreate) SetNillableCreatedAt(t *time.Time) *SystemConfigCreate {
	if t != nil {
		scc.SetCreatedAt(*t)
	}
	return scc
}

// SetUpdatedAt sets the "updated_at" field.
func (scc *SystemConfigCreate) SetUpdatedAt(t time.Time) *SystemConfigCreate {
	scc.mutation.SetUpdatedAt(t)
	return scc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (scc *SystemConfigCreate) SetNillableUpdatedAt(t *time.Time) *SystemConfigCreate {
	if t != nil {
		scc.SetUpdatedAt(*t)
	}
	return scc
}

// SetRemark sets the "remark" field.
func (scc *SystemConfigCreate) SetRemark(s string) *SystemConfigCreate {
	scc.mutation.SetRemark(s)
	return scc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (scc *SystemConfigCreate) SetNillableRemark(s *string) *SystemConfigCreate {
	if s != nil {
		scc.SetRemark(*s)
	}
	return scc
}

// SetWofangTestIP sets the "wofang_test_ip" field.
func (scc *SystemConfigCreate) SetWofangTestIP(s string) *SystemConfigCreate {
	scc.mutation.SetWofangTestIP(s)
	return scc
}

// SetNotifyPhones sets the "notify_phones" field.
func (scc *SystemConfigCreate) SetNotifyPhones(s *[]string) *SystemConfigCreate {
	scc.mutation.SetNotifyPhones(s)
	return scc
}

// SetNotifyEmails sets the "notify_emails" field.
func (scc *SystemConfigCreate) SetNotifyEmails(s *[]string) *SystemConfigCreate {
	scc.mutation.SetNotifyEmails(s)
	return scc
}

// SetNotifyScenes sets the "notify_scenes" field.
func (scc *SystemConfigCreate) SetNotifyScenes(s *[]string) *SystemConfigCreate {
	scc.mutation.SetNotifyScenes(s)
	return scc
}

// SetIPWhitelists sets the "ip_whitelists" field.
func (scc *SystemConfigCreate) SetIPWhitelists(s *[]string) *SystemConfigCreate {
	scc.mutation.SetIPWhitelists(s)
	return scc
}

// Mutation returns the SystemConfigMutation object of the builder.
func (scc *SystemConfigCreate) Mutation() *SystemConfigMutation {
	return scc.mutation
}

// Save creates the SystemConfig in the database.
func (scc *SystemConfigCreate) Save(ctx context.Context) (*SystemConfig, error) {
	if err := scc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, scc.sqlSave, scc.mutation, scc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (scc *SystemConfigCreate) SaveX(ctx context.Context) *SystemConfig {
	v, err := scc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (scc *SystemConfigCreate) Exec(ctx context.Context) error {
	_, err := scc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scc *SystemConfigCreate) ExecX(ctx context.Context) {
	if err := scc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (scc *SystemConfigCreate) defaults() error {
	if _, ok := scc.mutation.CreatedAt(); !ok {
		if systemconfig.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized systemconfig.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := systemconfig.DefaultCreatedAt()
		scc.mutation.SetCreatedAt(v)
	}
	if _, ok := scc.mutation.UpdatedAt(); !ok {
		if systemconfig.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized systemconfig.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := systemconfig.DefaultUpdatedAt()
		scc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (scc *SystemConfigCreate) check() error {
	if _, ok := scc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "SystemConfig.created_at"`)}
	}
	if _, ok := scc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "SystemConfig.updated_at"`)}
	}
	if v, ok := scc.mutation.Remark(); ok {
		if err := systemconfig.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SystemConfig.remark": %w`, err)}
		}
	}
	if _, ok := scc.mutation.WofangTestIP(); !ok {
		return &ValidationError{Name: "wofang_test_ip", err: errors.New(`ent: missing required field "SystemConfig.wofang_test_ip"`)}
	}
	if _, ok := scc.mutation.NotifyPhones(); !ok {
		return &ValidationError{Name: "notify_phones", err: errors.New(`ent: missing required field "SystemConfig.notify_phones"`)}
	}
	if _, ok := scc.mutation.NotifyEmails(); !ok {
		return &ValidationError{Name: "notify_emails", err: errors.New(`ent: missing required field "SystemConfig.notify_emails"`)}
	}
	if _, ok := scc.mutation.NotifyScenes(); !ok {
		return &ValidationError{Name: "notify_scenes", err: errors.New(`ent: missing required field "SystemConfig.notify_scenes"`)}
	}
	if _, ok := scc.mutation.IPWhitelists(); !ok {
		return &ValidationError{Name: "ip_whitelists", err: errors.New(`ent: missing required field "SystemConfig.ip_whitelists"`)}
	}
	return nil
}

func (scc *SystemConfigCreate) sqlSave(ctx context.Context) (*SystemConfig, error) {
	if err := scc.check(); err != nil {
		return nil, err
	}
	_node, _spec := scc.createSpec()
	if err := sqlgraph.CreateNode(ctx, scc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	scc.mutation.id = &_node.ID
	scc.mutation.done = true
	return _node, nil
}

func (scc *SystemConfigCreate) createSpec() (*SystemConfig, *sqlgraph.CreateSpec) {
	var (
		_node = &SystemConfig{config: scc.config}
		_spec = sqlgraph.NewCreateSpec(systemconfig.Table, sqlgraph.NewFieldSpec(systemconfig.FieldID, field.TypeInt))
	)
	if value, ok := scc.mutation.CreatedAt(); ok {
		_spec.SetField(systemconfig.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := scc.mutation.UpdatedAt(); ok {
		_spec.SetField(systemconfig.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := scc.mutation.Remark(); ok {
		_spec.SetField(systemconfig.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := scc.mutation.WofangTestIP(); ok {
		_spec.SetField(systemconfig.FieldWofangTestIP, field.TypeString, value)
		_node.WofangTestIP = value
	}
	if value, ok := scc.mutation.NotifyPhones(); ok {
		_spec.SetField(systemconfig.FieldNotifyPhones, field.TypeJSON, value)
		_node.NotifyPhones = value
	}
	if value, ok := scc.mutation.NotifyEmails(); ok {
		_spec.SetField(systemconfig.FieldNotifyEmails, field.TypeJSON, value)
		_node.NotifyEmails = value
	}
	if value, ok := scc.mutation.NotifyScenes(); ok {
		_spec.SetField(systemconfig.FieldNotifyScenes, field.TypeJSON, value)
		_node.NotifyScenes = value
	}
	if value, ok := scc.mutation.IPWhitelists(); ok {
		_spec.SetField(systemconfig.FieldIPWhitelists, field.TypeJSON, value)
		_node.IPWhitelists = value
	}
	return _node, _spec
}

// SystemConfigCreateBulk is the builder for creating many SystemConfig entities in bulk.
type SystemConfigCreateBulk struct {
	config
	err      error
	builders []*SystemConfigCreate
}

// Save creates the SystemConfig entities in the database.
func (sccb *SystemConfigCreateBulk) Save(ctx context.Context) ([]*SystemConfig, error) {
	if sccb.err != nil {
		return nil, sccb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(sccb.builders))
	nodes := make([]*SystemConfig, len(sccb.builders))
	mutators := make([]Mutator, len(sccb.builders))
	for i := range sccb.builders {
		func(i int, root context.Context) {
			builder := sccb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SystemConfigMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, sccb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, sccb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, sccb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (sccb *SystemConfigCreateBulk) SaveX(ctx context.Context) []*SystemConfig {
	v, err := sccb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sccb *SystemConfigCreateBulk) Exec(ctx context.Context) error {
	_, err := sccb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sccb *SystemConfigCreateBulk) ExecX(ctx context.Context) {
	if err := sccb.Exec(ctx); err != nil {
		panic(err)
	}
}
