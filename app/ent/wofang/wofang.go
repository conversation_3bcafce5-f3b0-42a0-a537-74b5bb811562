// Code generated by ent, DO NOT EDIT.

package wofang

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the wofang type in the database.
	Label = "wofang"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldIP holds the string denoting the ip field in the database.
	FieldIP = "ip"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldUnDragSecond holds the string denoting the un_drag_second field in the database.
	FieldUnDragSecond = "un_drag_second"
	// FieldStartTime holds the string denoting the start_time field in the database.
	FieldStartTime = "start_time"
	// FieldErrorInfo holds the string denoting the error_info field in the database.
	FieldErrorInfo = "error_info"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldCreateUserID holds the string denoting the create_user_id field in the database.
	FieldCreateUserID = "create_user_id"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// EdgeSpectrumAlerts holds the string denoting the spectrum_alerts edge name in mutations.
	EdgeSpectrumAlerts = "spectrum_alerts"
	// EdgeMatrixSpectrumAlerts holds the string denoting the matrix_spectrum_alerts edge name in mutations.
	EdgeMatrixSpectrumAlerts = "matrix_spectrum_alerts"
	// Table holds the table name of the wofang in the database.
	Table = "wofangs"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "wofangs"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "wofangs"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "users"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "create_user_id"
	// SpectrumAlertsTable is the table that holds the spectrum_alerts relation/edge.
	SpectrumAlertsTable = "spectrum_alerts"
	// SpectrumAlertsInverseTable is the table name for the SpectrumAlert entity.
	// It exists in this package in order to avoid circular dependency with the "spectrumalert" package.
	SpectrumAlertsInverseTable = "spectrum_alerts"
	// SpectrumAlertsColumn is the table column denoting the spectrum_alerts relation/edge.
	SpectrumAlertsColumn = "wofang_id"
	// MatrixSpectrumAlertsTable is the table that holds the matrix_spectrum_alerts relation/edge.
	MatrixSpectrumAlertsTable = "matrix_spectrum_alerts"
	// MatrixSpectrumAlertsInverseTable is the table name for the MatrixSpectrumAlert entity.
	// It exists in this package in order to avoid circular dependency with the "matrixspectrumalert" package.
	MatrixSpectrumAlertsInverseTable = "matrix_spectrum_alerts"
	// MatrixSpectrumAlertsColumn is the table column denoting the matrix_spectrum_alerts relation/edge.
	MatrixSpectrumAlertsColumn = "wofang_id"
)

// Columns holds all SQL columns for wofang fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldTenantID,
	FieldRemark,
	FieldName,
	FieldIP,
	FieldType,
	FieldUnDragSecond,
	FieldStartTime,
	FieldErrorInfo,
	FieldStatus,
	FieldCreateUserID,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
	// DefaultStartTime holds the default value on creation for the "start_time" field.
	DefaultStartTime func() time.Time
	// ErrorInfoValidator is a validator for the "error_info" field. It is called by the builders before save.
	ErrorInfoValidator func(string) error
)

// OrderOption defines the ordering options for the Wofang queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByIP orders the results by the ip field.
func ByIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIP, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByUnDragSecond orders the results by the un_drag_second field.
func ByUnDragSecond(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUnDragSecond, opts...).ToFunc()
}

// ByStartTime orders the results by the start_time field.
func ByStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartTime, opts...).ToFunc()
}

// ByErrorInfo orders the results by the error_info field.
func ByErrorInfo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldErrorInfo, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByCreateUserID orders the results by the create_user_id field.
func ByCreateUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateUserID, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}

// BySpectrumAlertsCount orders the results by spectrum_alerts count.
func BySpectrumAlertsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newSpectrumAlertsStep(), opts...)
	}
}

// BySpectrumAlerts orders the results by spectrum_alerts terms.
func BySpectrumAlerts(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSpectrumAlertsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByMatrixSpectrumAlertsCount orders the results by matrix_spectrum_alerts count.
func ByMatrixSpectrumAlertsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMatrixSpectrumAlertsStep(), opts...)
	}
}

// ByMatrixSpectrumAlerts orders the results by matrix_spectrum_alerts terms.
func ByMatrixSpectrumAlerts(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMatrixSpectrumAlertsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
	)
}
func newSpectrumAlertsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SpectrumAlertsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, SpectrumAlertsTable, SpectrumAlertsColumn),
	)
}
func newMatrixSpectrumAlertsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MatrixSpectrumAlertsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, MatrixSpectrumAlertsTable, MatrixSpectrumAlertsColumn),
	)
}
