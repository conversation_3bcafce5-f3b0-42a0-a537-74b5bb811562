// Code generated by ent, DO NOT EDIT.

package wofang

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldUpdatedAt, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldTenantID, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldRemark, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldName, v))
}

// IP applies equality check predicate on the "ip" field. It's identical to IPEQ.
func IP(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldIP, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldType, v))
}

// UnDragSecond applies equality check predicate on the "un_drag_second" field. It's identical to UnDragSecondEQ.
func UnDragSecond(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldUnDragSecond, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldStartTime, v))
}

// ErrorInfo applies equality check predicate on the "error_info" field. It's identical to ErrorInfoEQ.
func ErrorInfo(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldErrorInfo, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldStatus, v))
}

// CreateUserID applies equality check predicate on the "create_user_id" field. It's identical to CreateUserIDEQ.
func CreateUserID(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldCreateUserID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldUpdatedAt, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.Wofang {
	return predicate.Wofang(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.Wofang {
	return predicate.Wofang(sql.FieldNotNull(FieldTenantID))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.Wofang {
	return predicate.Wofang(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.Wofang {
	return predicate.Wofang(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContainsFold(FieldRemark, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContainsFold(FieldName, v))
}

// IPEQ applies the EQ predicate on the "ip" field.
func IPEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldIP, v))
}

// IPNEQ applies the NEQ predicate on the "ip" field.
func IPNEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldIP, v))
}

// IPIn applies the In predicate on the "ip" field.
func IPIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldIP, vs...))
}

// IPNotIn applies the NotIn predicate on the "ip" field.
func IPNotIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldIP, vs...))
}

// IPGT applies the GT predicate on the "ip" field.
func IPGT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldIP, v))
}

// IPGTE applies the GTE predicate on the "ip" field.
func IPGTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldIP, v))
}

// IPLT applies the LT predicate on the "ip" field.
func IPLT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldIP, v))
}

// IPLTE applies the LTE predicate on the "ip" field.
func IPLTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldIP, v))
}

// IPContains applies the Contains predicate on the "ip" field.
func IPContains(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContains(FieldIP, v))
}

// IPHasPrefix applies the HasPrefix predicate on the "ip" field.
func IPHasPrefix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasPrefix(FieldIP, v))
}

// IPHasSuffix applies the HasSuffix predicate on the "ip" field.
func IPHasSuffix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasSuffix(FieldIP, v))
}

// IPEqualFold applies the EqualFold predicate on the "ip" field.
func IPEqualFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEqualFold(FieldIP, v))
}

// IPContainsFold applies the ContainsFold predicate on the "ip" field.
func IPContainsFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContainsFold(FieldIP, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContainsFold(FieldType, v))
}

// UnDragSecondEQ applies the EQ predicate on the "un_drag_second" field.
func UnDragSecondEQ(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldUnDragSecond, v))
}

// UnDragSecondNEQ applies the NEQ predicate on the "un_drag_second" field.
func UnDragSecondNEQ(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldUnDragSecond, v))
}

// UnDragSecondIn applies the In predicate on the "un_drag_second" field.
func UnDragSecondIn(vs ...int) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldUnDragSecond, vs...))
}

// UnDragSecondNotIn applies the NotIn predicate on the "un_drag_second" field.
func UnDragSecondNotIn(vs ...int) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldUnDragSecond, vs...))
}

// UnDragSecondGT applies the GT predicate on the "un_drag_second" field.
func UnDragSecondGT(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldUnDragSecond, v))
}

// UnDragSecondGTE applies the GTE predicate on the "un_drag_second" field.
func UnDragSecondGTE(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldUnDragSecond, v))
}

// UnDragSecondLT applies the LT predicate on the "un_drag_second" field.
func UnDragSecondLT(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldUnDragSecond, v))
}

// UnDragSecondLTE applies the LTE predicate on the "un_drag_second" field.
func UnDragSecondLTE(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldUnDragSecond, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldStartTime, v))
}

// ErrorInfoEQ applies the EQ predicate on the "error_info" field.
func ErrorInfoEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldErrorInfo, v))
}

// ErrorInfoNEQ applies the NEQ predicate on the "error_info" field.
func ErrorInfoNEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldErrorInfo, v))
}

// ErrorInfoIn applies the In predicate on the "error_info" field.
func ErrorInfoIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldErrorInfo, vs...))
}

// ErrorInfoNotIn applies the NotIn predicate on the "error_info" field.
func ErrorInfoNotIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldErrorInfo, vs...))
}

// ErrorInfoGT applies the GT predicate on the "error_info" field.
func ErrorInfoGT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldErrorInfo, v))
}

// ErrorInfoGTE applies the GTE predicate on the "error_info" field.
func ErrorInfoGTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldErrorInfo, v))
}

// ErrorInfoLT applies the LT predicate on the "error_info" field.
func ErrorInfoLT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldErrorInfo, v))
}

// ErrorInfoLTE applies the LTE predicate on the "error_info" field.
func ErrorInfoLTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldErrorInfo, v))
}

// ErrorInfoContains applies the Contains predicate on the "error_info" field.
func ErrorInfoContains(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContains(FieldErrorInfo, v))
}

// ErrorInfoHasPrefix applies the HasPrefix predicate on the "error_info" field.
func ErrorInfoHasPrefix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasPrefix(FieldErrorInfo, v))
}

// ErrorInfoHasSuffix applies the HasSuffix predicate on the "error_info" field.
func ErrorInfoHasSuffix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasSuffix(FieldErrorInfo, v))
}

// ErrorInfoEqualFold applies the EqualFold predicate on the "error_info" field.
func ErrorInfoEqualFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEqualFold(FieldErrorInfo, v))
}

// ErrorInfoContainsFold applies the ContainsFold predicate on the "error_info" field.
func ErrorInfoContainsFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContainsFold(FieldErrorInfo, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.Wofang {
	return predicate.Wofang(sql.FieldContainsFold(FieldStatus, v))
}

// CreateUserIDEQ applies the EQ predicate on the "create_user_id" field.
func CreateUserIDEQ(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldEQ(FieldCreateUserID, v))
}

// CreateUserIDNEQ applies the NEQ predicate on the "create_user_id" field.
func CreateUserIDNEQ(v int) predicate.Wofang {
	return predicate.Wofang(sql.FieldNEQ(FieldCreateUserID, v))
}

// CreateUserIDIn applies the In predicate on the "create_user_id" field.
func CreateUserIDIn(vs ...int) predicate.Wofang {
	return predicate.Wofang(sql.FieldIn(FieldCreateUserID, vs...))
}

// CreateUserIDNotIn applies the NotIn predicate on the "create_user_id" field.
func CreateUserIDNotIn(vs ...int) predicate.Wofang {
	return predicate.Wofang(sql.FieldNotIn(FieldCreateUserID, vs...))
}

// CreateUserIDIsNil applies the IsNil predicate on the "create_user_id" field.
func CreateUserIDIsNil() predicate.Wofang {
	return predicate.Wofang(sql.FieldIsNull(FieldCreateUserID))
}

// CreateUserIDNotNil applies the NotNil predicate on the "create_user_id" field.
func CreateUserIDNotNil() predicate.Wofang {
	return predicate.Wofang(sql.FieldNotNull(FieldCreateUserID))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.Wofang {
	return predicate.Wofang(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.Wofang {
	return predicate.Wofang(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.Wofang {
	return predicate.Wofang(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.Wofang {
	return predicate.Wofang(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasSpectrumAlerts applies the HasEdge predicate on the "spectrum_alerts" edge.
func HasSpectrumAlerts() predicate.Wofang {
	return predicate.Wofang(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, SpectrumAlertsTable, SpectrumAlertsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSpectrumAlertsWith applies the HasEdge predicate on the "spectrum_alerts" edge with a given conditions (other predicates).
func HasSpectrumAlertsWith(preds ...predicate.SpectrumAlert) predicate.Wofang {
	return predicate.Wofang(func(s *sql.Selector) {
		step := newSpectrumAlertsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMatrixSpectrumAlerts applies the HasEdge predicate on the "matrix_spectrum_alerts" edge.
func HasMatrixSpectrumAlerts() predicate.Wofang {
	return predicate.Wofang(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, MatrixSpectrumAlertsTable, MatrixSpectrumAlertsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMatrixSpectrumAlertsWith applies the HasEdge predicate on the "matrix_spectrum_alerts" edge with a given conditions (other predicates).
func HasMatrixSpectrumAlertsWith(preds ...predicate.MatrixSpectrumAlert) predicate.Wofang {
	return predicate.Wofang(func(s *sql.Selector) {
		step := newMatrixSpectrumAlertsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Wofang) predicate.Wofang {
	return predicate.Wofang(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Wofang) predicate.Wofang {
	return predicate.Wofang(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Wofang) predicate.Wofang {
	return predicate.Wofang(sql.NotPredicates(p))
}
