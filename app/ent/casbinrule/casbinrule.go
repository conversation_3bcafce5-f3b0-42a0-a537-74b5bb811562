// Code generated by ent, DO NOT EDIT.

package casbinrule

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the casbinrule type in the database.
	Label = "casbin_rule"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "ptype"
	// FieldSub holds the string denoting the sub field in the database.
	FieldSub = "v0"
	// FieldDom holds the string denoting the dom field in the database.
	FieldDom = "v1"
	// FieldObj holds the string denoting the obj field in the database.
	FieldObj = "v2"
	// FieldAct holds the string denoting the act field in the database.
	FieldAct = "v3"
	// FieldV4 holds the string denoting the v4 field in the database.
	FieldV4 = "v4"
	// FieldV5 holds the string denoting the v5 field in the database.
	FieldV5 = "v5"
	// Table holds the table name of the casbinrule in the database.
	Table = "casbin_rules"
)

// Columns holds all SQL columns for casbinrule fields.
var Columns = []string{
	FieldID,
	FieldType,
	FieldSub,
	FieldDom,
	FieldObj,
	FieldAct,
	FieldV4,
	FieldV5,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// TypeValidator is a validator for the "type" field. It is called by the builders before save.
	TypeValidator func(string) error
	// SubValidator is a validator for the "sub" field. It is called by the builders before save.
	SubValidator func(string) error
	// DomValidator is a validator for the "dom" field. It is called by the builders before save.
	DomValidator func(string) error
	// ObjValidator is a validator for the "obj" field. It is called by the builders before save.
	ObjValidator func(string) error
)

// OrderOption defines the ordering options for the CasbinRule queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// BySub orders the results by the sub field.
func BySub(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSub, opts...).ToFunc()
}

// ByDom orders the results by the dom field.
func ByDom(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDom, opts...).ToFunc()
}

// ByObj orders the results by the obj field.
func ByObj(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldObj, opts...).ToFunc()
}

// ByAct orders the results by the act field.
func ByAct(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAct, opts...).ToFunc()
}

// ByV4 orders the results by the v4 field.
func ByV4(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldV4, opts...).ToFunc()
}

// ByV5 orders the results by the v5 field.
func ByV5(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldV5, opts...).ToFunc()
}
