// Code generated by ent, DO NOT EDIT.

package casbinrule

import (
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLTE(FieldID, id))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldType, v))
}

// Sub applies equality check predicate on the "sub" field. It's identical to SubEQ.
func Sub(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldSub, v))
}

// Dom applies equality check predicate on the "dom" field. It's identical to DomEQ.
func Dom(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldDom, v))
}

// Obj applies equality check predicate on the "obj" field. It's identical to ObjEQ.
func Obj(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldObj, v))
}

// Act applies equality check predicate on the "act" field. It's identical to ActEQ.
func Act(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldAct, v))
}

// V4 applies equality check predicate on the "v4" field. It's identical to V4EQ.
func V4(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldV4, v))
}

// V5 applies equality check predicate on the "v5" field. It's identical to V5EQ.
func V5(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldV5, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContainsFold(FieldType, v))
}

// SubEQ applies the EQ predicate on the "sub" field.
func SubEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldSub, v))
}

// SubNEQ applies the NEQ predicate on the "sub" field.
func SubNEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNEQ(FieldSub, v))
}

// SubIn applies the In predicate on the "sub" field.
func SubIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldIn(FieldSub, vs...))
}

// SubNotIn applies the NotIn predicate on the "sub" field.
func SubNotIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNotIn(FieldSub, vs...))
}

// SubGT applies the GT predicate on the "sub" field.
func SubGT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGT(FieldSub, v))
}

// SubGTE applies the GTE predicate on the "sub" field.
func SubGTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGTE(FieldSub, v))
}

// SubLT applies the LT predicate on the "sub" field.
func SubLT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLT(FieldSub, v))
}

// SubLTE applies the LTE predicate on the "sub" field.
func SubLTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLTE(FieldSub, v))
}

// SubContains applies the Contains predicate on the "sub" field.
func SubContains(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContains(FieldSub, v))
}

// SubHasPrefix applies the HasPrefix predicate on the "sub" field.
func SubHasPrefix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasPrefix(FieldSub, v))
}

// SubHasSuffix applies the HasSuffix predicate on the "sub" field.
func SubHasSuffix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasSuffix(FieldSub, v))
}

// SubEqualFold applies the EqualFold predicate on the "sub" field.
func SubEqualFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEqualFold(FieldSub, v))
}

// SubContainsFold applies the ContainsFold predicate on the "sub" field.
func SubContainsFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContainsFold(FieldSub, v))
}

// DomEQ applies the EQ predicate on the "dom" field.
func DomEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldDom, v))
}

// DomNEQ applies the NEQ predicate on the "dom" field.
func DomNEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNEQ(FieldDom, v))
}

// DomIn applies the In predicate on the "dom" field.
func DomIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldIn(FieldDom, vs...))
}

// DomNotIn applies the NotIn predicate on the "dom" field.
func DomNotIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNotIn(FieldDom, vs...))
}

// DomGT applies the GT predicate on the "dom" field.
func DomGT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGT(FieldDom, v))
}

// DomGTE applies the GTE predicate on the "dom" field.
func DomGTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGTE(FieldDom, v))
}

// DomLT applies the LT predicate on the "dom" field.
func DomLT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLT(FieldDom, v))
}

// DomLTE applies the LTE predicate on the "dom" field.
func DomLTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLTE(FieldDom, v))
}

// DomContains applies the Contains predicate on the "dom" field.
func DomContains(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContains(FieldDom, v))
}

// DomHasPrefix applies the HasPrefix predicate on the "dom" field.
func DomHasPrefix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasPrefix(FieldDom, v))
}

// DomHasSuffix applies the HasSuffix predicate on the "dom" field.
func DomHasSuffix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasSuffix(FieldDom, v))
}

// DomEqualFold applies the EqualFold predicate on the "dom" field.
func DomEqualFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEqualFold(FieldDom, v))
}

// DomContainsFold applies the ContainsFold predicate on the "dom" field.
func DomContainsFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContainsFold(FieldDom, v))
}

// ObjEQ applies the EQ predicate on the "obj" field.
func ObjEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldObj, v))
}

// ObjNEQ applies the NEQ predicate on the "obj" field.
func ObjNEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNEQ(FieldObj, v))
}

// ObjIn applies the In predicate on the "obj" field.
func ObjIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldIn(FieldObj, vs...))
}

// ObjNotIn applies the NotIn predicate on the "obj" field.
func ObjNotIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNotIn(FieldObj, vs...))
}

// ObjGT applies the GT predicate on the "obj" field.
func ObjGT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGT(FieldObj, v))
}

// ObjGTE applies the GTE predicate on the "obj" field.
func ObjGTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGTE(FieldObj, v))
}

// ObjLT applies the LT predicate on the "obj" field.
func ObjLT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLT(FieldObj, v))
}

// ObjLTE applies the LTE predicate on the "obj" field.
func ObjLTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLTE(FieldObj, v))
}

// ObjContains applies the Contains predicate on the "obj" field.
func ObjContains(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContains(FieldObj, v))
}

// ObjHasPrefix applies the HasPrefix predicate on the "obj" field.
func ObjHasPrefix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasPrefix(FieldObj, v))
}

// ObjHasSuffix applies the HasSuffix predicate on the "obj" field.
func ObjHasSuffix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasSuffix(FieldObj, v))
}

// ObjEqualFold applies the EqualFold predicate on the "obj" field.
func ObjEqualFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEqualFold(FieldObj, v))
}

// ObjContainsFold applies the ContainsFold predicate on the "obj" field.
func ObjContainsFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContainsFold(FieldObj, v))
}

// ActEQ applies the EQ predicate on the "act" field.
func ActEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldAct, v))
}

// ActNEQ applies the NEQ predicate on the "act" field.
func ActNEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNEQ(FieldAct, v))
}

// ActIn applies the In predicate on the "act" field.
func ActIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldIn(FieldAct, vs...))
}

// ActNotIn applies the NotIn predicate on the "act" field.
func ActNotIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNotIn(FieldAct, vs...))
}

// ActGT applies the GT predicate on the "act" field.
func ActGT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGT(FieldAct, v))
}

// ActGTE applies the GTE predicate on the "act" field.
func ActGTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGTE(FieldAct, v))
}

// ActLT applies the LT predicate on the "act" field.
func ActLT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLT(FieldAct, v))
}

// ActLTE applies the LTE predicate on the "act" field.
func ActLTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLTE(FieldAct, v))
}

// ActContains applies the Contains predicate on the "act" field.
func ActContains(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContains(FieldAct, v))
}

// ActHasPrefix applies the HasPrefix predicate on the "act" field.
func ActHasPrefix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasPrefix(FieldAct, v))
}

// ActHasSuffix applies the HasSuffix predicate on the "act" field.
func ActHasSuffix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasSuffix(FieldAct, v))
}

// ActEqualFold applies the EqualFold predicate on the "act" field.
func ActEqualFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEqualFold(FieldAct, v))
}

// ActContainsFold applies the ContainsFold predicate on the "act" field.
func ActContainsFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContainsFold(FieldAct, v))
}

// V4EQ applies the EQ predicate on the "v4" field.
func V4EQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldV4, v))
}

// V4NEQ applies the NEQ predicate on the "v4" field.
func V4NEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNEQ(FieldV4, v))
}

// V4In applies the In predicate on the "v4" field.
func V4In(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldIn(FieldV4, vs...))
}

// V4NotIn applies the NotIn predicate on the "v4" field.
func V4NotIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNotIn(FieldV4, vs...))
}

// V4GT applies the GT predicate on the "v4" field.
func V4GT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGT(FieldV4, v))
}

// V4GTE applies the GTE predicate on the "v4" field.
func V4GTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGTE(FieldV4, v))
}

// V4LT applies the LT predicate on the "v4" field.
func V4LT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLT(FieldV4, v))
}

// V4LTE applies the LTE predicate on the "v4" field.
func V4LTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLTE(FieldV4, v))
}

// V4Contains applies the Contains predicate on the "v4" field.
func V4Contains(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContains(FieldV4, v))
}

// V4HasPrefix applies the HasPrefix predicate on the "v4" field.
func V4HasPrefix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasPrefix(FieldV4, v))
}

// V4HasSuffix applies the HasSuffix predicate on the "v4" field.
func V4HasSuffix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasSuffix(FieldV4, v))
}

// V4EqualFold applies the EqualFold predicate on the "v4" field.
func V4EqualFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEqualFold(FieldV4, v))
}

// V4ContainsFold applies the ContainsFold predicate on the "v4" field.
func V4ContainsFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContainsFold(FieldV4, v))
}

// V5EQ applies the EQ predicate on the "v5" field.
func V5EQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEQ(FieldV5, v))
}

// V5NEQ applies the NEQ predicate on the "v5" field.
func V5NEQ(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNEQ(FieldV5, v))
}

// V5In applies the In predicate on the "v5" field.
func V5In(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldIn(FieldV5, vs...))
}

// V5NotIn applies the NotIn predicate on the "v5" field.
func V5NotIn(vs ...string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldNotIn(FieldV5, vs...))
}

// V5GT applies the GT predicate on the "v5" field.
func V5GT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGT(FieldV5, v))
}

// V5GTE applies the GTE predicate on the "v5" field.
func V5GTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldGTE(FieldV5, v))
}

// V5LT applies the LT predicate on the "v5" field.
func V5LT(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLT(FieldV5, v))
}

// V5LTE applies the LTE predicate on the "v5" field.
func V5LTE(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldLTE(FieldV5, v))
}

// V5Contains applies the Contains predicate on the "v5" field.
func V5Contains(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContains(FieldV5, v))
}

// V5HasPrefix applies the HasPrefix predicate on the "v5" field.
func V5HasPrefix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasPrefix(FieldV5, v))
}

// V5HasSuffix applies the HasSuffix predicate on the "v5" field.
func V5HasSuffix(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldHasSuffix(FieldV5, v))
}

// V5EqualFold applies the EqualFold predicate on the "v5" field.
func V5EqualFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldEqualFold(FieldV5, v))
}

// V5ContainsFold applies the ContainsFold predicate on the "v5" field.
func V5ContainsFold(v string) predicate.CasbinRule {
	return predicate.CasbinRule(sql.FieldContainsFold(FieldV5, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.CasbinRule) predicate.CasbinRule {
	return predicate.CasbinRule(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.CasbinRule) predicate.CasbinRule {
	return predicate.CasbinRule(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.CasbinRule) predicate.CasbinRule {
	return predicate.CasbinRule(sql.NotPredicates(p))
}
