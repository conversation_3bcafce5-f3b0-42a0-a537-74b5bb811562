// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// StrategyUpdate is the builder for updating Strategy entities.
type StrategyUpdate struct {
	config
	hooks    []Hook
	mutation *StrategyMutation
}

// Where appends a list predicates to the StrategyUpdate builder.
func (su *StrategyUpdate) Where(ps ...predicate.Strategy) *StrategyUpdate {
	su.mutation.Where(ps...)
	return su
}

// SetTenantID sets the "tenant_id" field.
func (su *StrategyUpdate) SetTenantID(i int) *StrategyUpdate {
	su.mutation.SetTenantID(i)
	return su
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableTenantID(i *int) *StrategyUpdate {
	if i != nil {
		su.SetTenantID(*i)
	}
	return su
}

// ClearTenantID clears the value of the "tenant_id" field.
func (su *StrategyUpdate) ClearTenantID() *StrategyUpdate {
	su.mutation.ClearTenantID()
	return su
}

// SetUpdatedAt sets the "updated_at" field.
func (su *StrategyUpdate) SetUpdatedAt(t time.Time) *StrategyUpdate {
	su.mutation.SetUpdatedAt(t)
	return su
}

// SetRemark sets the "remark" field.
func (su *StrategyUpdate) SetRemark(s string) *StrategyUpdate {
	su.mutation.SetRemark(s)
	return su
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableRemark(s *string) *StrategyUpdate {
	if s != nil {
		su.SetRemark(*s)
	}
	return su
}

// ClearRemark clears the value of the "remark" field.
func (su *StrategyUpdate) ClearRemark() *StrategyUpdate {
	su.mutation.ClearRemark()
	return su
}

// SetName sets the "name" field.
func (su *StrategyUpdate) SetName(s string) *StrategyUpdate {
	su.mutation.SetName(s)
	return su
}

// SetNillableName sets the "name" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableName(s *string) *StrategyUpdate {
	if s != nil {
		su.SetName(*s)
	}
	return su
}

// SetType sets the "type" field.
func (su *StrategyUpdate) SetType(i int) *StrategyUpdate {
	su.mutation.ResetType()
	su.mutation.SetType(i)
	return su
}

// SetNillableType sets the "type" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableType(i *int) *StrategyUpdate {
	if i != nil {
		su.SetType(*i)
	}
	return su
}

// AddType adds i to the "type" field.
func (su *StrategyUpdate) AddType(i int) *StrategyUpdate {
	su.mutation.AddType(i)
	return su
}

// SetEnabled sets the "enabled" field.
func (su *StrategyUpdate) SetEnabled(b bool) *StrategyUpdate {
	su.mutation.SetEnabled(b)
	return su
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableEnabled(b *bool) *StrategyUpdate {
	if b != nil {
		su.SetEnabled(*b)
	}
	return su
}

// SetSystem sets the "system" field.
func (su *StrategyUpdate) SetSystem(b bool) *StrategyUpdate {
	su.mutation.SetSystem(b)
	return su
}

// SetNillableSystem sets the "system" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableSystem(b *bool) *StrategyUpdate {
	if b != nil {
		su.SetSystem(*b)
	}
	return su
}

// SetBps sets the "bps" field.
func (su *StrategyUpdate) SetBps(i int64) *StrategyUpdate {
	su.mutation.ResetBps()
	su.mutation.SetBps(i)
	return su
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableBps(i *int64) *StrategyUpdate {
	if i != nil {
		su.SetBps(*i)
	}
	return su
}

// AddBps adds i to the "bps" field.
func (su *StrategyUpdate) AddBps(i int64) *StrategyUpdate {
	su.mutation.AddBps(i)
	return su
}

// SetPps sets the "pps" field.
func (su *StrategyUpdate) SetPps(i int64) *StrategyUpdate {
	su.mutation.ResetPps()
	su.mutation.SetPps(i)
	return su
}

// SetNillablePps sets the "pps" field if the given value is not nil.
func (su *StrategyUpdate) SetNillablePps(i *int64) *StrategyUpdate {
	if i != nil {
		su.SetPps(*i)
	}
	return su
}

// AddPps adds i to the "pps" field.
func (su *StrategyUpdate) AddPps(i int64) *StrategyUpdate {
	su.mutation.AddPps(i)
	return su
}

// SetBpsCount sets the "bps_count" field.
func (su *StrategyUpdate) SetBpsCount(i int) *StrategyUpdate {
	su.mutation.ResetBpsCount()
	su.mutation.SetBpsCount(i)
	return su
}

// SetNillableBpsCount sets the "bps_count" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableBpsCount(i *int) *StrategyUpdate {
	if i != nil {
		su.SetBpsCount(*i)
	}
	return su
}

// AddBpsCount adds i to the "bps_count" field.
func (su *StrategyUpdate) AddBpsCount(i int) *StrategyUpdate {
	su.mutation.AddBpsCount(i)
	return su
}

// SetPpsCount sets the "pps_count" field.
func (su *StrategyUpdate) SetPpsCount(i int) *StrategyUpdate {
	su.mutation.ResetPpsCount()
	su.mutation.SetPpsCount(i)
	return su
}

// SetNillablePpsCount sets the "pps_count" field if the given value is not nil.
func (su *StrategyUpdate) SetNillablePpsCount(i *int) *StrategyUpdate {
	if i != nil {
		su.SetPpsCount(*i)
	}
	return su
}

// AddPpsCount adds i to the "pps_count" field.
func (su *StrategyUpdate) AddPpsCount(i int) *StrategyUpdate {
	su.mutation.AddPpsCount(i)
	return su
}

// SetIspCode sets the "isp_code" field.
func (su *StrategyUpdate) SetIspCode(i int) *StrategyUpdate {
	su.mutation.ResetIspCode()
	su.mutation.SetIspCode(i)
	return su
}

// SetNillableIspCode sets the "isp_code" field if the given value is not nil.
func (su *StrategyUpdate) SetNillableIspCode(i *int) *StrategyUpdate {
	if i != nil {
		su.SetIspCode(*i)
	}
	return su
}

// AddIspCode adds i to the "isp_code" field.
func (su *StrategyUpdate) AddIspCode(i int) *StrategyUpdate {
	su.mutation.AddIspCode(i)
	return su
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (su *StrategyUpdate) SetTenant(t *Tenant) *StrategyUpdate {
	return su.SetTenantID(t.ID)
}

// AddStrategyAlertIDs adds the "strategy_alerts" edge to the SpectrumAlert entity by IDs.
func (su *StrategyUpdate) AddStrategyAlertIDs(ids ...int) *StrategyUpdate {
	su.mutation.AddStrategyAlertIDs(ids...)
	return su
}

// AddStrategyAlerts adds the "strategy_alerts" edges to the SpectrumAlert entity.
func (su *StrategyUpdate) AddStrategyAlerts(s ...*SpectrumAlert) *StrategyUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return su.AddStrategyAlertIDs(ids...)
}

// Mutation returns the StrategyMutation object of the builder.
func (su *StrategyUpdate) Mutation() *StrategyMutation {
	return su.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (su *StrategyUpdate) ClearTenant() *StrategyUpdate {
	su.mutation.ClearTenant()
	return su
}

// ClearStrategyAlerts clears all "strategy_alerts" edges to the SpectrumAlert entity.
func (su *StrategyUpdate) ClearStrategyAlerts() *StrategyUpdate {
	su.mutation.ClearStrategyAlerts()
	return su
}

// RemoveStrategyAlertIDs removes the "strategy_alerts" edge to SpectrumAlert entities by IDs.
func (su *StrategyUpdate) RemoveStrategyAlertIDs(ids ...int) *StrategyUpdate {
	su.mutation.RemoveStrategyAlertIDs(ids...)
	return su
}

// RemoveStrategyAlerts removes "strategy_alerts" edges to SpectrumAlert entities.
func (su *StrategyUpdate) RemoveStrategyAlerts(s ...*SpectrumAlert) *StrategyUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return su.RemoveStrategyAlertIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (su *StrategyUpdate) Save(ctx context.Context) (int, error) {
	if err := su.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, su.sqlSave, su.mutation, su.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (su *StrategyUpdate) SaveX(ctx context.Context) int {
	affected, err := su.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (su *StrategyUpdate) Exec(ctx context.Context) error {
	_, err := su.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (su *StrategyUpdate) ExecX(ctx context.Context) {
	if err := su.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (su *StrategyUpdate) defaults() error {
	if _, ok := su.mutation.UpdatedAt(); !ok {
		if strategy.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized strategy.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := strategy.UpdateDefaultUpdatedAt()
		su.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (su *StrategyUpdate) check() error {
	if v, ok := su.mutation.Remark(); ok {
		if err := strategy.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Strategy.remark": %w`, err)}
		}
	}
	return nil
}

func (su *StrategyUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := su.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(strategy.Table, strategy.Columns, sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt))
	if ps := su.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := su.mutation.UpdatedAt(); ok {
		_spec.SetField(strategy.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := su.mutation.Remark(); ok {
		_spec.SetField(strategy.FieldRemark, field.TypeString, value)
	}
	if su.mutation.RemarkCleared() {
		_spec.ClearField(strategy.FieldRemark, field.TypeString)
	}
	if value, ok := su.mutation.Name(); ok {
		_spec.SetField(strategy.FieldName, field.TypeString, value)
	}
	if value, ok := su.mutation.GetType(); ok {
		_spec.SetField(strategy.FieldType, field.TypeInt, value)
	}
	if value, ok := su.mutation.AddedType(); ok {
		_spec.AddField(strategy.FieldType, field.TypeInt, value)
	}
	if value, ok := su.mutation.Enabled(); ok {
		_spec.SetField(strategy.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := su.mutation.System(); ok {
		_spec.SetField(strategy.FieldSystem, field.TypeBool, value)
	}
	if value, ok := su.mutation.Bps(); ok {
		_spec.SetField(strategy.FieldBps, field.TypeInt64, value)
	}
	if value, ok := su.mutation.AddedBps(); ok {
		_spec.AddField(strategy.FieldBps, field.TypeInt64, value)
	}
	if value, ok := su.mutation.Pps(); ok {
		_spec.SetField(strategy.FieldPps, field.TypeInt64, value)
	}
	if value, ok := su.mutation.AddedPps(); ok {
		_spec.AddField(strategy.FieldPps, field.TypeInt64, value)
	}
	if value, ok := su.mutation.BpsCount(); ok {
		_spec.SetField(strategy.FieldBpsCount, field.TypeInt, value)
	}
	if value, ok := su.mutation.AddedBpsCount(); ok {
		_spec.AddField(strategy.FieldBpsCount, field.TypeInt, value)
	}
	if value, ok := su.mutation.PpsCount(); ok {
		_spec.SetField(strategy.FieldPpsCount, field.TypeInt, value)
	}
	if value, ok := su.mutation.AddedPpsCount(); ok {
		_spec.AddField(strategy.FieldPpsCount, field.TypeInt, value)
	}
	if value, ok := su.mutation.IspCode(); ok {
		_spec.SetField(strategy.FieldIspCode, field.TypeInt, value)
	}
	if value, ok := su.mutation.AddedIspCode(); ok {
		_spec.AddField(strategy.FieldIspCode, field.TypeInt, value)
	}
	if su.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   strategy.TenantTable,
			Columns: []string{strategy.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   strategy.TenantTable,
			Columns: []string{strategy.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if su.mutation.StrategyAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   strategy.StrategyAlertsTable,
			Columns: []string{strategy.StrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.RemovedStrategyAlertsIDs(); len(nodes) > 0 && !su.mutation.StrategyAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   strategy.StrategyAlertsTable,
			Columns: []string{strategy.StrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := su.mutation.StrategyAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   strategy.StrategyAlertsTable,
			Columns: []string{strategy.StrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, su.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{strategy.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	su.mutation.done = true
	return n, nil
}

// StrategyUpdateOne is the builder for updating a single Strategy entity.
type StrategyUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *StrategyMutation
}

// SetTenantID sets the "tenant_id" field.
func (suo *StrategyUpdateOne) SetTenantID(i int) *StrategyUpdateOne {
	suo.mutation.SetTenantID(i)
	return suo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableTenantID(i *int) *StrategyUpdateOne {
	if i != nil {
		suo.SetTenantID(*i)
	}
	return suo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (suo *StrategyUpdateOne) ClearTenantID() *StrategyUpdateOne {
	suo.mutation.ClearTenantID()
	return suo
}

// SetUpdatedAt sets the "updated_at" field.
func (suo *StrategyUpdateOne) SetUpdatedAt(t time.Time) *StrategyUpdateOne {
	suo.mutation.SetUpdatedAt(t)
	return suo
}

// SetRemark sets the "remark" field.
func (suo *StrategyUpdateOne) SetRemark(s string) *StrategyUpdateOne {
	suo.mutation.SetRemark(s)
	return suo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableRemark(s *string) *StrategyUpdateOne {
	if s != nil {
		suo.SetRemark(*s)
	}
	return suo
}

// ClearRemark clears the value of the "remark" field.
func (suo *StrategyUpdateOne) ClearRemark() *StrategyUpdateOne {
	suo.mutation.ClearRemark()
	return suo
}

// SetName sets the "name" field.
func (suo *StrategyUpdateOne) SetName(s string) *StrategyUpdateOne {
	suo.mutation.SetName(s)
	return suo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableName(s *string) *StrategyUpdateOne {
	if s != nil {
		suo.SetName(*s)
	}
	return suo
}

// SetType sets the "type" field.
func (suo *StrategyUpdateOne) SetType(i int) *StrategyUpdateOne {
	suo.mutation.ResetType()
	suo.mutation.SetType(i)
	return suo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableType(i *int) *StrategyUpdateOne {
	if i != nil {
		suo.SetType(*i)
	}
	return suo
}

// AddType adds i to the "type" field.
func (suo *StrategyUpdateOne) AddType(i int) *StrategyUpdateOne {
	suo.mutation.AddType(i)
	return suo
}

// SetEnabled sets the "enabled" field.
func (suo *StrategyUpdateOne) SetEnabled(b bool) *StrategyUpdateOne {
	suo.mutation.SetEnabled(b)
	return suo
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableEnabled(b *bool) *StrategyUpdateOne {
	if b != nil {
		suo.SetEnabled(*b)
	}
	return suo
}

// SetSystem sets the "system" field.
func (suo *StrategyUpdateOne) SetSystem(b bool) *StrategyUpdateOne {
	suo.mutation.SetSystem(b)
	return suo
}

// SetNillableSystem sets the "system" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableSystem(b *bool) *StrategyUpdateOne {
	if b != nil {
		suo.SetSystem(*b)
	}
	return suo
}

// SetBps sets the "bps" field.
func (suo *StrategyUpdateOne) SetBps(i int64) *StrategyUpdateOne {
	suo.mutation.ResetBps()
	suo.mutation.SetBps(i)
	return suo
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableBps(i *int64) *StrategyUpdateOne {
	if i != nil {
		suo.SetBps(*i)
	}
	return suo
}

// AddBps adds i to the "bps" field.
func (suo *StrategyUpdateOne) AddBps(i int64) *StrategyUpdateOne {
	suo.mutation.AddBps(i)
	return suo
}

// SetPps sets the "pps" field.
func (suo *StrategyUpdateOne) SetPps(i int64) *StrategyUpdateOne {
	suo.mutation.ResetPps()
	suo.mutation.SetPps(i)
	return suo
}

// SetNillablePps sets the "pps" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillablePps(i *int64) *StrategyUpdateOne {
	if i != nil {
		suo.SetPps(*i)
	}
	return suo
}

// AddPps adds i to the "pps" field.
func (suo *StrategyUpdateOne) AddPps(i int64) *StrategyUpdateOne {
	suo.mutation.AddPps(i)
	return suo
}

// SetBpsCount sets the "bps_count" field.
func (suo *StrategyUpdateOne) SetBpsCount(i int) *StrategyUpdateOne {
	suo.mutation.ResetBpsCount()
	suo.mutation.SetBpsCount(i)
	return suo
}

// SetNillableBpsCount sets the "bps_count" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableBpsCount(i *int) *StrategyUpdateOne {
	if i != nil {
		suo.SetBpsCount(*i)
	}
	return suo
}

// AddBpsCount adds i to the "bps_count" field.
func (suo *StrategyUpdateOne) AddBpsCount(i int) *StrategyUpdateOne {
	suo.mutation.AddBpsCount(i)
	return suo
}

// SetPpsCount sets the "pps_count" field.
func (suo *StrategyUpdateOne) SetPpsCount(i int) *StrategyUpdateOne {
	suo.mutation.ResetPpsCount()
	suo.mutation.SetPpsCount(i)
	return suo
}

// SetNillablePpsCount sets the "pps_count" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillablePpsCount(i *int) *StrategyUpdateOne {
	if i != nil {
		suo.SetPpsCount(*i)
	}
	return suo
}

// AddPpsCount adds i to the "pps_count" field.
func (suo *StrategyUpdateOne) AddPpsCount(i int) *StrategyUpdateOne {
	suo.mutation.AddPpsCount(i)
	return suo
}

// SetIspCode sets the "isp_code" field.
func (suo *StrategyUpdateOne) SetIspCode(i int) *StrategyUpdateOne {
	suo.mutation.ResetIspCode()
	suo.mutation.SetIspCode(i)
	return suo
}

// SetNillableIspCode sets the "isp_code" field if the given value is not nil.
func (suo *StrategyUpdateOne) SetNillableIspCode(i *int) *StrategyUpdateOne {
	if i != nil {
		suo.SetIspCode(*i)
	}
	return suo
}

// AddIspCode adds i to the "isp_code" field.
func (suo *StrategyUpdateOne) AddIspCode(i int) *StrategyUpdateOne {
	suo.mutation.AddIspCode(i)
	return suo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (suo *StrategyUpdateOne) SetTenant(t *Tenant) *StrategyUpdateOne {
	return suo.SetTenantID(t.ID)
}

// AddStrategyAlertIDs adds the "strategy_alerts" edge to the SpectrumAlert entity by IDs.
func (suo *StrategyUpdateOne) AddStrategyAlertIDs(ids ...int) *StrategyUpdateOne {
	suo.mutation.AddStrategyAlertIDs(ids...)
	return suo
}

// AddStrategyAlerts adds the "strategy_alerts" edges to the SpectrumAlert entity.
func (suo *StrategyUpdateOne) AddStrategyAlerts(s ...*SpectrumAlert) *StrategyUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return suo.AddStrategyAlertIDs(ids...)
}

// Mutation returns the StrategyMutation object of the builder.
func (suo *StrategyUpdateOne) Mutation() *StrategyMutation {
	return suo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (suo *StrategyUpdateOne) ClearTenant() *StrategyUpdateOne {
	suo.mutation.ClearTenant()
	return suo
}

// ClearStrategyAlerts clears all "strategy_alerts" edges to the SpectrumAlert entity.
func (suo *StrategyUpdateOne) ClearStrategyAlerts() *StrategyUpdateOne {
	suo.mutation.ClearStrategyAlerts()
	return suo
}

// RemoveStrategyAlertIDs removes the "strategy_alerts" edge to SpectrumAlert entities by IDs.
func (suo *StrategyUpdateOne) RemoveStrategyAlertIDs(ids ...int) *StrategyUpdateOne {
	suo.mutation.RemoveStrategyAlertIDs(ids...)
	return suo
}

// RemoveStrategyAlerts removes "strategy_alerts" edges to SpectrumAlert entities.
func (suo *StrategyUpdateOne) RemoveStrategyAlerts(s ...*SpectrumAlert) *StrategyUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return suo.RemoveStrategyAlertIDs(ids...)
}

// Where appends a list predicates to the StrategyUpdate builder.
func (suo *StrategyUpdateOne) Where(ps ...predicate.Strategy) *StrategyUpdateOne {
	suo.mutation.Where(ps...)
	return suo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (suo *StrategyUpdateOne) Select(field string, fields ...string) *StrategyUpdateOne {
	suo.fields = append([]string{field}, fields...)
	return suo
}

// Save executes the query and returns the updated Strategy entity.
func (suo *StrategyUpdateOne) Save(ctx context.Context) (*Strategy, error) {
	if err := suo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, suo.sqlSave, suo.mutation, suo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (suo *StrategyUpdateOne) SaveX(ctx context.Context) *Strategy {
	node, err := suo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (suo *StrategyUpdateOne) Exec(ctx context.Context) error {
	_, err := suo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (suo *StrategyUpdateOne) ExecX(ctx context.Context) {
	if err := suo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (suo *StrategyUpdateOne) defaults() error {
	if _, ok := suo.mutation.UpdatedAt(); !ok {
		if strategy.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized strategy.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := strategy.UpdateDefaultUpdatedAt()
		suo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (suo *StrategyUpdateOne) check() error {
	if v, ok := suo.mutation.Remark(); ok {
		if err := strategy.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Strategy.remark": %w`, err)}
		}
	}
	return nil
}

func (suo *StrategyUpdateOne) sqlSave(ctx context.Context) (_node *Strategy, err error) {
	if err := suo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(strategy.Table, strategy.Columns, sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt))
	id, ok := suo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Strategy.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := suo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, strategy.FieldID)
		for _, f := range fields {
			if !strategy.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != strategy.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := suo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := suo.mutation.UpdatedAt(); ok {
		_spec.SetField(strategy.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := suo.mutation.Remark(); ok {
		_spec.SetField(strategy.FieldRemark, field.TypeString, value)
	}
	if suo.mutation.RemarkCleared() {
		_spec.ClearField(strategy.FieldRemark, field.TypeString)
	}
	if value, ok := suo.mutation.Name(); ok {
		_spec.SetField(strategy.FieldName, field.TypeString, value)
	}
	if value, ok := suo.mutation.GetType(); ok {
		_spec.SetField(strategy.FieldType, field.TypeInt, value)
	}
	if value, ok := suo.mutation.AddedType(); ok {
		_spec.AddField(strategy.FieldType, field.TypeInt, value)
	}
	if value, ok := suo.mutation.Enabled(); ok {
		_spec.SetField(strategy.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := suo.mutation.System(); ok {
		_spec.SetField(strategy.FieldSystem, field.TypeBool, value)
	}
	if value, ok := suo.mutation.Bps(); ok {
		_spec.SetField(strategy.FieldBps, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.AddedBps(); ok {
		_spec.AddField(strategy.FieldBps, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.Pps(); ok {
		_spec.SetField(strategy.FieldPps, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.AddedPps(); ok {
		_spec.AddField(strategy.FieldPps, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.BpsCount(); ok {
		_spec.SetField(strategy.FieldBpsCount, field.TypeInt, value)
	}
	if value, ok := suo.mutation.AddedBpsCount(); ok {
		_spec.AddField(strategy.FieldBpsCount, field.TypeInt, value)
	}
	if value, ok := suo.mutation.PpsCount(); ok {
		_spec.SetField(strategy.FieldPpsCount, field.TypeInt, value)
	}
	if value, ok := suo.mutation.AddedPpsCount(); ok {
		_spec.AddField(strategy.FieldPpsCount, field.TypeInt, value)
	}
	if value, ok := suo.mutation.IspCode(); ok {
		_spec.SetField(strategy.FieldIspCode, field.TypeInt, value)
	}
	if value, ok := suo.mutation.AddedIspCode(); ok {
		_spec.AddField(strategy.FieldIspCode, field.TypeInt, value)
	}
	if suo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   strategy.TenantTable,
			Columns: []string{strategy.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   strategy.TenantTable,
			Columns: []string{strategy.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if suo.mutation.StrategyAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   strategy.StrategyAlertsTable,
			Columns: []string{strategy.StrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.RemovedStrategyAlertsIDs(); len(nodes) > 0 && !suo.mutation.StrategyAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   strategy.StrategyAlertsTable,
			Columns: []string{strategy.StrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := suo.mutation.StrategyAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   strategy.StrategyAlertsTable,
			Columns: []string{strategy.StrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Strategy{config: suo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, suo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{strategy.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	suo.mutation.done = true
	return _node, nil
}
