// Code generated by ent, DO NOT EDIT.

package skylinedos

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldRemark, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldStartTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldEndTime, v))
}

// Region applies equality check predicate on the "region" field. It's identical to RegionEQ.
func Region(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldRegion, v))
}

// Resource applies equality check predicate on the "resource" field. It's identical to ResourceEQ.
func Resource(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldResource, v))
}

// ResourceType applies equality check predicate on the "resource_type" field. It's identical to ResourceTypeEQ.
func ResourceType(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldResourceType, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldStatus, v))
}

// AttackID applies equality check predicate on the "attack_id" field. It's identical to AttackIDEQ.
func AttackID(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldAttackID, v))
}

// Project applies equality check predicate on the "project" field. It's identical to ProjectEQ.
func Project(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldProject, v))
}

// DurationTime applies equality check predicate on the "duration_time" field. It's identical to DurationTimeEQ.
func DurationTime(v int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldDurationTime, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContainsFold(FieldRemark, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldStartTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotNull(FieldEndTime))
}

// RegionEQ applies the EQ predicate on the "region" field.
func RegionEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldRegion, v))
}

// RegionNEQ applies the NEQ predicate on the "region" field.
func RegionNEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldRegion, v))
}

// RegionIn applies the In predicate on the "region" field.
func RegionIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldRegion, vs...))
}

// RegionNotIn applies the NotIn predicate on the "region" field.
func RegionNotIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldRegion, vs...))
}

// RegionGT applies the GT predicate on the "region" field.
func RegionGT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldRegion, v))
}

// RegionGTE applies the GTE predicate on the "region" field.
func RegionGTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldRegion, v))
}

// RegionLT applies the LT predicate on the "region" field.
func RegionLT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldRegion, v))
}

// RegionLTE applies the LTE predicate on the "region" field.
func RegionLTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldRegion, v))
}

// RegionContains applies the Contains predicate on the "region" field.
func RegionContains(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContains(FieldRegion, v))
}

// RegionHasPrefix applies the HasPrefix predicate on the "region" field.
func RegionHasPrefix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasPrefix(FieldRegion, v))
}

// RegionHasSuffix applies the HasSuffix predicate on the "region" field.
func RegionHasSuffix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasSuffix(FieldRegion, v))
}

// RegionEqualFold applies the EqualFold predicate on the "region" field.
func RegionEqualFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEqualFold(FieldRegion, v))
}

// RegionContainsFold applies the ContainsFold predicate on the "region" field.
func RegionContainsFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContainsFold(FieldRegion, v))
}

// ResourceEQ applies the EQ predicate on the "resource" field.
func ResourceEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldResource, v))
}

// ResourceNEQ applies the NEQ predicate on the "resource" field.
func ResourceNEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldResource, v))
}

// ResourceIn applies the In predicate on the "resource" field.
func ResourceIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldResource, vs...))
}

// ResourceNotIn applies the NotIn predicate on the "resource" field.
func ResourceNotIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldResource, vs...))
}

// ResourceGT applies the GT predicate on the "resource" field.
func ResourceGT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldResource, v))
}

// ResourceGTE applies the GTE predicate on the "resource" field.
func ResourceGTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldResource, v))
}

// ResourceLT applies the LT predicate on the "resource" field.
func ResourceLT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldResource, v))
}

// ResourceLTE applies the LTE predicate on the "resource" field.
func ResourceLTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldResource, v))
}

// ResourceContains applies the Contains predicate on the "resource" field.
func ResourceContains(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContains(FieldResource, v))
}

// ResourceHasPrefix applies the HasPrefix predicate on the "resource" field.
func ResourceHasPrefix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasPrefix(FieldResource, v))
}

// ResourceHasSuffix applies the HasSuffix predicate on the "resource" field.
func ResourceHasSuffix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasSuffix(FieldResource, v))
}

// ResourceEqualFold applies the EqualFold predicate on the "resource" field.
func ResourceEqualFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEqualFold(FieldResource, v))
}

// ResourceContainsFold applies the ContainsFold predicate on the "resource" field.
func ResourceContainsFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContainsFold(FieldResource, v))
}

// ResourceTypeEQ applies the EQ predicate on the "resource_type" field.
func ResourceTypeEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldResourceType, v))
}

// ResourceTypeNEQ applies the NEQ predicate on the "resource_type" field.
func ResourceTypeNEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldResourceType, v))
}

// ResourceTypeIn applies the In predicate on the "resource_type" field.
func ResourceTypeIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldResourceType, vs...))
}

// ResourceTypeNotIn applies the NotIn predicate on the "resource_type" field.
func ResourceTypeNotIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldResourceType, vs...))
}

// ResourceTypeGT applies the GT predicate on the "resource_type" field.
func ResourceTypeGT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldResourceType, v))
}

// ResourceTypeGTE applies the GTE predicate on the "resource_type" field.
func ResourceTypeGTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldResourceType, v))
}

// ResourceTypeLT applies the LT predicate on the "resource_type" field.
func ResourceTypeLT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldResourceType, v))
}

// ResourceTypeLTE applies the LTE predicate on the "resource_type" field.
func ResourceTypeLTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldResourceType, v))
}

// ResourceTypeContains applies the Contains predicate on the "resource_type" field.
func ResourceTypeContains(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContains(FieldResourceType, v))
}

// ResourceTypeHasPrefix applies the HasPrefix predicate on the "resource_type" field.
func ResourceTypeHasPrefix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasPrefix(FieldResourceType, v))
}

// ResourceTypeHasSuffix applies the HasSuffix predicate on the "resource_type" field.
func ResourceTypeHasSuffix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasSuffix(FieldResourceType, v))
}

// ResourceTypeEqualFold applies the EqualFold predicate on the "resource_type" field.
func ResourceTypeEqualFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEqualFold(FieldResourceType, v))
}

// ResourceTypeContainsFold applies the ContainsFold predicate on the "resource_type" field.
func ResourceTypeContainsFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContainsFold(FieldResourceType, v))
}

// VectorTypesIsNil applies the IsNil predicate on the "vector_types" field.
func VectorTypesIsNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIsNull(FieldVectorTypes))
}

// VectorTypesNotNil applies the NotNil predicate on the "vector_types" field.
func VectorTypesNotNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotNull(FieldVectorTypes))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContainsFold(FieldStatus, v))
}

// AttackIDEQ applies the EQ predicate on the "attack_id" field.
func AttackIDEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldAttackID, v))
}

// AttackIDNEQ applies the NEQ predicate on the "attack_id" field.
func AttackIDNEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldAttackID, v))
}

// AttackIDIn applies the In predicate on the "attack_id" field.
func AttackIDIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldAttackID, vs...))
}

// AttackIDNotIn applies the NotIn predicate on the "attack_id" field.
func AttackIDNotIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldAttackID, vs...))
}

// AttackIDGT applies the GT predicate on the "attack_id" field.
func AttackIDGT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldAttackID, v))
}

// AttackIDGTE applies the GTE predicate on the "attack_id" field.
func AttackIDGTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldAttackID, v))
}

// AttackIDLT applies the LT predicate on the "attack_id" field.
func AttackIDLT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldAttackID, v))
}

// AttackIDLTE applies the LTE predicate on the "attack_id" field.
func AttackIDLTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldAttackID, v))
}

// AttackIDContains applies the Contains predicate on the "attack_id" field.
func AttackIDContains(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContains(FieldAttackID, v))
}

// AttackIDHasPrefix applies the HasPrefix predicate on the "attack_id" field.
func AttackIDHasPrefix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasPrefix(FieldAttackID, v))
}

// AttackIDHasSuffix applies the HasSuffix predicate on the "attack_id" field.
func AttackIDHasSuffix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasSuffix(FieldAttackID, v))
}

// AttackIDEqualFold applies the EqualFold predicate on the "attack_id" field.
func AttackIDEqualFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEqualFold(FieldAttackID, v))
}

// AttackIDContainsFold applies the ContainsFold predicate on the "attack_id" field.
func AttackIDContainsFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContainsFold(FieldAttackID, v))
}

// AttackCountersIsNil applies the IsNil predicate on the "attack_counters" field.
func AttackCountersIsNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIsNull(FieldAttackCounters))
}

// AttackCountersNotNil applies the NotNil predicate on the "attack_counters" field.
func AttackCountersNotNil() predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotNull(FieldAttackCounters))
}

// ProjectEQ applies the EQ predicate on the "project" field.
func ProjectEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldProject, v))
}

// ProjectNEQ applies the NEQ predicate on the "project" field.
func ProjectNEQ(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldProject, v))
}

// ProjectIn applies the In predicate on the "project" field.
func ProjectIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldProject, vs...))
}

// ProjectNotIn applies the NotIn predicate on the "project" field.
func ProjectNotIn(vs ...string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldProject, vs...))
}

// ProjectGT applies the GT predicate on the "project" field.
func ProjectGT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldProject, v))
}

// ProjectGTE applies the GTE predicate on the "project" field.
func ProjectGTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldProject, v))
}

// ProjectLT applies the LT predicate on the "project" field.
func ProjectLT(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldProject, v))
}

// ProjectLTE applies the LTE predicate on the "project" field.
func ProjectLTE(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldProject, v))
}

// ProjectContains applies the Contains predicate on the "project" field.
func ProjectContains(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContains(FieldProject, v))
}

// ProjectHasPrefix applies the HasPrefix predicate on the "project" field.
func ProjectHasPrefix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasPrefix(FieldProject, v))
}

// ProjectHasSuffix applies the HasSuffix predicate on the "project" field.
func ProjectHasSuffix(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldHasSuffix(FieldProject, v))
}

// ProjectEqualFold applies the EqualFold predicate on the "project" field.
func ProjectEqualFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEqualFold(FieldProject, v))
}

// ProjectContainsFold applies the ContainsFold predicate on the "project" field.
func ProjectContainsFold(v string) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldContainsFold(FieldProject, v))
}

// DurationTimeEQ applies the EQ predicate on the "duration_time" field.
func DurationTimeEQ(v int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldEQ(FieldDurationTime, v))
}

// DurationTimeNEQ applies the NEQ predicate on the "duration_time" field.
func DurationTimeNEQ(v int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNEQ(FieldDurationTime, v))
}

// DurationTimeIn applies the In predicate on the "duration_time" field.
func DurationTimeIn(vs ...int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldIn(FieldDurationTime, vs...))
}

// DurationTimeNotIn applies the NotIn predicate on the "duration_time" field.
func DurationTimeNotIn(vs ...int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldNotIn(FieldDurationTime, vs...))
}

// DurationTimeGT applies the GT predicate on the "duration_time" field.
func DurationTimeGT(v int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGT(FieldDurationTime, v))
}

// DurationTimeGTE applies the GTE predicate on the "duration_time" field.
func DurationTimeGTE(v int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldGTE(FieldDurationTime, v))
}

// DurationTimeLT applies the LT predicate on the "duration_time" field.
func DurationTimeLT(v int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLT(FieldDurationTime, v))
}

// DurationTimeLTE applies the LTE predicate on the "duration_time" field.
func DurationTimeLTE(v int64) predicate.SkylineDos {
	return predicate.SkylineDos(sql.FieldLTE(FieldDurationTime, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.SkylineDos {
	return predicate.SkylineDos(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.SkylineDos {
	return predicate.SkylineDos(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SkylineDos) predicate.SkylineDos {
	return predicate.SkylineDos(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SkylineDos) predicate.SkylineDos {
	return predicate.SkylineDos(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SkylineDos) predicate.SkylineDos {
	return predicate.SkylineDos(sql.NotPredicates(p))
}
