// Code generated by ent, DO NOT EDIT.

package skylinedos

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the skylinedos type in the database.
	Label = "skyline_dos"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldStartTime holds the string denoting the start_time field in the database.
	FieldStartTime = "start_time"
	// FieldEndTime holds the string denoting the end_time field in the database.
	FieldEndTime = "end_time"
	// FieldRegion holds the string denoting the region field in the database.
	FieldRegion = "region"
	// FieldResource holds the string denoting the resource field in the database.
	FieldResource = "resource"
	// FieldResourceType holds the string denoting the resource_type field in the database.
	FieldResourceType = "resource_type"
	// FieldVectorTypes holds the string denoting the vector_types field in the database.
	FieldVectorTypes = "vector_types"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldAttackID holds the string denoting the attack_id field in the database.
	FieldAttackID = "attack_id"
	// FieldAttackCounters holds the string denoting the attack_counters field in the database.
	FieldAttackCounters = "attack_counters"
	// FieldProject holds the string denoting the project field in the database.
	FieldProject = "project"
	// FieldDurationTime holds the string denoting the duration_time field in the database.
	FieldDurationTime = "duration_time"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// Table holds the table name of the skylinedos in the database.
	Table = "skyline_dos"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "skyline_dos"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
)

// Columns holds all SQL columns for skylinedos fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldStartTime,
	FieldEndTime,
	FieldRegion,
	FieldResource,
	FieldResourceType,
	FieldVectorTypes,
	FieldStatus,
	FieldAttackID,
	FieldAttackCounters,
	FieldProject,
	FieldDurationTime,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the SkylineDos queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByStartTime orders the results by the start_time field.
func ByStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartTime, opts...).ToFunc()
}

// ByEndTime orders the results by the end_time field.
func ByEndTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEndTime, opts...).ToFunc()
}

// ByRegion orders the results by the region field.
func ByRegion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRegion, opts...).ToFunc()
}

// ByResource orders the results by the resource field.
func ByResource(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldResource, opts...).ToFunc()
}

// ByResourceType orders the results by the resource_type field.
func ByResourceType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldResourceType, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByAttackID orders the results by the attack_id field.
func ByAttackID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAttackID, opts...).ToFunc()
}

// ByProject orders the results by the project field.
func ByProject(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProject, opts...).ToFunc()
}

// ByDurationTime orders the results by the duration_time field.
func ByDurationTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDurationTime, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
