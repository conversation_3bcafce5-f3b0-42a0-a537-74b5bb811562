// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/predicate"
	"meta/app/ent/systemapi"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SystemApiQuery is the builder for querying SystemApi entities.
type SystemApiQuery struct {
	config
	ctx        *QueryContext
	order      []systemapi.OrderOption
	inters     []Interceptor
	predicates []predicate.SystemApi
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the SystemApiQuery builder.
func (saq *SystemApiQuery) Where(ps ...predicate.SystemApi) *SystemApiQuery {
	saq.predicates = append(saq.predicates, ps...)
	return saq
}

// Limit the number of records to be returned by this query.
func (saq *SystemApiQuery) Limit(limit int) *SystemApiQuery {
	saq.ctx.Limit = &limit
	return saq
}

// Offset to start from.
func (saq *SystemApiQuery) Offset(offset int) *SystemApiQuery {
	saq.ctx.Offset = &offset
	return saq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (saq *SystemApiQuery) Unique(unique bool) *SystemApiQuery {
	saq.ctx.Unique = &unique
	return saq
}

// Order specifies how the records should be ordered.
func (saq *SystemApiQuery) Order(o ...systemapi.OrderOption) *SystemApiQuery {
	saq.order = append(saq.order, o...)
	return saq
}

// First returns the first SystemApi entity from the query.
// Returns a *NotFoundError when no SystemApi was found.
func (saq *SystemApiQuery) First(ctx context.Context) (*SystemApi, error) {
	nodes, err := saq.Limit(1).All(setContextOp(ctx, saq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{systemapi.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (saq *SystemApiQuery) FirstX(ctx context.Context) *SystemApi {
	node, err := saq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first SystemApi ID from the query.
// Returns a *NotFoundError when no SystemApi ID was found.
func (saq *SystemApiQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = saq.Limit(1).IDs(setContextOp(ctx, saq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{systemapi.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (saq *SystemApiQuery) FirstIDX(ctx context.Context) int {
	id, err := saq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single SystemApi entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one SystemApi entity is found.
// Returns a *NotFoundError when no SystemApi entities are found.
func (saq *SystemApiQuery) Only(ctx context.Context) (*SystemApi, error) {
	nodes, err := saq.Limit(2).All(setContextOp(ctx, saq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{systemapi.Label}
	default:
		return nil, &NotSingularError{systemapi.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (saq *SystemApiQuery) OnlyX(ctx context.Context) *SystemApi {
	node, err := saq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only SystemApi ID in the query.
// Returns a *NotSingularError when more than one SystemApi ID is found.
// Returns a *NotFoundError when no entities are found.
func (saq *SystemApiQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = saq.Limit(2).IDs(setContextOp(ctx, saq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{systemapi.Label}
	default:
		err = &NotSingularError{systemapi.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (saq *SystemApiQuery) OnlyIDX(ctx context.Context) int {
	id, err := saq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of SystemApis.
func (saq *SystemApiQuery) All(ctx context.Context) ([]*SystemApi, error) {
	ctx = setContextOp(ctx, saq.ctx, "All")
	if err := saq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*SystemApi, *SystemApiQuery]()
	return withInterceptors[[]*SystemApi](ctx, saq, qr, saq.inters)
}

// AllX is like All, but panics if an error occurs.
func (saq *SystemApiQuery) AllX(ctx context.Context) []*SystemApi {
	nodes, err := saq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of SystemApi IDs.
func (saq *SystemApiQuery) IDs(ctx context.Context) (ids []int, err error) {
	if saq.ctx.Unique == nil && saq.path != nil {
		saq.Unique(true)
	}
	ctx = setContextOp(ctx, saq.ctx, "IDs")
	if err = saq.Select(systemapi.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (saq *SystemApiQuery) IDsX(ctx context.Context) []int {
	ids, err := saq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (saq *SystemApiQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, saq.ctx, "Count")
	if err := saq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, saq, querierCount[*SystemApiQuery](), saq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (saq *SystemApiQuery) CountX(ctx context.Context) int {
	count, err := saq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (saq *SystemApiQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, saq.ctx, "Exist")
	switch _, err := saq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (saq *SystemApiQuery) ExistX(ctx context.Context) bool {
	exist, err := saq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the SystemApiQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (saq *SystemApiQuery) Clone() *SystemApiQuery {
	if saq == nil {
		return nil
	}
	return &SystemApiQuery{
		config:     saq.config,
		ctx:        saq.ctx.Clone(),
		order:      append([]systemapi.OrderOption{}, saq.order...),
		inters:     append([]Interceptor{}, saq.inters...),
		predicates: append([]predicate.SystemApi{}, saq.predicates...),
		// clone intermediate query.
		sql:  saq.sql.Clone(),
		path: saq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.SystemApi.Query().
//		GroupBy(systemapi.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (saq *SystemApiQuery) GroupBy(field string, fields ...string) *SystemApiGroupBy {
	saq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &SystemApiGroupBy{build: saq}
	grbuild.flds = &saq.ctx.Fields
	grbuild.label = systemapi.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.SystemApi.Query().
//		Select(systemapi.FieldCreatedAt).
//		Scan(ctx, &v)
func (saq *SystemApiQuery) Select(fields ...string) *SystemApiSelect {
	saq.ctx.Fields = append(saq.ctx.Fields, fields...)
	sbuild := &SystemApiSelect{SystemApiQuery: saq}
	sbuild.label = systemapi.Label
	sbuild.flds, sbuild.scan = &saq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a SystemApiSelect configured with the given aggregations.
func (saq *SystemApiQuery) Aggregate(fns ...AggregateFunc) *SystemApiSelect {
	return saq.Select().Aggregate(fns...)
}

func (saq *SystemApiQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range saq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, saq); err != nil {
				return err
			}
		}
	}
	for _, f := range saq.ctx.Fields {
		if !systemapi.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if saq.path != nil {
		prev, err := saq.path(ctx)
		if err != nil {
			return err
		}
		saq.sql = prev
	}
	if systemapi.Policy == nil {
		return errors.New("ent: uninitialized systemapi.Policy (forgotten import ent/runtime?)")
	}
	if err := systemapi.Policy.EvalQuery(ctx, saq); err != nil {
		return err
	}
	return nil
}

func (saq *SystemApiQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*SystemApi, error) {
	var (
		nodes = []*SystemApi{}
		_spec = saq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*SystemApi).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &SystemApi{config: saq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, saq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (saq *SystemApiQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := saq.querySpec()
	_spec.Node.Columns = saq.ctx.Fields
	if len(saq.ctx.Fields) > 0 {
		_spec.Unique = saq.ctx.Unique != nil && *saq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, saq.driver, _spec)
}

func (saq *SystemApiQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(systemapi.Table, systemapi.Columns, sqlgraph.NewFieldSpec(systemapi.FieldID, field.TypeInt))
	_spec.From = saq.sql
	if unique := saq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if saq.path != nil {
		_spec.Unique = true
	}
	if fields := saq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, systemapi.FieldID)
		for i := range fields {
			if fields[i] != systemapi.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := saq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := saq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := saq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := saq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (saq *SystemApiQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(saq.driver.Dialect())
	t1 := builder.Table(systemapi.Table)
	columns := saq.ctx.Fields
	if len(columns) == 0 {
		columns = systemapi.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if saq.sql != nil {
		selector = saq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if saq.ctx.Unique != nil && *saq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range saq.predicates {
		p(selector)
	}
	for _, p := range saq.order {
		p(selector)
	}
	if offset := saq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := saq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// SystemApiGroupBy is the group-by builder for SystemApi entities.
type SystemApiGroupBy struct {
	selector
	build *SystemApiQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (sagb *SystemApiGroupBy) Aggregate(fns ...AggregateFunc) *SystemApiGroupBy {
	sagb.fns = append(sagb.fns, fns...)
	return sagb
}

// Scan applies the selector query and scans the result into the given value.
func (sagb *SystemApiGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sagb.build.ctx, "GroupBy")
	if err := sagb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SystemApiQuery, *SystemApiGroupBy](ctx, sagb.build, sagb, sagb.build.inters, v)
}

func (sagb *SystemApiGroupBy) sqlScan(ctx context.Context, root *SystemApiQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(sagb.fns))
	for _, fn := range sagb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*sagb.flds)+len(sagb.fns))
		for _, f := range *sagb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*sagb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sagb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// SystemApiSelect is the builder for selecting fields of SystemApi entities.
type SystemApiSelect struct {
	*SystemApiQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (sas *SystemApiSelect) Aggregate(fns ...AggregateFunc) *SystemApiSelect {
	sas.fns = append(sas.fns, fns...)
	return sas
}

// Scan applies the selector query and scans the result into the given value.
func (sas *SystemApiSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sas.ctx, "Select")
	if err := sas.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SystemApiQuery, *SystemApiSelect](ctx, sas.SystemApiQuery, sas, sas.inters, v)
}

func (sas *SystemApiSelect) sqlScan(ctx context.Context, root *SystemApiQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(sas.fns))
	for _, fn := range sas.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*sas.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sas.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
