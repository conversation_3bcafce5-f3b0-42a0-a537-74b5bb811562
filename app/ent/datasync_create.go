// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/datasync"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DataSyncCreate is the builder for creating a DataSync entity.
type DataSyncCreate struct {
	config
	mutation *DataSyncMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (dsc *DataSyncCreate) SetCreatedAt(t time.Time) *DataSyncCreate {
	dsc.mutation.SetCreatedAt(t)
	return dsc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (dsc *DataSyncCreate) SetNillableCreatedAt(t *time.Time) *DataSyncCreate {
	if t != nil {
		dsc.SetCreatedAt(*t)
	}
	return dsc
}

// SetUpdatedAt sets the "updated_at" field.
func (dsc *DataSyncCreate) SetUpdatedAt(t time.Time) *DataSyncCreate {
	dsc.mutation.SetUpdatedAt(t)
	return dsc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (dsc *DataSyncCreate) SetNillableUpdatedAt(t *time.Time) *DataSyncCreate {
	if t != nil {
		dsc.SetUpdatedAt(*t)
	}
	return dsc
}

// SetRemark sets the "remark" field.
func (dsc *DataSyncCreate) SetRemark(s string) *DataSyncCreate {
	dsc.mutation.SetRemark(s)
	return dsc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (dsc *DataSyncCreate) SetNillableRemark(s *string) *DataSyncCreate {
	if s != nil {
		dsc.SetRemark(*s)
	}
	return dsc
}

// SetPreDataList sets the "pre_data_list" field.
func (dsc *DataSyncCreate) SetPreDataList(s *[]string) *DataSyncCreate {
	dsc.mutation.SetPreDataList(s)
	return dsc
}

// SetDataList sets the "data_list" field.
func (dsc *DataSyncCreate) SetDataList(s *[]string) *DataSyncCreate {
	dsc.mutation.SetDataList(s)
	return dsc
}

// SetDataType sets the "data_type" field.
func (dsc *DataSyncCreate) SetDataType(s string) *DataSyncCreate {
	dsc.mutation.SetDataType(s)
	return dsc
}

// SetType sets the "type" field.
func (dsc *DataSyncCreate) SetType(s string) *DataSyncCreate {
	dsc.mutation.SetType(s)
	return dsc
}

// Mutation returns the DataSyncMutation object of the builder.
func (dsc *DataSyncCreate) Mutation() *DataSyncMutation {
	return dsc.mutation
}

// Save creates the DataSync in the database.
func (dsc *DataSyncCreate) Save(ctx context.Context) (*DataSync, error) {
	if err := dsc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, dsc.sqlSave, dsc.mutation, dsc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (dsc *DataSyncCreate) SaveX(ctx context.Context) *DataSync {
	v, err := dsc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dsc *DataSyncCreate) Exec(ctx context.Context) error {
	_, err := dsc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dsc *DataSyncCreate) ExecX(ctx context.Context) {
	if err := dsc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (dsc *DataSyncCreate) defaults() error {
	if _, ok := dsc.mutation.CreatedAt(); !ok {
		if datasync.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized datasync.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := datasync.DefaultCreatedAt()
		dsc.mutation.SetCreatedAt(v)
	}
	if _, ok := dsc.mutation.UpdatedAt(); !ok {
		if datasync.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized datasync.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := datasync.DefaultUpdatedAt()
		dsc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (dsc *DataSyncCreate) check() error {
	if _, ok := dsc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "DataSync.created_at"`)}
	}
	if _, ok := dsc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "DataSync.updated_at"`)}
	}
	if v, ok := dsc.mutation.Remark(); ok {
		if err := datasync.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "DataSync.remark": %w`, err)}
		}
	}
	if _, ok := dsc.mutation.DataType(); !ok {
		return &ValidationError{Name: "data_type", err: errors.New(`ent: missing required field "DataSync.data_type"`)}
	}
	if _, ok := dsc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "DataSync.type"`)}
	}
	return nil
}

func (dsc *DataSyncCreate) sqlSave(ctx context.Context) (*DataSync, error) {
	if err := dsc.check(); err != nil {
		return nil, err
	}
	_node, _spec := dsc.createSpec()
	if err := sqlgraph.CreateNode(ctx, dsc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	dsc.mutation.id = &_node.ID
	dsc.mutation.done = true
	return _node, nil
}

func (dsc *DataSyncCreate) createSpec() (*DataSync, *sqlgraph.CreateSpec) {
	var (
		_node = &DataSync{config: dsc.config}
		_spec = sqlgraph.NewCreateSpec(datasync.Table, sqlgraph.NewFieldSpec(datasync.FieldID, field.TypeInt))
	)
	if value, ok := dsc.mutation.CreatedAt(); ok {
		_spec.SetField(datasync.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := dsc.mutation.UpdatedAt(); ok {
		_spec.SetField(datasync.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := dsc.mutation.Remark(); ok {
		_spec.SetField(datasync.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := dsc.mutation.PreDataList(); ok {
		_spec.SetField(datasync.FieldPreDataList, field.TypeJSON, value)
		_node.PreDataList = value
	}
	if value, ok := dsc.mutation.DataList(); ok {
		_spec.SetField(datasync.FieldDataList, field.TypeJSON, value)
		_node.DataList = value
	}
	if value, ok := dsc.mutation.DataType(); ok {
		_spec.SetField(datasync.FieldDataType, field.TypeString, value)
		_node.DataType = value
	}
	if value, ok := dsc.mutation.GetType(); ok {
		_spec.SetField(datasync.FieldType, field.TypeString, value)
		_node.Type = value
	}
	return _node, _spec
}

// DataSyncCreateBulk is the builder for creating many DataSync entities in bulk.
type DataSyncCreateBulk struct {
	config
	err      error
	builders []*DataSyncCreate
}

// Save creates the DataSync entities in the database.
func (dscb *DataSyncCreateBulk) Save(ctx context.Context) ([]*DataSync, error) {
	if dscb.err != nil {
		return nil, dscb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(dscb.builders))
	nodes := make([]*DataSync, len(dscb.builders))
	mutators := make([]Mutator, len(dscb.builders))
	for i := range dscb.builders {
		func(i int, root context.Context) {
			builder := dscb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DataSyncMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, dscb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, dscb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, dscb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (dscb *DataSyncCreateBulk) SaveX(ctx context.Context) []*DataSync {
	v, err := dscb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (dscb *DataSyncCreateBulk) Exec(ctx context.Context) error {
	_, err := dscb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (dscb *DataSyncCreateBulk) ExecX(ctx context.Context) {
	if err := dscb.Exec(ctx); err != nil {
		panic(err)
	}
}
