// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/predicate"
	"meta/app/ent/socgroupticket"
	"meta/app/ent/tenant"
	"meta/app/ent/user"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SocGroupTicketQuery is the builder for querying SocGroupTicket entities.
type SocGroupTicketQuery struct {
	config
	ctx        *QueryContext
	order      []socgroupticket.OrderOption
	inters     []Interceptor
	predicates []predicate.SocGroupTicket
	withTenant *TenantQuery
	withUser   *UserQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the SocGroupTicketQuery builder.
func (sgtq *SocGroupTicketQuery) Where(ps ...predicate.SocGroupTicket) *SocGroupTicketQuery {
	sgtq.predicates = append(sgtq.predicates, ps...)
	return sgtq
}

// Limit the number of records to be returned by this query.
func (sgtq *SocGroupTicketQuery) Limit(limit int) *SocGroupTicketQuery {
	sgtq.ctx.Limit = &limit
	return sgtq
}

// Offset to start from.
func (sgtq *SocGroupTicketQuery) Offset(offset int) *SocGroupTicketQuery {
	sgtq.ctx.Offset = &offset
	return sgtq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (sgtq *SocGroupTicketQuery) Unique(unique bool) *SocGroupTicketQuery {
	sgtq.ctx.Unique = &unique
	return sgtq
}

// Order specifies how the records should be ordered.
func (sgtq *SocGroupTicketQuery) Order(o ...socgroupticket.OrderOption) *SocGroupTicketQuery {
	sgtq.order = append(sgtq.order, o...)
	return sgtq
}

// QueryTenant chains the current query on the "tenant" edge.
func (sgtq *SocGroupTicketQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: sgtq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sgtq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sgtq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(socgroupticket.Table, socgroupticket.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, socgroupticket.TenantTable, socgroupticket.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(sgtq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUser chains the current query on the "user" edge.
func (sgtq *SocGroupTicketQuery) QueryUser() *UserQuery {
	query := (&UserClient{config: sgtq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sgtq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sgtq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(socgroupticket.Table, socgroupticket.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, socgroupticket.UserTable, socgroupticket.UserColumn),
		)
		fromU = sqlgraph.SetNeighbors(sgtq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first SocGroupTicket entity from the query.
// Returns a *NotFoundError when no SocGroupTicket was found.
func (sgtq *SocGroupTicketQuery) First(ctx context.Context) (*SocGroupTicket, error) {
	nodes, err := sgtq.Limit(1).All(setContextOp(ctx, sgtq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{socgroupticket.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (sgtq *SocGroupTicketQuery) FirstX(ctx context.Context) *SocGroupTicket {
	node, err := sgtq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first SocGroupTicket ID from the query.
// Returns a *NotFoundError when no SocGroupTicket ID was found.
func (sgtq *SocGroupTicketQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = sgtq.Limit(1).IDs(setContextOp(ctx, sgtq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{socgroupticket.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (sgtq *SocGroupTicketQuery) FirstIDX(ctx context.Context) int {
	id, err := sgtq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single SocGroupTicket entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one SocGroupTicket entity is found.
// Returns a *NotFoundError when no SocGroupTicket entities are found.
func (sgtq *SocGroupTicketQuery) Only(ctx context.Context) (*SocGroupTicket, error) {
	nodes, err := sgtq.Limit(2).All(setContextOp(ctx, sgtq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{socgroupticket.Label}
	default:
		return nil, &NotSingularError{socgroupticket.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (sgtq *SocGroupTicketQuery) OnlyX(ctx context.Context) *SocGroupTicket {
	node, err := sgtq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only SocGroupTicket ID in the query.
// Returns a *NotSingularError when more than one SocGroupTicket ID is found.
// Returns a *NotFoundError when no entities are found.
func (sgtq *SocGroupTicketQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = sgtq.Limit(2).IDs(setContextOp(ctx, sgtq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{socgroupticket.Label}
	default:
		err = &NotSingularError{socgroupticket.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (sgtq *SocGroupTicketQuery) OnlyIDX(ctx context.Context) int {
	id, err := sgtq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of SocGroupTickets.
func (sgtq *SocGroupTicketQuery) All(ctx context.Context) ([]*SocGroupTicket, error) {
	ctx = setContextOp(ctx, sgtq.ctx, "All")
	if err := sgtq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*SocGroupTicket, *SocGroupTicketQuery]()
	return withInterceptors[[]*SocGroupTicket](ctx, sgtq, qr, sgtq.inters)
}

// AllX is like All, but panics if an error occurs.
func (sgtq *SocGroupTicketQuery) AllX(ctx context.Context) []*SocGroupTicket {
	nodes, err := sgtq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of SocGroupTicket IDs.
func (sgtq *SocGroupTicketQuery) IDs(ctx context.Context) (ids []int, err error) {
	if sgtq.ctx.Unique == nil && sgtq.path != nil {
		sgtq.Unique(true)
	}
	ctx = setContextOp(ctx, sgtq.ctx, "IDs")
	if err = sgtq.Select(socgroupticket.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (sgtq *SocGroupTicketQuery) IDsX(ctx context.Context) []int {
	ids, err := sgtq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (sgtq *SocGroupTicketQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, sgtq.ctx, "Count")
	if err := sgtq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, sgtq, querierCount[*SocGroupTicketQuery](), sgtq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (sgtq *SocGroupTicketQuery) CountX(ctx context.Context) int {
	count, err := sgtq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (sgtq *SocGroupTicketQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, sgtq.ctx, "Exist")
	switch _, err := sgtq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (sgtq *SocGroupTicketQuery) ExistX(ctx context.Context) bool {
	exist, err := sgtq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the SocGroupTicketQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (sgtq *SocGroupTicketQuery) Clone() *SocGroupTicketQuery {
	if sgtq == nil {
		return nil
	}
	return &SocGroupTicketQuery{
		config:     sgtq.config,
		ctx:        sgtq.ctx.Clone(),
		order:      append([]socgroupticket.OrderOption{}, sgtq.order...),
		inters:     append([]Interceptor{}, sgtq.inters...),
		predicates: append([]predicate.SocGroupTicket{}, sgtq.predicates...),
		withTenant: sgtq.withTenant.Clone(),
		withUser:   sgtq.withUser.Clone(),
		// clone intermediate query.
		sql:  sgtq.sql.Clone(),
		path: sgtq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (sgtq *SocGroupTicketQuery) WithTenant(opts ...func(*TenantQuery)) *SocGroupTicketQuery {
	query := (&TenantClient{config: sgtq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sgtq.withTenant = query
	return sgtq
}

// WithUser tells the query-builder to eager-load the nodes that are connected to
// the "user" edge. The optional arguments are used to configure the query builder of the edge.
func (sgtq *SocGroupTicketQuery) WithUser(opts ...func(*UserQuery)) *SocGroupTicketQuery {
	query := (&UserClient{config: sgtq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sgtq.withUser = query
	return sgtq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.SocGroupTicket.Query().
//		GroupBy(socgroupticket.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (sgtq *SocGroupTicketQuery) GroupBy(field string, fields ...string) *SocGroupTicketGroupBy {
	sgtq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &SocGroupTicketGroupBy{build: sgtq}
	grbuild.flds = &sgtq.ctx.Fields
	grbuild.label = socgroupticket.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.SocGroupTicket.Query().
//		Select(socgroupticket.FieldCreatedAt).
//		Scan(ctx, &v)
func (sgtq *SocGroupTicketQuery) Select(fields ...string) *SocGroupTicketSelect {
	sgtq.ctx.Fields = append(sgtq.ctx.Fields, fields...)
	sbuild := &SocGroupTicketSelect{SocGroupTicketQuery: sgtq}
	sbuild.label = socgroupticket.Label
	sbuild.flds, sbuild.scan = &sgtq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a SocGroupTicketSelect configured with the given aggregations.
func (sgtq *SocGroupTicketQuery) Aggregate(fns ...AggregateFunc) *SocGroupTicketSelect {
	return sgtq.Select().Aggregate(fns...)
}

func (sgtq *SocGroupTicketQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range sgtq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, sgtq); err != nil {
				return err
			}
		}
	}
	for _, f := range sgtq.ctx.Fields {
		if !socgroupticket.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if sgtq.path != nil {
		prev, err := sgtq.path(ctx)
		if err != nil {
			return err
		}
		sgtq.sql = prev
	}
	if socgroupticket.Policy == nil {
		return errors.New("ent: uninitialized socgroupticket.Policy (forgotten import ent/runtime?)")
	}
	if err := socgroupticket.Policy.EvalQuery(ctx, sgtq); err != nil {
		return err
	}
	return nil
}

func (sgtq *SocGroupTicketQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*SocGroupTicket, error) {
	var (
		nodes       = []*SocGroupTicket{}
		_spec       = sgtq.querySpec()
		loadedTypes = [2]bool{
			sgtq.withTenant != nil,
			sgtq.withUser != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*SocGroupTicket).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &SocGroupTicket{config: sgtq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, sgtq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := sgtq.withTenant; query != nil {
		if err := sgtq.loadTenant(ctx, query, nodes, nil,
			func(n *SocGroupTicket, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := sgtq.withUser; query != nil {
		if err := sgtq.loadUser(ctx, query, nodes, nil,
			func(n *SocGroupTicket, e *User) { n.Edges.User = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (sgtq *SocGroupTicketQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*SocGroupTicket, init func(*SocGroupTicket), assign func(*SocGroupTicket, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*SocGroupTicket)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (sgtq *SocGroupTicketQuery) loadUser(ctx context.Context, query *UserQuery, nodes []*SocGroupTicket, init func(*SocGroupTicket), assign func(*SocGroupTicket, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*SocGroupTicket)
	for i := range nodes {
		if nodes[i].CreateUserID == nil {
			continue
		}
		fk := *nodes[i].CreateUserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "create_user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (sgtq *SocGroupTicketQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := sgtq.querySpec()
	_spec.Node.Columns = sgtq.ctx.Fields
	if len(sgtq.ctx.Fields) > 0 {
		_spec.Unique = sgtq.ctx.Unique != nil && *sgtq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, sgtq.driver, _spec)
}

func (sgtq *SocGroupTicketQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(socgroupticket.Table, socgroupticket.Columns, sqlgraph.NewFieldSpec(socgroupticket.FieldID, field.TypeInt))
	_spec.From = sgtq.sql
	if unique := sgtq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if sgtq.path != nil {
		_spec.Unique = true
	}
	if fields := sgtq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, socgroupticket.FieldID)
		for i := range fields {
			if fields[i] != socgroupticket.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if sgtq.withTenant != nil {
			_spec.Node.AddColumnOnce(socgroupticket.FieldTenantID)
		}
		if sgtq.withUser != nil {
			_spec.Node.AddColumnOnce(socgroupticket.FieldCreateUserID)
		}
	}
	if ps := sgtq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := sgtq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := sgtq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := sgtq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (sgtq *SocGroupTicketQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(sgtq.driver.Dialect())
	t1 := builder.Table(socgroupticket.Table)
	columns := sgtq.ctx.Fields
	if len(columns) == 0 {
		columns = socgroupticket.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if sgtq.sql != nil {
		selector = sgtq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if sgtq.ctx.Unique != nil && *sgtq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range sgtq.predicates {
		p(selector)
	}
	for _, p := range sgtq.order {
		p(selector)
	}
	if offset := sgtq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := sgtq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// SocGroupTicketGroupBy is the group-by builder for SocGroupTicket entities.
type SocGroupTicketGroupBy struct {
	selector
	build *SocGroupTicketQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (sgtgb *SocGroupTicketGroupBy) Aggregate(fns ...AggregateFunc) *SocGroupTicketGroupBy {
	sgtgb.fns = append(sgtgb.fns, fns...)
	return sgtgb
}

// Scan applies the selector query and scans the result into the given value.
func (sgtgb *SocGroupTicketGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sgtgb.build.ctx, "GroupBy")
	if err := sgtgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SocGroupTicketQuery, *SocGroupTicketGroupBy](ctx, sgtgb.build, sgtgb, sgtgb.build.inters, v)
}

func (sgtgb *SocGroupTicketGroupBy) sqlScan(ctx context.Context, root *SocGroupTicketQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(sgtgb.fns))
	for _, fn := range sgtgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*sgtgb.flds)+len(sgtgb.fns))
		for _, f := range *sgtgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*sgtgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sgtgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// SocGroupTicketSelect is the builder for selecting fields of SocGroupTicket entities.
type SocGroupTicketSelect struct {
	*SocGroupTicketQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (sgts *SocGroupTicketSelect) Aggregate(fns ...AggregateFunc) *SocGroupTicketSelect {
	sgts.fns = append(sgts.fns, fns...)
	return sgts
}

// Scan applies the selector query and scans the result into the given value.
func (sgts *SocGroupTicketSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sgts.ctx, "Select")
	if err := sgts.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SocGroupTicketQuery, *SocGroupTicketSelect](ctx, sgts.SocGroupTicketQuery, sgts, sgts.inters, v)
}

func (sgts *SocGroupTicketSelect) sqlScan(ctx context.Context, root *SocGroupTicketQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(sgts.fns))
	for _, fn := range sgts.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*sgts.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sgts.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
