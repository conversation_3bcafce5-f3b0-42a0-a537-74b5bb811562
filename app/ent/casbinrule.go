// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/casbinrule"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// CasbinRule is the model entity for the CasbinRule schema.
type CasbinRule struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// Type holds the value of the "type" field.
	Type string `json:"type,omitempty"`
	// Sub holds the value of the "sub" field.
	Sub string `json:"sub,omitempty"`
	// Dom holds the value of the "dom" field.
	Dom string `json:"dom,omitempty"`
	// Obj holds the value of the "obj" field.
	Obj string `json:"obj,omitempty"`
	// Act holds the value of the "act" field.
	Act string `json:"act,omitempty"`
	// V4 holds the value of the "v4" field.
	V4 string `json:"-"`
	// V5 holds the value of the "v5" field.
	V5           string `json:"-"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CasbinRule) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case casbinrule.FieldID:
			values[i] = new(sql.NullInt64)
		case casbinrule.FieldType, casbinrule.FieldSub, casbinrule.FieldDom, casbinrule.FieldObj, casbinrule.FieldAct, casbinrule.FieldV4, casbinrule.FieldV5:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CasbinRule fields.
func (cr *CasbinRule) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case casbinrule.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			cr.ID = int(value.Int64)
		case casbinrule.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				cr.Type = value.String
			}
		case casbinrule.FieldSub:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field sub", values[i])
			} else if value.Valid {
				cr.Sub = value.String
			}
		case casbinrule.FieldDom:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field dom", values[i])
			} else if value.Valid {
				cr.Dom = value.String
			}
		case casbinrule.FieldObj:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field obj", values[i])
			} else if value.Valid {
				cr.Obj = value.String
			}
		case casbinrule.FieldAct:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field act", values[i])
			} else if value.Valid {
				cr.Act = value.String
			}
		case casbinrule.FieldV4:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field v4", values[i])
			} else if value.Valid {
				cr.V4 = value.String
			}
		case casbinrule.FieldV5:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field v5", values[i])
			} else if value.Valid {
				cr.V5 = value.String
			}
		default:
			cr.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CasbinRule.
// This includes values selected through modifiers, order, etc.
func (cr *CasbinRule) Value(name string) (ent.Value, error) {
	return cr.selectValues.Get(name)
}

// Update returns a builder for updating this CasbinRule.
// Note that you need to call CasbinRule.Unwrap() before calling this method if this CasbinRule
// was returned from a transaction, and the transaction was committed or rolled back.
func (cr *CasbinRule) Update() *CasbinRuleUpdateOne {
	return NewCasbinRuleClient(cr.config).UpdateOne(cr)
}

// Unwrap unwraps the CasbinRule entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cr *CasbinRule) Unwrap() *CasbinRule {
	_tx, ok := cr.config.driver.(*txDriver)
	if !ok {
		panic("ent: CasbinRule is not a transactional entity")
	}
	cr.config.driver = _tx.drv
	return cr
}

// String implements the fmt.Stringer.
func (cr *CasbinRule) String() string {
	var builder strings.Builder
	builder.WriteString("CasbinRule(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cr.ID))
	builder.WriteString("type=")
	builder.WriteString(cr.Type)
	builder.WriteString(", ")
	builder.WriteString("sub=")
	builder.WriteString(cr.Sub)
	builder.WriteString(", ")
	builder.WriteString("dom=")
	builder.WriteString(cr.Dom)
	builder.WriteString(", ")
	builder.WriteString("obj=")
	builder.WriteString(cr.Obj)
	builder.WriteString(", ")
	builder.WriteString("act=")
	builder.WriteString(cr.Act)
	builder.WriteString(", ")
	builder.WriteString("v4=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("v5=<sensitive>")
	builder.WriteByte(')')
	return builder.String()
}

// CasbinRules is a parsable slice of CasbinRule.
type CasbinRules []*CasbinRule
