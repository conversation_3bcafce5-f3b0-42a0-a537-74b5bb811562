// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixStrategyQuery is the builder for querying MatrixStrategy entities.
type MatrixStrategyQuery struct {
	config
	ctx                      *QueryContext
	order                    []matrixstrategy.OrderOption
	inters                   []Interceptor
	predicates               []predicate.MatrixStrategy
	withMatrixStrategyAlerts *MatrixSpectrumAlertQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the MatrixStrategyQuery builder.
func (msq *MatrixStrategyQuery) Where(ps ...predicate.MatrixStrategy) *MatrixStrategyQuery {
	msq.predicates = append(msq.predicates, ps...)
	return msq
}

// Limit the number of records to be returned by this query.
func (msq *MatrixStrategyQuery) Limit(limit int) *MatrixStrategyQuery {
	msq.ctx.Limit = &limit
	return msq
}

// Offset to start from.
func (msq *MatrixStrategyQuery) Offset(offset int) *MatrixStrategyQuery {
	msq.ctx.Offset = &offset
	return msq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (msq *MatrixStrategyQuery) Unique(unique bool) *MatrixStrategyQuery {
	msq.ctx.Unique = &unique
	return msq
}

// Order specifies how the records should be ordered.
func (msq *MatrixStrategyQuery) Order(o ...matrixstrategy.OrderOption) *MatrixStrategyQuery {
	msq.order = append(msq.order, o...)
	return msq
}

// QueryMatrixStrategyAlerts chains the current query on the "matrix_strategy_alerts" edge.
func (msq *MatrixStrategyQuery) QueryMatrixStrategyAlerts() *MatrixSpectrumAlertQuery {
	query := (&MatrixSpectrumAlertClient{config: msq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := msq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := msq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixstrategy.Table, matrixstrategy.FieldID, selector),
			sqlgraph.To(matrixspectrumalert.Table, matrixspectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, matrixstrategy.MatrixStrategyAlertsTable, matrixstrategy.MatrixStrategyAlertsColumn),
		)
		fromU = sqlgraph.SetNeighbors(msq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first MatrixStrategy entity from the query.
// Returns a *NotFoundError when no MatrixStrategy was found.
func (msq *MatrixStrategyQuery) First(ctx context.Context) (*MatrixStrategy, error) {
	nodes, err := msq.Limit(1).All(setContextOp(ctx, msq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{matrixstrategy.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (msq *MatrixStrategyQuery) FirstX(ctx context.Context) *MatrixStrategy {
	node, err := msq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first MatrixStrategy ID from the query.
// Returns a *NotFoundError when no MatrixStrategy ID was found.
func (msq *MatrixStrategyQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = msq.Limit(1).IDs(setContextOp(ctx, msq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{matrixstrategy.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (msq *MatrixStrategyQuery) FirstIDX(ctx context.Context) int {
	id, err := msq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single MatrixStrategy entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one MatrixStrategy entity is found.
// Returns a *NotFoundError when no MatrixStrategy entities are found.
func (msq *MatrixStrategyQuery) Only(ctx context.Context) (*MatrixStrategy, error) {
	nodes, err := msq.Limit(2).All(setContextOp(ctx, msq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{matrixstrategy.Label}
	default:
		return nil, &NotSingularError{matrixstrategy.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (msq *MatrixStrategyQuery) OnlyX(ctx context.Context) *MatrixStrategy {
	node, err := msq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only MatrixStrategy ID in the query.
// Returns a *NotSingularError when more than one MatrixStrategy ID is found.
// Returns a *NotFoundError when no entities are found.
func (msq *MatrixStrategyQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = msq.Limit(2).IDs(setContextOp(ctx, msq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{matrixstrategy.Label}
	default:
		err = &NotSingularError{matrixstrategy.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (msq *MatrixStrategyQuery) OnlyIDX(ctx context.Context) int {
	id, err := msq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of MatrixStrategies.
func (msq *MatrixStrategyQuery) All(ctx context.Context) ([]*MatrixStrategy, error) {
	ctx = setContextOp(ctx, msq.ctx, "All")
	if err := msq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*MatrixStrategy, *MatrixStrategyQuery]()
	return withInterceptors[[]*MatrixStrategy](ctx, msq, qr, msq.inters)
}

// AllX is like All, but panics if an error occurs.
func (msq *MatrixStrategyQuery) AllX(ctx context.Context) []*MatrixStrategy {
	nodes, err := msq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of MatrixStrategy IDs.
func (msq *MatrixStrategyQuery) IDs(ctx context.Context) (ids []int, err error) {
	if msq.ctx.Unique == nil && msq.path != nil {
		msq.Unique(true)
	}
	ctx = setContextOp(ctx, msq.ctx, "IDs")
	if err = msq.Select(matrixstrategy.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (msq *MatrixStrategyQuery) IDsX(ctx context.Context) []int {
	ids, err := msq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (msq *MatrixStrategyQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, msq.ctx, "Count")
	if err := msq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, msq, querierCount[*MatrixStrategyQuery](), msq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (msq *MatrixStrategyQuery) CountX(ctx context.Context) int {
	count, err := msq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (msq *MatrixStrategyQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, msq.ctx, "Exist")
	switch _, err := msq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (msq *MatrixStrategyQuery) ExistX(ctx context.Context) bool {
	exist, err := msq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the MatrixStrategyQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (msq *MatrixStrategyQuery) Clone() *MatrixStrategyQuery {
	if msq == nil {
		return nil
	}
	return &MatrixStrategyQuery{
		config:                   msq.config,
		ctx:                      msq.ctx.Clone(),
		order:                    append([]matrixstrategy.OrderOption{}, msq.order...),
		inters:                   append([]Interceptor{}, msq.inters...),
		predicates:               append([]predicate.MatrixStrategy{}, msq.predicates...),
		withMatrixStrategyAlerts: msq.withMatrixStrategyAlerts.Clone(),
		// clone intermediate query.
		sql:  msq.sql.Clone(),
		path: msq.path,
	}
}

// WithMatrixStrategyAlerts tells the query-builder to eager-load the nodes that are connected to
// the "matrix_strategy_alerts" edge. The optional arguments are used to configure the query builder of the edge.
func (msq *MatrixStrategyQuery) WithMatrixStrategyAlerts(opts ...func(*MatrixSpectrumAlertQuery)) *MatrixStrategyQuery {
	query := (&MatrixSpectrumAlertClient{config: msq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	msq.withMatrixStrategyAlerts = query
	return msq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.MatrixStrategy.Query().
//		GroupBy(matrixstrategy.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (msq *MatrixStrategyQuery) GroupBy(field string, fields ...string) *MatrixStrategyGroupBy {
	msq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &MatrixStrategyGroupBy{build: msq}
	grbuild.flds = &msq.ctx.Fields
	grbuild.label = matrixstrategy.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.MatrixStrategy.Query().
//		Select(matrixstrategy.FieldCreatedAt).
//		Scan(ctx, &v)
func (msq *MatrixStrategyQuery) Select(fields ...string) *MatrixStrategySelect {
	msq.ctx.Fields = append(msq.ctx.Fields, fields...)
	sbuild := &MatrixStrategySelect{MatrixStrategyQuery: msq}
	sbuild.label = matrixstrategy.Label
	sbuild.flds, sbuild.scan = &msq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a MatrixStrategySelect configured with the given aggregations.
func (msq *MatrixStrategyQuery) Aggregate(fns ...AggregateFunc) *MatrixStrategySelect {
	return msq.Select().Aggregate(fns...)
}

func (msq *MatrixStrategyQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range msq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, msq); err != nil {
				return err
			}
		}
	}
	for _, f := range msq.ctx.Fields {
		if !matrixstrategy.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if msq.path != nil {
		prev, err := msq.path(ctx)
		if err != nil {
			return err
		}
		msq.sql = prev
	}
	if matrixstrategy.Policy == nil {
		return errors.New("ent: uninitialized matrixstrategy.Policy (forgotten import ent/runtime?)")
	}
	if err := matrixstrategy.Policy.EvalQuery(ctx, msq); err != nil {
		return err
	}
	return nil
}

func (msq *MatrixStrategyQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*MatrixStrategy, error) {
	var (
		nodes       = []*MatrixStrategy{}
		_spec       = msq.querySpec()
		loadedTypes = [1]bool{
			msq.withMatrixStrategyAlerts != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*MatrixStrategy).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &MatrixStrategy{config: msq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, msq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := msq.withMatrixStrategyAlerts; query != nil {
		if err := msq.loadMatrixStrategyAlerts(ctx, query, nodes,
			func(n *MatrixStrategy) { n.Edges.MatrixStrategyAlerts = []*MatrixSpectrumAlert{} },
			func(n *MatrixStrategy, e *MatrixSpectrumAlert) {
				n.Edges.MatrixStrategyAlerts = append(n.Edges.MatrixStrategyAlerts, e)
			}); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (msq *MatrixStrategyQuery) loadMatrixStrategyAlerts(ctx context.Context, query *MatrixSpectrumAlertQuery, nodes []*MatrixStrategy, init func(*MatrixStrategy), assign func(*MatrixStrategy, *MatrixSpectrumAlert)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*MatrixStrategy)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(matrixspectrumalert.FieldMatrixStrategyID)
	}
	query.Where(predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(matrixstrategy.MatrixStrategyAlertsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.MatrixStrategyID
		if fk == nil {
			return fmt.Errorf(`foreign-key "matrix_strategy_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "matrix_strategy_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (msq *MatrixStrategyQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := msq.querySpec()
	_spec.Node.Columns = msq.ctx.Fields
	if len(msq.ctx.Fields) > 0 {
		_spec.Unique = msq.ctx.Unique != nil && *msq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, msq.driver, _spec)
}

func (msq *MatrixStrategyQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(matrixstrategy.Table, matrixstrategy.Columns, sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt))
	_spec.From = msq.sql
	if unique := msq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if msq.path != nil {
		_spec.Unique = true
	}
	if fields := msq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, matrixstrategy.FieldID)
		for i := range fields {
			if fields[i] != matrixstrategy.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := msq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := msq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := msq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := msq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (msq *MatrixStrategyQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(msq.driver.Dialect())
	t1 := builder.Table(matrixstrategy.Table)
	columns := msq.ctx.Fields
	if len(columns) == 0 {
		columns = matrixstrategy.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if msq.sql != nil {
		selector = msq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if msq.ctx.Unique != nil && *msq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range msq.predicates {
		p(selector)
	}
	for _, p := range msq.order {
		p(selector)
	}
	if offset := msq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := msq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// MatrixStrategyGroupBy is the group-by builder for MatrixStrategy entities.
type MatrixStrategyGroupBy struct {
	selector
	build *MatrixStrategyQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (msgb *MatrixStrategyGroupBy) Aggregate(fns ...AggregateFunc) *MatrixStrategyGroupBy {
	msgb.fns = append(msgb.fns, fns...)
	return msgb
}

// Scan applies the selector query and scans the result into the given value.
func (msgb *MatrixStrategyGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, msgb.build.ctx, "GroupBy")
	if err := msgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MatrixStrategyQuery, *MatrixStrategyGroupBy](ctx, msgb.build, msgb, msgb.build.inters, v)
}

func (msgb *MatrixStrategyGroupBy) sqlScan(ctx context.Context, root *MatrixStrategyQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(msgb.fns))
	for _, fn := range msgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*msgb.flds)+len(msgb.fns))
		for _, f := range *msgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*msgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := msgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// MatrixStrategySelect is the builder for selecting fields of MatrixStrategy entities.
type MatrixStrategySelect struct {
	*MatrixStrategyQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (mss *MatrixStrategySelect) Aggregate(fns ...AggregateFunc) *MatrixStrategySelect {
	mss.fns = append(mss.fns, fns...)
	return mss
}

// Scan applies the selector query and scans the result into the given value.
func (mss *MatrixStrategySelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, mss.ctx, "Select")
	if err := mss.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MatrixStrategyQuery, *MatrixStrategySelect](ctx, mss.MatrixStrategyQuery, mss, mss.inters, v)
}

func (mss *MatrixStrategySelect) sqlScan(ctx context.Context, root *MatrixStrategyQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(mss.fns))
	for _, fn := range mss.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*mss.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := mss.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
