// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/protectgroup"
	"meta/app/ent/tenant"
	"meta/app/entity/netease"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// ProtectGroup is the model entity for the ProtectGroup schema.
type ProtectGroup struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// GroupName holds the value of the "group_name" field.
	GroupName string `json:"group_name,omitempty" query:"group_name,omitempty"`
	// GroupID holds the value of the "group_id" field.
	GroupID int64 `json:"group_id,omitempty"`
	// Type holds the value of the "type" field.
	Type int `json:"type,omitempty"`
	// IPList holds the value of the "ip_list" field.
	IPList *[]string `json:"ip_list,omitempty" query:"ip_list,omitempty"`
	// ExpandIP holds the value of the "expand_ip" field.
	ExpandIP string `json:"expand_ip,omitempty" query:"expand_ip,omitempty"`
	// MonitorInfo holds the value of the "monitor_info" field.
	MonitorInfo *netease.MonitorInfo `json:"monitor_info,omitempty"`
	// DragInfo holds the value of the "drag_info" field.
	DragInfo *netease.DragInfo `json:"drag_info,omitempty"`
	// Nds4Config holds the value of the "nds4_config" field.
	Nds4Config *netease.Nds4Config `json:"nds4_config,omitempty"`
	// Nds6Config holds the value of the "nds6_config" field.
	Nds6Config *netease.Nds6Config `json:"nds6_config,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the ProtectGroupQuery when eager-loading is set.
	Edges        ProtectGroupEdges `json:"edges"`
	selectValues sql.SelectValues
}

// ProtectGroupEdges holds the relations/edges for other nodes in the graph.
type ProtectGroupEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// SpectrumAlerts holds the value of the spectrum_alerts edge.
	SpectrumAlerts []*SpectrumAlert `json:"spectrum_alerts,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e ProtectGroupEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// SpectrumAlertsOrErr returns the SpectrumAlerts value or an error if the edge
// was not loaded in eager-loading.
func (e ProtectGroupEdges) SpectrumAlertsOrErr() ([]*SpectrumAlert, error) {
	if e.loadedTypes[1] {
		return e.SpectrumAlerts, nil
	}
	return nil, &NotLoadedError{edge: "spectrum_alerts"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ProtectGroup) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case protectgroup.FieldIPList, protectgroup.FieldMonitorInfo, protectgroup.FieldDragInfo, protectgroup.FieldNds4Config, protectgroup.FieldNds6Config:
			values[i] = new([]byte)
		case protectgroup.FieldID, protectgroup.FieldTenantID, protectgroup.FieldGroupID, protectgroup.FieldType:
			values[i] = new(sql.NullInt64)
		case protectgroup.FieldRemark, protectgroup.FieldGroupName, protectgroup.FieldExpandIP:
			values[i] = new(sql.NullString)
		case protectgroup.FieldCreatedAt, protectgroup.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ProtectGroup fields.
func (pg *ProtectGroup) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case protectgroup.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			pg.ID = int(value.Int64)
		case protectgroup.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				pg.TenantID = new(int)
				*pg.TenantID = int(value.Int64)
			}
		case protectgroup.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				pg.CreatedAt = value.Time
			}
		case protectgroup.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				pg.UpdatedAt = value.Time
			}
		case protectgroup.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				pg.Remark = new(string)
				*pg.Remark = value.String
			}
		case protectgroup.FieldGroupName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field group_name", values[i])
			} else if value.Valid {
				pg.GroupName = value.String
			}
		case protectgroup.FieldGroupID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field group_id", values[i])
			} else if value.Valid {
				pg.GroupID = value.Int64
			}
		case protectgroup.FieldType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				pg.Type = int(value.Int64)
			}
		case protectgroup.FieldIPList:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field ip_list", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pg.IPList); err != nil {
					return fmt.Errorf("unmarshal field ip_list: %w", err)
				}
			}
		case protectgroup.FieldExpandIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field expand_ip", values[i])
			} else if value.Valid {
				pg.ExpandIP = value.String
			}
		case protectgroup.FieldMonitorInfo:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field monitor_info", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pg.MonitorInfo); err != nil {
					return fmt.Errorf("unmarshal field monitor_info: %w", err)
				}
			}
		case protectgroup.FieldDragInfo:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field drag_info", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pg.DragInfo); err != nil {
					return fmt.Errorf("unmarshal field drag_info: %w", err)
				}
			}
		case protectgroup.FieldNds4Config:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field nds4_config", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pg.Nds4Config); err != nil {
					return fmt.Errorf("unmarshal field nds4_config: %w", err)
				}
			}
		case protectgroup.FieldNds6Config:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field nds6_config", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &pg.Nds6Config); err != nil {
					return fmt.Errorf("unmarshal field nds6_config: %w", err)
				}
			}
		default:
			pg.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ProtectGroup.
// This includes values selected through modifiers, order, etc.
func (pg *ProtectGroup) Value(name string) (ent.Value, error) {
	return pg.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the ProtectGroup entity.
func (pg *ProtectGroup) QueryTenant() *TenantQuery {
	return NewProtectGroupClient(pg.config).QueryTenant(pg)
}

// QuerySpectrumAlerts queries the "spectrum_alerts" edge of the ProtectGroup entity.
func (pg *ProtectGroup) QuerySpectrumAlerts() *SpectrumAlertQuery {
	return NewProtectGroupClient(pg.config).QuerySpectrumAlerts(pg)
}

// Update returns a builder for updating this ProtectGroup.
// Note that you need to call ProtectGroup.Unwrap() before calling this method if this ProtectGroup
// was returned from a transaction, and the transaction was committed or rolled back.
func (pg *ProtectGroup) Update() *ProtectGroupUpdateOne {
	return NewProtectGroupClient(pg.config).UpdateOne(pg)
}

// Unwrap unwraps the ProtectGroup entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (pg *ProtectGroup) Unwrap() *ProtectGroup {
	_tx, ok := pg.config.driver.(*txDriver)
	if !ok {
		panic("ent: ProtectGroup is not a transactional entity")
	}
	pg.config.driver = _tx.drv
	return pg
}

// String implements the fmt.Stringer.
func (pg *ProtectGroup) String() string {
	var builder strings.Builder
	builder.WriteString("ProtectGroup(")
	builder.WriteString(fmt.Sprintf("id=%v, ", pg.ID))
	if v := pg.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(pg.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(pg.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := pg.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("group_name=")
	builder.WriteString(pg.GroupName)
	builder.WriteString(", ")
	builder.WriteString("group_id=")
	builder.WriteString(fmt.Sprintf("%v", pg.GroupID))
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", pg.Type))
	builder.WriteString(", ")
	builder.WriteString("ip_list=")
	builder.WriteString(fmt.Sprintf("%v", pg.IPList))
	builder.WriteString(", ")
	builder.WriteString("expand_ip=")
	builder.WriteString(pg.ExpandIP)
	builder.WriteString(", ")
	builder.WriteString("monitor_info=")
	builder.WriteString(fmt.Sprintf("%v", pg.MonitorInfo))
	builder.WriteString(", ")
	builder.WriteString("drag_info=")
	builder.WriteString(fmt.Sprintf("%v", pg.DragInfo))
	builder.WriteString(", ")
	builder.WriteString("nds4_config=")
	builder.WriteString(fmt.Sprintf("%v", pg.Nds4Config))
	builder.WriteString(", ")
	builder.WriteString("nds6_config=")
	builder.WriteString(fmt.Sprintf("%v", pg.Nds6Config))
	builder.WriteByte(')')
	return builder.String()
}

// ProtectGroups is a parsable slice of ProtectGroup.
type ProtectGroups []*ProtectGroup
