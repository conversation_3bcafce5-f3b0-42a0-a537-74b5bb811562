// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/systemapi"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SystemApiCreate is the builder for creating a SystemApi entity.
type SystemApiCreate struct {
	config
	mutation *SystemApiMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (sac *SystemApiCreate) SetCreatedAt(t time.Time) *SystemApiCreate {
	sac.mutation.SetCreatedAt(t)
	return sac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sac *SystemApiCreate) SetNillableCreatedAt(t *time.Time) *SystemApiCreate {
	if t != nil {
		sac.SetCreatedAt(*t)
	}
	return sac
}

// SetUpdatedAt sets the "updated_at" field.
func (sac *SystemApiCreate) SetUpdatedAt(t time.Time) *SystemApiCreate {
	sac.mutation.SetUpdatedAt(t)
	return sac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sac *SystemApiCreate) SetNillableUpdatedAt(t *time.Time) *SystemApiCreate {
	if t != nil {
		sac.SetUpdatedAt(*t)
	}
	return sac
}

// SetRemark sets the "remark" field.
func (sac *SystemApiCreate) SetRemark(s string) *SystemApiCreate {
	sac.mutation.SetRemark(s)
	return sac
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sac *SystemApiCreate) SetNillableRemark(s *string) *SystemApiCreate {
	if s != nil {
		sac.SetRemark(*s)
	}
	return sac
}

// SetName sets the "name" field.
func (sac *SystemApiCreate) SetName(s string) *SystemApiCreate {
	sac.mutation.SetName(s)
	return sac
}

// SetPath sets the "path" field.
func (sac *SystemApiCreate) SetPath(s string) *SystemApiCreate {
	sac.mutation.SetPath(s)
	return sac
}

// SetHTTPMethod sets the "http_method" field.
func (sac *SystemApiCreate) SetHTTPMethod(s string) *SystemApiCreate {
	sac.mutation.SetHTTPMethod(s)
	return sac
}

// SetRoles sets the "roles" field.
func (sac *SystemApiCreate) SetRoles(s *[]string) *SystemApiCreate {
	sac.mutation.SetRoles(s)
	return sac
}

// SetPublic sets the "public" field.
func (sac *SystemApiCreate) SetPublic(b bool) *SystemApiCreate {
	sac.mutation.SetPublic(b)
	return sac
}

// SetSa sets the "sa" field.
func (sac *SystemApiCreate) SetSa(b bool) *SystemApiCreate {
	sac.mutation.SetSa(b)
	return sac
}

// Mutation returns the SystemApiMutation object of the builder.
func (sac *SystemApiCreate) Mutation() *SystemApiMutation {
	return sac.mutation
}

// Save creates the SystemApi in the database.
func (sac *SystemApiCreate) Save(ctx context.Context) (*SystemApi, error) {
	if err := sac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sac.sqlSave, sac.mutation, sac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sac *SystemApiCreate) SaveX(ctx context.Context) *SystemApi {
	v, err := sac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sac *SystemApiCreate) Exec(ctx context.Context) error {
	_, err := sac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sac *SystemApiCreate) ExecX(ctx context.Context) {
	if err := sac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sac *SystemApiCreate) defaults() error {
	if _, ok := sac.mutation.CreatedAt(); !ok {
		if systemapi.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized systemapi.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := systemapi.DefaultCreatedAt()
		sac.mutation.SetCreatedAt(v)
	}
	if _, ok := sac.mutation.UpdatedAt(); !ok {
		if systemapi.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized systemapi.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := systemapi.DefaultUpdatedAt()
		sac.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sac *SystemApiCreate) check() error {
	if _, ok := sac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "SystemApi.created_at"`)}
	}
	if _, ok := sac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "SystemApi.updated_at"`)}
	}
	if v, ok := sac.mutation.Remark(); ok {
		if err := systemapi.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SystemApi.remark": %w`, err)}
		}
	}
	if _, ok := sac.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "SystemApi.name"`)}
	}
	if _, ok := sac.mutation.Path(); !ok {
		return &ValidationError{Name: "path", err: errors.New(`ent: missing required field "SystemApi.path"`)}
	}
	if _, ok := sac.mutation.HTTPMethod(); !ok {
		return &ValidationError{Name: "http_method", err: errors.New(`ent: missing required field "SystemApi.http_method"`)}
	}
	if _, ok := sac.mutation.Roles(); !ok {
		return &ValidationError{Name: "roles", err: errors.New(`ent: missing required field "SystemApi.roles"`)}
	}
	if _, ok := sac.mutation.Public(); !ok {
		return &ValidationError{Name: "public", err: errors.New(`ent: missing required field "SystemApi.public"`)}
	}
	if _, ok := sac.mutation.Sa(); !ok {
		return &ValidationError{Name: "sa", err: errors.New(`ent: missing required field "SystemApi.sa"`)}
	}
	return nil
}

func (sac *SystemApiCreate) sqlSave(ctx context.Context) (*SystemApi, error) {
	if err := sac.check(); err != nil {
		return nil, err
	}
	_node, _spec := sac.createSpec()
	if err := sqlgraph.CreateNode(ctx, sac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sac.mutation.id = &_node.ID
	sac.mutation.done = true
	return _node, nil
}

func (sac *SystemApiCreate) createSpec() (*SystemApi, *sqlgraph.CreateSpec) {
	var (
		_node = &SystemApi{config: sac.config}
		_spec = sqlgraph.NewCreateSpec(systemapi.Table, sqlgraph.NewFieldSpec(systemapi.FieldID, field.TypeInt))
	)
	if value, ok := sac.mutation.CreatedAt(); ok {
		_spec.SetField(systemapi.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sac.mutation.UpdatedAt(); ok {
		_spec.SetField(systemapi.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := sac.mutation.Remark(); ok {
		_spec.SetField(systemapi.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := sac.mutation.Name(); ok {
		_spec.SetField(systemapi.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := sac.mutation.Path(); ok {
		_spec.SetField(systemapi.FieldPath, field.TypeString, value)
		_node.Path = value
	}
	if value, ok := sac.mutation.HTTPMethod(); ok {
		_spec.SetField(systemapi.FieldHTTPMethod, field.TypeString, value)
		_node.HTTPMethod = value
	}
	if value, ok := sac.mutation.Roles(); ok {
		_spec.SetField(systemapi.FieldRoles, field.TypeJSON, value)
		_node.Roles = value
	}
	if value, ok := sac.mutation.Public(); ok {
		_spec.SetField(systemapi.FieldPublic, field.TypeBool, value)
		_node.Public = value
	}
	if value, ok := sac.mutation.Sa(); ok {
		_spec.SetField(systemapi.FieldSa, field.TypeBool, value)
		_node.Sa = value
	}
	return _node, _spec
}

// SystemApiCreateBulk is the builder for creating many SystemApi entities in bulk.
type SystemApiCreateBulk struct {
	config
	err      error
	builders []*SystemApiCreate
}

// Save creates the SystemApi entities in the database.
func (sacb *SystemApiCreateBulk) Save(ctx context.Context) ([]*SystemApi, error) {
	if sacb.err != nil {
		return nil, sacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(sacb.builders))
	nodes := make([]*SystemApi, len(sacb.builders))
	mutators := make([]Mutator, len(sacb.builders))
	for i := range sacb.builders {
		func(i int, root context.Context) {
			builder := sacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SystemApiMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, sacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, sacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, sacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (sacb *SystemApiCreateBulk) SaveX(ctx context.Context) []*SystemApi {
	v, err := sacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sacb *SystemApiCreateBulk) Exec(ctx context.Context) error {
	_, err := sacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sacb *SystemApiCreateBulk) ExecX(ctx context.Context) {
	if err := sacb.Exec(ctx); err != nil {
		panic(err)
	}
}
