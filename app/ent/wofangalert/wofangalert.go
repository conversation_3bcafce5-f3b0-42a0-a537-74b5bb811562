// Code generated by ent, DO NOT EDIT.

package wofangalert

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the wofangalert type in the database.
	Label = "wofang_alert"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldAttackStatus holds the string denoting the attack_status field in the database.
	FieldAttackStatus = "attack_status"
	// FieldAttackType holds the string denoting the attack_type field in the database.
	FieldAttackType = "attack_type"
	// FieldDeviceIP holds the string denoting the device_ip field in the database.
	FieldDeviceIP = "device_ip"
	// FieldZoneIP holds the string denoting the zone_ip field in the database.
	FieldZoneIP = "zone_ip"
	// FieldAttackID holds the string denoting the attack_id field in the database.
	FieldAttackID = "attack_id"
	// FieldStartTime holds the string denoting the start_time field in the database.
	FieldStartTime = "start_time"
	// FieldEndTime holds the string denoting the end_time field in the database.
	FieldEndTime = "end_time"
	// FieldMaxDropBps holds the string denoting the max_drop_bps field in the database.
	FieldMaxDropBps = "max_drop_bps"
	// FieldMaxInBps holds the string denoting the max_in_bps field in the database.
	FieldMaxInBps = "max_in_bps"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// Table holds the table name of the wofangalert in the database.
	Table = "wofang_alerts"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "wofang_alerts"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
)

// Columns holds all SQL columns for wofangalert fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldAttackStatus,
	FieldAttackType,
	FieldDeviceIP,
	FieldZoneIP,
	FieldAttackID,
	FieldStartTime,
	FieldEndTime,
	FieldMaxDropBps,
	FieldMaxInBps,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the WofangAlert queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByAttackStatus orders the results by the attack_status field.
func ByAttackStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAttackStatus, opts...).ToFunc()
}

// ByDeviceIP orders the results by the device_ip field.
func ByDeviceIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeviceIP, opts...).ToFunc()
}

// ByZoneIP orders the results by the zone_ip field.
func ByZoneIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldZoneIP, opts...).ToFunc()
}

// ByAttackID orders the results by the attack_id field.
func ByAttackID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAttackID, opts...).ToFunc()
}

// ByStartTime orders the results by the start_time field.
func ByStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartTime, opts...).ToFunc()
}

// ByEndTime orders the results by the end_time field.
func ByEndTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEndTime, opts...).ToFunc()
}

// ByMaxDropBps orders the results by the max_drop_bps field.
func ByMaxDropBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxDropBps, opts...).ToFunc()
}

// ByMaxInBps orders the results by the max_in_bps field.
func ByMaxInBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxInBps, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
