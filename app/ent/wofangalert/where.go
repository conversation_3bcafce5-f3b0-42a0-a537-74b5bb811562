// Code generated by ent, DO NOT EDIT.

package wofangalert

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldRemark, v))
}

// AttackStatus applies equality check predicate on the "attack_status" field. It's identical to AttackStatusEQ.
func AttackStatus(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldAttackStatus, v))
}

// DeviceIP applies equality check predicate on the "device_ip" field. It's identical to DeviceIPEQ.
func DeviceIP(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldDeviceIP, v))
}

// ZoneIP applies equality check predicate on the "zone_ip" field. It's identical to ZoneIPEQ.
func ZoneIP(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldZoneIP, v))
}

// AttackID applies equality check predicate on the "attack_id" field. It's identical to AttackIDEQ.
func AttackID(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldAttackID, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldStartTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldEndTime, v))
}

// MaxDropBps applies equality check predicate on the "max_drop_bps" field. It's identical to MaxDropBpsEQ.
func MaxDropBps(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldMaxDropBps, v))
}

// MaxInBps applies equality check predicate on the "max_in_bps" field. It's identical to MaxInBpsEQ.
func MaxInBps(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldMaxInBps, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldContainsFold(FieldRemark, v))
}

// AttackStatusEQ applies the EQ predicate on the "attack_status" field.
func AttackStatusEQ(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldAttackStatus, v))
}

// AttackStatusNEQ applies the NEQ predicate on the "attack_status" field.
func AttackStatusNEQ(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldAttackStatus, v))
}

// AttackStatusIn applies the In predicate on the "attack_status" field.
func AttackStatusIn(vs ...int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldAttackStatus, vs...))
}

// AttackStatusNotIn applies the NotIn predicate on the "attack_status" field.
func AttackStatusNotIn(vs ...int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldAttackStatus, vs...))
}

// AttackStatusGT applies the GT predicate on the "attack_status" field.
func AttackStatusGT(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldAttackStatus, v))
}

// AttackStatusGTE applies the GTE predicate on the "attack_status" field.
func AttackStatusGTE(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldAttackStatus, v))
}

// AttackStatusLT applies the LT predicate on the "attack_status" field.
func AttackStatusLT(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldAttackStatus, v))
}

// AttackStatusLTE applies the LTE predicate on the "attack_status" field.
func AttackStatusLTE(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldAttackStatus, v))
}

// AttackTypeIsNil applies the IsNil predicate on the "attack_type" field.
func AttackTypeIsNil() predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIsNull(FieldAttackType))
}

// AttackTypeNotNil applies the NotNil predicate on the "attack_type" field.
func AttackTypeNotNil() predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotNull(FieldAttackType))
}

// DeviceIPEQ applies the EQ predicate on the "device_ip" field.
func DeviceIPEQ(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldDeviceIP, v))
}

// DeviceIPNEQ applies the NEQ predicate on the "device_ip" field.
func DeviceIPNEQ(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldDeviceIP, v))
}

// DeviceIPIn applies the In predicate on the "device_ip" field.
func DeviceIPIn(vs ...string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldDeviceIP, vs...))
}

// DeviceIPNotIn applies the NotIn predicate on the "device_ip" field.
func DeviceIPNotIn(vs ...string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldDeviceIP, vs...))
}

// DeviceIPGT applies the GT predicate on the "device_ip" field.
func DeviceIPGT(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldDeviceIP, v))
}

// DeviceIPGTE applies the GTE predicate on the "device_ip" field.
func DeviceIPGTE(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldDeviceIP, v))
}

// DeviceIPLT applies the LT predicate on the "device_ip" field.
func DeviceIPLT(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldDeviceIP, v))
}

// DeviceIPLTE applies the LTE predicate on the "device_ip" field.
func DeviceIPLTE(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldDeviceIP, v))
}

// DeviceIPContains applies the Contains predicate on the "device_ip" field.
func DeviceIPContains(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldContains(FieldDeviceIP, v))
}

// DeviceIPHasPrefix applies the HasPrefix predicate on the "device_ip" field.
func DeviceIPHasPrefix(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldHasPrefix(FieldDeviceIP, v))
}

// DeviceIPHasSuffix applies the HasSuffix predicate on the "device_ip" field.
func DeviceIPHasSuffix(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldHasSuffix(FieldDeviceIP, v))
}

// DeviceIPEqualFold applies the EqualFold predicate on the "device_ip" field.
func DeviceIPEqualFold(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEqualFold(FieldDeviceIP, v))
}

// DeviceIPContainsFold applies the ContainsFold predicate on the "device_ip" field.
func DeviceIPContainsFold(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldContainsFold(FieldDeviceIP, v))
}

// ZoneIPEQ applies the EQ predicate on the "zone_ip" field.
func ZoneIPEQ(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldZoneIP, v))
}

// ZoneIPNEQ applies the NEQ predicate on the "zone_ip" field.
func ZoneIPNEQ(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldZoneIP, v))
}

// ZoneIPIn applies the In predicate on the "zone_ip" field.
func ZoneIPIn(vs ...string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldZoneIP, vs...))
}

// ZoneIPNotIn applies the NotIn predicate on the "zone_ip" field.
func ZoneIPNotIn(vs ...string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldZoneIP, vs...))
}

// ZoneIPGT applies the GT predicate on the "zone_ip" field.
func ZoneIPGT(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldZoneIP, v))
}

// ZoneIPGTE applies the GTE predicate on the "zone_ip" field.
func ZoneIPGTE(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldZoneIP, v))
}

// ZoneIPLT applies the LT predicate on the "zone_ip" field.
func ZoneIPLT(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldZoneIP, v))
}

// ZoneIPLTE applies the LTE predicate on the "zone_ip" field.
func ZoneIPLTE(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldZoneIP, v))
}

// ZoneIPContains applies the Contains predicate on the "zone_ip" field.
func ZoneIPContains(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldContains(FieldZoneIP, v))
}

// ZoneIPHasPrefix applies the HasPrefix predicate on the "zone_ip" field.
func ZoneIPHasPrefix(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldHasPrefix(FieldZoneIP, v))
}

// ZoneIPHasSuffix applies the HasSuffix predicate on the "zone_ip" field.
func ZoneIPHasSuffix(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldHasSuffix(FieldZoneIP, v))
}

// ZoneIPEqualFold applies the EqualFold predicate on the "zone_ip" field.
func ZoneIPEqualFold(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEqualFold(FieldZoneIP, v))
}

// ZoneIPContainsFold applies the ContainsFold predicate on the "zone_ip" field.
func ZoneIPContainsFold(v string) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldContainsFold(FieldZoneIP, v))
}

// AttackIDEQ applies the EQ predicate on the "attack_id" field.
func AttackIDEQ(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldAttackID, v))
}

// AttackIDNEQ applies the NEQ predicate on the "attack_id" field.
func AttackIDNEQ(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldAttackID, v))
}

// AttackIDIn applies the In predicate on the "attack_id" field.
func AttackIDIn(vs ...int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldAttackID, vs...))
}

// AttackIDNotIn applies the NotIn predicate on the "attack_id" field.
func AttackIDNotIn(vs ...int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldAttackID, vs...))
}

// AttackIDGT applies the GT predicate on the "attack_id" field.
func AttackIDGT(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldAttackID, v))
}

// AttackIDGTE applies the GTE predicate on the "attack_id" field.
func AttackIDGTE(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldAttackID, v))
}

// AttackIDLT applies the LT predicate on the "attack_id" field.
func AttackIDLT(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldAttackID, v))
}

// AttackIDLTE applies the LTE predicate on the "attack_id" field.
func AttackIDLTE(v int) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldAttackID, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldStartTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotNull(FieldEndTime))
}

// MaxDropBpsEQ applies the EQ predicate on the "max_drop_bps" field.
func MaxDropBpsEQ(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldMaxDropBps, v))
}

// MaxDropBpsNEQ applies the NEQ predicate on the "max_drop_bps" field.
func MaxDropBpsNEQ(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldMaxDropBps, v))
}

// MaxDropBpsIn applies the In predicate on the "max_drop_bps" field.
func MaxDropBpsIn(vs ...int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldMaxDropBps, vs...))
}

// MaxDropBpsNotIn applies the NotIn predicate on the "max_drop_bps" field.
func MaxDropBpsNotIn(vs ...int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldMaxDropBps, vs...))
}

// MaxDropBpsGT applies the GT predicate on the "max_drop_bps" field.
func MaxDropBpsGT(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldMaxDropBps, v))
}

// MaxDropBpsGTE applies the GTE predicate on the "max_drop_bps" field.
func MaxDropBpsGTE(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldMaxDropBps, v))
}

// MaxDropBpsLT applies the LT predicate on the "max_drop_bps" field.
func MaxDropBpsLT(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldMaxDropBps, v))
}

// MaxDropBpsLTE applies the LTE predicate on the "max_drop_bps" field.
func MaxDropBpsLTE(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldMaxDropBps, v))
}

// MaxInBpsEQ applies the EQ predicate on the "max_in_bps" field.
func MaxInBpsEQ(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldEQ(FieldMaxInBps, v))
}

// MaxInBpsNEQ applies the NEQ predicate on the "max_in_bps" field.
func MaxInBpsNEQ(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNEQ(FieldMaxInBps, v))
}

// MaxInBpsIn applies the In predicate on the "max_in_bps" field.
func MaxInBpsIn(vs ...int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldIn(FieldMaxInBps, vs...))
}

// MaxInBpsNotIn applies the NotIn predicate on the "max_in_bps" field.
func MaxInBpsNotIn(vs ...int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldNotIn(FieldMaxInBps, vs...))
}

// MaxInBpsGT applies the GT predicate on the "max_in_bps" field.
func MaxInBpsGT(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGT(FieldMaxInBps, v))
}

// MaxInBpsGTE applies the GTE predicate on the "max_in_bps" field.
func MaxInBpsGTE(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldGTE(FieldMaxInBps, v))
}

// MaxInBpsLT applies the LT predicate on the "max_in_bps" field.
func MaxInBpsLT(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLT(FieldMaxInBps, v))
}

// MaxInBpsLTE applies the LTE predicate on the "max_in_bps" field.
func MaxInBpsLTE(v int64) predicate.WofangAlert {
	return predicate.WofangAlert(sql.FieldLTE(FieldMaxInBps, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.WofangAlert {
	return predicate.WofangAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.WofangAlert {
	return predicate.WofangAlert(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.WofangAlert) predicate.WofangAlert {
	return predicate.WofangAlert(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.WofangAlert) predicate.WofangAlert {
	return predicate.WofangAlert(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.WofangAlert) predicate.WofangAlert {
	return predicate.WofangAlert(sql.NotPredicates(p))
}
