// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/predicate"
	"meta/app/ent/systemconfig"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SystemConfigQuery is the builder for querying SystemConfig entities.
type SystemConfigQuery struct {
	config
	ctx        *QueryContext
	order      []systemconfig.OrderOption
	inters     []Interceptor
	predicates []predicate.SystemConfig
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the SystemConfigQuery builder.
func (scq *SystemConfigQuery) Where(ps ...predicate.SystemConfig) *SystemConfigQuery {
	scq.predicates = append(scq.predicates, ps...)
	return scq
}

// Limit the number of records to be returned by this query.
func (scq *SystemConfigQuery) Limit(limit int) *SystemConfigQuery {
	scq.ctx.Limit = &limit
	return scq
}

// Offset to start from.
func (scq *SystemConfigQuery) Offset(offset int) *SystemConfigQuery {
	scq.ctx.Offset = &offset
	return scq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (scq *SystemConfigQuery) Unique(unique bool) *SystemConfigQuery {
	scq.ctx.Unique = &unique
	return scq
}

// Order specifies how the records should be ordered.
func (scq *SystemConfigQuery) Order(o ...systemconfig.OrderOption) *SystemConfigQuery {
	scq.order = append(scq.order, o...)
	return scq
}

// First returns the first SystemConfig entity from the query.
// Returns a *NotFoundError when no SystemConfig was found.
func (scq *SystemConfigQuery) First(ctx context.Context) (*SystemConfig, error) {
	nodes, err := scq.Limit(1).All(setContextOp(ctx, scq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{systemconfig.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (scq *SystemConfigQuery) FirstX(ctx context.Context) *SystemConfig {
	node, err := scq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first SystemConfig ID from the query.
// Returns a *NotFoundError when no SystemConfig ID was found.
func (scq *SystemConfigQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = scq.Limit(1).IDs(setContextOp(ctx, scq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{systemconfig.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (scq *SystemConfigQuery) FirstIDX(ctx context.Context) int {
	id, err := scq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single SystemConfig entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one SystemConfig entity is found.
// Returns a *NotFoundError when no SystemConfig entities are found.
func (scq *SystemConfigQuery) Only(ctx context.Context) (*SystemConfig, error) {
	nodes, err := scq.Limit(2).All(setContextOp(ctx, scq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{systemconfig.Label}
	default:
		return nil, &NotSingularError{systemconfig.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (scq *SystemConfigQuery) OnlyX(ctx context.Context) *SystemConfig {
	node, err := scq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only SystemConfig ID in the query.
// Returns a *NotSingularError when more than one SystemConfig ID is found.
// Returns a *NotFoundError when no entities are found.
func (scq *SystemConfigQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = scq.Limit(2).IDs(setContextOp(ctx, scq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{systemconfig.Label}
	default:
		err = &NotSingularError{systemconfig.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (scq *SystemConfigQuery) OnlyIDX(ctx context.Context) int {
	id, err := scq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of SystemConfigs.
func (scq *SystemConfigQuery) All(ctx context.Context) ([]*SystemConfig, error) {
	ctx = setContextOp(ctx, scq.ctx, "All")
	if err := scq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*SystemConfig, *SystemConfigQuery]()
	return withInterceptors[[]*SystemConfig](ctx, scq, qr, scq.inters)
}

// AllX is like All, but panics if an error occurs.
func (scq *SystemConfigQuery) AllX(ctx context.Context) []*SystemConfig {
	nodes, err := scq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of SystemConfig IDs.
func (scq *SystemConfigQuery) IDs(ctx context.Context) (ids []int, err error) {
	if scq.ctx.Unique == nil && scq.path != nil {
		scq.Unique(true)
	}
	ctx = setContextOp(ctx, scq.ctx, "IDs")
	if err = scq.Select(systemconfig.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (scq *SystemConfigQuery) IDsX(ctx context.Context) []int {
	ids, err := scq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (scq *SystemConfigQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, scq.ctx, "Count")
	if err := scq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, scq, querierCount[*SystemConfigQuery](), scq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (scq *SystemConfigQuery) CountX(ctx context.Context) int {
	count, err := scq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (scq *SystemConfigQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, scq.ctx, "Exist")
	switch _, err := scq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (scq *SystemConfigQuery) ExistX(ctx context.Context) bool {
	exist, err := scq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the SystemConfigQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (scq *SystemConfigQuery) Clone() *SystemConfigQuery {
	if scq == nil {
		return nil
	}
	return &SystemConfigQuery{
		config:     scq.config,
		ctx:        scq.ctx.Clone(),
		order:      append([]systemconfig.OrderOption{}, scq.order...),
		inters:     append([]Interceptor{}, scq.inters...),
		predicates: append([]predicate.SystemConfig{}, scq.predicates...),
		// clone intermediate query.
		sql:  scq.sql.Clone(),
		path: scq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.SystemConfig.Query().
//		GroupBy(systemconfig.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (scq *SystemConfigQuery) GroupBy(field string, fields ...string) *SystemConfigGroupBy {
	scq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &SystemConfigGroupBy{build: scq}
	grbuild.flds = &scq.ctx.Fields
	grbuild.label = systemconfig.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.SystemConfig.Query().
//		Select(systemconfig.FieldCreatedAt).
//		Scan(ctx, &v)
func (scq *SystemConfigQuery) Select(fields ...string) *SystemConfigSelect {
	scq.ctx.Fields = append(scq.ctx.Fields, fields...)
	sbuild := &SystemConfigSelect{SystemConfigQuery: scq}
	sbuild.label = systemconfig.Label
	sbuild.flds, sbuild.scan = &scq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a SystemConfigSelect configured with the given aggregations.
func (scq *SystemConfigQuery) Aggregate(fns ...AggregateFunc) *SystemConfigSelect {
	return scq.Select().Aggregate(fns...)
}

func (scq *SystemConfigQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range scq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, scq); err != nil {
				return err
			}
		}
	}
	for _, f := range scq.ctx.Fields {
		if !systemconfig.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if scq.path != nil {
		prev, err := scq.path(ctx)
		if err != nil {
			return err
		}
		scq.sql = prev
	}
	if systemconfig.Policy == nil {
		return errors.New("ent: uninitialized systemconfig.Policy (forgotten import ent/runtime?)")
	}
	if err := systemconfig.Policy.EvalQuery(ctx, scq); err != nil {
		return err
	}
	return nil
}

func (scq *SystemConfigQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*SystemConfig, error) {
	var (
		nodes = []*SystemConfig{}
		_spec = scq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*SystemConfig).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &SystemConfig{config: scq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, scq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (scq *SystemConfigQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := scq.querySpec()
	_spec.Node.Columns = scq.ctx.Fields
	if len(scq.ctx.Fields) > 0 {
		_spec.Unique = scq.ctx.Unique != nil && *scq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, scq.driver, _spec)
}

func (scq *SystemConfigQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(systemconfig.Table, systemconfig.Columns, sqlgraph.NewFieldSpec(systemconfig.FieldID, field.TypeInt))
	_spec.From = scq.sql
	if unique := scq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if scq.path != nil {
		_spec.Unique = true
	}
	if fields := scq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, systemconfig.FieldID)
		for i := range fields {
			if fields[i] != systemconfig.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := scq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := scq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := scq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := scq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (scq *SystemConfigQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(scq.driver.Dialect())
	t1 := builder.Table(systemconfig.Table)
	columns := scq.ctx.Fields
	if len(columns) == 0 {
		columns = systemconfig.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if scq.sql != nil {
		selector = scq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if scq.ctx.Unique != nil && *scq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range scq.predicates {
		p(selector)
	}
	for _, p := range scq.order {
		p(selector)
	}
	if offset := scq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := scq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// SystemConfigGroupBy is the group-by builder for SystemConfig entities.
type SystemConfigGroupBy struct {
	selector
	build *SystemConfigQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (scgb *SystemConfigGroupBy) Aggregate(fns ...AggregateFunc) *SystemConfigGroupBy {
	scgb.fns = append(scgb.fns, fns...)
	return scgb
}

// Scan applies the selector query and scans the result into the given value.
func (scgb *SystemConfigGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, scgb.build.ctx, "GroupBy")
	if err := scgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SystemConfigQuery, *SystemConfigGroupBy](ctx, scgb.build, scgb, scgb.build.inters, v)
}

func (scgb *SystemConfigGroupBy) sqlScan(ctx context.Context, root *SystemConfigQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(scgb.fns))
	for _, fn := range scgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*scgb.flds)+len(scgb.fns))
		for _, f := range *scgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*scgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := scgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// SystemConfigSelect is the builder for selecting fields of SystemConfig entities.
type SystemConfigSelect struct {
	*SystemConfigQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (scs *SystemConfigSelect) Aggregate(fns ...AggregateFunc) *SystemConfigSelect {
	scs.fns = append(scs.fns, fns...)
	return scs
}

// Scan applies the selector query and scans the result into the given value.
func (scs *SystemConfigSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, scs.ctx, "Select")
	if err := scs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SystemConfigQuery, *SystemConfigSelect](ctx, scs.SystemConfigQuery, scs, scs.inters, v)
}

func (scs *SystemConfigSelect) sqlScan(ctx context.Context, root *SystemConfigQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(scs.fns))
	for _, fn := range scs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*scs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := scs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
