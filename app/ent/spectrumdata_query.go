// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SpectrumDataQuery is the builder for querying SpectrumData entities.
type SpectrumDataQuery struct {
	config
	ctx               *QueryContext
	order             []spectrumdata.OrderOption
	inters            []Interceptor
	predicates        []predicate.SpectrumData
	withTenant        *TenantQuery
	withSpectrumAlert *SpectrumAlertQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the SpectrumDataQuery builder.
func (sdq *SpectrumDataQuery) Where(ps ...predicate.SpectrumData) *SpectrumDataQuery {
	sdq.predicates = append(sdq.predicates, ps...)
	return sdq
}

// Limit the number of records to be returned by this query.
func (sdq *SpectrumDataQuery) Limit(limit int) *SpectrumDataQuery {
	sdq.ctx.Limit = &limit
	return sdq
}

// Offset to start from.
func (sdq *SpectrumDataQuery) Offset(offset int) *SpectrumDataQuery {
	sdq.ctx.Offset = &offset
	return sdq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (sdq *SpectrumDataQuery) Unique(unique bool) *SpectrumDataQuery {
	sdq.ctx.Unique = &unique
	return sdq
}

// Order specifies how the records should be ordered.
func (sdq *SpectrumDataQuery) Order(o ...spectrumdata.OrderOption) *SpectrumDataQuery {
	sdq.order = append(sdq.order, o...)
	return sdq
}

// QueryTenant chains the current query on the "tenant" edge.
func (sdq *SpectrumDataQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: sdq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sdq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sdq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumdata.Table, spectrumdata.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, spectrumdata.TenantTable, spectrumdata.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(sdq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QuerySpectrumAlert chains the current query on the "spectrum_alert" edge.
func (sdq *SpectrumDataQuery) QuerySpectrumAlert() *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: sdq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := sdq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := sdq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(spectrumdata.Table, spectrumdata.FieldID, selector),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, spectrumdata.SpectrumAlertTable, spectrumdata.SpectrumAlertColumn),
		)
		fromU = sqlgraph.SetNeighbors(sdq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first SpectrumData entity from the query.
// Returns a *NotFoundError when no SpectrumData was found.
func (sdq *SpectrumDataQuery) First(ctx context.Context) (*SpectrumData, error) {
	nodes, err := sdq.Limit(1).All(setContextOp(ctx, sdq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{spectrumdata.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (sdq *SpectrumDataQuery) FirstX(ctx context.Context) *SpectrumData {
	node, err := sdq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first SpectrumData ID from the query.
// Returns a *NotFoundError when no SpectrumData ID was found.
func (sdq *SpectrumDataQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = sdq.Limit(1).IDs(setContextOp(ctx, sdq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{spectrumdata.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (sdq *SpectrumDataQuery) FirstIDX(ctx context.Context) int {
	id, err := sdq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single SpectrumData entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one SpectrumData entity is found.
// Returns a *NotFoundError when no SpectrumData entities are found.
func (sdq *SpectrumDataQuery) Only(ctx context.Context) (*SpectrumData, error) {
	nodes, err := sdq.Limit(2).All(setContextOp(ctx, sdq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{spectrumdata.Label}
	default:
		return nil, &NotSingularError{spectrumdata.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (sdq *SpectrumDataQuery) OnlyX(ctx context.Context) *SpectrumData {
	node, err := sdq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only SpectrumData ID in the query.
// Returns a *NotSingularError when more than one SpectrumData ID is found.
// Returns a *NotFoundError when no entities are found.
func (sdq *SpectrumDataQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = sdq.Limit(2).IDs(setContextOp(ctx, sdq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{spectrumdata.Label}
	default:
		err = &NotSingularError{spectrumdata.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (sdq *SpectrumDataQuery) OnlyIDX(ctx context.Context) int {
	id, err := sdq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of SpectrumDataSlice.
func (sdq *SpectrumDataQuery) All(ctx context.Context) ([]*SpectrumData, error) {
	ctx = setContextOp(ctx, sdq.ctx, "All")
	if err := sdq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*SpectrumData, *SpectrumDataQuery]()
	return withInterceptors[[]*SpectrumData](ctx, sdq, qr, sdq.inters)
}

// AllX is like All, but panics if an error occurs.
func (sdq *SpectrumDataQuery) AllX(ctx context.Context) []*SpectrumData {
	nodes, err := sdq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of SpectrumData IDs.
func (sdq *SpectrumDataQuery) IDs(ctx context.Context) (ids []int, err error) {
	if sdq.ctx.Unique == nil && sdq.path != nil {
		sdq.Unique(true)
	}
	ctx = setContextOp(ctx, sdq.ctx, "IDs")
	if err = sdq.Select(spectrumdata.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (sdq *SpectrumDataQuery) IDsX(ctx context.Context) []int {
	ids, err := sdq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (sdq *SpectrumDataQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, sdq.ctx, "Count")
	if err := sdq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, sdq, querierCount[*SpectrumDataQuery](), sdq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (sdq *SpectrumDataQuery) CountX(ctx context.Context) int {
	count, err := sdq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (sdq *SpectrumDataQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, sdq.ctx, "Exist")
	switch _, err := sdq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (sdq *SpectrumDataQuery) ExistX(ctx context.Context) bool {
	exist, err := sdq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the SpectrumDataQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (sdq *SpectrumDataQuery) Clone() *SpectrumDataQuery {
	if sdq == nil {
		return nil
	}
	return &SpectrumDataQuery{
		config:            sdq.config,
		ctx:               sdq.ctx.Clone(),
		order:             append([]spectrumdata.OrderOption{}, sdq.order...),
		inters:            append([]Interceptor{}, sdq.inters...),
		predicates:        append([]predicate.SpectrumData{}, sdq.predicates...),
		withTenant:        sdq.withTenant.Clone(),
		withSpectrumAlert: sdq.withSpectrumAlert.Clone(),
		// clone intermediate query.
		sql:  sdq.sql.Clone(),
		path: sdq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (sdq *SpectrumDataQuery) WithTenant(opts ...func(*TenantQuery)) *SpectrumDataQuery {
	query := (&TenantClient{config: sdq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sdq.withTenant = query
	return sdq
}

// WithSpectrumAlert tells the query-builder to eager-load the nodes that are connected to
// the "spectrum_alert" edge. The optional arguments are used to configure the query builder of the edge.
func (sdq *SpectrumDataQuery) WithSpectrumAlert(opts ...func(*SpectrumAlertQuery)) *SpectrumDataQuery {
	query := (&SpectrumAlertClient{config: sdq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	sdq.withSpectrumAlert = query
	return sdq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.SpectrumData.Query().
//		GroupBy(spectrumdata.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (sdq *SpectrumDataQuery) GroupBy(field string, fields ...string) *SpectrumDataGroupBy {
	sdq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &SpectrumDataGroupBy{build: sdq}
	grbuild.flds = &sdq.ctx.Fields
	grbuild.label = spectrumdata.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.SpectrumData.Query().
//		Select(spectrumdata.FieldTenantID).
//		Scan(ctx, &v)
func (sdq *SpectrumDataQuery) Select(fields ...string) *SpectrumDataSelect {
	sdq.ctx.Fields = append(sdq.ctx.Fields, fields...)
	sbuild := &SpectrumDataSelect{SpectrumDataQuery: sdq}
	sbuild.label = spectrumdata.Label
	sbuild.flds, sbuild.scan = &sdq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a SpectrumDataSelect configured with the given aggregations.
func (sdq *SpectrumDataQuery) Aggregate(fns ...AggregateFunc) *SpectrumDataSelect {
	return sdq.Select().Aggregate(fns...)
}

func (sdq *SpectrumDataQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range sdq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, sdq); err != nil {
				return err
			}
		}
	}
	for _, f := range sdq.ctx.Fields {
		if !spectrumdata.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if sdq.path != nil {
		prev, err := sdq.path(ctx)
		if err != nil {
			return err
		}
		sdq.sql = prev
	}
	if spectrumdata.Policy == nil {
		return errors.New("ent: uninitialized spectrumdata.Policy (forgotten import ent/runtime?)")
	}
	if err := spectrumdata.Policy.EvalQuery(ctx, sdq); err != nil {
		return err
	}
	return nil
}

func (sdq *SpectrumDataQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*SpectrumData, error) {
	var (
		nodes       = []*SpectrumData{}
		_spec       = sdq.querySpec()
		loadedTypes = [2]bool{
			sdq.withTenant != nil,
			sdq.withSpectrumAlert != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*SpectrumData).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &SpectrumData{config: sdq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, sdq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := sdq.withTenant; query != nil {
		if err := sdq.loadTenant(ctx, query, nodes, nil,
			func(n *SpectrumData, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := sdq.withSpectrumAlert; query != nil {
		if err := sdq.loadSpectrumAlert(ctx, query, nodes, nil,
			func(n *SpectrumData, e *SpectrumAlert) { n.Edges.SpectrumAlert = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (sdq *SpectrumDataQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*SpectrumData, init func(*SpectrumData), assign func(*SpectrumData, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*SpectrumData)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (sdq *SpectrumDataQuery) loadSpectrumAlert(ctx context.Context, query *SpectrumAlertQuery, nodes []*SpectrumData, init func(*SpectrumData), assign func(*SpectrumData, *SpectrumAlert)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*SpectrumData)
	for i := range nodes {
		if nodes[i].SpectrumAlertID == nil {
			continue
		}
		fk := *nodes[i].SpectrumAlertID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(spectrumalert.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "spectrum_alert_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (sdq *SpectrumDataQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := sdq.querySpec()
	_spec.Node.Columns = sdq.ctx.Fields
	if len(sdq.ctx.Fields) > 0 {
		_spec.Unique = sdq.ctx.Unique != nil && *sdq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, sdq.driver, _spec)
}

func (sdq *SpectrumDataQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(spectrumdata.Table, spectrumdata.Columns, sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt))
	_spec.From = sdq.sql
	if unique := sdq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if sdq.path != nil {
		_spec.Unique = true
	}
	if fields := sdq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, spectrumdata.FieldID)
		for i := range fields {
			if fields[i] != spectrumdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if sdq.withTenant != nil {
			_spec.Node.AddColumnOnce(spectrumdata.FieldTenantID)
		}
		if sdq.withSpectrumAlert != nil {
			_spec.Node.AddColumnOnce(spectrumdata.FieldSpectrumAlertID)
		}
	}
	if ps := sdq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := sdq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := sdq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := sdq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (sdq *SpectrumDataQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(sdq.driver.Dialect())
	t1 := builder.Table(spectrumdata.Table)
	columns := sdq.ctx.Fields
	if len(columns) == 0 {
		columns = spectrumdata.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if sdq.sql != nil {
		selector = sdq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if sdq.ctx.Unique != nil && *sdq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range sdq.predicates {
		p(selector)
	}
	for _, p := range sdq.order {
		p(selector)
	}
	if offset := sdq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := sdq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// SpectrumDataGroupBy is the group-by builder for SpectrumData entities.
type SpectrumDataGroupBy struct {
	selector
	build *SpectrumDataQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (sdgb *SpectrumDataGroupBy) Aggregate(fns ...AggregateFunc) *SpectrumDataGroupBy {
	sdgb.fns = append(sdgb.fns, fns...)
	return sdgb
}

// Scan applies the selector query and scans the result into the given value.
func (sdgb *SpectrumDataGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sdgb.build.ctx, "GroupBy")
	if err := sdgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SpectrumDataQuery, *SpectrumDataGroupBy](ctx, sdgb.build, sdgb, sdgb.build.inters, v)
}

func (sdgb *SpectrumDataGroupBy) sqlScan(ctx context.Context, root *SpectrumDataQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(sdgb.fns))
	for _, fn := range sdgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*sdgb.flds)+len(sdgb.fns))
		for _, f := range *sdgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*sdgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sdgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// SpectrumDataSelect is the builder for selecting fields of SpectrumData entities.
type SpectrumDataSelect struct {
	*SpectrumDataQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (sds *SpectrumDataSelect) Aggregate(fns ...AggregateFunc) *SpectrumDataSelect {
	sds.fns = append(sds.fns, fns...)
	return sds
}

// Scan applies the selector query and scans the result into the given value.
func (sds *SpectrumDataSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, sds.ctx, "Select")
	if err := sds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*SpectrumDataQuery, *SpectrumDataSelect](ctx, sds.SpectrumDataQuery, sds, sds.inters, v)
}

func (sds *SpectrumDataSelect) sqlScan(ctx context.Context, root *SpectrumDataQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(sds.fns))
	for _, fn := range sds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*sds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := sds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
