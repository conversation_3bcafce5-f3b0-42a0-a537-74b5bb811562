// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudFlowDataUpdate is the builder for updating CloudFlowData entities.
type CloudFlowDataUpdate struct {
	config
	hooks    []Hook
	mutation *CloudFlowDataMutation
}

// Where appends a list predicates to the CloudFlowDataUpdate builder.
func (cfdu *CloudFlowDataUpdate) Where(ps ...predicate.CloudFlowData) *CloudFlowDataUpdate {
	cfdu.mutation.Where(ps...)
	return cfdu
}

// SetTenantID sets the "tenant_id" field.
func (cfdu *CloudFlowDataUpdate) SetTenantID(i int) *CloudFlowDataUpdate {
	cfdu.mutation.SetTenantID(i)
	return cfdu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableTenantID(i *int) *CloudFlowDataUpdate {
	if i != nil {
		cfdu.SetTenantID(*i)
	}
	return cfdu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (cfdu *CloudFlowDataUpdate) ClearTenantID() *CloudFlowDataUpdate {
	cfdu.mutation.ClearTenantID()
	return cfdu
}

// SetUpdatedAt sets the "updated_at" field.
func (cfdu *CloudFlowDataUpdate) SetUpdatedAt(t time.Time) *CloudFlowDataUpdate {
	cfdu.mutation.SetUpdatedAt(t)
	return cfdu
}

// SetRemark sets the "remark" field.
func (cfdu *CloudFlowDataUpdate) SetRemark(s string) *CloudFlowDataUpdate {
	cfdu.mutation.SetRemark(s)
	return cfdu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableRemark(s *string) *CloudFlowDataUpdate {
	if s != nil {
		cfdu.SetRemark(*s)
	}
	return cfdu
}

// ClearRemark clears the value of the "remark" field.
func (cfdu *CloudFlowDataUpdate) ClearRemark() *CloudFlowDataUpdate {
	cfdu.mutation.ClearRemark()
	return cfdu
}

// SetCloudAlertID sets the "cloud_alert_id" field.
func (cfdu *CloudFlowDataUpdate) SetCloudAlertID(i int) *CloudFlowDataUpdate {
	cfdu.mutation.SetCloudAlertID(i)
	return cfdu
}

// SetNillableCloudAlertID sets the "cloud_alert_id" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableCloudAlertID(i *int) *CloudFlowDataUpdate {
	if i != nil {
		cfdu.SetCloudAlertID(*i)
	}
	return cfdu
}

// ClearCloudAlertID clears the value of the "cloud_alert_id" field.
func (cfdu *CloudFlowDataUpdate) ClearCloudAlertID() *CloudFlowDataUpdate {
	cfdu.mutation.ClearCloudAlertID()
	return cfdu
}

// SetSrcIP sets the "src_ip" field.
func (cfdu *CloudFlowDataUpdate) SetSrcIP(s string) *CloudFlowDataUpdate {
	cfdu.mutation.SetSrcIP(s)
	return cfdu
}

// SetNillableSrcIP sets the "src_ip" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableSrcIP(s *string) *CloudFlowDataUpdate {
	if s != nil {
		cfdu.SetSrcIP(*s)
	}
	return cfdu
}

// SetSrcPort sets the "src_port" field.
func (cfdu *CloudFlowDataUpdate) SetSrcPort(i int) *CloudFlowDataUpdate {
	cfdu.mutation.ResetSrcPort()
	cfdu.mutation.SetSrcPort(i)
	return cfdu
}

// SetNillableSrcPort sets the "src_port" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableSrcPort(i *int) *CloudFlowDataUpdate {
	if i != nil {
		cfdu.SetSrcPort(*i)
	}
	return cfdu
}

// AddSrcPort adds i to the "src_port" field.
func (cfdu *CloudFlowDataUpdate) AddSrcPort(i int) *CloudFlowDataUpdate {
	cfdu.mutation.AddSrcPort(i)
	return cfdu
}

// SetDstIP sets the "dst_ip" field.
func (cfdu *CloudFlowDataUpdate) SetDstIP(s string) *CloudFlowDataUpdate {
	cfdu.mutation.SetDstIP(s)
	return cfdu
}

// SetNillableDstIP sets the "dst_ip" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableDstIP(s *string) *CloudFlowDataUpdate {
	if s != nil {
		cfdu.SetDstIP(*s)
	}
	return cfdu
}

// SetDstPort sets the "dst_port" field.
func (cfdu *CloudFlowDataUpdate) SetDstPort(i int) *CloudFlowDataUpdate {
	cfdu.mutation.ResetDstPort()
	cfdu.mutation.SetDstPort(i)
	return cfdu
}

// SetNillableDstPort sets the "dst_port" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableDstPort(i *int) *CloudFlowDataUpdate {
	if i != nil {
		cfdu.SetDstPort(*i)
	}
	return cfdu
}

// AddDstPort adds i to the "dst_port" field.
func (cfdu *CloudFlowDataUpdate) AddDstPort(i int) *CloudFlowDataUpdate {
	cfdu.mutation.AddDstPort(i)
	return cfdu
}

// SetProtocol sets the "protocol" field.
func (cfdu *CloudFlowDataUpdate) SetProtocol(i int) *CloudFlowDataUpdate {
	cfdu.mutation.ResetProtocol()
	cfdu.mutation.SetProtocol(i)
	return cfdu
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableProtocol(i *int) *CloudFlowDataUpdate {
	if i != nil {
		cfdu.SetProtocol(*i)
	}
	return cfdu
}

// AddProtocol adds i to the "protocol" field.
func (cfdu *CloudFlowDataUpdate) AddProtocol(i int) *CloudFlowDataUpdate {
	cfdu.mutation.AddProtocol(i)
	return cfdu
}

// SetMaxAttackPps sets the "max_attack_pps" field.
func (cfdu *CloudFlowDataUpdate) SetMaxAttackPps(i int64) *CloudFlowDataUpdate {
	cfdu.mutation.ResetMaxAttackPps()
	cfdu.mutation.SetMaxAttackPps(i)
	return cfdu
}

// SetNillableMaxAttackPps sets the "max_attack_pps" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableMaxAttackPps(i *int64) *CloudFlowDataUpdate {
	if i != nil {
		cfdu.SetMaxAttackPps(*i)
	}
	return cfdu
}

// AddMaxAttackPps adds i to the "max_attack_pps" field.
func (cfdu *CloudFlowDataUpdate) AddMaxAttackPps(i int64) *CloudFlowDataUpdate {
	cfdu.mutation.AddMaxAttackPps(i)
	return cfdu
}

// SetFlowOverMaxPpsCount sets the "flow_over_max_pps_count" field.
func (cfdu *CloudFlowDataUpdate) SetFlowOverMaxPpsCount(i int) *CloudFlowDataUpdate {
	cfdu.mutation.ResetFlowOverMaxPpsCount()
	cfdu.mutation.SetFlowOverMaxPpsCount(i)
	return cfdu
}

// SetNillableFlowOverMaxPpsCount sets the "flow_over_max_pps_count" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableFlowOverMaxPpsCount(i *int) *CloudFlowDataUpdate {
	if i != nil {
		cfdu.SetFlowOverMaxPpsCount(*i)
	}
	return cfdu
}

// AddFlowOverMaxPpsCount adds i to the "flow_over_max_pps_count" field.
func (cfdu *CloudFlowDataUpdate) AddFlowOverMaxPpsCount(i int) *CloudFlowDataUpdate {
	cfdu.mutation.AddFlowOverMaxPpsCount(i)
	return cfdu
}

// SetStartTime sets the "start_time" field.
func (cfdu *CloudFlowDataUpdate) SetStartTime(t time.Time) *CloudFlowDataUpdate {
	cfdu.mutation.SetStartTime(t)
	return cfdu
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableStartTime(t *time.Time) *CloudFlowDataUpdate {
	if t != nil {
		cfdu.SetStartTime(*t)
	}
	return cfdu
}

// SetEndTime sets the "end_time" field.
func (cfdu *CloudFlowDataUpdate) SetEndTime(t time.Time) *CloudFlowDataUpdate {
	cfdu.mutation.SetEndTime(t)
	return cfdu
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (cfdu *CloudFlowDataUpdate) SetNillableEndTime(t *time.Time) *CloudFlowDataUpdate {
	if t != nil {
		cfdu.SetEndTime(*t)
	}
	return cfdu
}

// ClearEndTime clears the value of the "end_time" field.
func (cfdu *CloudFlowDataUpdate) ClearEndTime() *CloudFlowDataUpdate {
	cfdu.mutation.ClearEndTime()
	return cfdu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cfdu *CloudFlowDataUpdate) SetTenant(t *Tenant) *CloudFlowDataUpdate {
	return cfdu.SetTenantID(t.ID)
}

// SetCloudAlert sets the "cloud_alert" edge to the CloudAlert entity.
func (cfdu *CloudFlowDataUpdate) SetCloudAlert(c *CloudAlert) *CloudFlowDataUpdate {
	return cfdu.SetCloudAlertID(c.ID)
}

// Mutation returns the CloudFlowDataMutation object of the builder.
func (cfdu *CloudFlowDataUpdate) Mutation() *CloudFlowDataMutation {
	return cfdu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (cfdu *CloudFlowDataUpdate) ClearTenant() *CloudFlowDataUpdate {
	cfdu.mutation.ClearTenant()
	return cfdu
}

// ClearCloudAlert clears the "cloud_alert" edge to the CloudAlert entity.
func (cfdu *CloudFlowDataUpdate) ClearCloudAlert() *CloudFlowDataUpdate {
	cfdu.mutation.ClearCloudAlert()
	return cfdu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cfdu *CloudFlowDataUpdate) Save(ctx context.Context) (int, error) {
	if err := cfdu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, cfdu.sqlSave, cfdu.mutation, cfdu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cfdu *CloudFlowDataUpdate) SaveX(ctx context.Context) int {
	affected, err := cfdu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cfdu *CloudFlowDataUpdate) Exec(ctx context.Context) error {
	_, err := cfdu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cfdu *CloudFlowDataUpdate) ExecX(ctx context.Context) {
	if err := cfdu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cfdu *CloudFlowDataUpdate) defaults() error {
	if _, ok := cfdu.mutation.UpdatedAt(); !ok {
		if cloudflowdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudflowdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudflowdata.UpdateDefaultUpdatedAt()
		cfdu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cfdu *CloudFlowDataUpdate) check() error {
	if v, ok := cfdu.mutation.Remark(); ok {
		if err := cloudflowdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudFlowData.remark": %w`, err)}
		}
	}
	return nil
}

func (cfdu *CloudFlowDataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := cfdu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(cloudflowdata.Table, cloudflowdata.Columns, sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt))
	if ps := cfdu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cfdu.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudflowdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := cfdu.mutation.Remark(); ok {
		_spec.SetField(cloudflowdata.FieldRemark, field.TypeString, value)
	}
	if cfdu.mutation.RemarkCleared() {
		_spec.ClearField(cloudflowdata.FieldRemark, field.TypeString)
	}
	if value, ok := cfdu.mutation.SrcIP(); ok {
		_spec.SetField(cloudflowdata.FieldSrcIP, field.TypeString, value)
	}
	if value, ok := cfdu.mutation.SrcPort(); ok {
		_spec.SetField(cloudflowdata.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cfdu.mutation.AddedSrcPort(); ok {
		_spec.AddField(cloudflowdata.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cfdu.mutation.DstIP(); ok {
		_spec.SetField(cloudflowdata.FieldDstIP, field.TypeString, value)
	}
	if value, ok := cfdu.mutation.DstPort(); ok {
		_spec.SetField(cloudflowdata.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cfdu.mutation.AddedDstPort(); ok {
		_spec.AddField(cloudflowdata.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cfdu.mutation.Protocol(); ok {
		_spec.SetField(cloudflowdata.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cfdu.mutation.AddedProtocol(); ok {
		_spec.AddField(cloudflowdata.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cfdu.mutation.MaxAttackPps(); ok {
		_spec.SetField(cloudflowdata.FieldMaxAttackPps, field.TypeInt64, value)
	}
	if value, ok := cfdu.mutation.AddedMaxAttackPps(); ok {
		_spec.AddField(cloudflowdata.FieldMaxAttackPps, field.TypeInt64, value)
	}
	if value, ok := cfdu.mutation.FlowOverMaxPpsCount(); ok {
		_spec.SetField(cloudflowdata.FieldFlowOverMaxPpsCount, field.TypeInt, value)
	}
	if value, ok := cfdu.mutation.AddedFlowOverMaxPpsCount(); ok {
		_spec.AddField(cloudflowdata.FieldFlowOverMaxPpsCount, field.TypeInt, value)
	}
	if value, ok := cfdu.mutation.StartTime(); ok {
		_spec.SetField(cloudflowdata.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := cfdu.mutation.EndTime(); ok {
		_spec.SetField(cloudflowdata.FieldEndTime, field.TypeTime, value)
	}
	if cfdu.mutation.EndTimeCleared() {
		_spec.ClearField(cloudflowdata.FieldEndTime, field.TypeTime)
	}
	if cfdu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudflowdata.TenantTable,
			Columns: []string{cloudflowdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cfdu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudflowdata.TenantTable,
			Columns: []string{cloudflowdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if cfdu.mutation.CloudAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cloudflowdata.CloudAlertTable,
			Columns: []string{cloudflowdata.CloudAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cfdu.mutation.CloudAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cloudflowdata.CloudAlertTable,
			Columns: []string{cloudflowdata.CloudAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cfdu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cloudflowdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cfdu.mutation.done = true
	return n, nil
}

// CloudFlowDataUpdateOne is the builder for updating a single CloudFlowData entity.
type CloudFlowDataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CloudFlowDataMutation
}

// SetTenantID sets the "tenant_id" field.
func (cfduo *CloudFlowDataUpdateOne) SetTenantID(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.SetTenantID(i)
	return cfduo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableTenantID(i *int) *CloudFlowDataUpdateOne {
	if i != nil {
		cfduo.SetTenantID(*i)
	}
	return cfduo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (cfduo *CloudFlowDataUpdateOne) ClearTenantID() *CloudFlowDataUpdateOne {
	cfduo.mutation.ClearTenantID()
	return cfduo
}

// SetUpdatedAt sets the "updated_at" field.
func (cfduo *CloudFlowDataUpdateOne) SetUpdatedAt(t time.Time) *CloudFlowDataUpdateOne {
	cfduo.mutation.SetUpdatedAt(t)
	return cfduo
}

// SetRemark sets the "remark" field.
func (cfduo *CloudFlowDataUpdateOne) SetRemark(s string) *CloudFlowDataUpdateOne {
	cfduo.mutation.SetRemark(s)
	return cfduo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableRemark(s *string) *CloudFlowDataUpdateOne {
	if s != nil {
		cfduo.SetRemark(*s)
	}
	return cfduo
}

// ClearRemark clears the value of the "remark" field.
func (cfduo *CloudFlowDataUpdateOne) ClearRemark() *CloudFlowDataUpdateOne {
	cfduo.mutation.ClearRemark()
	return cfduo
}

// SetCloudAlertID sets the "cloud_alert_id" field.
func (cfduo *CloudFlowDataUpdateOne) SetCloudAlertID(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.SetCloudAlertID(i)
	return cfduo
}

// SetNillableCloudAlertID sets the "cloud_alert_id" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableCloudAlertID(i *int) *CloudFlowDataUpdateOne {
	if i != nil {
		cfduo.SetCloudAlertID(*i)
	}
	return cfduo
}

// ClearCloudAlertID clears the value of the "cloud_alert_id" field.
func (cfduo *CloudFlowDataUpdateOne) ClearCloudAlertID() *CloudFlowDataUpdateOne {
	cfduo.mutation.ClearCloudAlertID()
	return cfduo
}

// SetSrcIP sets the "src_ip" field.
func (cfduo *CloudFlowDataUpdateOne) SetSrcIP(s string) *CloudFlowDataUpdateOne {
	cfduo.mutation.SetSrcIP(s)
	return cfduo
}

// SetNillableSrcIP sets the "src_ip" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableSrcIP(s *string) *CloudFlowDataUpdateOne {
	if s != nil {
		cfduo.SetSrcIP(*s)
	}
	return cfduo
}

// SetSrcPort sets the "src_port" field.
func (cfduo *CloudFlowDataUpdateOne) SetSrcPort(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.ResetSrcPort()
	cfduo.mutation.SetSrcPort(i)
	return cfduo
}

// SetNillableSrcPort sets the "src_port" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableSrcPort(i *int) *CloudFlowDataUpdateOne {
	if i != nil {
		cfduo.SetSrcPort(*i)
	}
	return cfduo
}

// AddSrcPort adds i to the "src_port" field.
func (cfduo *CloudFlowDataUpdateOne) AddSrcPort(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.AddSrcPort(i)
	return cfduo
}

// SetDstIP sets the "dst_ip" field.
func (cfduo *CloudFlowDataUpdateOne) SetDstIP(s string) *CloudFlowDataUpdateOne {
	cfduo.mutation.SetDstIP(s)
	return cfduo
}

// SetNillableDstIP sets the "dst_ip" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableDstIP(s *string) *CloudFlowDataUpdateOne {
	if s != nil {
		cfduo.SetDstIP(*s)
	}
	return cfduo
}

// SetDstPort sets the "dst_port" field.
func (cfduo *CloudFlowDataUpdateOne) SetDstPort(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.ResetDstPort()
	cfduo.mutation.SetDstPort(i)
	return cfduo
}

// SetNillableDstPort sets the "dst_port" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableDstPort(i *int) *CloudFlowDataUpdateOne {
	if i != nil {
		cfduo.SetDstPort(*i)
	}
	return cfduo
}

// AddDstPort adds i to the "dst_port" field.
func (cfduo *CloudFlowDataUpdateOne) AddDstPort(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.AddDstPort(i)
	return cfduo
}

// SetProtocol sets the "protocol" field.
func (cfduo *CloudFlowDataUpdateOne) SetProtocol(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.ResetProtocol()
	cfduo.mutation.SetProtocol(i)
	return cfduo
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableProtocol(i *int) *CloudFlowDataUpdateOne {
	if i != nil {
		cfduo.SetProtocol(*i)
	}
	return cfduo
}

// AddProtocol adds i to the "protocol" field.
func (cfduo *CloudFlowDataUpdateOne) AddProtocol(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.AddProtocol(i)
	return cfduo
}

// SetMaxAttackPps sets the "max_attack_pps" field.
func (cfduo *CloudFlowDataUpdateOne) SetMaxAttackPps(i int64) *CloudFlowDataUpdateOne {
	cfduo.mutation.ResetMaxAttackPps()
	cfduo.mutation.SetMaxAttackPps(i)
	return cfduo
}

// SetNillableMaxAttackPps sets the "max_attack_pps" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableMaxAttackPps(i *int64) *CloudFlowDataUpdateOne {
	if i != nil {
		cfduo.SetMaxAttackPps(*i)
	}
	return cfduo
}

// AddMaxAttackPps adds i to the "max_attack_pps" field.
func (cfduo *CloudFlowDataUpdateOne) AddMaxAttackPps(i int64) *CloudFlowDataUpdateOne {
	cfduo.mutation.AddMaxAttackPps(i)
	return cfduo
}

// SetFlowOverMaxPpsCount sets the "flow_over_max_pps_count" field.
func (cfduo *CloudFlowDataUpdateOne) SetFlowOverMaxPpsCount(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.ResetFlowOverMaxPpsCount()
	cfduo.mutation.SetFlowOverMaxPpsCount(i)
	return cfduo
}

// SetNillableFlowOverMaxPpsCount sets the "flow_over_max_pps_count" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableFlowOverMaxPpsCount(i *int) *CloudFlowDataUpdateOne {
	if i != nil {
		cfduo.SetFlowOverMaxPpsCount(*i)
	}
	return cfduo
}

// AddFlowOverMaxPpsCount adds i to the "flow_over_max_pps_count" field.
func (cfduo *CloudFlowDataUpdateOne) AddFlowOverMaxPpsCount(i int) *CloudFlowDataUpdateOne {
	cfduo.mutation.AddFlowOverMaxPpsCount(i)
	return cfduo
}

// SetStartTime sets the "start_time" field.
func (cfduo *CloudFlowDataUpdateOne) SetStartTime(t time.Time) *CloudFlowDataUpdateOne {
	cfduo.mutation.SetStartTime(t)
	return cfduo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableStartTime(t *time.Time) *CloudFlowDataUpdateOne {
	if t != nil {
		cfduo.SetStartTime(*t)
	}
	return cfduo
}

// SetEndTime sets the "end_time" field.
func (cfduo *CloudFlowDataUpdateOne) SetEndTime(t time.Time) *CloudFlowDataUpdateOne {
	cfduo.mutation.SetEndTime(t)
	return cfduo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (cfduo *CloudFlowDataUpdateOne) SetNillableEndTime(t *time.Time) *CloudFlowDataUpdateOne {
	if t != nil {
		cfduo.SetEndTime(*t)
	}
	return cfduo
}

// ClearEndTime clears the value of the "end_time" field.
func (cfduo *CloudFlowDataUpdateOne) ClearEndTime() *CloudFlowDataUpdateOne {
	cfduo.mutation.ClearEndTime()
	return cfduo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cfduo *CloudFlowDataUpdateOne) SetTenant(t *Tenant) *CloudFlowDataUpdateOne {
	return cfduo.SetTenantID(t.ID)
}

// SetCloudAlert sets the "cloud_alert" edge to the CloudAlert entity.
func (cfduo *CloudFlowDataUpdateOne) SetCloudAlert(c *CloudAlert) *CloudFlowDataUpdateOne {
	return cfduo.SetCloudAlertID(c.ID)
}

// Mutation returns the CloudFlowDataMutation object of the builder.
func (cfduo *CloudFlowDataUpdateOne) Mutation() *CloudFlowDataMutation {
	return cfduo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (cfduo *CloudFlowDataUpdateOne) ClearTenant() *CloudFlowDataUpdateOne {
	cfduo.mutation.ClearTenant()
	return cfduo
}

// ClearCloudAlert clears the "cloud_alert" edge to the CloudAlert entity.
func (cfduo *CloudFlowDataUpdateOne) ClearCloudAlert() *CloudFlowDataUpdateOne {
	cfduo.mutation.ClearCloudAlert()
	return cfduo
}

// Where appends a list predicates to the CloudFlowDataUpdate builder.
func (cfduo *CloudFlowDataUpdateOne) Where(ps ...predicate.CloudFlowData) *CloudFlowDataUpdateOne {
	cfduo.mutation.Where(ps...)
	return cfduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cfduo *CloudFlowDataUpdateOne) Select(field string, fields ...string) *CloudFlowDataUpdateOne {
	cfduo.fields = append([]string{field}, fields...)
	return cfduo
}

// Save executes the query and returns the updated CloudFlowData entity.
func (cfduo *CloudFlowDataUpdateOne) Save(ctx context.Context) (*CloudFlowData, error) {
	if err := cfduo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, cfduo.sqlSave, cfduo.mutation, cfduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cfduo *CloudFlowDataUpdateOne) SaveX(ctx context.Context) *CloudFlowData {
	node, err := cfduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cfduo *CloudFlowDataUpdateOne) Exec(ctx context.Context) error {
	_, err := cfduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cfduo *CloudFlowDataUpdateOne) ExecX(ctx context.Context) {
	if err := cfduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cfduo *CloudFlowDataUpdateOne) defaults() error {
	if _, ok := cfduo.mutation.UpdatedAt(); !ok {
		if cloudflowdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudflowdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudflowdata.UpdateDefaultUpdatedAt()
		cfduo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cfduo *CloudFlowDataUpdateOne) check() error {
	if v, ok := cfduo.mutation.Remark(); ok {
		if err := cloudflowdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudFlowData.remark": %w`, err)}
		}
	}
	return nil
}

func (cfduo *CloudFlowDataUpdateOne) sqlSave(ctx context.Context) (_node *CloudFlowData, err error) {
	if err := cfduo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(cloudflowdata.Table, cloudflowdata.Columns, sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt))
	id, ok := cfduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CloudFlowData.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cfduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cloudflowdata.FieldID)
		for _, f := range fields {
			if !cloudflowdata.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != cloudflowdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cfduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cfduo.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudflowdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := cfduo.mutation.Remark(); ok {
		_spec.SetField(cloudflowdata.FieldRemark, field.TypeString, value)
	}
	if cfduo.mutation.RemarkCleared() {
		_spec.ClearField(cloudflowdata.FieldRemark, field.TypeString)
	}
	if value, ok := cfduo.mutation.SrcIP(); ok {
		_spec.SetField(cloudflowdata.FieldSrcIP, field.TypeString, value)
	}
	if value, ok := cfduo.mutation.SrcPort(); ok {
		_spec.SetField(cloudflowdata.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cfduo.mutation.AddedSrcPort(); ok {
		_spec.AddField(cloudflowdata.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cfduo.mutation.DstIP(); ok {
		_spec.SetField(cloudflowdata.FieldDstIP, field.TypeString, value)
	}
	if value, ok := cfduo.mutation.DstPort(); ok {
		_spec.SetField(cloudflowdata.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cfduo.mutation.AddedDstPort(); ok {
		_spec.AddField(cloudflowdata.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cfduo.mutation.Protocol(); ok {
		_spec.SetField(cloudflowdata.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cfduo.mutation.AddedProtocol(); ok {
		_spec.AddField(cloudflowdata.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cfduo.mutation.MaxAttackPps(); ok {
		_spec.SetField(cloudflowdata.FieldMaxAttackPps, field.TypeInt64, value)
	}
	if value, ok := cfduo.mutation.AddedMaxAttackPps(); ok {
		_spec.AddField(cloudflowdata.FieldMaxAttackPps, field.TypeInt64, value)
	}
	if value, ok := cfduo.mutation.FlowOverMaxPpsCount(); ok {
		_spec.SetField(cloudflowdata.FieldFlowOverMaxPpsCount, field.TypeInt, value)
	}
	if value, ok := cfduo.mutation.AddedFlowOverMaxPpsCount(); ok {
		_spec.AddField(cloudflowdata.FieldFlowOverMaxPpsCount, field.TypeInt, value)
	}
	if value, ok := cfduo.mutation.StartTime(); ok {
		_spec.SetField(cloudflowdata.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := cfduo.mutation.EndTime(); ok {
		_spec.SetField(cloudflowdata.FieldEndTime, field.TypeTime, value)
	}
	if cfduo.mutation.EndTimeCleared() {
		_spec.ClearField(cloudflowdata.FieldEndTime, field.TypeTime)
	}
	if cfduo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudflowdata.TenantTable,
			Columns: []string{cloudflowdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cfduo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudflowdata.TenantTable,
			Columns: []string{cloudflowdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if cfduo.mutation.CloudAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cloudflowdata.CloudAlertTable,
			Columns: []string{cloudflowdata.CloudAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cfduo.mutation.CloudAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cloudflowdata.CloudAlertTable,
			Columns: []string{cloudflowdata.CloudAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &CloudFlowData{config: cfduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cfduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cloudflowdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cfduo.mutation.done = true
	return _node, nil
}
