// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/systemapi"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SystemApiDelete is the builder for deleting a SystemApi entity.
type SystemApiDelete struct {
	config
	hooks    []Hook
	mutation *SystemApiMutation
}

// Where appends a list predicates to the SystemApiDelete builder.
func (sad *SystemApiDelete) Where(ps ...predicate.SystemApi) *SystemApiDelete {
	sad.mutation.Where(ps...)
	return sad
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (sad *SystemApiDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, sad.sqlExec, sad.mutation, sad.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (sad *SystemApiDelete) ExecX(ctx context.Context) int {
	n, err := sad.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (sad *SystemApiDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(systemapi.Table, sqlgraph.NewFieldSpec(systemapi.FieldID, field.TypeInt))
	if ps := sad.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, sad.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	sad.mutation.done = true
	return affected, err
}

// SystemApiDeleteOne is the builder for deleting a single SystemApi entity.
type SystemApiDeleteOne struct {
	sad *SystemApiDelete
}

// Where appends a list predicates to the SystemApiDelete builder.
func (sado *SystemApiDeleteOne) Where(ps ...predicate.SystemApi) *SystemApiDeleteOne {
	sado.sad.mutation.Where(ps...)
	return sado
}

// Exec executes the deletion query.
func (sado *SystemApiDeleteOne) Exec(ctx context.Context) error {
	n, err := sado.sad.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{systemapi.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (sado *SystemApiDeleteOne) ExecX(ctx context.Context) {
	if err := sado.Exec(ctx); err != nil {
		panic(err)
	}
}
