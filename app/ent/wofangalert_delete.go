// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/wofangalert"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WofangAlertDelete is the builder for deleting a WofangAlert entity.
type WofangAlertDelete struct {
	config
	hooks    []Hook
	mutation *WofangAlertMutation
}

// Where appends a list predicates to the WofangAlertDelete builder.
func (wad *WofangAlertDelete) Where(ps ...predicate.WofangAlert) *WofangAlertDelete {
	wad.mutation.Where(ps...)
	return wad
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (wad *WofangAlertDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, wad.sqlExec, wad.mutation, wad.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (wad *WofangAlertDelete) ExecX(ctx context.Context) int {
	n, err := wad.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (wad *WofangAlertDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(wofangalert.Table, sqlgraph.NewFieldSpec(wofangalert.FieldID, field.TypeInt))
	if ps := wad.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, wad.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	wad.mutation.done = true
	return affected, err
}

// WofangAlertDeleteOne is the builder for deleting a single WofangAlert entity.
type WofangAlertDeleteOne struct {
	wad *WofangAlertDelete
}

// Where appends a list predicates to the WofangAlertDelete builder.
func (wado *WofangAlertDeleteOne) Where(ps ...predicate.WofangAlert) *WofangAlertDeleteOne {
	wado.wad.mutation.Where(ps...)
	return wado
}

// Exec executes the deletion query.
func (wado *WofangAlertDeleteOne) Exec(ctx context.Context) error {
	n, err := wado.wad.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{wofangalert.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (wado *WofangAlertDeleteOne) ExecX(ctx context.Context) {
	if err := wado.Exec(ctx); err != nil {
		panic(err)
	}
}
