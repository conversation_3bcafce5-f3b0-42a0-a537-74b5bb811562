// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/tenant"
	"meta/app/ent/wofangalert"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WofangAlertCreate is the builder for creating a WofangAlert entity.
type WofangAlertCreate struct {
	config
	mutation *WofangAlertMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (wac *WofangAlertCreate) SetTenantID(i int) *WofangAlertCreate {
	wac.mutation.SetTenantID(i)
	return wac
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (wac *WofangAlertCreate) SetNillableTenantID(i *int) *WofangAlertCreate {
	if i != nil {
		wac.SetTenantID(*i)
	}
	return wac
}

// SetCreatedAt sets the "created_at" field.
func (wac *WofangAlertCreate) SetCreatedAt(t time.Time) *WofangAlertCreate {
	wac.mutation.SetCreatedAt(t)
	return wac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wac *WofangAlertCreate) SetNillableCreatedAt(t *time.Time) *WofangAlertCreate {
	if t != nil {
		wac.SetCreatedAt(*t)
	}
	return wac
}

// SetUpdatedAt sets the "updated_at" field.
func (wac *WofangAlertCreate) SetUpdatedAt(t time.Time) *WofangAlertCreate {
	wac.mutation.SetUpdatedAt(t)
	return wac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wac *WofangAlertCreate) SetNillableUpdatedAt(t *time.Time) *WofangAlertCreate {
	if t != nil {
		wac.SetUpdatedAt(*t)
	}
	return wac
}

// SetRemark sets the "remark" field.
func (wac *WofangAlertCreate) SetRemark(s string) *WofangAlertCreate {
	wac.mutation.SetRemark(s)
	return wac
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wac *WofangAlertCreate) SetNillableRemark(s *string) *WofangAlertCreate {
	if s != nil {
		wac.SetRemark(*s)
	}
	return wac
}

// SetAttackStatus sets the "attack_status" field.
func (wac *WofangAlertCreate) SetAttackStatus(i int) *WofangAlertCreate {
	wac.mutation.SetAttackStatus(i)
	return wac
}

// SetAttackType sets the "attack_type" field.
func (wac *WofangAlertCreate) SetAttackType(s *[]string) *WofangAlertCreate {
	wac.mutation.SetAttackType(s)
	return wac
}

// SetDeviceIP sets the "device_ip" field.
func (wac *WofangAlertCreate) SetDeviceIP(s string) *WofangAlertCreate {
	wac.mutation.SetDeviceIP(s)
	return wac
}

// SetZoneIP sets the "zone_ip" field.
func (wac *WofangAlertCreate) SetZoneIP(s string) *WofangAlertCreate {
	wac.mutation.SetZoneIP(s)
	return wac
}

// SetAttackID sets the "attack_id" field.
func (wac *WofangAlertCreate) SetAttackID(i int) *WofangAlertCreate {
	wac.mutation.SetAttackID(i)
	return wac
}

// SetStartTime sets the "start_time" field.
func (wac *WofangAlertCreate) SetStartTime(t time.Time) *WofangAlertCreate {
	wac.mutation.SetStartTime(t)
	return wac
}

// SetEndTime sets the "end_time" field.
func (wac *WofangAlertCreate) SetEndTime(t time.Time) *WofangAlertCreate {
	wac.mutation.SetEndTime(t)
	return wac
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (wac *WofangAlertCreate) SetNillableEndTime(t *time.Time) *WofangAlertCreate {
	if t != nil {
		wac.SetEndTime(*t)
	}
	return wac
}

// SetMaxDropBps sets the "max_drop_bps" field.
func (wac *WofangAlertCreate) SetMaxDropBps(i int64) *WofangAlertCreate {
	wac.mutation.SetMaxDropBps(i)
	return wac
}

// SetMaxInBps sets the "max_in_bps" field.
func (wac *WofangAlertCreate) SetMaxInBps(i int64) *WofangAlertCreate {
	wac.mutation.SetMaxInBps(i)
	return wac
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (wac *WofangAlertCreate) SetTenant(t *Tenant) *WofangAlertCreate {
	return wac.SetTenantID(t.ID)
}

// Mutation returns the WofangAlertMutation object of the builder.
func (wac *WofangAlertCreate) Mutation() *WofangAlertMutation {
	return wac.mutation
}

// Save creates the WofangAlert in the database.
func (wac *WofangAlertCreate) Save(ctx context.Context) (*WofangAlert, error) {
	if err := wac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, wac.sqlSave, wac.mutation, wac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wac *WofangAlertCreate) SaveX(ctx context.Context) *WofangAlert {
	v, err := wac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wac *WofangAlertCreate) Exec(ctx context.Context) error {
	_, err := wac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wac *WofangAlertCreate) ExecX(ctx context.Context) {
	if err := wac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wac *WofangAlertCreate) defaults() error {
	if _, ok := wac.mutation.CreatedAt(); !ok {
		if wofangalert.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized wofangalert.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := wofangalert.DefaultCreatedAt()
		wac.mutation.SetCreatedAt(v)
	}
	if _, ok := wac.mutation.UpdatedAt(); !ok {
		if wofangalert.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized wofangalert.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := wofangalert.DefaultUpdatedAt()
		wac.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (wac *WofangAlertCreate) check() error {
	if _, ok := wac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "WofangAlert.created_at"`)}
	}
	if _, ok := wac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "WofangAlert.updated_at"`)}
	}
	if v, ok := wac.mutation.Remark(); ok {
		if err := wofangalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "WofangAlert.remark": %w`, err)}
		}
	}
	if _, ok := wac.mutation.AttackStatus(); !ok {
		return &ValidationError{Name: "attack_status", err: errors.New(`ent: missing required field "WofangAlert.attack_status"`)}
	}
	if _, ok := wac.mutation.DeviceIP(); !ok {
		return &ValidationError{Name: "device_ip", err: errors.New(`ent: missing required field "WofangAlert.device_ip"`)}
	}
	if _, ok := wac.mutation.ZoneIP(); !ok {
		return &ValidationError{Name: "zone_ip", err: errors.New(`ent: missing required field "WofangAlert.zone_ip"`)}
	}
	if _, ok := wac.mutation.AttackID(); !ok {
		return &ValidationError{Name: "attack_id", err: errors.New(`ent: missing required field "WofangAlert.attack_id"`)}
	}
	if _, ok := wac.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "WofangAlert.start_time"`)}
	}
	if _, ok := wac.mutation.MaxDropBps(); !ok {
		return &ValidationError{Name: "max_drop_bps", err: errors.New(`ent: missing required field "WofangAlert.max_drop_bps"`)}
	}
	if _, ok := wac.mutation.MaxInBps(); !ok {
		return &ValidationError{Name: "max_in_bps", err: errors.New(`ent: missing required field "WofangAlert.max_in_bps"`)}
	}
	return nil
}

func (wac *WofangAlertCreate) sqlSave(ctx context.Context) (*WofangAlert, error) {
	if err := wac.check(); err != nil {
		return nil, err
	}
	_node, _spec := wac.createSpec()
	if err := sqlgraph.CreateNode(ctx, wac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	wac.mutation.id = &_node.ID
	wac.mutation.done = true
	return _node, nil
}

func (wac *WofangAlertCreate) createSpec() (*WofangAlert, *sqlgraph.CreateSpec) {
	var (
		_node = &WofangAlert{config: wac.config}
		_spec = sqlgraph.NewCreateSpec(wofangalert.Table, sqlgraph.NewFieldSpec(wofangalert.FieldID, field.TypeInt))
	)
	if value, ok := wac.mutation.CreatedAt(); ok {
		_spec.SetField(wofangalert.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wac.mutation.UpdatedAt(); ok {
		_spec.SetField(wofangalert.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := wac.mutation.Remark(); ok {
		_spec.SetField(wofangalert.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := wac.mutation.AttackStatus(); ok {
		_spec.SetField(wofangalert.FieldAttackStatus, field.TypeInt, value)
		_node.AttackStatus = value
	}
	if value, ok := wac.mutation.AttackType(); ok {
		_spec.SetField(wofangalert.FieldAttackType, field.TypeJSON, value)
		_node.AttackType = value
	}
	if value, ok := wac.mutation.DeviceIP(); ok {
		_spec.SetField(wofangalert.FieldDeviceIP, field.TypeString, value)
		_node.DeviceIP = value
	}
	if value, ok := wac.mutation.ZoneIP(); ok {
		_spec.SetField(wofangalert.FieldZoneIP, field.TypeString, value)
		_node.ZoneIP = value
	}
	if value, ok := wac.mutation.AttackID(); ok {
		_spec.SetField(wofangalert.FieldAttackID, field.TypeInt, value)
		_node.AttackID = value
	}
	if value, ok := wac.mutation.StartTime(); ok {
		_spec.SetField(wofangalert.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := wac.mutation.EndTime(); ok {
		_spec.SetField(wofangalert.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if value, ok := wac.mutation.MaxDropBps(); ok {
		_spec.SetField(wofangalert.FieldMaxDropBps, field.TypeInt64, value)
		_node.MaxDropBps = value
	}
	if value, ok := wac.mutation.MaxInBps(); ok {
		_spec.SetField(wofangalert.FieldMaxInBps, field.TypeInt64, value)
		_node.MaxInBps = value
	}
	if nodes := wac.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofangalert.TenantTable,
			Columns: []string{wofangalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// WofangAlertCreateBulk is the builder for creating many WofangAlert entities in bulk.
type WofangAlertCreateBulk struct {
	config
	err      error
	builders []*WofangAlertCreate
}

// Save creates the WofangAlert entities in the database.
func (wacb *WofangAlertCreateBulk) Save(ctx context.Context) ([]*WofangAlert, error) {
	if wacb.err != nil {
		return nil, wacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wacb.builders))
	nodes := make([]*WofangAlert, len(wacb.builders))
	mutators := make([]Mutator, len(wacb.builders))
	for i := range wacb.builders {
		func(i int, root context.Context) {
			builder := wacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WofangAlertMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wacb *WofangAlertCreateBulk) SaveX(ctx context.Context) []*WofangAlert {
	v, err := wacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wacb *WofangAlertCreateBulk) Exec(ctx context.Context) error {
	_, err := wacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wacb *WofangAlertCreateBulk) ExecX(ctx context.Context) {
	if err := wacb.Exec(ctx); err != nil {
		panic(err)
	}
}
