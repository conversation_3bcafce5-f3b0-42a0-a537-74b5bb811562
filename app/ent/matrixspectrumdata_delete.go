// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumDataDelete is the builder for deleting a MatrixSpectrumData entity.
type MatrixSpectrumDataDelete struct {
	config
	hooks    []Hook
	mutation *MatrixSpectrumDataMutation
}

// Where appends a list predicates to the MatrixSpectrumDataDelete builder.
func (msdd *MatrixSpectrumDataDelete) Where(ps ...predicate.MatrixSpectrumData) *MatrixSpectrumDataDelete {
	msdd.mutation.Where(ps...)
	return msdd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (msdd *MatrixSpectrumDataDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, msdd.sqlExec, msdd.mutation, msdd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (msdd *MatrixSpectrumDataDelete) ExecX(ctx context.Context) int {
	n, err := msdd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (msdd *MatrixSpectrumDataDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(matrixspectrumdata.Table, sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt))
	if ps := msdd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, msdd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	msdd.mutation.done = true
	return affected, err
}

// MatrixSpectrumDataDeleteOne is the builder for deleting a single MatrixSpectrumData entity.
type MatrixSpectrumDataDeleteOne struct {
	msdd *MatrixSpectrumDataDelete
}

// Where appends a list predicates to the MatrixSpectrumDataDelete builder.
func (msddo *MatrixSpectrumDataDeleteOne) Where(ps ...predicate.MatrixSpectrumData) *MatrixSpectrumDataDeleteOne {
	msddo.msdd.mutation.Where(ps...)
	return msddo
}

// Exec executes the deletion query.
func (msddo *MatrixSpectrumDataDeleteOne) Exec(ctx context.Context) error {
	n, err := msddo.msdd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{matrixspectrumdata.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (msddo *MatrixSpectrumDataDeleteOne) ExecX(ctx context.Context) {
	if err := msddo.Exec(ctx); err != nil {
		panic(err)
	}
}
