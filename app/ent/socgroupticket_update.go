// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/socgroupticket"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/entity/netease/socgroup"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SocGroupTicketUpdate is the builder for updating SocGroupTicket entities.
type SocGroupTicketUpdate struct {
	config
	hooks    []Hook
	mutation *SocGroupTicketMutation
}

// Where appends a list predicates to the SocGroupTicketUpdate builder.
func (sgtu *SocGroupTicketUpdate) Where(ps ...predicate.SocGroupTicket) *SocGroupTicketUpdate {
	sgtu.mutation.Where(ps...)
	return sgtu
}

// SetUpdatedAt sets the "updated_at" field.
func (sgtu *SocGroupTicketUpdate) SetUpdatedAt(t time.Time) *SocGroupTicketUpdate {
	sgtu.mutation.SetUpdatedAt(t)
	return sgtu
}

// SetTenantID sets the "tenant_id" field.
func (sgtu *SocGroupTicketUpdate) SetTenantID(i int) *SocGroupTicketUpdate {
	sgtu.mutation.SetTenantID(i)
	return sgtu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableTenantID(i *int) *SocGroupTicketUpdate {
	if i != nil {
		sgtu.SetTenantID(*i)
	}
	return sgtu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (sgtu *SocGroupTicketUpdate) ClearTenantID() *SocGroupTicketUpdate {
	sgtu.mutation.ClearTenantID()
	return sgtu
}

// SetRemark sets the "remark" field.
func (sgtu *SocGroupTicketUpdate) SetRemark(s string) *SocGroupTicketUpdate {
	sgtu.mutation.SetRemark(s)
	return sgtu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableRemark(s *string) *SocGroupTicketUpdate {
	if s != nil {
		sgtu.SetRemark(*s)
	}
	return sgtu
}

// ClearRemark clears the value of the "remark" field.
func (sgtu *SocGroupTicketUpdate) ClearRemark() *SocGroupTicketUpdate {
	sgtu.mutation.ClearRemark()
	return sgtu
}

// SetName sets the "name" field.
func (sgtu *SocGroupTicketUpdate) SetName(s string) *SocGroupTicketUpdate {
	sgtu.mutation.SetName(s)
	return sgtu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableName(s *string) *SocGroupTicketUpdate {
	if s != nil {
		sgtu.SetName(*s)
	}
	return sgtu
}

// SetType sets the "type" field.
func (sgtu *SocGroupTicketUpdate) SetType(s string) *SocGroupTicketUpdate {
	sgtu.mutation.SetType(s)
	return sgtu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableType(s *string) *SocGroupTicketUpdate {
	if s != nil {
		sgtu.SetType(*s)
	}
	return sgtu
}

// SetDescription sets the "description" field.
func (sgtu *SocGroupTicketUpdate) SetDescription(s string) *SocGroupTicketUpdate {
	sgtu.mutation.SetDescription(s)
	return sgtu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableDescription(s *string) *SocGroupTicketUpdate {
	if s != nil {
		sgtu.SetDescription(*s)
	}
	return sgtu
}

// SetFollowList sets the "follow_list" field.
func (sgtu *SocGroupTicketUpdate) SetFollowList(i *[]int) *SocGroupTicketUpdate {
	sgtu.mutation.SetFollowList(i)
	return sgtu
}

// ClearFollowList clears the value of the "follow_list" field.
func (sgtu *SocGroupTicketUpdate) ClearFollowList() *SocGroupTicketUpdate {
	sgtu.mutation.ClearFollowList()
	return sgtu
}

// SetDepartmentID sets the "department_id" field.
func (sgtu *SocGroupTicketUpdate) SetDepartmentID(i int) *SocGroupTicketUpdate {
	sgtu.mutation.ResetDepartmentID()
	sgtu.mutation.SetDepartmentID(i)
	return sgtu
}

// SetNillableDepartmentID sets the "department_id" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableDepartmentID(i *int) *SocGroupTicketUpdate {
	if i != nil {
		sgtu.SetDepartmentID(*i)
	}
	return sgtu
}

// AddDepartmentID adds i to the "department_id" field.
func (sgtu *SocGroupTicketUpdate) AddDepartmentID(i int) *SocGroupTicketUpdate {
	sgtu.mutation.AddDepartmentID(i)
	return sgtu
}

// SetIPList sets the "ip_list" field.
func (sgtu *SocGroupTicketUpdate) SetIPList(s *[]string) *SocGroupTicketUpdate {
	sgtu.mutation.SetIPList(s)
	return sgtu
}

// ClearIPList clears the value of the "ip_list" field.
func (sgtu *SocGroupTicketUpdate) ClearIPList() *SocGroupTicketUpdate {
	sgtu.mutation.ClearIPList()
	return sgtu
}

// SetMinBandwidth sets the "min_bandwidth" field.
func (sgtu *SocGroupTicketUpdate) SetMinBandwidth(f float32) *SocGroupTicketUpdate {
	sgtu.mutation.ResetMinBandwidth()
	sgtu.mutation.SetMinBandwidth(f)
	return sgtu
}

// SetNillableMinBandwidth sets the "min_bandwidth" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableMinBandwidth(f *float32) *SocGroupTicketUpdate {
	if f != nil {
		sgtu.SetMinBandwidth(*f)
	}
	return sgtu
}

// AddMinBandwidth adds f to the "min_bandwidth" field.
func (sgtu *SocGroupTicketUpdate) AddMinBandwidth(f float32) *SocGroupTicketUpdate {
	sgtu.mutation.AddMinBandwidth(f)
	return sgtu
}

// SetDivertType sets the "divert_type" field.
func (sgtu *SocGroupTicketUpdate) SetDivertType(i int) *SocGroupTicketUpdate {
	sgtu.mutation.ResetDivertType()
	sgtu.mutation.SetDivertType(i)
	return sgtu
}

// SetNillableDivertType sets the "divert_type" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableDivertType(i *int) *SocGroupTicketUpdate {
	if i != nil {
		sgtu.SetDivertType(*i)
	}
	return sgtu
}

// AddDivertType adds i to the "divert_type" field.
func (sgtu *SocGroupTicketUpdate) AddDivertType(i int) *SocGroupTicketUpdate {
	sgtu.mutation.AddDivertType(i)
	return sgtu
}

// SetOpType sets the "op_type" field.
func (sgtu *SocGroupTicketUpdate) SetOpType(i int) *SocGroupTicketUpdate {
	sgtu.mutation.ResetOpType()
	sgtu.mutation.SetOpType(i)
	return sgtu
}

// SetNillableOpType sets the "op_type" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableOpType(i *int) *SocGroupTicketUpdate {
	if i != nil {
		sgtu.SetOpType(*i)
	}
	return sgtu
}

// AddOpType adds i to the "op_type" field.
func (sgtu *SocGroupTicketUpdate) AddOpType(i int) *SocGroupTicketUpdate {
	sgtu.mutation.AddOpType(i)
	return sgtu
}

// SetOpTime sets the "op_time" field.
func (sgtu *SocGroupTicketUpdate) SetOpTime(t time.Time) *SocGroupTicketUpdate {
	sgtu.mutation.SetOpTime(t)
	return sgtu
}

// SetNillableOpTime sets the "op_time" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableOpTime(t *time.Time) *SocGroupTicketUpdate {
	if t != nil {
		sgtu.SetOpTime(*t)
	}
	return sgtu
}

// ClearOpTime clears the value of the "op_time" field.
func (sgtu *SocGroupTicketUpdate) ClearOpTime() *SocGroupTicketUpdate {
	sgtu.mutation.ClearOpTime()
	return sgtu
}

// SetConfigType sets the "config_type" field.
func (sgtu *SocGroupTicketUpdate) SetConfigType(i int) *SocGroupTicketUpdate {
	sgtu.mutation.ResetConfigType()
	sgtu.mutation.SetConfigType(i)
	return sgtu
}

// SetNillableConfigType sets the "config_type" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableConfigType(i *int) *SocGroupTicketUpdate {
	if i != nil {
		sgtu.SetConfigType(*i)
	}
	return sgtu
}

// AddConfigType adds i to the "config_type" field.
func (sgtu *SocGroupTicketUpdate) AddConfigType(i int) *SocGroupTicketUpdate {
	sgtu.mutation.AddConfigType(i)
	return sgtu
}

// SetConfigArgs sets the "config_args" field.
func (sgtu *SocGroupTicketUpdate) SetConfigArgs(s string) *SocGroupTicketUpdate {
	sgtu.mutation.SetConfigArgs(s)
	return sgtu
}

// SetNillableConfigArgs sets the "config_args" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableConfigArgs(s *string) *SocGroupTicketUpdate {
	if s != nil {
		sgtu.SetConfigArgs(*s)
	}
	return sgtu
}

// SetProductName sets the "product_name" field.
func (sgtu *SocGroupTicketUpdate) SetProductName(s string) *SocGroupTicketUpdate {
	sgtu.mutation.SetProductName(s)
	return sgtu
}

// SetNillableProductName sets the "product_name" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableProductName(s *string) *SocGroupTicketUpdate {
	if s != nil {
		sgtu.SetProductName(*s)
	}
	return sgtu
}

// SetProductCode sets the "product_code" field.
func (sgtu *SocGroupTicketUpdate) SetProductCode(s string) *SocGroupTicketUpdate {
	sgtu.mutation.SetProductCode(s)
	return sgtu
}

// SetNillableProductCode sets the "product_code" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableProductCode(s *string) *SocGroupTicketUpdate {
	if s != nil {
		sgtu.SetProductCode(*s)
	}
	return sgtu
}

// ClearProductCode clears the value of the "product_code" field.
func (sgtu *SocGroupTicketUpdate) ClearProductCode() *SocGroupTicketUpdate {
	sgtu.mutation.ClearProductCode()
	return sgtu
}

// SetContactList sets the "contact_list" field.
func (sgtu *SocGroupTicketUpdate) SetContactList(s *[]socgroup.User) *SocGroupTicketUpdate {
	sgtu.mutation.SetContactList(s)
	return sgtu
}

// ClearContactList clears the value of the "contact_list" field.
func (sgtu *SocGroupTicketUpdate) ClearContactList() *SocGroupTicketUpdate {
	sgtu.mutation.ClearContactList()
	return sgtu
}

// SetGroupTicketID sets the "group_ticket_id" field.
func (sgtu *SocGroupTicketUpdate) SetGroupTicketID(i int) *SocGroupTicketUpdate {
	sgtu.mutation.ResetGroupTicketID()
	sgtu.mutation.SetGroupTicketID(i)
	return sgtu
}

// SetNillableGroupTicketID sets the "group_ticket_id" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableGroupTicketID(i *int) *SocGroupTicketUpdate {
	if i != nil {
		sgtu.SetGroupTicketID(*i)
	}
	return sgtu
}

// AddGroupTicketID adds i to the "group_ticket_id" field.
func (sgtu *SocGroupTicketUpdate) AddGroupTicketID(i int) *SocGroupTicketUpdate {
	sgtu.mutation.AddGroupTicketID(i)
	return sgtu
}

// ClearGroupTicketID clears the value of the "group_ticket_id" field.
func (sgtu *SocGroupTicketUpdate) ClearGroupTicketID() *SocGroupTicketUpdate {
	sgtu.mutation.ClearGroupTicketID()
	return sgtu
}

// SetErrorInfo sets the "error_info" field.
func (sgtu *SocGroupTicketUpdate) SetErrorInfo(s string) *SocGroupTicketUpdate {
	sgtu.mutation.SetErrorInfo(s)
	return sgtu
}

// SetNillableErrorInfo sets the "error_info" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableErrorInfo(s *string) *SocGroupTicketUpdate {
	if s != nil {
		sgtu.SetErrorInfo(*s)
	}
	return sgtu
}

// ClearErrorInfo clears the value of the "error_info" field.
func (sgtu *SocGroupTicketUpdate) ClearErrorInfo() *SocGroupTicketUpdate {
	sgtu.mutation.ClearErrorInfo()
	return sgtu
}

// SetCreateUserID sets the "create_user_id" field.
func (sgtu *SocGroupTicketUpdate) SetCreateUserID(i int) *SocGroupTicketUpdate {
	sgtu.mutation.SetCreateUserID(i)
	return sgtu
}

// SetNillableCreateUserID sets the "create_user_id" field if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableCreateUserID(i *int) *SocGroupTicketUpdate {
	if i != nil {
		sgtu.SetCreateUserID(*i)
	}
	return sgtu
}

// ClearCreateUserID clears the value of the "create_user_id" field.
func (sgtu *SocGroupTicketUpdate) ClearCreateUserID() *SocGroupTicketUpdate {
	sgtu.mutation.ClearCreateUserID()
	return sgtu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sgtu *SocGroupTicketUpdate) SetTenant(t *Tenant) *SocGroupTicketUpdate {
	return sgtu.SetTenantID(t.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (sgtu *SocGroupTicketUpdate) SetUserID(id int) *SocGroupTicketUpdate {
	sgtu.mutation.SetUserID(id)
	return sgtu
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (sgtu *SocGroupTicketUpdate) SetNillableUserID(id *int) *SocGroupTicketUpdate {
	if id != nil {
		sgtu = sgtu.SetUserID(*id)
	}
	return sgtu
}

// SetUser sets the "user" edge to the User entity.
func (sgtu *SocGroupTicketUpdate) SetUser(u *User) *SocGroupTicketUpdate {
	return sgtu.SetUserID(u.ID)
}

// Mutation returns the SocGroupTicketMutation object of the builder.
func (sgtu *SocGroupTicketUpdate) Mutation() *SocGroupTicketMutation {
	return sgtu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (sgtu *SocGroupTicketUpdate) ClearTenant() *SocGroupTicketUpdate {
	sgtu.mutation.ClearTenant()
	return sgtu
}

// ClearUser clears the "user" edge to the User entity.
func (sgtu *SocGroupTicketUpdate) ClearUser() *SocGroupTicketUpdate {
	sgtu.mutation.ClearUser()
	return sgtu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (sgtu *SocGroupTicketUpdate) Save(ctx context.Context) (int, error) {
	if err := sgtu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, sgtu.sqlSave, sgtu.mutation, sgtu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sgtu *SocGroupTicketUpdate) SaveX(ctx context.Context) int {
	affected, err := sgtu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (sgtu *SocGroupTicketUpdate) Exec(ctx context.Context) error {
	_, err := sgtu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sgtu *SocGroupTicketUpdate) ExecX(ctx context.Context) {
	if err := sgtu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sgtu *SocGroupTicketUpdate) defaults() error {
	if _, ok := sgtu.mutation.UpdatedAt(); !ok {
		if socgroupticket.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized socgroupticket.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := socgroupticket.UpdateDefaultUpdatedAt()
		sgtu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sgtu *SocGroupTicketUpdate) check() error {
	if v, ok := sgtu.mutation.Remark(); ok {
		if err := socgroupticket.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.remark": %w`, err)}
		}
	}
	if v, ok := sgtu.mutation.Description(); ok {
		if err := socgroupticket.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.description": %w`, err)}
		}
	}
	if v, ok := sgtu.mutation.ConfigArgs(); ok {
		if err := socgroupticket.ConfigArgsValidator(v); err != nil {
			return &ValidationError{Name: "config_args", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.config_args": %w`, err)}
		}
	}
	if v, ok := sgtu.mutation.ErrorInfo(); ok {
		if err := socgroupticket.ErrorInfoValidator(v); err != nil {
			return &ValidationError{Name: "error_info", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.error_info": %w`, err)}
		}
	}
	return nil
}

func (sgtu *SocGroupTicketUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := sgtu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(socgroupticket.Table, socgroupticket.Columns, sqlgraph.NewFieldSpec(socgroupticket.FieldID, field.TypeInt))
	if ps := sgtu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sgtu.mutation.UpdatedAt(); ok {
		_spec.SetField(socgroupticket.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := sgtu.mutation.Remark(); ok {
		_spec.SetField(socgroupticket.FieldRemark, field.TypeString, value)
	}
	if sgtu.mutation.RemarkCleared() {
		_spec.ClearField(socgroupticket.FieldRemark, field.TypeString)
	}
	if value, ok := sgtu.mutation.Name(); ok {
		_spec.SetField(socgroupticket.FieldName, field.TypeString, value)
	}
	if value, ok := sgtu.mutation.GetType(); ok {
		_spec.SetField(socgroupticket.FieldType, field.TypeString, value)
	}
	if value, ok := sgtu.mutation.Description(); ok {
		_spec.SetField(socgroupticket.FieldDescription, field.TypeString, value)
	}
	if value, ok := sgtu.mutation.FollowList(); ok {
		_spec.SetField(socgroupticket.FieldFollowList, field.TypeJSON, value)
	}
	if sgtu.mutation.FollowListCleared() {
		_spec.ClearField(socgroupticket.FieldFollowList, field.TypeJSON)
	}
	if value, ok := sgtu.mutation.DepartmentID(); ok {
		_spec.SetField(socgroupticket.FieldDepartmentID, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.AddedDepartmentID(); ok {
		_spec.AddField(socgroupticket.FieldDepartmentID, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.IPList(); ok {
		_spec.SetField(socgroupticket.FieldIPList, field.TypeJSON, value)
	}
	if sgtu.mutation.IPListCleared() {
		_spec.ClearField(socgroupticket.FieldIPList, field.TypeJSON)
	}
	if value, ok := sgtu.mutation.MinBandwidth(); ok {
		_spec.SetField(socgroupticket.FieldMinBandwidth, field.TypeFloat32, value)
	}
	if value, ok := sgtu.mutation.AddedMinBandwidth(); ok {
		_spec.AddField(socgroupticket.FieldMinBandwidth, field.TypeFloat32, value)
	}
	if value, ok := sgtu.mutation.DivertType(); ok {
		_spec.SetField(socgroupticket.FieldDivertType, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.AddedDivertType(); ok {
		_spec.AddField(socgroupticket.FieldDivertType, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.OpType(); ok {
		_spec.SetField(socgroupticket.FieldOpType, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.AddedOpType(); ok {
		_spec.AddField(socgroupticket.FieldOpType, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.OpTime(); ok {
		_spec.SetField(socgroupticket.FieldOpTime, field.TypeTime, value)
	}
	if sgtu.mutation.OpTimeCleared() {
		_spec.ClearField(socgroupticket.FieldOpTime, field.TypeTime)
	}
	if value, ok := sgtu.mutation.ConfigType(); ok {
		_spec.SetField(socgroupticket.FieldConfigType, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.AddedConfigType(); ok {
		_spec.AddField(socgroupticket.FieldConfigType, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.ConfigArgs(); ok {
		_spec.SetField(socgroupticket.FieldConfigArgs, field.TypeString, value)
	}
	if value, ok := sgtu.mutation.ProductName(); ok {
		_spec.SetField(socgroupticket.FieldProductName, field.TypeString, value)
	}
	if value, ok := sgtu.mutation.ProductCode(); ok {
		_spec.SetField(socgroupticket.FieldProductCode, field.TypeString, value)
	}
	if sgtu.mutation.ProductCodeCleared() {
		_spec.ClearField(socgroupticket.FieldProductCode, field.TypeString)
	}
	if value, ok := sgtu.mutation.ContactList(); ok {
		_spec.SetField(socgroupticket.FieldContactList, field.TypeJSON, value)
	}
	if sgtu.mutation.ContactListCleared() {
		_spec.ClearField(socgroupticket.FieldContactList, field.TypeJSON)
	}
	if value, ok := sgtu.mutation.GroupTicketID(); ok {
		_spec.SetField(socgroupticket.FieldGroupTicketID, field.TypeInt, value)
	}
	if value, ok := sgtu.mutation.AddedGroupTicketID(); ok {
		_spec.AddField(socgroupticket.FieldGroupTicketID, field.TypeInt, value)
	}
	if sgtu.mutation.GroupTicketIDCleared() {
		_spec.ClearField(socgroupticket.FieldGroupTicketID, field.TypeInt)
	}
	if value, ok := sgtu.mutation.ErrorInfo(); ok {
		_spec.SetField(socgroupticket.FieldErrorInfo, field.TypeString, value)
	}
	if sgtu.mutation.ErrorInfoCleared() {
		_spec.ClearField(socgroupticket.FieldErrorInfo, field.TypeString)
	}
	if sgtu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.TenantTable,
			Columns: []string{socgroupticket.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sgtu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.TenantTable,
			Columns: []string{socgroupticket.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sgtu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.UserTable,
			Columns: []string{socgroupticket.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sgtu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.UserTable,
			Columns: []string{socgroupticket.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, sgtu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{socgroupticket.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	sgtu.mutation.done = true
	return n, nil
}

// SocGroupTicketUpdateOne is the builder for updating a single SocGroupTicket entity.
type SocGroupTicketUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SocGroupTicketMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (sgtuo *SocGroupTicketUpdateOne) SetUpdatedAt(t time.Time) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetUpdatedAt(t)
	return sgtuo
}

// SetTenantID sets the "tenant_id" field.
func (sgtuo *SocGroupTicketUpdateOne) SetTenantID(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetTenantID(i)
	return sgtuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableTenantID(i *int) *SocGroupTicketUpdateOne {
	if i != nil {
		sgtuo.SetTenantID(*i)
	}
	return sgtuo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearTenantID() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearTenantID()
	return sgtuo
}

// SetRemark sets the "remark" field.
func (sgtuo *SocGroupTicketUpdateOne) SetRemark(s string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetRemark(s)
	return sgtuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableRemark(s *string) *SocGroupTicketUpdateOne {
	if s != nil {
		sgtuo.SetRemark(*s)
	}
	return sgtuo
}

// ClearRemark clears the value of the "remark" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearRemark() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearRemark()
	return sgtuo
}

// SetName sets the "name" field.
func (sgtuo *SocGroupTicketUpdateOne) SetName(s string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetName(s)
	return sgtuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableName(s *string) *SocGroupTicketUpdateOne {
	if s != nil {
		sgtuo.SetName(*s)
	}
	return sgtuo
}

// SetType sets the "type" field.
func (sgtuo *SocGroupTicketUpdateOne) SetType(s string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetType(s)
	return sgtuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableType(s *string) *SocGroupTicketUpdateOne {
	if s != nil {
		sgtuo.SetType(*s)
	}
	return sgtuo
}

// SetDescription sets the "description" field.
func (sgtuo *SocGroupTicketUpdateOne) SetDescription(s string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetDescription(s)
	return sgtuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableDescription(s *string) *SocGroupTicketUpdateOne {
	if s != nil {
		sgtuo.SetDescription(*s)
	}
	return sgtuo
}

// SetFollowList sets the "follow_list" field.
func (sgtuo *SocGroupTicketUpdateOne) SetFollowList(i *[]int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetFollowList(i)
	return sgtuo
}

// ClearFollowList clears the value of the "follow_list" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearFollowList() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearFollowList()
	return sgtuo
}

// SetDepartmentID sets the "department_id" field.
func (sgtuo *SocGroupTicketUpdateOne) SetDepartmentID(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.ResetDepartmentID()
	sgtuo.mutation.SetDepartmentID(i)
	return sgtuo
}

// SetNillableDepartmentID sets the "department_id" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableDepartmentID(i *int) *SocGroupTicketUpdateOne {
	if i != nil {
		sgtuo.SetDepartmentID(*i)
	}
	return sgtuo
}

// AddDepartmentID adds i to the "department_id" field.
func (sgtuo *SocGroupTicketUpdateOne) AddDepartmentID(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.AddDepartmentID(i)
	return sgtuo
}

// SetIPList sets the "ip_list" field.
func (sgtuo *SocGroupTicketUpdateOne) SetIPList(s *[]string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetIPList(s)
	return sgtuo
}

// ClearIPList clears the value of the "ip_list" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearIPList() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearIPList()
	return sgtuo
}

// SetMinBandwidth sets the "min_bandwidth" field.
func (sgtuo *SocGroupTicketUpdateOne) SetMinBandwidth(f float32) *SocGroupTicketUpdateOne {
	sgtuo.mutation.ResetMinBandwidth()
	sgtuo.mutation.SetMinBandwidth(f)
	return sgtuo
}

// SetNillableMinBandwidth sets the "min_bandwidth" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableMinBandwidth(f *float32) *SocGroupTicketUpdateOne {
	if f != nil {
		sgtuo.SetMinBandwidth(*f)
	}
	return sgtuo
}

// AddMinBandwidth adds f to the "min_bandwidth" field.
func (sgtuo *SocGroupTicketUpdateOne) AddMinBandwidth(f float32) *SocGroupTicketUpdateOne {
	sgtuo.mutation.AddMinBandwidth(f)
	return sgtuo
}

// SetDivertType sets the "divert_type" field.
func (sgtuo *SocGroupTicketUpdateOne) SetDivertType(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.ResetDivertType()
	sgtuo.mutation.SetDivertType(i)
	return sgtuo
}

// SetNillableDivertType sets the "divert_type" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableDivertType(i *int) *SocGroupTicketUpdateOne {
	if i != nil {
		sgtuo.SetDivertType(*i)
	}
	return sgtuo
}

// AddDivertType adds i to the "divert_type" field.
func (sgtuo *SocGroupTicketUpdateOne) AddDivertType(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.AddDivertType(i)
	return sgtuo
}

// SetOpType sets the "op_type" field.
func (sgtuo *SocGroupTicketUpdateOne) SetOpType(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.ResetOpType()
	sgtuo.mutation.SetOpType(i)
	return sgtuo
}

// SetNillableOpType sets the "op_type" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableOpType(i *int) *SocGroupTicketUpdateOne {
	if i != nil {
		sgtuo.SetOpType(*i)
	}
	return sgtuo
}

// AddOpType adds i to the "op_type" field.
func (sgtuo *SocGroupTicketUpdateOne) AddOpType(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.AddOpType(i)
	return sgtuo
}

// SetOpTime sets the "op_time" field.
func (sgtuo *SocGroupTicketUpdateOne) SetOpTime(t time.Time) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetOpTime(t)
	return sgtuo
}

// SetNillableOpTime sets the "op_time" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableOpTime(t *time.Time) *SocGroupTicketUpdateOne {
	if t != nil {
		sgtuo.SetOpTime(*t)
	}
	return sgtuo
}

// ClearOpTime clears the value of the "op_time" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearOpTime() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearOpTime()
	return sgtuo
}

// SetConfigType sets the "config_type" field.
func (sgtuo *SocGroupTicketUpdateOne) SetConfigType(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.ResetConfigType()
	sgtuo.mutation.SetConfigType(i)
	return sgtuo
}

// SetNillableConfigType sets the "config_type" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableConfigType(i *int) *SocGroupTicketUpdateOne {
	if i != nil {
		sgtuo.SetConfigType(*i)
	}
	return sgtuo
}

// AddConfigType adds i to the "config_type" field.
func (sgtuo *SocGroupTicketUpdateOne) AddConfigType(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.AddConfigType(i)
	return sgtuo
}

// SetConfigArgs sets the "config_args" field.
func (sgtuo *SocGroupTicketUpdateOne) SetConfigArgs(s string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetConfigArgs(s)
	return sgtuo
}

// SetNillableConfigArgs sets the "config_args" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableConfigArgs(s *string) *SocGroupTicketUpdateOne {
	if s != nil {
		sgtuo.SetConfigArgs(*s)
	}
	return sgtuo
}

// SetProductName sets the "product_name" field.
func (sgtuo *SocGroupTicketUpdateOne) SetProductName(s string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetProductName(s)
	return sgtuo
}

// SetNillableProductName sets the "product_name" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableProductName(s *string) *SocGroupTicketUpdateOne {
	if s != nil {
		sgtuo.SetProductName(*s)
	}
	return sgtuo
}

// SetProductCode sets the "product_code" field.
func (sgtuo *SocGroupTicketUpdateOne) SetProductCode(s string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetProductCode(s)
	return sgtuo
}

// SetNillableProductCode sets the "product_code" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableProductCode(s *string) *SocGroupTicketUpdateOne {
	if s != nil {
		sgtuo.SetProductCode(*s)
	}
	return sgtuo
}

// ClearProductCode clears the value of the "product_code" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearProductCode() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearProductCode()
	return sgtuo
}

// SetContactList sets the "contact_list" field.
func (sgtuo *SocGroupTicketUpdateOne) SetContactList(s *[]socgroup.User) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetContactList(s)
	return sgtuo
}

// ClearContactList clears the value of the "contact_list" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearContactList() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearContactList()
	return sgtuo
}

// SetGroupTicketID sets the "group_ticket_id" field.
func (sgtuo *SocGroupTicketUpdateOne) SetGroupTicketID(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.ResetGroupTicketID()
	sgtuo.mutation.SetGroupTicketID(i)
	return sgtuo
}

// SetNillableGroupTicketID sets the "group_ticket_id" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableGroupTicketID(i *int) *SocGroupTicketUpdateOne {
	if i != nil {
		sgtuo.SetGroupTicketID(*i)
	}
	return sgtuo
}

// AddGroupTicketID adds i to the "group_ticket_id" field.
func (sgtuo *SocGroupTicketUpdateOne) AddGroupTicketID(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.AddGroupTicketID(i)
	return sgtuo
}

// ClearGroupTicketID clears the value of the "group_ticket_id" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearGroupTicketID() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearGroupTicketID()
	return sgtuo
}

// SetErrorInfo sets the "error_info" field.
func (sgtuo *SocGroupTicketUpdateOne) SetErrorInfo(s string) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetErrorInfo(s)
	return sgtuo
}

// SetNillableErrorInfo sets the "error_info" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableErrorInfo(s *string) *SocGroupTicketUpdateOne {
	if s != nil {
		sgtuo.SetErrorInfo(*s)
	}
	return sgtuo
}

// ClearErrorInfo clears the value of the "error_info" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearErrorInfo() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearErrorInfo()
	return sgtuo
}

// SetCreateUserID sets the "create_user_id" field.
func (sgtuo *SocGroupTicketUpdateOne) SetCreateUserID(i int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetCreateUserID(i)
	return sgtuo
}

// SetNillableCreateUserID sets the "create_user_id" field if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableCreateUserID(i *int) *SocGroupTicketUpdateOne {
	if i != nil {
		sgtuo.SetCreateUserID(*i)
	}
	return sgtuo
}

// ClearCreateUserID clears the value of the "create_user_id" field.
func (sgtuo *SocGroupTicketUpdateOne) ClearCreateUserID() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearCreateUserID()
	return sgtuo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sgtuo *SocGroupTicketUpdateOne) SetTenant(t *Tenant) *SocGroupTicketUpdateOne {
	return sgtuo.SetTenantID(t.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (sgtuo *SocGroupTicketUpdateOne) SetUserID(id int) *SocGroupTicketUpdateOne {
	sgtuo.mutation.SetUserID(id)
	return sgtuo
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (sgtuo *SocGroupTicketUpdateOne) SetNillableUserID(id *int) *SocGroupTicketUpdateOne {
	if id != nil {
		sgtuo = sgtuo.SetUserID(*id)
	}
	return sgtuo
}

// SetUser sets the "user" edge to the User entity.
func (sgtuo *SocGroupTicketUpdateOne) SetUser(u *User) *SocGroupTicketUpdateOne {
	return sgtuo.SetUserID(u.ID)
}

// Mutation returns the SocGroupTicketMutation object of the builder.
func (sgtuo *SocGroupTicketUpdateOne) Mutation() *SocGroupTicketMutation {
	return sgtuo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (sgtuo *SocGroupTicketUpdateOne) ClearTenant() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearTenant()
	return sgtuo
}

// ClearUser clears the "user" edge to the User entity.
func (sgtuo *SocGroupTicketUpdateOne) ClearUser() *SocGroupTicketUpdateOne {
	sgtuo.mutation.ClearUser()
	return sgtuo
}

// Where appends a list predicates to the SocGroupTicketUpdate builder.
func (sgtuo *SocGroupTicketUpdateOne) Where(ps ...predicate.SocGroupTicket) *SocGroupTicketUpdateOne {
	sgtuo.mutation.Where(ps...)
	return sgtuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (sgtuo *SocGroupTicketUpdateOne) Select(field string, fields ...string) *SocGroupTicketUpdateOne {
	sgtuo.fields = append([]string{field}, fields...)
	return sgtuo
}

// Save executes the query and returns the updated SocGroupTicket entity.
func (sgtuo *SocGroupTicketUpdateOne) Save(ctx context.Context) (*SocGroupTicket, error) {
	if err := sgtuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sgtuo.sqlSave, sgtuo.mutation, sgtuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sgtuo *SocGroupTicketUpdateOne) SaveX(ctx context.Context) *SocGroupTicket {
	node, err := sgtuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (sgtuo *SocGroupTicketUpdateOne) Exec(ctx context.Context) error {
	_, err := sgtuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sgtuo *SocGroupTicketUpdateOne) ExecX(ctx context.Context) {
	if err := sgtuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sgtuo *SocGroupTicketUpdateOne) defaults() error {
	if _, ok := sgtuo.mutation.UpdatedAt(); !ok {
		if socgroupticket.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized socgroupticket.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := socgroupticket.UpdateDefaultUpdatedAt()
		sgtuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sgtuo *SocGroupTicketUpdateOne) check() error {
	if v, ok := sgtuo.mutation.Remark(); ok {
		if err := socgroupticket.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.remark": %w`, err)}
		}
	}
	if v, ok := sgtuo.mutation.Description(); ok {
		if err := socgroupticket.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.description": %w`, err)}
		}
	}
	if v, ok := sgtuo.mutation.ConfigArgs(); ok {
		if err := socgroupticket.ConfigArgsValidator(v); err != nil {
			return &ValidationError{Name: "config_args", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.config_args": %w`, err)}
		}
	}
	if v, ok := sgtuo.mutation.ErrorInfo(); ok {
		if err := socgroupticket.ErrorInfoValidator(v); err != nil {
			return &ValidationError{Name: "error_info", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.error_info": %w`, err)}
		}
	}
	return nil
}

func (sgtuo *SocGroupTicketUpdateOne) sqlSave(ctx context.Context) (_node *SocGroupTicket, err error) {
	if err := sgtuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(socgroupticket.Table, socgroupticket.Columns, sqlgraph.NewFieldSpec(socgroupticket.FieldID, field.TypeInt))
	id, ok := sgtuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SocGroupTicket.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := sgtuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, socgroupticket.FieldID)
		for _, f := range fields {
			if !socgroupticket.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != socgroupticket.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := sgtuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sgtuo.mutation.UpdatedAt(); ok {
		_spec.SetField(socgroupticket.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := sgtuo.mutation.Remark(); ok {
		_spec.SetField(socgroupticket.FieldRemark, field.TypeString, value)
	}
	if sgtuo.mutation.RemarkCleared() {
		_spec.ClearField(socgroupticket.FieldRemark, field.TypeString)
	}
	if value, ok := sgtuo.mutation.Name(); ok {
		_spec.SetField(socgroupticket.FieldName, field.TypeString, value)
	}
	if value, ok := sgtuo.mutation.GetType(); ok {
		_spec.SetField(socgroupticket.FieldType, field.TypeString, value)
	}
	if value, ok := sgtuo.mutation.Description(); ok {
		_spec.SetField(socgroupticket.FieldDescription, field.TypeString, value)
	}
	if value, ok := sgtuo.mutation.FollowList(); ok {
		_spec.SetField(socgroupticket.FieldFollowList, field.TypeJSON, value)
	}
	if sgtuo.mutation.FollowListCleared() {
		_spec.ClearField(socgroupticket.FieldFollowList, field.TypeJSON)
	}
	if value, ok := sgtuo.mutation.DepartmentID(); ok {
		_spec.SetField(socgroupticket.FieldDepartmentID, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.AddedDepartmentID(); ok {
		_spec.AddField(socgroupticket.FieldDepartmentID, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.IPList(); ok {
		_spec.SetField(socgroupticket.FieldIPList, field.TypeJSON, value)
	}
	if sgtuo.mutation.IPListCleared() {
		_spec.ClearField(socgroupticket.FieldIPList, field.TypeJSON)
	}
	if value, ok := sgtuo.mutation.MinBandwidth(); ok {
		_spec.SetField(socgroupticket.FieldMinBandwidth, field.TypeFloat32, value)
	}
	if value, ok := sgtuo.mutation.AddedMinBandwidth(); ok {
		_spec.AddField(socgroupticket.FieldMinBandwidth, field.TypeFloat32, value)
	}
	if value, ok := sgtuo.mutation.DivertType(); ok {
		_spec.SetField(socgroupticket.FieldDivertType, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.AddedDivertType(); ok {
		_spec.AddField(socgroupticket.FieldDivertType, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.OpType(); ok {
		_spec.SetField(socgroupticket.FieldOpType, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.AddedOpType(); ok {
		_spec.AddField(socgroupticket.FieldOpType, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.OpTime(); ok {
		_spec.SetField(socgroupticket.FieldOpTime, field.TypeTime, value)
	}
	if sgtuo.mutation.OpTimeCleared() {
		_spec.ClearField(socgroupticket.FieldOpTime, field.TypeTime)
	}
	if value, ok := sgtuo.mutation.ConfigType(); ok {
		_spec.SetField(socgroupticket.FieldConfigType, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.AddedConfigType(); ok {
		_spec.AddField(socgroupticket.FieldConfigType, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.ConfigArgs(); ok {
		_spec.SetField(socgroupticket.FieldConfigArgs, field.TypeString, value)
	}
	if value, ok := sgtuo.mutation.ProductName(); ok {
		_spec.SetField(socgroupticket.FieldProductName, field.TypeString, value)
	}
	if value, ok := sgtuo.mutation.ProductCode(); ok {
		_spec.SetField(socgroupticket.FieldProductCode, field.TypeString, value)
	}
	if sgtuo.mutation.ProductCodeCleared() {
		_spec.ClearField(socgroupticket.FieldProductCode, field.TypeString)
	}
	if value, ok := sgtuo.mutation.ContactList(); ok {
		_spec.SetField(socgroupticket.FieldContactList, field.TypeJSON, value)
	}
	if sgtuo.mutation.ContactListCleared() {
		_spec.ClearField(socgroupticket.FieldContactList, field.TypeJSON)
	}
	if value, ok := sgtuo.mutation.GroupTicketID(); ok {
		_spec.SetField(socgroupticket.FieldGroupTicketID, field.TypeInt, value)
	}
	if value, ok := sgtuo.mutation.AddedGroupTicketID(); ok {
		_spec.AddField(socgroupticket.FieldGroupTicketID, field.TypeInt, value)
	}
	if sgtuo.mutation.GroupTicketIDCleared() {
		_spec.ClearField(socgroupticket.FieldGroupTicketID, field.TypeInt)
	}
	if value, ok := sgtuo.mutation.ErrorInfo(); ok {
		_spec.SetField(socgroupticket.FieldErrorInfo, field.TypeString, value)
	}
	if sgtuo.mutation.ErrorInfoCleared() {
		_spec.ClearField(socgroupticket.FieldErrorInfo, field.TypeString)
	}
	if sgtuo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.TenantTable,
			Columns: []string{socgroupticket.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sgtuo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.TenantTable,
			Columns: []string{socgroupticket.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sgtuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.UserTable,
			Columns: []string{socgroupticket.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sgtuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.UserTable,
			Columns: []string{socgroupticket.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &SocGroupTicket{config: sgtuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, sgtuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{socgroupticket.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	sgtuo.mutation.done = true
	return _node, nil
}
