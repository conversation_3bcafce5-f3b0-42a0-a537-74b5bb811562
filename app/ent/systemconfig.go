// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/systemconfig"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// SystemConfig is the model entity for the SystemConfig schema.
type SystemConfig struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// WofangTestIP holds the value of the "wofang_test_ip" field.
	WofangTestIP string `json:"wofang_test_ip,omitempty"`
	// NotifyPhones holds the value of the "notify_phones" field.
	NotifyPhones *[]string `json:"notify_phones,omitempty"`
	// NotifyEmails holds the value of the "notify_emails" field.
	NotifyEmails *[]string `json:"notify_emails,omitempty"`
	// NotifyScenes holds the value of the "notify_scenes" field.
	NotifyScenes *[]string `json:"notify_scenes,omitempty"`
	// 通知IP白名单，IP在白名单中将不再通知
	IPWhitelists *[]string `json:"ip_whitelists,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*SystemConfig) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case systemconfig.FieldNotifyPhones, systemconfig.FieldNotifyEmails, systemconfig.FieldNotifyScenes, systemconfig.FieldIPWhitelists:
			values[i] = new([]byte)
		case systemconfig.FieldID:
			values[i] = new(sql.NullInt64)
		case systemconfig.FieldRemark, systemconfig.FieldWofangTestIP:
			values[i] = new(sql.NullString)
		case systemconfig.FieldCreatedAt, systemconfig.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the SystemConfig fields.
func (sc *SystemConfig) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case systemconfig.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			sc.ID = int(value.Int64)
		case systemconfig.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				sc.CreatedAt = value.Time
			}
		case systemconfig.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				sc.UpdatedAt = value.Time
			}
		case systemconfig.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				sc.Remark = new(string)
				*sc.Remark = value.String
			}
		case systemconfig.FieldWofangTestIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field wofang_test_ip", values[i])
			} else if value.Valid {
				sc.WofangTestIP = value.String
			}
		case systemconfig.FieldNotifyPhones:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field notify_phones", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sc.NotifyPhones); err != nil {
					return fmt.Errorf("unmarshal field notify_phones: %w", err)
				}
			}
		case systemconfig.FieldNotifyEmails:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field notify_emails", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sc.NotifyEmails); err != nil {
					return fmt.Errorf("unmarshal field notify_emails: %w", err)
				}
			}
		case systemconfig.FieldNotifyScenes:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field notify_scenes", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sc.NotifyScenes); err != nil {
					return fmt.Errorf("unmarshal field notify_scenes: %w", err)
				}
			}
		case systemconfig.FieldIPWhitelists:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field ip_whitelists", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &sc.IPWhitelists); err != nil {
					return fmt.Errorf("unmarshal field ip_whitelists: %w", err)
				}
			}
		default:
			sc.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the SystemConfig.
// This includes values selected through modifiers, order, etc.
func (sc *SystemConfig) Value(name string) (ent.Value, error) {
	return sc.selectValues.Get(name)
}

// Update returns a builder for updating this SystemConfig.
// Note that you need to call SystemConfig.Unwrap() before calling this method if this SystemConfig
// was returned from a transaction, and the transaction was committed or rolled back.
func (sc *SystemConfig) Update() *SystemConfigUpdateOne {
	return NewSystemConfigClient(sc.config).UpdateOne(sc)
}

// Unwrap unwraps the SystemConfig entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (sc *SystemConfig) Unwrap() *SystemConfig {
	_tx, ok := sc.config.driver.(*txDriver)
	if !ok {
		panic("ent: SystemConfig is not a transactional entity")
	}
	sc.config.driver = _tx.drv
	return sc
}

// String implements the fmt.Stringer.
func (sc *SystemConfig) String() string {
	var builder strings.Builder
	builder.WriteString("SystemConfig(")
	builder.WriteString(fmt.Sprintf("id=%v, ", sc.ID))
	builder.WriteString("created_at=")
	builder.WriteString(sc.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(sc.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := sc.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("wofang_test_ip=")
	builder.WriteString(sc.WofangTestIP)
	builder.WriteString(", ")
	builder.WriteString("notify_phones=")
	builder.WriteString(fmt.Sprintf("%v", sc.NotifyPhones))
	builder.WriteString(", ")
	builder.WriteString("notify_emails=")
	builder.WriteString(fmt.Sprintf("%v", sc.NotifyEmails))
	builder.WriteString(", ")
	builder.WriteString("notify_scenes=")
	builder.WriteString(fmt.Sprintf("%v", sc.NotifyScenes))
	builder.WriteString(", ")
	builder.WriteString("ip_whitelists=")
	builder.WriteString(fmt.Sprintf("%v", sc.IPWhitelists))
	builder.WriteByte(')')
	return builder.String()
}

// SystemConfigs is a parsable slice of SystemConfig.
type SystemConfigs []*SystemConfig
