// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// CloudAttackData is the model entity for the CloudAttackData schema.
type CloudAttackData struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 来源IP
	SrcIP string `json:"src_ip,omitempty" query:"src_ip,omitempty"`
	// 来源端口
	SrcPort int `json:"src_port,omitempty" query:"src_port,omitempty"`
	// 被攻击IP
	DstIP string `json:"dst_ip,omitempty" query:"dst_ip,omitempty"`
	// 被攻击端口
	DstPort int `json:"dst_port,omitempty" query:"dst_port,omitempty"`
	// 4层协议：6tcp，17udp
	Protocol int `json:"protocol,omitempty"`
	// 这一秒pps
	CurrentAttackPps int64 `json:"current_attack_pps,omitempty" query:"current_attack_pps,omitempty"`
	// 攻击开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 结束时间
	EndTime time.Time `json:"end_time,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the CloudAttackDataQuery when eager-loading is set.
	Edges        CloudAttackDataEdges `json:"edges"`
	selectValues sql.SelectValues
}

// CloudAttackDataEdges holds the relations/edges for other nodes in the graph.
type CloudAttackDataEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e CloudAttackDataEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*CloudAttackData) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case cloudattackdata.FieldID, cloudattackdata.FieldTenantID, cloudattackdata.FieldSrcPort, cloudattackdata.FieldDstPort, cloudattackdata.FieldProtocol, cloudattackdata.FieldCurrentAttackPps:
			values[i] = new(sql.NullInt64)
		case cloudattackdata.FieldRemark, cloudattackdata.FieldSrcIP, cloudattackdata.FieldDstIP:
			values[i] = new(sql.NullString)
		case cloudattackdata.FieldCreatedAt, cloudattackdata.FieldUpdatedAt, cloudattackdata.FieldStartTime, cloudattackdata.FieldEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the CloudAttackData fields.
func (cad *CloudAttackData) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case cloudattackdata.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			cad.ID = int(value.Int64)
		case cloudattackdata.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				cad.TenantID = new(int)
				*cad.TenantID = int(value.Int64)
			}
		case cloudattackdata.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				cad.CreatedAt = value.Time
			}
		case cloudattackdata.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				cad.UpdatedAt = value.Time
			}
		case cloudattackdata.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				cad.Remark = new(string)
				*cad.Remark = value.String
			}
		case cloudattackdata.FieldSrcIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field src_ip", values[i])
			} else if value.Valid {
				cad.SrcIP = value.String
			}
		case cloudattackdata.FieldSrcPort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field src_port", values[i])
			} else if value.Valid {
				cad.SrcPort = int(value.Int64)
			}
		case cloudattackdata.FieldDstIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field dst_ip", values[i])
			} else if value.Valid {
				cad.DstIP = value.String
			}
		case cloudattackdata.FieldDstPort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dst_port", values[i])
			} else if value.Valid {
				cad.DstPort = int(value.Int64)
			}
		case cloudattackdata.FieldProtocol:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field protocol", values[i])
			} else if value.Valid {
				cad.Protocol = int(value.Int64)
			}
		case cloudattackdata.FieldCurrentAttackPps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field current_attack_pps", values[i])
			} else if value.Valid {
				cad.CurrentAttackPps = value.Int64
			}
		case cloudattackdata.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				cad.StartTime = value.Time
			}
		case cloudattackdata.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				cad.EndTime = value.Time
			}
		default:
			cad.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the CloudAttackData.
// This includes values selected through modifiers, order, etc.
func (cad *CloudAttackData) Value(name string) (ent.Value, error) {
	return cad.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the CloudAttackData entity.
func (cad *CloudAttackData) QueryTenant() *TenantQuery {
	return NewCloudAttackDataClient(cad.config).QueryTenant(cad)
}

// Update returns a builder for updating this CloudAttackData.
// Note that you need to call CloudAttackData.Unwrap() before calling this method if this CloudAttackData
// was returned from a transaction, and the transaction was committed or rolled back.
func (cad *CloudAttackData) Update() *CloudAttackDataUpdateOne {
	return NewCloudAttackDataClient(cad.config).UpdateOne(cad)
}

// Unwrap unwraps the CloudAttackData entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cad *CloudAttackData) Unwrap() *CloudAttackData {
	_tx, ok := cad.config.driver.(*txDriver)
	if !ok {
		panic("ent: CloudAttackData is not a transactional entity")
	}
	cad.config.driver = _tx.drv
	return cad
}

// String implements the fmt.Stringer.
func (cad *CloudAttackData) String() string {
	var builder strings.Builder
	builder.WriteString("CloudAttackData(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cad.ID))
	if v := cad.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(cad.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(cad.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := cad.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("src_ip=")
	builder.WriteString(cad.SrcIP)
	builder.WriteString(", ")
	builder.WriteString("src_port=")
	builder.WriteString(fmt.Sprintf("%v", cad.SrcPort))
	builder.WriteString(", ")
	builder.WriteString("dst_ip=")
	builder.WriteString(cad.DstIP)
	builder.WriteString(", ")
	builder.WriteString("dst_port=")
	builder.WriteString(fmt.Sprintf("%v", cad.DstPort))
	builder.WriteString(", ")
	builder.WriteString("protocol=")
	builder.WriteString(fmt.Sprintf("%v", cad.Protocol))
	builder.WriteString(", ")
	builder.WriteString("current_attack_pps=")
	builder.WriteString(fmt.Sprintf("%v", cad.CurrentAttackPps))
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(cad.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end_time=")
	builder.WriteString(cad.EndTime.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// CloudAttackDataSlice is a parsable slice of CloudAttackData.
type CloudAttackDataSlice []*CloudAttackData
