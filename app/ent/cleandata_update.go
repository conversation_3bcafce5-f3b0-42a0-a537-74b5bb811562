// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cleandata"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CleanDataUpdate is the builder for updating CleanData entities.
type CleanDataUpdate struct {
	config
	hooks    []Hook
	mutation *CleanDataMutation
}

// Where appends a list predicates to the CleanDataUpdate builder.
func (cdu *CleanDataUpdate) Where(ps ...predicate.CleanData) *CleanDataUpdate {
	cdu.mutation.Where(ps...)
	return cdu
}

// SetTenantID sets the "tenant_id" field.
func (cdu *CleanDataUpdate) SetTenantID(i int) *CleanDataUpdate {
	cdu.mutation.SetTenantID(i)
	return cdu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableTenantID(i *int) *CleanDataUpdate {
	if i != nil {
		cdu.SetTenantID(*i)
	}
	return cdu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (cdu *CleanDataUpdate) ClearTenantID() *CleanDataUpdate {
	cdu.mutation.ClearTenantID()
	return cdu
}

// SetSpectrumAlertID sets the "spectrum_alert_id" field.
func (cdu *CleanDataUpdate) SetSpectrumAlertID(i int) *CleanDataUpdate {
	cdu.mutation.SetSpectrumAlertID(i)
	return cdu
}

// SetNillableSpectrumAlertID sets the "spectrum_alert_id" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableSpectrumAlertID(i *int) *CleanDataUpdate {
	if i != nil {
		cdu.SetSpectrumAlertID(*i)
	}
	return cdu
}

// ClearSpectrumAlertID clears the value of the "spectrum_alert_id" field.
func (cdu *CleanDataUpdate) ClearSpectrumAlertID() *CleanDataUpdate {
	cdu.mutation.ClearSpectrumAlertID()
	return cdu
}

// SetIP sets the "ip" field.
func (cdu *CleanDataUpdate) SetIP(s string) *CleanDataUpdate {
	cdu.mutation.SetIP(s)
	return cdu
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableIP(s *string) *CleanDataUpdate {
	if s != nil {
		cdu.SetIP(*s)
	}
	return cdu
}

// SetTime sets the "time" field.
func (cdu *CleanDataUpdate) SetTime(t time.Time) *CleanDataUpdate {
	cdu.mutation.SetTime(t)
	return cdu
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableTime(t *time.Time) *CleanDataUpdate {
	if t != nil {
		cdu.SetTime(*t)
	}
	return cdu
}

// SetInBps sets the "in_bps" field.
func (cdu *CleanDataUpdate) SetInBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInBps()
	cdu.mutation.SetInBps(i)
	return cdu
}

// SetNillableInBps sets the "in_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInBps(*i)
	}
	return cdu
}

// AddInBps adds i to the "in_bps" field.
func (cdu *CleanDataUpdate) AddInBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInBps(i)
	return cdu
}

// SetOutBps sets the "out_bps" field.
func (cdu *CleanDataUpdate) SetOutBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutBps()
	cdu.mutation.SetOutBps(i)
	return cdu
}

// SetNillableOutBps sets the "out_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutBps(*i)
	}
	return cdu
}

// AddOutBps adds i to the "out_bps" field.
func (cdu *CleanDataUpdate) AddOutBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutBps(i)
	return cdu
}

// SetInPps sets the "in_pps" field.
func (cdu *CleanDataUpdate) SetInPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInPps()
	cdu.mutation.SetInPps(i)
	return cdu
}

// SetNillableInPps sets the "in_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInPps(*i)
	}
	return cdu
}

// AddInPps adds i to the "in_pps" field.
func (cdu *CleanDataUpdate) AddInPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInPps(i)
	return cdu
}

// SetOutPps sets the "out_pps" field.
func (cdu *CleanDataUpdate) SetOutPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutPps()
	cdu.mutation.SetOutPps(i)
	return cdu
}

// SetNillableOutPps sets the "out_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutPps(*i)
	}
	return cdu
}

// AddOutPps adds i to the "out_pps" field.
func (cdu *CleanDataUpdate) AddOutPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutPps(i)
	return cdu
}

// SetInAckPps sets the "in_ack_pps" field.
func (cdu *CleanDataUpdate) SetInAckPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInAckPps()
	cdu.mutation.SetInAckPps(i)
	return cdu
}

// SetNillableInAckPps sets the "in_ack_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInAckPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInAckPps(*i)
	}
	return cdu
}

// AddInAckPps adds i to the "in_ack_pps" field.
func (cdu *CleanDataUpdate) AddInAckPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInAckPps(i)
	return cdu
}

// SetOutAckPps sets the "out_ack_pps" field.
func (cdu *CleanDataUpdate) SetOutAckPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutAckPps()
	cdu.mutation.SetOutAckPps(i)
	return cdu
}

// SetNillableOutAckPps sets the "out_ack_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutAckPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutAckPps(*i)
	}
	return cdu
}

// AddOutAckPps adds i to the "out_ack_pps" field.
func (cdu *CleanDataUpdate) AddOutAckPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutAckPps(i)
	return cdu
}

// SetInAckBps sets the "in_ack_bps" field.
func (cdu *CleanDataUpdate) SetInAckBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInAckBps()
	cdu.mutation.SetInAckBps(i)
	return cdu
}

// SetNillableInAckBps sets the "in_ack_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInAckBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInAckBps(*i)
	}
	return cdu
}

// AddInAckBps adds i to the "in_ack_bps" field.
func (cdu *CleanDataUpdate) AddInAckBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInAckBps(i)
	return cdu
}

// SetOutAckBps sets the "out_ack_bps" field.
func (cdu *CleanDataUpdate) SetOutAckBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutAckBps()
	cdu.mutation.SetOutAckBps(i)
	return cdu
}

// SetNillableOutAckBps sets the "out_ack_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutAckBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutAckBps(*i)
	}
	return cdu
}

// AddOutAckBps adds i to the "out_ack_bps" field.
func (cdu *CleanDataUpdate) AddOutAckBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutAckBps(i)
	return cdu
}

// SetInSynPps sets the "in_syn_pps" field.
func (cdu *CleanDataUpdate) SetInSynPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInSynPps()
	cdu.mutation.SetInSynPps(i)
	return cdu
}

// SetNillableInSynPps sets the "in_syn_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInSynPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInSynPps(*i)
	}
	return cdu
}

// AddInSynPps adds i to the "in_syn_pps" field.
func (cdu *CleanDataUpdate) AddInSynPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInSynPps(i)
	return cdu
}

// SetOutSynPps sets the "out_syn_pps" field.
func (cdu *CleanDataUpdate) SetOutSynPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutSynPps()
	cdu.mutation.SetOutSynPps(i)
	return cdu
}

// SetNillableOutSynPps sets the "out_syn_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutSynPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutSynPps(*i)
	}
	return cdu
}

// AddOutSynPps adds i to the "out_syn_pps" field.
func (cdu *CleanDataUpdate) AddOutSynPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutSynPps(i)
	return cdu
}

// SetInUDPPps sets the "in_udp_pps" field.
func (cdu *CleanDataUpdate) SetInUDPPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInUDPPps()
	cdu.mutation.SetInUDPPps(i)
	return cdu
}

// SetNillableInUDPPps sets the "in_udp_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInUDPPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInUDPPps(*i)
	}
	return cdu
}

// AddInUDPPps adds i to the "in_udp_pps" field.
func (cdu *CleanDataUpdate) AddInUDPPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInUDPPps(i)
	return cdu
}

// SetOutUDPPps sets the "out_udp_pps" field.
func (cdu *CleanDataUpdate) SetOutUDPPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutUDPPps()
	cdu.mutation.SetOutUDPPps(i)
	return cdu
}

// SetNillableOutUDPPps sets the "out_udp_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutUDPPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutUDPPps(*i)
	}
	return cdu
}

// AddOutUDPPps adds i to the "out_udp_pps" field.
func (cdu *CleanDataUpdate) AddOutUDPPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutUDPPps(i)
	return cdu
}

// SetInUDPBps sets the "in_udp_bps" field.
func (cdu *CleanDataUpdate) SetInUDPBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInUDPBps()
	cdu.mutation.SetInUDPBps(i)
	return cdu
}

// SetNillableInUDPBps sets the "in_udp_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInUDPBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInUDPBps(*i)
	}
	return cdu
}

// AddInUDPBps adds i to the "in_udp_bps" field.
func (cdu *CleanDataUpdate) AddInUDPBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInUDPBps(i)
	return cdu
}

// SetOutUDPBps sets the "out_udp_bps" field.
func (cdu *CleanDataUpdate) SetOutUDPBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutUDPBps()
	cdu.mutation.SetOutUDPBps(i)
	return cdu
}

// SetNillableOutUDPBps sets the "out_udp_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutUDPBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutUDPBps(*i)
	}
	return cdu
}

// AddOutUDPBps adds i to the "out_udp_bps" field.
func (cdu *CleanDataUpdate) AddOutUDPBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutUDPBps(i)
	return cdu
}

// SetInIcmpPps sets the "in_icmp_pps" field.
func (cdu *CleanDataUpdate) SetInIcmpPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInIcmpPps()
	cdu.mutation.SetInIcmpPps(i)
	return cdu
}

// SetNillableInIcmpPps sets the "in_icmp_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInIcmpPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInIcmpPps(*i)
	}
	return cdu
}

// AddInIcmpPps adds i to the "in_icmp_pps" field.
func (cdu *CleanDataUpdate) AddInIcmpPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInIcmpPps(i)
	return cdu
}

// SetInIcmpBps sets the "in_icmp_bps" field.
func (cdu *CleanDataUpdate) SetInIcmpBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInIcmpBps()
	cdu.mutation.SetInIcmpBps(i)
	return cdu
}

// SetNillableInIcmpBps sets the "in_icmp_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInIcmpBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInIcmpBps(*i)
	}
	return cdu
}

// AddInIcmpBps adds i to the "in_icmp_bps" field.
func (cdu *CleanDataUpdate) AddInIcmpBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInIcmpBps(i)
	return cdu
}

// SetOutIcmpBps sets the "out_icmp_bps" field.
func (cdu *CleanDataUpdate) SetOutIcmpBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutIcmpBps()
	cdu.mutation.SetOutIcmpBps(i)
	return cdu
}

// SetNillableOutIcmpBps sets the "out_icmp_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutIcmpBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutIcmpBps(*i)
	}
	return cdu
}

// AddOutIcmpBps adds i to the "out_icmp_bps" field.
func (cdu *CleanDataUpdate) AddOutIcmpBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutIcmpBps(i)
	return cdu
}

// SetOutIcmpPps sets the "out_icmp_pps" field.
func (cdu *CleanDataUpdate) SetOutIcmpPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutIcmpPps()
	cdu.mutation.SetOutIcmpPps(i)
	return cdu
}

// SetNillableOutIcmpPps sets the "out_icmp_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutIcmpPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutIcmpPps(*i)
	}
	return cdu
}

// AddOutIcmpPps adds i to the "out_icmp_pps" field.
func (cdu *CleanDataUpdate) AddOutIcmpPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutIcmpPps(i)
	return cdu
}

// SetInDNSPps sets the "in_dns_pps" field.
func (cdu *CleanDataUpdate) SetInDNSPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInDNSPps()
	cdu.mutation.SetInDNSPps(i)
	return cdu
}

// SetNillableInDNSPps sets the "in_dns_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInDNSPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInDNSPps(*i)
	}
	return cdu
}

// AddInDNSPps adds i to the "in_dns_pps" field.
func (cdu *CleanDataUpdate) AddInDNSPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInDNSPps(i)
	return cdu
}

// SetOutDNSPps sets the "out_dns_pps" field.
func (cdu *CleanDataUpdate) SetOutDNSPps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutDNSPps()
	cdu.mutation.SetOutDNSPps(i)
	return cdu
}

// SetNillableOutDNSPps sets the "out_dns_pps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutDNSPps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutDNSPps(*i)
	}
	return cdu
}

// AddOutDNSPps adds i to the "out_dns_pps" field.
func (cdu *CleanDataUpdate) AddOutDNSPps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutDNSPps(i)
	return cdu
}

// SetInDNSBps sets the "in_dns_bps" field.
func (cdu *CleanDataUpdate) SetInDNSBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetInDNSBps()
	cdu.mutation.SetInDNSBps(i)
	return cdu
}

// SetNillableInDNSBps sets the "in_dns_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableInDNSBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetInDNSBps(*i)
	}
	return cdu
}

// AddInDNSBps adds i to the "in_dns_bps" field.
func (cdu *CleanDataUpdate) AddInDNSBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddInDNSBps(i)
	return cdu
}

// SetOutDNSBps sets the "out_dns_bps" field.
func (cdu *CleanDataUpdate) SetOutDNSBps(i int64) *CleanDataUpdate {
	cdu.mutation.ResetOutDNSBps()
	cdu.mutation.SetOutDNSBps(i)
	return cdu
}

// SetNillableOutDNSBps sets the "out_dns_bps" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableOutDNSBps(i *int64) *CleanDataUpdate {
	if i != nil {
		cdu.SetOutDNSBps(*i)
	}
	return cdu
}

// AddOutDNSBps adds i to the "out_dns_bps" field.
func (cdu *CleanDataUpdate) AddOutDNSBps(i int64) *CleanDataUpdate {
	cdu.mutation.AddOutDNSBps(i)
	return cdu
}

// SetCFilterID sets the "c_filter_id" field.
func (cdu *CleanDataUpdate) SetCFilterID(i int) *CleanDataUpdate {
	cdu.mutation.ResetCFilterID()
	cdu.mutation.SetCFilterID(i)
	return cdu
}

// SetNillableCFilterID sets the "c_filter_id" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableCFilterID(i *int) *CleanDataUpdate {
	if i != nil {
		cdu.SetCFilterID(*i)
	}
	return cdu
}

// AddCFilterID adds i to the "c_filter_id" field.
func (cdu *CleanDataUpdate) AddCFilterID(i int) *CleanDataUpdate {
	cdu.mutation.AddCFilterID(i)
	return cdu
}

// SetAttackFlags sets the "attack_flags" field.
func (cdu *CleanDataUpdate) SetAttackFlags(i int) *CleanDataUpdate {
	cdu.mutation.ResetAttackFlags()
	cdu.mutation.SetAttackFlags(i)
	return cdu
}

// SetNillableAttackFlags sets the "attack_flags" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableAttackFlags(i *int) *CleanDataUpdate {
	if i != nil {
		cdu.SetAttackFlags(*i)
	}
	return cdu
}

// AddAttackFlags adds i to the "attack_flags" field.
func (cdu *CleanDataUpdate) AddAttackFlags(i int) *CleanDataUpdate {
	cdu.mutation.AddAttackFlags(i)
	return cdu
}

// SetCount sets the "count" field.
func (cdu *CleanDataUpdate) SetCount(i int) *CleanDataUpdate {
	cdu.mutation.ResetCount()
	cdu.mutation.SetCount(i)
	return cdu
}

// SetNillableCount sets the "count" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableCount(i *int) *CleanDataUpdate {
	if i != nil {
		cdu.SetCount(*i)
	}
	return cdu
}

// AddCount adds i to the "count" field.
func (cdu *CleanDataUpdate) AddCount(i int) *CleanDataUpdate {
	cdu.mutation.AddCount(i)
	return cdu
}

// SetIPType sets the "ip_type" field.
func (cdu *CleanDataUpdate) SetIPType(i int) *CleanDataUpdate {
	cdu.mutation.ResetIPType()
	cdu.mutation.SetIPType(i)
	return cdu
}

// SetNillableIPType sets the "ip_type" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableIPType(i *int) *CleanDataUpdate {
	if i != nil {
		cdu.SetIPType(*i)
	}
	return cdu
}

// AddIPType adds i to the "ip_type" field.
func (cdu *CleanDataUpdate) AddIPType(i int) *CleanDataUpdate {
	cdu.mutation.AddIPType(i)
	return cdu
}

// SetCFilter sets the "c_filter" field.
func (cdu *CleanDataUpdate) SetCFilter(s string) *CleanDataUpdate {
	cdu.mutation.SetCFilter(s)
	return cdu
}

// SetNillableCFilter sets the "c_filter" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableCFilter(s *string) *CleanDataUpdate {
	if s != nil {
		cdu.SetCFilter(*s)
	}
	return cdu
}

// ClearCFilter clears the value of the "c_filter" field.
func (cdu *CleanDataUpdate) ClearCFilter() *CleanDataUpdate {
	cdu.mutation.ClearCFilter()
	return cdu
}

// SetHost sets the "host" field.
func (cdu *CleanDataUpdate) SetHost(s string) *CleanDataUpdate {
	cdu.mutation.SetHost(s)
	return cdu
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (cdu *CleanDataUpdate) SetNillableHost(s *string) *CleanDataUpdate {
	if s != nil {
		cdu.SetHost(*s)
	}
	return cdu
}

// ClearHost clears the value of the "host" field.
func (cdu *CleanDataUpdate) ClearHost() *CleanDataUpdate {
	cdu.mutation.ClearHost()
	return cdu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cdu *CleanDataUpdate) SetTenant(t *Tenant) *CleanDataUpdate {
	return cdu.SetTenantID(t.ID)
}

// SetSpectrumAlert sets the "spectrum_alert" edge to the SpectrumAlert entity.
func (cdu *CleanDataUpdate) SetSpectrumAlert(s *SpectrumAlert) *CleanDataUpdate {
	return cdu.SetSpectrumAlertID(s.ID)
}

// Mutation returns the CleanDataMutation object of the builder.
func (cdu *CleanDataUpdate) Mutation() *CleanDataMutation {
	return cdu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (cdu *CleanDataUpdate) ClearTenant() *CleanDataUpdate {
	cdu.mutation.ClearTenant()
	return cdu
}

// ClearSpectrumAlert clears the "spectrum_alert" edge to the SpectrumAlert entity.
func (cdu *CleanDataUpdate) ClearSpectrumAlert() *CleanDataUpdate {
	cdu.mutation.ClearSpectrumAlert()
	return cdu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cdu *CleanDataUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, cdu.sqlSave, cdu.mutation, cdu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cdu *CleanDataUpdate) SaveX(ctx context.Context) int {
	affected, err := cdu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cdu *CleanDataUpdate) Exec(ctx context.Context) error {
	_, err := cdu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cdu *CleanDataUpdate) ExecX(ctx context.Context) {
	if err := cdu.Exec(ctx); err != nil {
		panic(err)
	}
}

func (cdu *CleanDataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(cleandata.Table, cleandata.Columns, sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt))
	if ps := cdu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cdu.mutation.IP(); ok {
		_spec.SetField(cleandata.FieldIP, field.TypeString, value)
	}
	if value, ok := cdu.mutation.Time(); ok {
		_spec.SetField(cleandata.FieldTime, field.TypeTime, value)
	}
	if value, ok := cdu.mutation.InBps(); ok {
		_spec.SetField(cleandata.FieldInBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInBps(); ok {
		_spec.AddField(cleandata.FieldInBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutBps(); ok {
		_spec.SetField(cleandata.FieldOutBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutBps(); ok {
		_spec.AddField(cleandata.FieldOutBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InPps(); ok {
		_spec.SetField(cleandata.FieldInPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInPps(); ok {
		_spec.AddField(cleandata.FieldInPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutPps(); ok {
		_spec.SetField(cleandata.FieldOutPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutPps(); ok {
		_spec.AddField(cleandata.FieldOutPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InAckPps(); ok {
		_spec.SetField(cleandata.FieldInAckPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInAckPps(); ok {
		_spec.AddField(cleandata.FieldInAckPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutAckPps(); ok {
		_spec.SetField(cleandata.FieldOutAckPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutAckPps(); ok {
		_spec.AddField(cleandata.FieldOutAckPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InAckBps(); ok {
		_spec.SetField(cleandata.FieldInAckBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInAckBps(); ok {
		_spec.AddField(cleandata.FieldInAckBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutAckBps(); ok {
		_spec.SetField(cleandata.FieldOutAckBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutAckBps(); ok {
		_spec.AddField(cleandata.FieldOutAckBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InSynPps(); ok {
		_spec.SetField(cleandata.FieldInSynPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInSynPps(); ok {
		_spec.AddField(cleandata.FieldInSynPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutSynPps(); ok {
		_spec.SetField(cleandata.FieldOutSynPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutSynPps(); ok {
		_spec.AddField(cleandata.FieldOutSynPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InUDPPps(); ok {
		_spec.SetField(cleandata.FieldInUDPPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInUDPPps(); ok {
		_spec.AddField(cleandata.FieldInUDPPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutUDPPps(); ok {
		_spec.SetField(cleandata.FieldOutUDPPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutUDPPps(); ok {
		_spec.AddField(cleandata.FieldOutUDPPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InUDPBps(); ok {
		_spec.SetField(cleandata.FieldInUDPBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInUDPBps(); ok {
		_spec.AddField(cleandata.FieldInUDPBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutUDPBps(); ok {
		_spec.SetField(cleandata.FieldOutUDPBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutUDPBps(); ok {
		_spec.AddField(cleandata.FieldOutUDPBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InIcmpPps(); ok {
		_spec.SetField(cleandata.FieldInIcmpPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInIcmpPps(); ok {
		_spec.AddField(cleandata.FieldInIcmpPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InIcmpBps(); ok {
		_spec.SetField(cleandata.FieldInIcmpBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInIcmpBps(); ok {
		_spec.AddField(cleandata.FieldInIcmpBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutIcmpBps(); ok {
		_spec.SetField(cleandata.FieldOutIcmpBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutIcmpBps(); ok {
		_spec.AddField(cleandata.FieldOutIcmpBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutIcmpPps(); ok {
		_spec.SetField(cleandata.FieldOutIcmpPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutIcmpPps(); ok {
		_spec.AddField(cleandata.FieldOutIcmpPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InDNSPps(); ok {
		_spec.SetField(cleandata.FieldInDNSPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInDNSPps(); ok {
		_spec.AddField(cleandata.FieldInDNSPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutDNSPps(); ok {
		_spec.SetField(cleandata.FieldOutDNSPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutDNSPps(); ok {
		_spec.AddField(cleandata.FieldOutDNSPps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.InDNSBps(); ok {
		_spec.SetField(cleandata.FieldInDNSBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedInDNSBps(); ok {
		_spec.AddField(cleandata.FieldInDNSBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.OutDNSBps(); ok {
		_spec.SetField(cleandata.FieldOutDNSBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.AddedOutDNSBps(); ok {
		_spec.AddField(cleandata.FieldOutDNSBps, field.TypeInt64, value)
	}
	if value, ok := cdu.mutation.CFilterID(); ok {
		_spec.SetField(cleandata.FieldCFilterID, field.TypeInt, value)
	}
	if value, ok := cdu.mutation.AddedCFilterID(); ok {
		_spec.AddField(cleandata.FieldCFilterID, field.TypeInt, value)
	}
	if value, ok := cdu.mutation.AttackFlags(); ok {
		_spec.SetField(cleandata.FieldAttackFlags, field.TypeInt, value)
	}
	if value, ok := cdu.mutation.AddedAttackFlags(); ok {
		_spec.AddField(cleandata.FieldAttackFlags, field.TypeInt, value)
	}
	if value, ok := cdu.mutation.Count(); ok {
		_spec.SetField(cleandata.FieldCount, field.TypeInt, value)
	}
	if value, ok := cdu.mutation.AddedCount(); ok {
		_spec.AddField(cleandata.FieldCount, field.TypeInt, value)
	}
	if value, ok := cdu.mutation.IPType(); ok {
		_spec.SetField(cleandata.FieldIPType, field.TypeInt, value)
	}
	if value, ok := cdu.mutation.AddedIPType(); ok {
		_spec.AddField(cleandata.FieldIPType, field.TypeInt, value)
	}
	if value, ok := cdu.mutation.CFilter(); ok {
		_spec.SetField(cleandata.FieldCFilter, field.TypeString, value)
	}
	if cdu.mutation.CFilterCleared() {
		_spec.ClearField(cleandata.FieldCFilter, field.TypeString)
	}
	if value, ok := cdu.mutation.Host(); ok {
		_spec.SetField(cleandata.FieldHost, field.TypeString, value)
	}
	if cdu.mutation.HostCleared() {
		_spec.ClearField(cleandata.FieldHost, field.TypeString)
	}
	if cdu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cleandata.TenantTable,
			Columns: []string{cleandata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cdu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cleandata.TenantTable,
			Columns: []string{cleandata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if cdu.mutation.SpectrumAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cleandata.SpectrumAlertTable,
			Columns: []string{cleandata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cdu.mutation.SpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cleandata.SpectrumAlertTable,
			Columns: []string{cleandata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cdu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cleandata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cdu.mutation.done = true
	return n, nil
}

// CleanDataUpdateOne is the builder for updating a single CleanData entity.
type CleanDataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CleanDataMutation
}

// SetTenantID sets the "tenant_id" field.
func (cduo *CleanDataUpdateOne) SetTenantID(i int) *CleanDataUpdateOne {
	cduo.mutation.SetTenantID(i)
	return cduo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableTenantID(i *int) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetTenantID(*i)
	}
	return cduo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (cduo *CleanDataUpdateOne) ClearTenantID() *CleanDataUpdateOne {
	cduo.mutation.ClearTenantID()
	return cduo
}

// SetSpectrumAlertID sets the "spectrum_alert_id" field.
func (cduo *CleanDataUpdateOne) SetSpectrumAlertID(i int) *CleanDataUpdateOne {
	cduo.mutation.SetSpectrumAlertID(i)
	return cduo
}

// SetNillableSpectrumAlertID sets the "spectrum_alert_id" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableSpectrumAlertID(i *int) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetSpectrumAlertID(*i)
	}
	return cduo
}

// ClearSpectrumAlertID clears the value of the "spectrum_alert_id" field.
func (cduo *CleanDataUpdateOne) ClearSpectrumAlertID() *CleanDataUpdateOne {
	cduo.mutation.ClearSpectrumAlertID()
	return cduo
}

// SetIP sets the "ip" field.
func (cduo *CleanDataUpdateOne) SetIP(s string) *CleanDataUpdateOne {
	cduo.mutation.SetIP(s)
	return cduo
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableIP(s *string) *CleanDataUpdateOne {
	if s != nil {
		cduo.SetIP(*s)
	}
	return cduo
}

// SetTime sets the "time" field.
func (cduo *CleanDataUpdateOne) SetTime(t time.Time) *CleanDataUpdateOne {
	cduo.mutation.SetTime(t)
	return cduo
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableTime(t *time.Time) *CleanDataUpdateOne {
	if t != nil {
		cduo.SetTime(*t)
	}
	return cduo
}

// SetInBps sets the "in_bps" field.
func (cduo *CleanDataUpdateOne) SetInBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInBps()
	cduo.mutation.SetInBps(i)
	return cduo
}

// SetNillableInBps sets the "in_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInBps(*i)
	}
	return cduo
}

// AddInBps adds i to the "in_bps" field.
func (cduo *CleanDataUpdateOne) AddInBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInBps(i)
	return cduo
}

// SetOutBps sets the "out_bps" field.
func (cduo *CleanDataUpdateOne) SetOutBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutBps()
	cduo.mutation.SetOutBps(i)
	return cduo
}

// SetNillableOutBps sets the "out_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutBps(*i)
	}
	return cduo
}

// AddOutBps adds i to the "out_bps" field.
func (cduo *CleanDataUpdateOne) AddOutBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutBps(i)
	return cduo
}

// SetInPps sets the "in_pps" field.
func (cduo *CleanDataUpdateOne) SetInPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInPps()
	cduo.mutation.SetInPps(i)
	return cduo
}

// SetNillableInPps sets the "in_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInPps(*i)
	}
	return cduo
}

// AddInPps adds i to the "in_pps" field.
func (cduo *CleanDataUpdateOne) AddInPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInPps(i)
	return cduo
}

// SetOutPps sets the "out_pps" field.
func (cduo *CleanDataUpdateOne) SetOutPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutPps()
	cduo.mutation.SetOutPps(i)
	return cduo
}

// SetNillableOutPps sets the "out_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutPps(*i)
	}
	return cduo
}

// AddOutPps adds i to the "out_pps" field.
func (cduo *CleanDataUpdateOne) AddOutPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutPps(i)
	return cduo
}

// SetInAckPps sets the "in_ack_pps" field.
func (cduo *CleanDataUpdateOne) SetInAckPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInAckPps()
	cduo.mutation.SetInAckPps(i)
	return cduo
}

// SetNillableInAckPps sets the "in_ack_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInAckPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInAckPps(*i)
	}
	return cduo
}

// AddInAckPps adds i to the "in_ack_pps" field.
func (cduo *CleanDataUpdateOne) AddInAckPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInAckPps(i)
	return cduo
}

// SetOutAckPps sets the "out_ack_pps" field.
func (cduo *CleanDataUpdateOne) SetOutAckPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutAckPps()
	cduo.mutation.SetOutAckPps(i)
	return cduo
}

// SetNillableOutAckPps sets the "out_ack_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutAckPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutAckPps(*i)
	}
	return cduo
}

// AddOutAckPps adds i to the "out_ack_pps" field.
func (cduo *CleanDataUpdateOne) AddOutAckPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutAckPps(i)
	return cduo
}

// SetInAckBps sets the "in_ack_bps" field.
func (cduo *CleanDataUpdateOne) SetInAckBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInAckBps()
	cduo.mutation.SetInAckBps(i)
	return cduo
}

// SetNillableInAckBps sets the "in_ack_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInAckBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInAckBps(*i)
	}
	return cduo
}

// AddInAckBps adds i to the "in_ack_bps" field.
func (cduo *CleanDataUpdateOne) AddInAckBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInAckBps(i)
	return cduo
}

// SetOutAckBps sets the "out_ack_bps" field.
func (cduo *CleanDataUpdateOne) SetOutAckBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutAckBps()
	cduo.mutation.SetOutAckBps(i)
	return cduo
}

// SetNillableOutAckBps sets the "out_ack_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutAckBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutAckBps(*i)
	}
	return cduo
}

// AddOutAckBps adds i to the "out_ack_bps" field.
func (cduo *CleanDataUpdateOne) AddOutAckBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutAckBps(i)
	return cduo
}

// SetInSynPps sets the "in_syn_pps" field.
func (cduo *CleanDataUpdateOne) SetInSynPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInSynPps()
	cduo.mutation.SetInSynPps(i)
	return cduo
}

// SetNillableInSynPps sets the "in_syn_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInSynPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInSynPps(*i)
	}
	return cduo
}

// AddInSynPps adds i to the "in_syn_pps" field.
func (cduo *CleanDataUpdateOne) AddInSynPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInSynPps(i)
	return cduo
}

// SetOutSynPps sets the "out_syn_pps" field.
func (cduo *CleanDataUpdateOne) SetOutSynPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutSynPps()
	cduo.mutation.SetOutSynPps(i)
	return cduo
}

// SetNillableOutSynPps sets the "out_syn_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutSynPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutSynPps(*i)
	}
	return cduo
}

// AddOutSynPps adds i to the "out_syn_pps" field.
func (cduo *CleanDataUpdateOne) AddOutSynPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutSynPps(i)
	return cduo
}

// SetInUDPPps sets the "in_udp_pps" field.
func (cduo *CleanDataUpdateOne) SetInUDPPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInUDPPps()
	cduo.mutation.SetInUDPPps(i)
	return cduo
}

// SetNillableInUDPPps sets the "in_udp_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInUDPPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInUDPPps(*i)
	}
	return cduo
}

// AddInUDPPps adds i to the "in_udp_pps" field.
func (cduo *CleanDataUpdateOne) AddInUDPPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInUDPPps(i)
	return cduo
}

// SetOutUDPPps sets the "out_udp_pps" field.
func (cduo *CleanDataUpdateOne) SetOutUDPPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutUDPPps()
	cduo.mutation.SetOutUDPPps(i)
	return cduo
}

// SetNillableOutUDPPps sets the "out_udp_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutUDPPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutUDPPps(*i)
	}
	return cduo
}

// AddOutUDPPps adds i to the "out_udp_pps" field.
func (cduo *CleanDataUpdateOne) AddOutUDPPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutUDPPps(i)
	return cduo
}

// SetInUDPBps sets the "in_udp_bps" field.
func (cduo *CleanDataUpdateOne) SetInUDPBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInUDPBps()
	cduo.mutation.SetInUDPBps(i)
	return cduo
}

// SetNillableInUDPBps sets the "in_udp_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInUDPBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInUDPBps(*i)
	}
	return cduo
}

// AddInUDPBps adds i to the "in_udp_bps" field.
func (cduo *CleanDataUpdateOne) AddInUDPBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInUDPBps(i)
	return cduo
}

// SetOutUDPBps sets the "out_udp_bps" field.
func (cduo *CleanDataUpdateOne) SetOutUDPBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutUDPBps()
	cduo.mutation.SetOutUDPBps(i)
	return cduo
}

// SetNillableOutUDPBps sets the "out_udp_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutUDPBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutUDPBps(*i)
	}
	return cduo
}

// AddOutUDPBps adds i to the "out_udp_bps" field.
func (cduo *CleanDataUpdateOne) AddOutUDPBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutUDPBps(i)
	return cduo
}

// SetInIcmpPps sets the "in_icmp_pps" field.
func (cduo *CleanDataUpdateOne) SetInIcmpPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInIcmpPps()
	cduo.mutation.SetInIcmpPps(i)
	return cduo
}

// SetNillableInIcmpPps sets the "in_icmp_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInIcmpPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInIcmpPps(*i)
	}
	return cduo
}

// AddInIcmpPps adds i to the "in_icmp_pps" field.
func (cduo *CleanDataUpdateOne) AddInIcmpPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInIcmpPps(i)
	return cduo
}

// SetInIcmpBps sets the "in_icmp_bps" field.
func (cduo *CleanDataUpdateOne) SetInIcmpBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInIcmpBps()
	cduo.mutation.SetInIcmpBps(i)
	return cduo
}

// SetNillableInIcmpBps sets the "in_icmp_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInIcmpBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInIcmpBps(*i)
	}
	return cduo
}

// AddInIcmpBps adds i to the "in_icmp_bps" field.
func (cduo *CleanDataUpdateOne) AddInIcmpBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInIcmpBps(i)
	return cduo
}

// SetOutIcmpBps sets the "out_icmp_bps" field.
func (cduo *CleanDataUpdateOne) SetOutIcmpBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutIcmpBps()
	cduo.mutation.SetOutIcmpBps(i)
	return cduo
}

// SetNillableOutIcmpBps sets the "out_icmp_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutIcmpBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutIcmpBps(*i)
	}
	return cduo
}

// AddOutIcmpBps adds i to the "out_icmp_bps" field.
func (cduo *CleanDataUpdateOne) AddOutIcmpBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutIcmpBps(i)
	return cduo
}

// SetOutIcmpPps sets the "out_icmp_pps" field.
func (cduo *CleanDataUpdateOne) SetOutIcmpPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutIcmpPps()
	cduo.mutation.SetOutIcmpPps(i)
	return cduo
}

// SetNillableOutIcmpPps sets the "out_icmp_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutIcmpPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutIcmpPps(*i)
	}
	return cduo
}

// AddOutIcmpPps adds i to the "out_icmp_pps" field.
func (cduo *CleanDataUpdateOne) AddOutIcmpPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutIcmpPps(i)
	return cduo
}

// SetInDNSPps sets the "in_dns_pps" field.
func (cduo *CleanDataUpdateOne) SetInDNSPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInDNSPps()
	cduo.mutation.SetInDNSPps(i)
	return cduo
}

// SetNillableInDNSPps sets the "in_dns_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInDNSPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInDNSPps(*i)
	}
	return cduo
}

// AddInDNSPps adds i to the "in_dns_pps" field.
func (cduo *CleanDataUpdateOne) AddInDNSPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInDNSPps(i)
	return cduo
}

// SetOutDNSPps sets the "out_dns_pps" field.
func (cduo *CleanDataUpdateOne) SetOutDNSPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutDNSPps()
	cduo.mutation.SetOutDNSPps(i)
	return cduo
}

// SetNillableOutDNSPps sets the "out_dns_pps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutDNSPps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutDNSPps(*i)
	}
	return cduo
}

// AddOutDNSPps adds i to the "out_dns_pps" field.
func (cduo *CleanDataUpdateOne) AddOutDNSPps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutDNSPps(i)
	return cduo
}

// SetInDNSBps sets the "in_dns_bps" field.
func (cduo *CleanDataUpdateOne) SetInDNSBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetInDNSBps()
	cduo.mutation.SetInDNSBps(i)
	return cduo
}

// SetNillableInDNSBps sets the "in_dns_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableInDNSBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetInDNSBps(*i)
	}
	return cduo
}

// AddInDNSBps adds i to the "in_dns_bps" field.
func (cduo *CleanDataUpdateOne) AddInDNSBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddInDNSBps(i)
	return cduo
}

// SetOutDNSBps sets the "out_dns_bps" field.
func (cduo *CleanDataUpdateOne) SetOutDNSBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.ResetOutDNSBps()
	cduo.mutation.SetOutDNSBps(i)
	return cduo
}

// SetNillableOutDNSBps sets the "out_dns_bps" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableOutDNSBps(i *int64) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetOutDNSBps(*i)
	}
	return cduo
}

// AddOutDNSBps adds i to the "out_dns_bps" field.
func (cduo *CleanDataUpdateOne) AddOutDNSBps(i int64) *CleanDataUpdateOne {
	cduo.mutation.AddOutDNSBps(i)
	return cduo
}

// SetCFilterID sets the "c_filter_id" field.
func (cduo *CleanDataUpdateOne) SetCFilterID(i int) *CleanDataUpdateOne {
	cduo.mutation.ResetCFilterID()
	cduo.mutation.SetCFilterID(i)
	return cduo
}

// SetNillableCFilterID sets the "c_filter_id" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableCFilterID(i *int) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetCFilterID(*i)
	}
	return cduo
}

// AddCFilterID adds i to the "c_filter_id" field.
func (cduo *CleanDataUpdateOne) AddCFilterID(i int) *CleanDataUpdateOne {
	cduo.mutation.AddCFilterID(i)
	return cduo
}

// SetAttackFlags sets the "attack_flags" field.
func (cduo *CleanDataUpdateOne) SetAttackFlags(i int) *CleanDataUpdateOne {
	cduo.mutation.ResetAttackFlags()
	cduo.mutation.SetAttackFlags(i)
	return cduo
}

// SetNillableAttackFlags sets the "attack_flags" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableAttackFlags(i *int) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetAttackFlags(*i)
	}
	return cduo
}

// AddAttackFlags adds i to the "attack_flags" field.
func (cduo *CleanDataUpdateOne) AddAttackFlags(i int) *CleanDataUpdateOne {
	cduo.mutation.AddAttackFlags(i)
	return cduo
}

// SetCount sets the "count" field.
func (cduo *CleanDataUpdateOne) SetCount(i int) *CleanDataUpdateOne {
	cduo.mutation.ResetCount()
	cduo.mutation.SetCount(i)
	return cduo
}

// SetNillableCount sets the "count" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableCount(i *int) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetCount(*i)
	}
	return cduo
}

// AddCount adds i to the "count" field.
func (cduo *CleanDataUpdateOne) AddCount(i int) *CleanDataUpdateOne {
	cduo.mutation.AddCount(i)
	return cduo
}

// SetIPType sets the "ip_type" field.
func (cduo *CleanDataUpdateOne) SetIPType(i int) *CleanDataUpdateOne {
	cduo.mutation.ResetIPType()
	cduo.mutation.SetIPType(i)
	return cduo
}

// SetNillableIPType sets the "ip_type" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableIPType(i *int) *CleanDataUpdateOne {
	if i != nil {
		cduo.SetIPType(*i)
	}
	return cduo
}

// AddIPType adds i to the "ip_type" field.
func (cduo *CleanDataUpdateOne) AddIPType(i int) *CleanDataUpdateOne {
	cduo.mutation.AddIPType(i)
	return cduo
}

// SetCFilter sets the "c_filter" field.
func (cduo *CleanDataUpdateOne) SetCFilter(s string) *CleanDataUpdateOne {
	cduo.mutation.SetCFilter(s)
	return cduo
}

// SetNillableCFilter sets the "c_filter" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableCFilter(s *string) *CleanDataUpdateOne {
	if s != nil {
		cduo.SetCFilter(*s)
	}
	return cduo
}

// ClearCFilter clears the value of the "c_filter" field.
func (cduo *CleanDataUpdateOne) ClearCFilter() *CleanDataUpdateOne {
	cduo.mutation.ClearCFilter()
	return cduo
}

// SetHost sets the "host" field.
func (cduo *CleanDataUpdateOne) SetHost(s string) *CleanDataUpdateOne {
	cduo.mutation.SetHost(s)
	return cduo
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (cduo *CleanDataUpdateOne) SetNillableHost(s *string) *CleanDataUpdateOne {
	if s != nil {
		cduo.SetHost(*s)
	}
	return cduo
}

// ClearHost clears the value of the "host" field.
func (cduo *CleanDataUpdateOne) ClearHost() *CleanDataUpdateOne {
	cduo.mutation.ClearHost()
	return cduo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cduo *CleanDataUpdateOne) SetTenant(t *Tenant) *CleanDataUpdateOne {
	return cduo.SetTenantID(t.ID)
}

// SetSpectrumAlert sets the "spectrum_alert" edge to the SpectrumAlert entity.
func (cduo *CleanDataUpdateOne) SetSpectrumAlert(s *SpectrumAlert) *CleanDataUpdateOne {
	return cduo.SetSpectrumAlertID(s.ID)
}

// Mutation returns the CleanDataMutation object of the builder.
func (cduo *CleanDataUpdateOne) Mutation() *CleanDataMutation {
	return cduo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (cduo *CleanDataUpdateOne) ClearTenant() *CleanDataUpdateOne {
	cduo.mutation.ClearTenant()
	return cduo
}

// ClearSpectrumAlert clears the "spectrum_alert" edge to the SpectrumAlert entity.
func (cduo *CleanDataUpdateOne) ClearSpectrumAlert() *CleanDataUpdateOne {
	cduo.mutation.ClearSpectrumAlert()
	return cduo
}

// Where appends a list predicates to the CleanDataUpdate builder.
func (cduo *CleanDataUpdateOne) Where(ps ...predicate.CleanData) *CleanDataUpdateOne {
	cduo.mutation.Where(ps...)
	return cduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cduo *CleanDataUpdateOne) Select(field string, fields ...string) *CleanDataUpdateOne {
	cduo.fields = append([]string{field}, fields...)
	return cduo
}

// Save executes the query and returns the updated CleanData entity.
func (cduo *CleanDataUpdateOne) Save(ctx context.Context) (*CleanData, error) {
	return withHooks(ctx, cduo.sqlSave, cduo.mutation, cduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cduo *CleanDataUpdateOne) SaveX(ctx context.Context) *CleanData {
	node, err := cduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cduo *CleanDataUpdateOne) Exec(ctx context.Context) error {
	_, err := cduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cduo *CleanDataUpdateOne) ExecX(ctx context.Context) {
	if err := cduo.Exec(ctx); err != nil {
		panic(err)
	}
}

func (cduo *CleanDataUpdateOne) sqlSave(ctx context.Context) (_node *CleanData, err error) {
	_spec := sqlgraph.NewUpdateSpec(cleandata.Table, cleandata.Columns, sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt))
	id, ok := cduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CleanData.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cleandata.FieldID)
		for _, f := range fields {
			if !cleandata.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != cleandata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cduo.mutation.IP(); ok {
		_spec.SetField(cleandata.FieldIP, field.TypeString, value)
	}
	if value, ok := cduo.mutation.Time(); ok {
		_spec.SetField(cleandata.FieldTime, field.TypeTime, value)
	}
	if value, ok := cduo.mutation.InBps(); ok {
		_spec.SetField(cleandata.FieldInBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInBps(); ok {
		_spec.AddField(cleandata.FieldInBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutBps(); ok {
		_spec.SetField(cleandata.FieldOutBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutBps(); ok {
		_spec.AddField(cleandata.FieldOutBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InPps(); ok {
		_spec.SetField(cleandata.FieldInPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInPps(); ok {
		_spec.AddField(cleandata.FieldInPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutPps(); ok {
		_spec.SetField(cleandata.FieldOutPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutPps(); ok {
		_spec.AddField(cleandata.FieldOutPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InAckPps(); ok {
		_spec.SetField(cleandata.FieldInAckPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInAckPps(); ok {
		_spec.AddField(cleandata.FieldInAckPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutAckPps(); ok {
		_spec.SetField(cleandata.FieldOutAckPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutAckPps(); ok {
		_spec.AddField(cleandata.FieldOutAckPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InAckBps(); ok {
		_spec.SetField(cleandata.FieldInAckBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInAckBps(); ok {
		_spec.AddField(cleandata.FieldInAckBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutAckBps(); ok {
		_spec.SetField(cleandata.FieldOutAckBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutAckBps(); ok {
		_spec.AddField(cleandata.FieldOutAckBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InSynPps(); ok {
		_spec.SetField(cleandata.FieldInSynPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInSynPps(); ok {
		_spec.AddField(cleandata.FieldInSynPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutSynPps(); ok {
		_spec.SetField(cleandata.FieldOutSynPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutSynPps(); ok {
		_spec.AddField(cleandata.FieldOutSynPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InUDPPps(); ok {
		_spec.SetField(cleandata.FieldInUDPPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInUDPPps(); ok {
		_spec.AddField(cleandata.FieldInUDPPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutUDPPps(); ok {
		_spec.SetField(cleandata.FieldOutUDPPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutUDPPps(); ok {
		_spec.AddField(cleandata.FieldOutUDPPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InUDPBps(); ok {
		_spec.SetField(cleandata.FieldInUDPBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInUDPBps(); ok {
		_spec.AddField(cleandata.FieldInUDPBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutUDPBps(); ok {
		_spec.SetField(cleandata.FieldOutUDPBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutUDPBps(); ok {
		_spec.AddField(cleandata.FieldOutUDPBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InIcmpPps(); ok {
		_spec.SetField(cleandata.FieldInIcmpPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInIcmpPps(); ok {
		_spec.AddField(cleandata.FieldInIcmpPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InIcmpBps(); ok {
		_spec.SetField(cleandata.FieldInIcmpBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInIcmpBps(); ok {
		_spec.AddField(cleandata.FieldInIcmpBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutIcmpBps(); ok {
		_spec.SetField(cleandata.FieldOutIcmpBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutIcmpBps(); ok {
		_spec.AddField(cleandata.FieldOutIcmpBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutIcmpPps(); ok {
		_spec.SetField(cleandata.FieldOutIcmpPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutIcmpPps(); ok {
		_spec.AddField(cleandata.FieldOutIcmpPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InDNSPps(); ok {
		_spec.SetField(cleandata.FieldInDNSPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInDNSPps(); ok {
		_spec.AddField(cleandata.FieldInDNSPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutDNSPps(); ok {
		_spec.SetField(cleandata.FieldOutDNSPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutDNSPps(); ok {
		_spec.AddField(cleandata.FieldOutDNSPps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.InDNSBps(); ok {
		_spec.SetField(cleandata.FieldInDNSBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedInDNSBps(); ok {
		_spec.AddField(cleandata.FieldInDNSBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.OutDNSBps(); ok {
		_spec.SetField(cleandata.FieldOutDNSBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.AddedOutDNSBps(); ok {
		_spec.AddField(cleandata.FieldOutDNSBps, field.TypeInt64, value)
	}
	if value, ok := cduo.mutation.CFilterID(); ok {
		_spec.SetField(cleandata.FieldCFilterID, field.TypeInt, value)
	}
	if value, ok := cduo.mutation.AddedCFilterID(); ok {
		_spec.AddField(cleandata.FieldCFilterID, field.TypeInt, value)
	}
	if value, ok := cduo.mutation.AttackFlags(); ok {
		_spec.SetField(cleandata.FieldAttackFlags, field.TypeInt, value)
	}
	if value, ok := cduo.mutation.AddedAttackFlags(); ok {
		_spec.AddField(cleandata.FieldAttackFlags, field.TypeInt, value)
	}
	if value, ok := cduo.mutation.Count(); ok {
		_spec.SetField(cleandata.FieldCount, field.TypeInt, value)
	}
	if value, ok := cduo.mutation.AddedCount(); ok {
		_spec.AddField(cleandata.FieldCount, field.TypeInt, value)
	}
	if value, ok := cduo.mutation.IPType(); ok {
		_spec.SetField(cleandata.FieldIPType, field.TypeInt, value)
	}
	if value, ok := cduo.mutation.AddedIPType(); ok {
		_spec.AddField(cleandata.FieldIPType, field.TypeInt, value)
	}
	if value, ok := cduo.mutation.CFilter(); ok {
		_spec.SetField(cleandata.FieldCFilter, field.TypeString, value)
	}
	if cduo.mutation.CFilterCleared() {
		_spec.ClearField(cleandata.FieldCFilter, field.TypeString)
	}
	if value, ok := cduo.mutation.Host(); ok {
		_spec.SetField(cleandata.FieldHost, field.TypeString, value)
	}
	if cduo.mutation.HostCleared() {
		_spec.ClearField(cleandata.FieldHost, field.TypeString)
	}
	if cduo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cleandata.TenantTable,
			Columns: []string{cleandata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cduo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cleandata.TenantTable,
			Columns: []string{cleandata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if cduo.mutation.SpectrumAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cleandata.SpectrumAlertTable,
			Columns: []string{cleandata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cduo.mutation.SpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cleandata.SpectrumAlertTable,
			Columns: []string{cleandata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &CleanData{config: cduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cleandata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cduo.mutation.done = true
	return _node, nil
}
