// Code generated by ent, DO NOT EDIT.

package cloudflowdata

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldRemark, v))
}

// CloudAlertID applies equality check predicate on the "cloud_alert_id" field. It's identical to CloudAlertIDEQ.
func CloudAlertID(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldCloudAlertID, v))
}

// SrcIP applies equality check predicate on the "src_ip" field. It's identical to SrcIPEQ.
func SrcIP(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldSrcIP, v))
}

// SrcPort applies equality check predicate on the "src_port" field. It's identical to SrcPortEQ.
func SrcPort(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldSrcPort, v))
}

// DstIP applies equality check predicate on the "dst_ip" field. It's identical to DstIPEQ.
func DstIP(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldDstIP, v))
}

// DstPort applies equality check predicate on the "dst_port" field. It's identical to DstPortEQ.
func DstPort(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldDstPort, v))
}

// Protocol applies equality check predicate on the "protocol" field. It's identical to ProtocolEQ.
func Protocol(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldProtocol, v))
}

// MaxAttackPps applies equality check predicate on the "max_attack_pps" field. It's identical to MaxAttackPpsEQ.
func MaxAttackPps(v int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldMaxAttackPps, v))
}

// FlowOverMaxPpsCount applies equality check predicate on the "flow_over_max_pps_count" field. It's identical to FlowOverMaxPpsCountEQ.
func FlowOverMaxPpsCount(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldFlowOverMaxPpsCount, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldStartTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldEndTime, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldContainsFold(FieldRemark, v))
}

// CloudAlertIDEQ applies the EQ predicate on the "cloud_alert_id" field.
func CloudAlertIDEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldCloudAlertID, v))
}

// CloudAlertIDNEQ applies the NEQ predicate on the "cloud_alert_id" field.
func CloudAlertIDNEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldCloudAlertID, v))
}

// CloudAlertIDIn applies the In predicate on the "cloud_alert_id" field.
func CloudAlertIDIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldCloudAlertID, vs...))
}

// CloudAlertIDNotIn applies the NotIn predicate on the "cloud_alert_id" field.
func CloudAlertIDNotIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldCloudAlertID, vs...))
}

// CloudAlertIDIsNil applies the IsNil predicate on the "cloud_alert_id" field.
func CloudAlertIDIsNil() predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIsNull(FieldCloudAlertID))
}

// CloudAlertIDNotNil applies the NotNil predicate on the "cloud_alert_id" field.
func CloudAlertIDNotNil() predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotNull(FieldCloudAlertID))
}

// SrcIPEQ applies the EQ predicate on the "src_ip" field.
func SrcIPEQ(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldSrcIP, v))
}

// SrcIPNEQ applies the NEQ predicate on the "src_ip" field.
func SrcIPNEQ(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldSrcIP, v))
}

// SrcIPIn applies the In predicate on the "src_ip" field.
func SrcIPIn(vs ...string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldSrcIP, vs...))
}

// SrcIPNotIn applies the NotIn predicate on the "src_ip" field.
func SrcIPNotIn(vs ...string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldSrcIP, vs...))
}

// SrcIPGT applies the GT predicate on the "src_ip" field.
func SrcIPGT(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldSrcIP, v))
}

// SrcIPGTE applies the GTE predicate on the "src_ip" field.
func SrcIPGTE(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldSrcIP, v))
}

// SrcIPLT applies the LT predicate on the "src_ip" field.
func SrcIPLT(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldSrcIP, v))
}

// SrcIPLTE applies the LTE predicate on the "src_ip" field.
func SrcIPLTE(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldSrcIP, v))
}

// SrcIPContains applies the Contains predicate on the "src_ip" field.
func SrcIPContains(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldContains(FieldSrcIP, v))
}

// SrcIPHasPrefix applies the HasPrefix predicate on the "src_ip" field.
func SrcIPHasPrefix(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldHasPrefix(FieldSrcIP, v))
}

// SrcIPHasSuffix applies the HasSuffix predicate on the "src_ip" field.
func SrcIPHasSuffix(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldHasSuffix(FieldSrcIP, v))
}

// SrcIPEqualFold applies the EqualFold predicate on the "src_ip" field.
func SrcIPEqualFold(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEqualFold(FieldSrcIP, v))
}

// SrcIPContainsFold applies the ContainsFold predicate on the "src_ip" field.
func SrcIPContainsFold(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldContainsFold(FieldSrcIP, v))
}

// SrcPortEQ applies the EQ predicate on the "src_port" field.
func SrcPortEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldSrcPort, v))
}

// SrcPortNEQ applies the NEQ predicate on the "src_port" field.
func SrcPortNEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldSrcPort, v))
}

// SrcPortIn applies the In predicate on the "src_port" field.
func SrcPortIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldSrcPort, vs...))
}

// SrcPortNotIn applies the NotIn predicate on the "src_port" field.
func SrcPortNotIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldSrcPort, vs...))
}

// SrcPortGT applies the GT predicate on the "src_port" field.
func SrcPortGT(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldSrcPort, v))
}

// SrcPortGTE applies the GTE predicate on the "src_port" field.
func SrcPortGTE(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldSrcPort, v))
}

// SrcPortLT applies the LT predicate on the "src_port" field.
func SrcPortLT(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldSrcPort, v))
}

// SrcPortLTE applies the LTE predicate on the "src_port" field.
func SrcPortLTE(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldSrcPort, v))
}

// DstIPEQ applies the EQ predicate on the "dst_ip" field.
func DstIPEQ(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldDstIP, v))
}

// DstIPNEQ applies the NEQ predicate on the "dst_ip" field.
func DstIPNEQ(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldDstIP, v))
}

// DstIPIn applies the In predicate on the "dst_ip" field.
func DstIPIn(vs ...string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldDstIP, vs...))
}

// DstIPNotIn applies the NotIn predicate on the "dst_ip" field.
func DstIPNotIn(vs ...string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldDstIP, vs...))
}

// DstIPGT applies the GT predicate on the "dst_ip" field.
func DstIPGT(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldDstIP, v))
}

// DstIPGTE applies the GTE predicate on the "dst_ip" field.
func DstIPGTE(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldDstIP, v))
}

// DstIPLT applies the LT predicate on the "dst_ip" field.
func DstIPLT(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldDstIP, v))
}

// DstIPLTE applies the LTE predicate on the "dst_ip" field.
func DstIPLTE(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldDstIP, v))
}

// DstIPContains applies the Contains predicate on the "dst_ip" field.
func DstIPContains(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldContains(FieldDstIP, v))
}

// DstIPHasPrefix applies the HasPrefix predicate on the "dst_ip" field.
func DstIPHasPrefix(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldHasPrefix(FieldDstIP, v))
}

// DstIPHasSuffix applies the HasSuffix predicate on the "dst_ip" field.
func DstIPHasSuffix(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldHasSuffix(FieldDstIP, v))
}

// DstIPEqualFold applies the EqualFold predicate on the "dst_ip" field.
func DstIPEqualFold(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEqualFold(FieldDstIP, v))
}

// DstIPContainsFold applies the ContainsFold predicate on the "dst_ip" field.
func DstIPContainsFold(v string) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldContainsFold(FieldDstIP, v))
}

// DstPortEQ applies the EQ predicate on the "dst_port" field.
func DstPortEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldDstPort, v))
}

// DstPortNEQ applies the NEQ predicate on the "dst_port" field.
func DstPortNEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldDstPort, v))
}

// DstPortIn applies the In predicate on the "dst_port" field.
func DstPortIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldDstPort, vs...))
}

// DstPortNotIn applies the NotIn predicate on the "dst_port" field.
func DstPortNotIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldDstPort, vs...))
}

// DstPortGT applies the GT predicate on the "dst_port" field.
func DstPortGT(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldDstPort, v))
}

// DstPortGTE applies the GTE predicate on the "dst_port" field.
func DstPortGTE(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldDstPort, v))
}

// DstPortLT applies the LT predicate on the "dst_port" field.
func DstPortLT(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldDstPort, v))
}

// DstPortLTE applies the LTE predicate on the "dst_port" field.
func DstPortLTE(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldDstPort, v))
}

// ProtocolEQ applies the EQ predicate on the "protocol" field.
func ProtocolEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldProtocol, v))
}

// ProtocolNEQ applies the NEQ predicate on the "protocol" field.
func ProtocolNEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldProtocol, v))
}

// ProtocolIn applies the In predicate on the "protocol" field.
func ProtocolIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldProtocol, vs...))
}

// ProtocolNotIn applies the NotIn predicate on the "protocol" field.
func ProtocolNotIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldProtocol, vs...))
}

// ProtocolGT applies the GT predicate on the "protocol" field.
func ProtocolGT(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldProtocol, v))
}

// ProtocolGTE applies the GTE predicate on the "protocol" field.
func ProtocolGTE(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldProtocol, v))
}

// ProtocolLT applies the LT predicate on the "protocol" field.
func ProtocolLT(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldProtocol, v))
}

// ProtocolLTE applies the LTE predicate on the "protocol" field.
func ProtocolLTE(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldProtocol, v))
}

// MaxAttackPpsEQ applies the EQ predicate on the "max_attack_pps" field.
func MaxAttackPpsEQ(v int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldMaxAttackPps, v))
}

// MaxAttackPpsNEQ applies the NEQ predicate on the "max_attack_pps" field.
func MaxAttackPpsNEQ(v int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldMaxAttackPps, v))
}

// MaxAttackPpsIn applies the In predicate on the "max_attack_pps" field.
func MaxAttackPpsIn(vs ...int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldMaxAttackPps, vs...))
}

// MaxAttackPpsNotIn applies the NotIn predicate on the "max_attack_pps" field.
func MaxAttackPpsNotIn(vs ...int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldMaxAttackPps, vs...))
}

// MaxAttackPpsGT applies the GT predicate on the "max_attack_pps" field.
func MaxAttackPpsGT(v int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldMaxAttackPps, v))
}

// MaxAttackPpsGTE applies the GTE predicate on the "max_attack_pps" field.
func MaxAttackPpsGTE(v int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldMaxAttackPps, v))
}

// MaxAttackPpsLT applies the LT predicate on the "max_attack_pps" field.
func MaxAttackPpsLT(v int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldMaxAttackPps, v))
}

// MaxAttackPpsLTE applies the LTE predicate on the "max_attack_pps" field.
func MaxAttackPpsLTE(v int64) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldMaxAttackPps, v))
}

// FlowOverMaxPpsCountEQ applies the EQ predicate on the "flow_over_max_pps_count" field.
func FlowOverMaxPpsCountEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldFlowOverMaxPpsCount, v))
}

// FlowOverMaxPpsCountNEQ applies the NEQ predicate on the "flow_over_max_pps_count" field.
func FlowOverMaxPpsCountNEQ(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldFlowOverMaxPpsCount, v))
}

// FlowOverMaxPpsCountIn applies the In predicate on the "flow_over_max_pps_count" field.
func FlowOverMaxPpsCountIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldFlowOverMaxPpsCount, vs...))
}

// FlowOverMaxPpsCountNotIn applies the NotIn predicate on the "flow_over_max_pps_count" field.
func FlowOverMaxPpsCountNotIn(vs ...int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldFlowOverMaxPpsCount, vs...))
}

// FlowOverMaxPpsCountGT applies the GT predicate on the "flow_over_max_pps_count" field.
func FlowOverMaxPpsCountGT(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldFlowOverMaxPpsCount, v))
}

// FlowOverMaxPpsCountGTE applies the GTE predicate on the "flow_over_max_pps_count" field.
func FlowOverMaxPpsCountGTE(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldFlowOverMaxPpsCount, v))
}

// FlowOverMaxPpsCountLT applies the LT predicate on the "flow_over_max_pps_count" field.
func FlowOverMaxPpsCountLT(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldFlowOverMaxPpsCount, v))
}

// FlowOverMaxPpsCountLTE applies the LTE predicate on the "flow_over_max_pps_count" field.
func FlowOverMaxPpsCountLTE(v int) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldFlowOverMaxPpsCount, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldStartTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.FieldNotNull(FieldEndTime))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.CloudFlowData {
	return predicate.CloudFlowData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.CloudFlowData {
	return predicate.CloudFlowData(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCloudAlert applies the HasEdge predicate on the "cloud_alert" edge.
func HasCloudAlert() predicate.CloudFlowData {
	return predicate.CloudFlowData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, CloudAlertTable, CloudAlertColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCloudAlertWith applies the HasEdge predicate on the "cloud_alert" edge with a given conditions (other predicates).
func HasCloudAlertWith(preds ...predicate.CloudAlert) predicate.CloudFlowData {
	return predicate.CloudFlowData(func(s *sql.Selector) {
		step := newCloudAlertStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.CloudFlowData) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.CloudFlowData) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.CloudFlowData) predicate.CloudFlowData {
	return predicate.CloudFlowData(sql.NotPredicates(p))
}
