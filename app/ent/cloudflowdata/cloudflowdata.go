// Code generated by ent, DO NOT EDIT.

package cloudflowdata

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the cloudflowdata type in the database.
	Label = "cloud_flow_data"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldCloudAlertID holds the string denoting the cloud_alert_id field in the database.
	FieldCloudAlertID = "cloud_alert_id"
	// FieldSrcIP holds the string denoting the src_ip field in the database.
	FieldSrcIP = "src_ip"
	// FieldSrcPort holds the string denoting the src_port field in the database.
	FieldSrcPort = "src_port"
	// FieldDstIP holds the string denoting the dst_ip field in the database.
	FieldDstIP = "dst_ip"
	// FieldDstPort holds the string denoting the dst_port field in the database.
	FieldDstPort = "dst_port"
	// FieldProtocol holds the string denoting the protocol field in the database.
	FieldProtocol = "protocol"
	// FieldMaxAttackPps holds the string denoting the max_attack_pps field in the database.
	FieldMaxAttackPps = "max_attack_pps"
	// FieldFlowOverMaxPpsCount holds the string denoting the flow_over_max_pps_count field in the database.
	FieldFlowOverMaxPpsCount = "flow_over_max_pps_count"
	// FieldStartTime holds the string denoting the start_time field in the database.
	FieldStartTime = "start_time"
	// FieldEndTime holds the string denoting the end_time field in the database.
	FieldEndTime = "end_time"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeCloudAlert holds the string denoting the cloud_alert edge name in mutations.
	EdgeCloudAlert = "cloud_alert"
	// Table holds the table name of the cloudflowdata in the database.
	Table = "cloud_flow_data"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "cloud_flow_data"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// CloudAlertTable is the table that holds the cloud_alert relation/edge.
	CloudAlertTable = "cloud_flow_data"
	// CloudAlertInverseTable is the table name for the CloudAlert entity.
	// It exists in this package in order to avoid circular dependency with the "cloudalert" package.
	CloudAlertInverseTable = "cloud_alerts"
	// CloudAlertColumn is the table column denoting the cloud_alert relation/edge.
	CloudAlertColumn = "cloud_alert_id"
)

// Columns holds all SQL columns for cloudflowdata fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldCloudAlertID,
	FieldSrcIP,
	FieldSrcPort,
	FieldDstIP,
	FieldDstPort,
	FieldProtocol,
	FieldMaxAttackPps,
	FieldFlowOverMaxPpsCount,
	FieldStartTime,
	FieldEndTime,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the CloudFlowData queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByCloudAlertID orders the results by the cloud_alert_id field.
func ByCloudAlertID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCloudAlertID, opts...).ToFunc()
}

// BySrcIP orders the results by the src_ip field.
func BySrcIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSrcIP, opts...).ToFunc()
}

// BySrcPort orders the results by the src_port field.
func BySrcPort(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSrcPort, opts...).ToFunc()
}

// ByDstIP orders the results by the dst_ip field.
func ByDstIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDstIP, opts...).ToFunc()
}

// ByDstPort orders the results by the dst_port field.
func ByDstPort(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDstPort, opts...).ToFunc()
}

// ByProtocol orders the results by the protocol field.
func ByProtocol(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProtocol, opts...).ToFunc()
}

// ByMaxAttackPps orders the results by the max_attack_pps field.
func ByMaxAttackPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxAttackPps, opts...).ToFunc()
}

// ByFlowOverMaxPpsCount orders the results by the flow_over_max_pps_count field.
func ByFlowOverMaxPpsCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFlowOverMaxPpsCount, opts...).ToFunc()
}

// ByStartTime orders the results by the start_time field.
func ByStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartTime, opts...).ToFunc()
}

// ByEndTime orders the results by the end_time field.
func ByEndTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEndTime, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// ByCloudAlertField orders the results by cloud_alert field.
func ByCloudAlertField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCloudAlertStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newCloudAlertStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CloudAlertInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, CloudAlertTable, CloudAlertColumn),
	)
}
