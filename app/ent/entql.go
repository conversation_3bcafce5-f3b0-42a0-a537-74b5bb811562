// Code generated by ent, DO NOT EDIT.

package ent

import (
	"meta/app/ent/casbinrule"
	"meta/app/ent/cleandata"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/datasync"
	"meta/app/ent/group"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/notify"
	"meta/app/ent/predicate"
	"meta/app/ent/protectgroup"
	"meta/app/ent/skylinedos"
	"meta/app/ent/socgroupticket"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/strategy"
	"meta/app/ent/systemapi"
	"meta/app/ent/systemconfig"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/ent/useroperationlog"
	"meta/app/ent/wofang"
	"meta/app/ent/wofangalert"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/entql"
	"entgo.io/ent/schema/field"
)

// schemaGraph holds a representation of ent/schema at runtime.
var schemaGraph = func() *sqlgraph.Schema {
	graph := &sqlgraph.Schema{Nodes: make([]*sqlgraph.Node, 24)}
	graph.Nodes[0] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   casbinrule.Table,
			Columns: casbinrule.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: casbinrule.FieldID,
			},
		},
		Type: "CasbinRule",
		Fields: map[string]*sqlgraph.FieldSpec{
			casbinrule.FieldType: {Type: field.TypeString, Column: casbinrule.FieldType},
			casbinrule.FieldSub:  {Type: field.TypeString, Column: casbinrule.FieldSub},
			casbinrule.FieldDom:  {Type: field.TypeString, Column: casbinrule.FieldDom},
			casbinrule.FieldObj:  {Type: field.TypeString, Column: casbinrule.FieldObj},
			casbinrule.FieldAct:  {Type: field.TypeString, Column: casbinrule.FieldAct},
			casbinrule.FieldV4:   {Type: field.TypeString, Column: casbinrule.FieldV4},
			casbinrule.FieldV5:   {Type: field.TypeString, Column: casbinrule.FieldV5},
		},
	}
	graph.Nodes[1] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   cleandata.Table,
			Columns: cleandata.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: cleandata.FieldID,
			},
		},
		Type: "CleanData",
		Fields: map[string]*sqlgraph.FieldSpec{
			cleandata.FieldTenantID:        {Type: field.TypeInt, Column: cleandata.FieldTenantID},
			cleandata.FieldCreatedAt:       {Type: field.TypeTime, Column: cleandata.FieldCreatedAt},
			cleandata.FieldSpectrumAlertID: {Type: field.TypeInt, Column: cleandata.FieldSpectrumAlertID},
			cleandata.FieldIP:              {Type: field.TypeString, Column: cleandata.FieldIP},
			cleandata.FieldTime:            {Type: field.TypeTime, Column: cleandata.FieldTime},
			cleandata.FieldInBps:           {Type: field.TypeInt64, Column: cleandata.FieldInBps},
			cleandata.FieldOutBps:          {Type: field.TypeInt64, Column: cleandata.FieldOutBps},
			cleandata.FieldInPps:           {Type: field.TypeInt64, Column: cleandata.FieldInPps},
			cleandata.FieldOutPps:          {Type: field.TypeInt64, Column: cleandata.FieldOutPps},
			cleandata.FieldInAckPps:        {Type: field.TypeInt64, Column: cleandata.FieldInAckPps},
			cleandata.FieldOutAckPps:       {Type: field.TypeInt64, Column: cleandata.FieldOutAckPps},
			cleandata.FieldInAckBps:        {Type: field.TypeInt64, Column: cleandata.FieldInAckBps},
			cleandata.FieldOutAckBps:       {Type: field.TypeInt64, Column: cleandata.FieldOutAckBps},
			cleandata.FieldInSynPps:        {Type: field.TypeInt64, Column: cleandata.FieldInSynPps},
			cleandata.FieldOutSynPps:       {Type: field.TypeInt64, Column: cleandata.FieldOutSynPps},
			cleandata.FieldInUDPPps:        {Type: field.TypeInt64, Column: cleandata.FieldInUDPPps},
			cleandata.FieldOutUDPPps:       {Type: field.TypeInt64, Column: cleandata.FieldOutUDPPps},
			cleandata.FieldInUDPBps:        {Type: field.TypeInt64, Column: cleandata.FieldInUDPBps},
			cleandata.FieldOutUDPBps:       {Type: field.TypeInt64, Column: cleandata.FieldOutUDPBps},
			cleandata.FieldInIcmpPps:       {Type: field.TypeInt64, Column: cleandata.FieldInIcmpPps},
			cleandata.FieldInIcmpBps:       {Type: field.TypeInt64, Column: cleandata.FieldInIcmpBps},
			cleandata.FieldOutIcmpBps:      {Type: field.TypeInt64, Column: cleandata.FieldOutIcmpBps},
			cleandata.FieldOutIcmpPps:      {Type: field.TypeInt64, Column: cleandata.FieldOutIcmpPps},
			cleandata.FieldInDNSPps:        {Type: field.TypeInt64, Column: cleandata.FieldInDNSPps},
			cleandata.FieldOutDNSPps:       {Type: field.TypeInt64, Column: cleandata.FieldOutDNSPps},
			cleandata.FieldInDNSBps:        {Type: field.TypeInt64, Column: cleandata.FieldInDNSBps},
			cleandata.FieldOutDNSBps:       {Type: field.TypeInt64, Column: cleandata.FieldOutDNSBps},
			cleandata.FieldCFilterID:       {Type: field.TypeInt, Column: cleandata.FieldCFilterID},
			cleandata.FieldAttackFlags:     {Type: field.TypeInt, Column: cleandata.FieldAttackFlags},
			cleandata.FieldCount:           {Type: field.TypeInt, Column: cleandata.FieldCount},
			cleandata.FieldIPType:          {Type: field.TypeInt, Column: cleandata.FieldIPType},
			cleandata.FieldCFilter:         {Type: field.TypeString, Column: cleandata.FieldCFilter},
			cleandata.FieldHost:            {Type: field.TypeString, Column: cleandata.FieldHost},
		},
	}
	graph.Nodes[2] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   cloudalert.Table,
			Columns: cloudalert.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: cloudalert.FieldID,
			},
		},
		Type: "CloudAlert",
		Fields: map[string]*sqlgraph.FieldSpec{
			cloudalert.FieldTenantID:          {Type: field.TypeInt, Column: cloudalert.FieldTenantID},
			cloudalert.FieldCreatedAt:         {Type: field.TypeTime, Column: cloudalert.FieldCreatedAt},
			cloudalert.FieldUpdatedAt:         {Type: field.TypeTime, Column: cloudalert.FieldUpdatedAt},
			cloudalert.FieldRemark:            {Type: field.TypeString, Column: cloudalert.FieldRemark},
			cloudalert.FieldSrcIP:             {Type: field.TypeString, Column: cloudalert.FieldSrcIP},
			cloudalert.FieldSrcPort:           {Type: field.TypeInt, Column: cloudalert.FieldSrcPort},
			cloudalert.FieldDstIP:             {Type: field.TypeString, Column: cloudalert.FieldDstIP},
			cloudalert.FieldDstPort:           {Type: field.TypeInt, Column: cloudalert.FieldDstPort},
			cloudalert.FieldDefenceMode:       {Type: field.TypeInt, Column: cloudalert.FieldDefenceMode},
			cloudalert.FieldFlowMode:          {Type: field.TypeInt, Column: cloudalert.FieldFlowMode},
			cloudalert.FieldTCPAckNum:         {Type: field.TypeString, Column: cloudalert.FieldTCPAckNum},
			cloudalert.FieldTCPSeqNum:         {Type: field.TypeString, Column: cloudalert.FieldTCPSeqNum},
			cloudalert.FieldProtocol:          {Type: field.TypeInt, Column: cloudalert.FieldProtocol},
			cloudalert.FieldDefenceLevel:      {Type: field.TypeInt, Column: cloudalert.FieldDefenceLevel},
			cloudalert.FieldMaxPps:            {Type: field.TypeInt64, Column: cloudalert.FieldMaxPps},
			cloudalert.FieldMaxAttackPps:      {Type: field.TypeInt64, Column: cloudalert.FieldMaxAttackPps},
			cloudalert.FieldOverlimitPktCount: {Type: field.TypeInt, Column: cloudalert.FieldOverlimitPktCount},
			cloudalert.FieldStartTime:         {Type: field.TypeTime, Column: cloudalert.FieldStartTime},
			cloudalert.FieldEndTime:           {Type: field.TypeTime, Column: cloudalert.FieldEndTime},
		},
	}
	graph.Nodes[3] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   cloudattackdata.Table,
			Columns: cloudattackdata.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: cloudattackdata.FieldID,
			},
		},
		Type: "CloudAttackData",
		Fields: map[string]*sqlgraph.FieldSpec{
			cloudattackdata.FieldTenantID:         {Type: field.TypeInt, Column: cloudattackdata.FieldTenantID},
			cloudattackdata.FieldCreatedAt:        {Type: field.TypeTime, Column: cloudattackdata.FieldCreatedAt},
			cloudattackdata.FieldUpdatedAt:        {Type: field.TypeTime, Column: cloudattackdata.FieldUpdatedAt},
			cloudattackdata.FieldRemark:           {Type: field.TypeString, Column: cloudattackdata.FieldRemark},
			cloudattackdata.FieldSrcIP:            {Type: field.TypeString, Column: cloudattackdata.FieldSrcIP},
			cloudattackdata.FieldSrcPort:          {Type: field.TypeInt, Column: cloudattackdata.FieldSrcPort},
			cloudattackdata.FieldDstIP:            {Type: field.TypeString, Column: cloudattackdata.FieldDstIP},
			cloudattackdata.FieldDstPort:          {Type: field.TypeInt, Column: cloudattackdata.FieldDstPort},
			cloudattackdata.FieldProtocol:         {Type: field.TypeInt, Column: cloudattackdata.FieldProtocol},
			cloudattackdata.FieldCurrentAttackPps: {Type: field.TypeInt64, Column: cloudattackdata.FieldCurrentAttackPps},
			cloudattackdata.FieldStartTime:        {Type: field.TypeTime, Column: cloudattackdata.FieldStartTime},
			cloudattackdata.FieldEndTime:          {Type: field.TypeTime, Column: cloudattackdata.FieldEndTime},
		},
	}
	graph.Nodes[4] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   cloudflowdata.Table,
			Columns: cloudflowdata.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: cloudflowdata.FieldID,
			},
		},
		Type: "CloudFlowData",
		Fields: map[string]*sqlgraph.FieldSpec{
			cloudflowdata.FieldTenantID:            {Type: field.TypeInt, Column: cloudflowdata.FieldTenantID},
			cloudflowdata.FieldCreatedAt:           {Type: field.TypeTime, Column: cloudflowdata.FieldCreatedAt},
			cloudflowdata.FieldUpdatedAt:           {Type: field.TypeTime, Column: cloudflowdata.FieldUpdatedAt},
			cloudflowdata.FieldRemark:              {Type: field.TypeString, Column: cloudflowdata.FieldRemark},
			cloudflowdata.FieldCloudAlertID:        {Type: field.TypeInt, Column: cloudflowdata.FieldCloudAlertID},
			cloudflowdata.FieldSrcIP:               {Type: field.TypeString, Column: cloudflowdata.FieldSrcIP},
			cloudflowdata.FieldSrcPort:             {Type: field.TypeInt, Column: cloudflowdata.FieldSrcPort},
			cloudflowdata.FieldDstIP:               {Type: field.TypeString, Column: cloudflowdata.FieldDstIP},
			cloudflowdata.FieldDstPort:             {Type: field.TypeInt, Column: cloudflowdata.FieldDstPort},
			cloudflowdata.FieldProtocol:            {Type: field.TypeInt, Column: cloudflowdata.FieldProtocol},
			cloudflowdata.FieldMaxAttackPps:        {Type: field.TypeInt64, Column: cloudflowdata.FieldMaxAttackPps},
			cloudflowdata.FieldFlowOverMaxPpsCount: {Type: field.TypeInt, Column: cloudflowdata.FieldFlowOverMaxPpsCount},
			cloudflowdata.FieldStartTime:           {Type: field.TypeTime, Column: cloudflowdata.FieldStartTime},
			cloudflowdata.FieldEndTime:             {Type: field.TypeTime, Column: cloudflowdata.FieldEndTime},
		},
	}
	graph.Nodes[5] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   datasync.Table,
			Columns: datasync.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: datasync.FieldID,
			},
		},
		Type: "DataSync",
		Fields: map[string]*sqlgraph.FieldSpec{
			datasync.FieldCreatedAt:   {Type: field.TypeTime, Column: datasync.FieldCreatedAt},
			datasync.FieldUpdatedAt:   {Type: field.TypeTime, Column: datasync.FieldUpdatedAt},
			datasync.FieldRemark:      {Type: field.TypeString, Column: datasync.FieldRemark},
			datasync.FieldPreDataList: {Type: field.TypeJSON, Column: datasync.FieldPreDataList},
			datasync.FieldDataList:    {Type: field.TypeJSON, Column: datasync.FieldDataList},
			datasync.FieldDataType:    {Type: field.TypeString, Column: datasync.FieldDataType},
			datasync.FieldType:        {Type: field.TypeString, Column: datasync.FieldType},
		},
	}
	graph.Nodes[6] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   group.Table,
			Columns: group.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: group.FieldID,
			},
		},
		Type: "Group",
		Fields: map[string]*sqlgraph.FieldSpec{
			group.FieldTenantID: {Type: field.TypeInt, Column: group.FieldTenantID},
			group.FieldName:     {Type: field.TypeString, Column: group.FieldName},
		},
	}
	graph.Nodes[7] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   matrixspectrumalert.Table,
			Columns: matrixspectrumalert.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: matrixspectrumalert.FieldID,
			},
		},
		Type: "MatrixSpectrumAlert",
		Fields: map[string]*sqlgraph.FieldSpec{
			matrixspectrumalert.FieldTenantID:         {Type: field.TypeInt, Column: matrixspectrumalert.FieldTenantID},
			matrixspectrumalert.FieldCreatedAt:        {Type: field.TypeTime, Column: matrixspectrumalert.FieldCreatedAt},
			matrixspectrumalert.FieldUpdatedAt:        {Type: field.TypeTime, Column: matrixspectrumalert.FieldUpdatedAt},
			matrixspectrumalert.FieldRemark:           {Type: field.TypeString, Column: matrixspectrumalert.FieldRemark},
			matrixspectrumalert.FieldWofangID:         {Type: field.TypeInt, Column: matrixspectrumalert.FieldWofangID},
			matrixspectrumalert.FieldMatrixStrategyID: {Type: field.TypeInt, Column: matrixspectrumalert.FieldMatrixStrategyID},
			matrixspectrumalert.FieldIPList:           {Type: field.TypeJSON, Column: matrixspectrumalert.FieldIPList},
			matrixspectrumalert.FieldRegion:           {Type: field.TypeString, Column: matrixspectrumalert.FieldRegion},
			matrixspectrumalert.FieldNetType:          {Type: field.TypeString, Column: matrixspectrumalert.FieldNetType},
			matrixspectrumalert.FieldIsp:              {Type: field.TypeString, Column: matrixspectrumalert.FieldIsp},
			matrixspectrumalert.FieldStartTime:        {Type: field.TypeTime, Column: matrixspectrumalert.FieldStartTime},
			matrixspectrumalert.FieldEndTime:          {Type: field.TypeTime, Column: matrixspectrumalert.FieldEndTime},
			matrixspectrumalert.FieldAttackType:       {Type: field.TypeString, Column: matrixspectrumalert.FieldAttackType},
			matrixspectrumalert.FieldBps:              {Type: field.TypeInt64, Column: matrixspectrumalert.FieldBps},
			matrixspectrumalert.FieldAttackInfo:       {Type: field.TypeJSON, Column: matrixspectrumalert.FieldAttackInfo},
		},
	}
	graph.Nodes[8] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   matrixspectrumdata.Table,
			Columns: matrixspectrumdata.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: matrixspectrumdata.FieldID,
			},
		},
		Type: "MatrixSpectrumData",
		Fields: map[string]*sqlgraph.FieldSpec{
			matrixspectrumdata.FieldTenantID:              {Type: field.TypeInt, Column: matrixspectrumdata.FieldTenantID},
			matrixspectrumdata.FieldCreatedAt:             {Type: field.TypeTime, Column: matrixspectrumdata.FieldCreatedAt},
			matrixspectrumdata.FieldUpdatedAt:             {Type: field.TypeTime, Column: matrixspectrumdata.FieldUpdatedAt},
			matrixspectrumdata.FieldMatrixSpectrumAlertID: {Type: field.TypeInt, Column: matrixspectrumdata.FieldMatrixSpectrumAlertID},
			matrixspectrumdata.FieldRegion:                {Type: field.TypeString, Column: matrixspectrumdata.FieldRegion},
			matrixspectrumdata.FieldNetType:               {Type: field.TypeString, Column: matrixspectrumdata.FieldNetType},
			matrixspectrumdata.FieldIsp:                   {Type: field.TypeString, Column: matrixspectrumdata.FieldIsp},
			matrixspectrumdata.FieldBps:                   {Type: field.TypeInt64, Column: matrixspectrumdata.FieldBps},
			matrixspectrumdata.FieldTime:                  {Type: field.TypeTime, Column: matrixspectrumdata.FieldTime},
		},
	}
	graph.Nodes[9] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   matrixstrategy.Table,
			Columns: matrixstrategy.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: matrixstrategy.FieldID,
			},
		},
		Type: "MatrixStrategy",
		Fields: map[string]*sqlgraph.FieldSpec{
			matrixstrategy.FieldCreatedAt:  {Type: field.TypeTime, Column: matrixstrategy.FieldCreatedAt},
			matrixstrategy.FieldUpdatedAt:  {Type: field.TypeTime, Column: matrixstrategy.FieldUpdatedAt},
			matrixstrategy.FieldRemark:     {Type: field.TypeString, Column: matrixstrategy.FieldRemark},
			matrixstrategy.FieldName:       {Type: field.TypeString, Column: matrixstrategy.FieldName},
			matrixstrategy.FieldRegion:     {Type: field.TypeString, Column: matrixstrategy.FieldRegion},
			matrixstrategy.FieldNetType:    {Type: field.TypeString, Column: matrixstrategy.FieldNetType},
			matrixstrategy.FieldIsp:        {Type: field.TypeString, Column: matrixstrategy.FieldIsp},
			matrixstrategy.FieldMonitorBps: {Type: field.TypeInt64, Column: matrixstrategy.FieldMonitorBps},
			matrixstrategy.FieldDragBps:    {Type: field.TypeInt64, Column: matrixstrategy.FieldDragBps},
			matrixstrategy.FieldDragType:   {Type: field.TypeInt, Column: matrixstrategy.FieldDragType},
		},
	}
	graph.Nodes[10] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   notify.Table,
			Columns: notify.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: notify.FieldID,
			},
		},
		Type: "Notify",
		Fields: map[string]*sqlgraph.FieldSpec{
			notify.FieldCreatedAt:     {Type: field.TypeTime, Column: notify.FieldCreatedAt},
			notify.FieldUpdatedAt:     {Type: field.TypeTime, Column: notify.FieldUpdatedAt},
			notify.FieldTenantID:      {Type: field.TypeInt, Column: notify.FieldTenantID},
			notify.FieldRemark:        {Type: field.TypeString, Column: notify.FieldRemark},
			notify.FieldName:          {Type: field.TypeString, Column: notify.FieldName},
			notify.FieldPopo:          {Type: field.TypeBool, Column: notify.FieldPopo},
			notify.FieldEmail:         {Type: field.TypeBool, Column: notify.FieldEmail},
			notify.FieldSms:           {Type: field.TypeBool, Column: notify.FieldSms},
			notify.FieldPhone:         {Type: field.TypeBool, Column: notify.FieldPhone},
			notify.FieldPopoGroups:    {Type: field.TypeJSON, Column: notify.FieldPopoGroups},
			notify.FieldEmails:        {Type: field.TypeJSON, Column: notify.FieldEmails},
			notify.FieldPhones:        {Type: field.TypeJSON, Column: notify.FieldPhones},
			notify.FieldIPWhitelists:  {Type: field.TypeJSON, Column: notify.FieldIPWhitelists},
			notify.FieldSystem:        {Type: field.TypeBool, Column: notify.FieldSystem},
			notify.FieldEnabled:       {Type: field.TypeBool, Column: notify.FieldEnabled},
			notify.FieldSaNotifyPopo:  {Type: field.TypeBool, Column: notify.FieldSaNotifyPopo},
			notify.FieldSaNotifyEmail: {Type: field.TypeBool, Column: notify.FieldSaNotifyEmail},
		},
	}
	graph.Nodes[11] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   protectgroup.Table,
			Columns: protectgroup.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: protectgroup.FieldID,
			},
		},
		Type: "ProtectGroup",
		Fields: map[string]*sqlgraph.FieldSpec{
			protectgroup.FieldTenantID:    {Type: field.TypeInt, Column: protectgroup.FieldTenantID},
			protectgroup.FieldCreatedAt:   {Type: field.TypeTime, Column: protectgroup.FieldCreatedAt},
			protectgroup.FieldUpdatedAt:   {Type: field.TypeTime, Column: protectgroup.FieldUpdatedAt},
			protectgroup.FieldRemark:      {Type: field.TypeString, Column: protectgroup.FieldRemark},
			protectgroup.FieldGroupName:   {Type: field.TypeString, Column: protectgroup.FieldGroupName},
			protectgroup.FieldGroupID:     {Type: field.TypeInt64, Column: protectgroup.FieldGroupID},
			protectgroup.FieldType:        {Type: field.TypeInt, Column: protectgroup.FieldType},
			protectgroup.FieldIPList:      {Type: field.TypeJSON, Column: protectgroup.FieldIPList},
			protectgroup.FieldExpandIP:    {Type: field.TypeString, Column: protectgroup.FieldExpandIP},
			protectgroup.FieldMonitorInfo: {Type: field.TypeJSON, Column: protectgroup.FieldMonitorInfo},
			protectgroup.FieldDragInfo:    {Type: field.TypeJSON, Column: protectgroup.FieldDragInfo},
			protectgroup.FieldNds4Config:  {Type: field.TypeJSON, Column: protectgroup.FieldNds4Config},
			protectgroup.FieldNds6Config:  {Type: field.TypeJSON, Column: protectgroup.FieldNds6Config},
		},
	}
	graph.Nodes[12] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   skylinedos.Table,
			Columns: skylinedos.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: skylinedos.FieldID,
			},
		},
		Type: "SkylineDos",
		Fields: map[string]*sqlgraph.FieldSpec{
			skylinedos.FieldTenantID:       {Type: field.TypeInt, Column: skylinedos.FieldTenantID},
			skylinedos.FieldCreatedAt:      {Type: field.TypeTime, Column: skylinedos.FieldCreatedAt},
			skylinedos.FieldUpdatedAt:      {Type: field.TypeTime, Column: skylinedos.FieldUpdatedAt},
			skylinedos.FieldRemark:         {Type: field.TypeString, Column: skylinedos.FieldRemark},
			skylinedos.FieldStartTime:      {Type: field.TypeTime, Column: skylinedos.FieldStartTime},
			skylinedos.FieldEndTime:        {Type: field.TypeTime, Column: skylinedos.FieldEndTime},
			skylinedos.FieldRegion:         {Type: field.TypeString, Column: skylinedos.FieldRegion},
			skylinedos.FieldResource:       {Type: field.TypeString, Column: skylinedos.FieldResource},
			skylinedos.FieldResourceType:   {Type: field.TypeString, Column: skylinedos.FieldResourceType},
			skylinedos.FieldVectorTypes:    {Type: field.TypeJSON, Column: skylinedos.FieldVectorTypes},
			skylinedos.FieldStatus:         {Type: field.TypeString, Column: skylinedos.FieldStatus},
			skylinedos.FieldAttackID:       {Type: field.TypeString, Column: skylinedos.FieldAttackID},
			skylinedos.FieldAttackCounters: {Type: field.TypeJSON, Column: skylinedos.FieldAttackCounters},
			skylinedos.FieldProject:        {Type: field.TypeString, Column: skylinedos.FieldProject},
			skylinedos.FieldDurationTime:   {Type: field.TypeInt64, Column: skylinedos.FieldDurationTime},
		},
	}
	graph.Nodes[13] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   socgroupticket.Table,
			Columns: socgroupticket.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: socgroupticket.FieldID,
			},
		},
		Type: "SocGroupTicket",
		Fields: map[string]*sqlgraph.FieldSpec{
			socgroupticket.FieldCreatedAt:     {Type: field.TypeTime, Column: socgroupticket.FieldCreatedAt},
			socgroupticket.FieldUpdatedAt:     {Type: field.TypeTime, Column: socgroupticket.FieldUpdatedAt},
			socgroupticket.FieldTenantID:      {Type: field.TypeInt, Column: socgroupticket.FieldTenantID},
			socgroupticket.FieldRemark:        {Type: field.TypeString, Column: socgroupticket.FieldRemark},
			socgroupticket.FieldName:          {Type: field.TypeString, Column: socgroupticket.FieldName},
			socgroupticket.FieldType:          {Type: field.TypeString, Column: socgroupticket.FieldType},
			socgroupticket.FieldDescription:   {Type: field.TypeString, Column: socgroupticket.FieldDescription},
			socgroupticket.FieldFollowList:    {Type: field.TypeJSON, Column: socgroupticket.FieldFollowList},
			socgroupticket.FieldDepartmentID:  {Type: field.TypeInt, Column: socgroupticket.FieldDepartmentID},
			socgroupticket.FieldIPList:        {Type: field.TypeJSON, Column: socgroupticket.FieldIPList},
			socgroupticket.FieldMinBandwidth:  {Type: field.TypeFloat32, Column: socgroupticket.FieldMinBandwidth},
			socgroupticket.FieldDivertType:    {Type: field.TypeInt, Column: socgroupticket.FieldDivertType},
			socgroupticket.FieldOpType:        {Type: field.TypeInt, Column: socgroupticket.FieldOpType},
			socgroupticket.FieldOpTime:        {Type: field.TypeTime, Column: socgroupticket.FieldOpTime},
			socgroupticket.FieldConfigType:    {Type: field.TypeInt, Column: socgroupticket.FieldConfigType},
			socgroupticket.FieldConfigArgs:    {Type: field.TypeString, Column: socgroupticket.FieldConfigArgs},
			socgroupticket.FieldProductName:   {Type: field.TypeString, Column: socgroupticket.FieldProductName},
			socgroupticket.FieldProductCode:   {Type: field.TypeString, Column: socgroupticket.FieldProductCode},
			socgroupticket.FieldContactList:   {Type: field.TypeJSON, Column: socgroupticket.FieldContactList},
			socgroupticket.FieldGroupTicketID: {Type: field.TypeInt, Column: socgroupticket.FieldGroupTicketID},
			socgroupticket.FieldErrorInfo:     {Type: field.TypeString, Column: socgroupticket.FieldErrorInfo},
			socgroupticket.FieldCreateUserID:  {Type: field.TypeInt, Column: socgroupticket.FieldCreateUserID},
		},
	}
	graph.Nodes[14] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   spectrumalert.Table,
			Columns: spectrumalert.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: spectrumalert.FieldID,
			},
		},
		Type: "SpectrumAlert",
		Fields: map[string]*sqlgraph.FieldSpec{
			spectrumalert.FieldTenantID:       {Type: field.TypeInt, Column: spectrumalert.FieldTenantID},
			spectrumalert.FieldCreatedAt:      {Type: field.TypeTime, Column: spectrumalert.FieldCreatedAt},
			spectrumalert.FieldUpdatedAt:      {Type: field.TypeTime, Column: spectrumalert.FieldUpdatedAt},
			spectrumalert.FieldRemark:         {Type: field.TypeString, Column: spectrumalert.FieldRemark},
			spectrumalert.FieldProtectGroupID: {Type: field.TypeInt, Column: spectrumalert.FieldProtectGroupID},
			spectrumalert.FieldStrategyID:     {Type: field.TypeInt, Column: spectrumalert.FieldStrategyID},
			spectrumalert.FieldWofangID:       {Type: field.TypeInt, Column: spectrumalert.FieldWofangID},
			spectrumalert.FieldProtectStatus:  {Type: field.TypeJSON, Column: spectrumalert.FieldProtectStatus},
			spectrumalert.FieldIP:             {Type: field.TypeString, Column: spectrumalert.FieldIP},
			spectrumalert.FieldStartTime:      {Type: field.TypeTime, Column: spectrumalert.FieldStartTime},
			spectrumalert.FieldEndTime:        {Type: field.TypeTime, Column: spectrumalert.FieldEndTime},
			spectrumalert.FieldAttackType:     {Type: field.TypeString, Column: spectrumalert.FieldAttackType},
			spectrumalert.FieldMaxPps:         {Type: field.TypeInt64, Column: spectrumalert.FieldMaxPps},
			spectrumalert.FieldMaxBps:         {Type: field.TypeInt64, Column: spectrumalert.FieldMaxBps},
			spectrumalert.FieldAttackInfo:     {Type: field.TypeJSON, Column: spectrumalert.FieldAttackInfo},
			spectrumalert.FieldIspCode:        {Type: field.TypeInt, Column: spectrumalert.FieldIspCode},
		},
	}
	graph.Nodes[15] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   spectrumdata.Table,
			Columns: spectrumdata.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: spectrumdata.FieldID,
			},
		},
		Type: "SpectrumData",
		Fields: map[string]*sqlgraph.FieldSpec{
			spectrumdata.FieldTenantID:        {Type: field.TypeInt, Column: spectrumdata.FieldTenantID},
			spectrumdata.FieldCreatedAt:       {Type: field.TypeTime, Column: spectrumdata.FieldCreatedAt},
			spectrumdata.FieldSpectrumAlertID: {Type: field.TypeInt, Column: spectrumdata.FieldSpectrumAlertID},
			spectrumdata.FieldIP:              {Type: field.TypeString, Column: spectrumdata.FieldIP},
			spectrumdata.FieldTime:            {Type: field.TypeTime, Column: spectrumdata.FieldTime},
			spectrumdata.FieldMonitorID:       {Type: field.TypeInt, Column: spectrumdata.FieldMonitorID},
			spectrumdata.FieldDataType:        {Type: field.TypeInt, Column: spectrumdata.FieldDataType},
			spectrumdata.FieldBps:             {Type: field.TypeInt64, Column: spectrumdata.FieldBps},
			spectrumdata.FieldPps:             {Type: field.TypeInt64, Column: spectrumdata.FieldPps},
			spectrumdata.FieldSynBps:          {Type: field.TypeInt64, Column: spectrumdata.FieldSynBps},
			spectrumdata.FieldSynPps:          {Type: field.TypeInt64, Column: spectrumdata.FieldSynPps},
			spectrumdata.FieldAckBps:          {Type: field.TypeInt64, Column: spectrumdata.FieldAckBps},
			spectrumdata.FieldAckPps:          {Type: field.TypeInt64, Column: spectrumdata.FieldAckPps},
			spectrumdata.FieldSynAckBps:       {Type: field.TypeInt64, Column: spectrumdata.FieldSynAckBps},
			spectrumdata.FieldSynAckPps:       {Type: field.TypeInt64, Column: spectrumdata.FieldSynAckPps},
			spectrumdata.FieldIcmpBps:         {Type: field.TypeInt64, Column: spectrumdata.FieldIcmpBps},
			spectrumdata.FieldIcmpPps:         {Type: field.TypeInt64, Column: spectrumdata.FieldIcmpPps},
			spectrumdata.FieldSmallPps:        {Type: field.TypeInt64, Column: spectrumdata.FieldSmallPps},
			spectrumdata.FieldNtpPps:          {Type: field.TypeInt64, Column: spectrumdata.FieldNtpPps},
			spectrumdata.FieldNtpBps:          {Type: field.TypeInt64, Column: spectrumdata.FieldNtpBps},
			spectrumdata.FieldDNSQueryPps:     {Type: field.TypeInt64, Column: spectrumdata.FieldDNSQueryPps},
			spectrumdata.FieldDNSQueryBps:     {Type: field.TypeInt64, Column: spectrumdata.FieldDNSQueryBps},
			spectrumdata.FieldDNSAnswerPps:    {Type: field.TypeInt64, Column: spectrumdata.FieldDNSAnswerPps},
			spectrumdata.FieldDNSAnswerBps:    {Type: field.TypeInt64, Column: spectrumdata.FieldDNSAnswerBps},
			spectrumdata.FieldSsdpBps:         {Type: field.TypeInt64, Column: spectrumdata.FieldSsdpBps},
			spectrumdata.FieldSsdpPps:         {Type: field.TypeInt64, Column: spectrumdata.FieldSsdpPps},
			spectrumdata.FieldUDPPps:          {Type: field.TypeInt64, Column: spectrumdata.FieldUDPPps},
			spectrumdata.FieldUDPBps:          {Type: field.TypeInt64, Column: spectrumdata.FieldUDPBps},
			spectrumdata.FieldQPS:             {Type: field.TypeInt64, Column: spectrumdata.FieldQPS},
			spectrumdata.FieldReceiveCount:    {Type: field.TypeInt, Column: spectrumdata.FieldReceiveCount},
			spectrumdata.FieldIPType:          {Type: field.TypeInt, Column: spectrumdata.FieldIPType},
			spectrumdata.FieldMonitor:         {Type: field.TypeString, Column: spectrumdata.FieldMonitor},
			spectrumdata.FieldProduct:         {Type: field.TypeString, Column: spectrumdata.FieldProduct},
			spectrumdata.FieldHost:            {Type: field.TypeString, Column: spectrumdata.FieldHost},
		},
	}
	graph.Nodes[16] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   strategy.Table,
			Columns: strategy.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: strategy.FieldID,
			},
		},
		Type: "Strategy",
		Fields: map[string]*sqlgraph.FieldSpec{
			strategy.FieldTenantID:  {Type: field.TypeInt, Column: strategy.FieldTenantID},
			strategy.FieldCreatedAt: {Type: field.TypeTime, Column: strategy.FieldCreatedAt},
			strategy.FieldUpdatedAt: {Type: field.TypeTime, Column: strategy.FieldUpdatedAt},
			strategy.FieldRemark:    {Type: field.TypeString, Column: strategy.FieldRemark},
			strategy.FieldName:      {Type: field.TypeString, Column: strategy.FieldName},
			strategy.FieldType:      {Type: field.TypeInt, Column: strategy.FieldType},
			strategy.FieldEnabled:   {Type: field.TypeBool, Column: strategy.FieldEnabled},
			strategy.FieldSystem:    {Type: field.TypeBool, Column: strategy.FieldSystem},
			strategy.FieldBps:       {Type: field.TypeInt64, Column: strategy.FieldBps},
			strategy.FieldPps:       {Type: field.TypeInt64, Column: strategy.FieldPps},
			strategy.FieldBpsCount:  {Type: field.TypeInt, Column: strategy.FieldBpsCount},
			strategy.FieldPpsCount:  {Type: field.TypeInt, Column: strategy.FieldPpsCount},
			strategy.FieldIspCode:   {Type: field.TypeInt, Column: strategy.FieldIspCode},
		},
	}
	graph.Nodes[17] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   systemapi.Table,
			Columns: systemapi.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: systemapi.FieldID,
			},
		},
		Type: "SystemApi",
		Fields: map[string]*sqlgraph.FieldSpec{
			systemapi.FieldCreatedAt:  {Type: field.TypeTime, Column: systemapi.FieldCreatedAt},
			systemapi.FieldUpdatedAt:  {Type: field.TypeTime, Column: systemapi.FieldUpdatedAt},
			systemapi.FieldRemark:     {Type: field.TypeString, Column: systemapi.FieldRemark},
			systemapi.FieldName:       {Type: field.TypeString, Column: systemapi.FieldName},
			systemapi.FieldPath:       {Type: field.TypeString, Column: systemapi.FieldPath},
			systemapi.FieldHTTPMethod: {Type: field.TypeString, Column: systemapi.FieldHTTPMethod},
			systemapi.FieldRoles:      {Type: field.TypeJSON, Column: systemapi.FieldRoles},
			systemapi.FieldPublic:     {Type: field.TypeBool, Column: systemapi.FieldPublic},
			systemapi.FieldSa:         {Type: field.TypeBool, Column: systemapi.FieldSa},
		},
	}
	graph.Nodes[18] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   systemconfig.Table,
			Columns: systemconfig.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: systemconfig.FieldID,
			},
		},
		Type: "SystemConfig",
		Fields: map[string]*sqlgraph.FieldSpec{
			systemconfig.FieldCreatedAt:    {Type: field.TypeTime, Column: systemconfig.FieldCreatedAt},
			systemconfig.FieldUpdatedAt:    {Type: field.TypeTime, Column: systemconfig.FieldUpdatedAt},
			systemconfig.FieldRemark:       {Type: field.TypeString, Column: systemconfig.FieldRemark},
			systemconfig.FieldWofangTestIP: {Type: field.TypeString, Column: systemconfig.FieldWofangTestIP},
			systemconfig.FieldNotifyPhones: {Type: field.TypeJSON, Column: systemconfig.FieldNotifyPhones},
			systemconfig.FieldNotifyEmails: {Type: field.TypeJSON, Column: systemconfig.FieldNotifyEmails},
			systemconfig.FieldNotifyScenes: {Type: field.TypeJSON, Column: systemconfig.FieldNotifyScenes},
			systemconfig.FieldIPWhitelists: {Type: field.TypeJSON, Column: systemconfig.FieldIPWhitelists},
		},
	}
	graph.Nodes[19] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   tenant.Table,
			Columns: tenant.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: tenant.FieldID,
			},
		},
		Type: "Tenant",
		Fields: map[string]*sqlgraph.FieldSpec{
			tenant.FieldName:     {Type: field.TypeString, Column: tenant.FieldName},
			tenant.FieldCode:     {Type: field.TypeString, Column: tenant.FieldCode},
			tenant.FieldOffline:  {Type: field.TypeBool, Column: tenant.FieldOffline},
			tenant.FieldIsdefend: {Type: field.TypeBool, Column: tenant.FieldIsdefend},
		},
	}
	graph.Nodes[20] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   user.Table,
			Columns: user.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: user.FieldID,
			},
		},
		Type: "User",
		Fields: map[string]*sqlgraph.FieldSpec{
			user.FieldValid:      {Type: field.TypeBool, Column: user.FieldValid},
			user.FieldCreatedAt:  {Type: field.TypeTime, Column: user.FieldCreatedAt},
			user.FieldUpdatedAt:  {Type: field.TypeTime, Column: user.FieldUpdatedAt},
			user.FieldName:       {Type: field.TypeString, Column: user.FieldName},
			user.FieldPassword:   {Type: field.TypeString, Column: user.FieldPassword},
			user.FieldSuperAdmin: {Type: field.TypeBool, Column: user.FieldSuperAdmin},
			user.FieldUpdateAuth: {Type: field.TypeBool, Column: user.FieldUpdateAuth},
		},
	}
	graph.Nodes[21] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   useroperationlog.Table,
			Columns: useroperationlog.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: useroperationlog.FieldID,
			},
		},
		Type: "UserOperationLog",
		Fields: map[string]*sqlgraph.FieldSpec{
			useroperationlog.FieldRemark:      {Type: field.TypeString, Column: useroperationlog.FieldRemark},
			useroperationlog.FieldCreatedAt:   {Type: field.TypeTime, Column: useroperationlog.FieldCreatedAt},
			useroperationlog.FieldUpdatedAt:   {Type: field.TypeTime, Column: useroperationlog.FieldUpdatedAt},
			useroperationlog.FieldUsername:    {Type: field.TypeString, Column: useroperationlog.FieldUsername},
			useroperationlog.FieldMethod:      {Type: field.TypeString, Column: useroperationlog.FieldMethod},
			useroperationlog.FieldRequestID:   {Type: field.TypeString, Column: useroperationlog.FieldRequestID},
			useroperationlog.FieldURI:         {Type: field.TypeString, Column: useroperationlog.FieldURI},
			useroperationlog.FieldRequestBody: {Type: field.TypeString, Column: useroperationlog.FieldRequestBody},
			useroperationlog.FieldProject:     {Type: field.TypeString, Column: useroperationlog.FieldProject},
		},
	}
	graph.Nodes[22] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   wofang.Table,
			Columns: wofang.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: wofang.FieldID,
			},
		},
		Type: "Wofang",
		Fields: map[string]*sqlgraph.FieldSpec{
			wofang.FieldCreatedAt:    {Type: field.TypeTime, Column: wofang.FieldCreatedAt},
			wofang.FieldUpdatedAt:    {Type: field.TypeTime, Column: wofang.FieldUpdatedAt},
			wofang.FieldTenantID:     {Type: field.TypeInt, Column: wofang.FieldTenantID},
			wofang.FieldRemark:       {Type: field.TypeString, Column: wofang.FieldRemark},
			wofang.FieldName:         {Type: field.TypeString, Column: wofang.FieldName},
			wofang.FieldIP:           {Type: field.TypeString, Column: wofang.FieldIP},
			wofang.FieldType:         {Type: field.TypeString, Column: wofang.FieldType},
			wofang.FieldUnDragSecond: {Type: field.TypeInt, Column: wofang.FieldUnDragSecond},
			wofang.FieldStartTime:    {Type: field.TypeTime, Column: wofang.FieldStartTime},
			wofang.FieldErrorInfo:    {Type: field.TypeString, Column: wofang.FieldErrorInfo},
			wofang.FieldStatus:       {Type: field.TypeString, Column: wofang.FieldStatus},
			wofang.FieldCreateUserID: {Type: field.TypeInt, Column: wofang.FieldCreateUserID},
		},
	}
	graph.Nodes[23] = &sqlgraph.Node{
		NodeSpec: sqlgraph.NodeSpec{
			Table:   wofangalert.Table,
			Columns: wofangalert.Columns,
			ID: &sqlgraph.FieldSpec{
				Type:   field.TypeInt,
				Column: wofangalert.FieldID,
			},
		},
		Type: "WofangAlert",
		Fields: map[string]*sqlgraph.FieldSpec{
			wofangalert.FieldTenantID:     {Type: field.TypeInt, Column: wofangalert.FieldTenantID},
			wofangalert.FieldCreatedAt:    {Type: field.TypeTime, Column: wofangalert.FieldCreatedAt},
			wofangalert.FieldUpdatedAt:    {Type: field.TypeTime, Column: wofangalert.FieldUpdatedAt},
			wofangalert.FieldRemark:       {Type: field.TypeString, Column: wofangalert.FieldRemark},
			wofangalert.FieldAttackStatus: {Type: field.TypeInt, Column: wofangalert.FieldAttackStatus},
			wofangalert.FieldAttackType:   {Type: field.TypeJSON, Column: wofangalert.FieldAttackType},
			wofangalert.FieldDeviceIP:     {Type: field.TypeString, Column: wofangalert.FieldDeviceIP},
			wofangalert.FieldZoneIP:       {Type: field.TypeString, Column: wofangalert.FieldZoneIP},
			wofangalert.FieldAttackID:     {Type: field.TypeInt, Column: wofangalert.FieldAttackID},
			wofangalert.FieldStartTime:    {Type: field.TypeTime, Column: wofangalert.FieldStartTime},
			wofangalert.FieldEndTime:      {Type: field.TypeTime, Column: wofangalert.FieldEndTime},
			wofangalert.FieldMaxDropBps:   {Type: field.TypeInt64, Column: wofangalert.FieldMaxDropBps},
			wofangalert.FieldMaxInBps:     {Type: field.TypeInt64, Column: wofangalert.FieldMaxInBps},
		},
	}
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cleandata.TenantTable,
			Columns: []string{cleandata.TenantColumn},
			Bidi:    false,
		},
		"CleanData",
		"Tenant",
	)
	graph.MustAddE(
		"spectrum_alert",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cleandata.SpectrumAlertTable,
			Columns: []string{cleandata.SpectrumAlertColumn},
			Bidi:    false,
		},
		"CleanData",
		"SpectrumAlert",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudalert.TenantTable,
			Columns: []string{cloudalert.TenantColumn},
			Bidi:    false,
		},
		"CloudAlert",
		"Tenant",
	)
	graph.MustAddE(
		"cloudflow_datas",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   cloudalert.CloudflowDatasTable,
			Columns: []string{cloudalert.CloudflowDatasColumn},
			Bidi:    false,
		},
		"CloudAlert",
		"CloudFlowData",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudattackdata.TenantTable,
			Columns: []string{cloudattackdata.TenantColumn},
			Bidi:    false,
		},
		"CloudAttackData",
		"Tenant",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudflowdata.TenantTable,
			Columns: []string{cloudflowdata.TenantColumn},
			Bidi:    false,
		},
		"CloudFlowData",
		"Tenant",
	)
	graph.MustAddE(
		"cloud_alert",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cloudflowdata.CloudAlertTable,
			Columns: []string{cloudflowdata.CloudAlertColumn},
			Bidi:    false,
		},
		"CloudFlowData",
		"CloudAlert",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   group.TenantTable,
			Columns: []string{group.TenantColumn},
			Bidi:    false,
		},
		"Group",
		"Tenant",
	)
	graph.MustAddE(
		"users",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.UsersTable,
			Columns: group.UsersPrimaryKey,
			Bidi:    false,
		},
		"Group",
		"User",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumalert.TenantTable,
			Columns: []string{matrixspectrumalert.TenantColumn},
			Bidi:    false,
		},
		"MatrixSpectrumAlert",
		"Tenant",
	)
	graph.MustAddE(
		"matrix_spectrum_datas",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixspectrumalert.MatrixSpectrumDatasTable,
			Columns: []string{matrixspectrumalert.MatrixSpectrumDatasColumn},
			Bidi:    false,
		},
		"MatrixSpectrumAlert",
		"MatrixSpectrumData",
	)
	graph.MustAddE(
		"matrix_strategy",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.MatrixStrategyTable,
			Columns: []string{matrixspectrumalert.MatrixStrategyColumn},
			Bidi:    false,
		},
		"MatrixSpectrumAlert",
		"MatrixStrategy",
	)
	graph.MustAddE(
		"wofang_ticket",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumalert.WofangTicketTable,
			Columns: []string{matrixspectrumalert.WofangTicketColumn},
			Bidi:    false,
		},
		"MatrixSpectrumAlert",
		"Wofang",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumdata.TenantTable,
			Columns: []string{matrixspectrumdata.TenantColumn},
			Bidi:    false,
		},
		"MatrixSpectrumData",
		"Tenant",
	)
	graph.MustAddE(
		"matrix_spectrum_alert",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumdata.MatrixSpectrumAlertTable,
			Columns: []string{matrixspectrumdata.MatrixSpectrumAlertColumn},
			Bidi:    false,
		},
		"MatrixSpectrumData",
		"MatrixSpectrumAlert",
	)
	graph.MustAddE(
		"matrix_strategy_alerts",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixstrategy.MatrixStrategyAlertsTable,
			Columns: []string{matrixstrategy.MatrixStrategyAlertsColumn},
			Bidi:    false,
		},
		"MatrixStrategy",
		"MatrixSpectrumAlert",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   notify.TenantTable,
			Columns: []string{notify.TenantColumn},
			Bidi:    false,
		},
		"Notify",
		"Tenant",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   protectgroup.TenantTable,
			Columns: []string{protectgroup.TenantColumn},
			Bidi:    false,
		},
		"ProtectGroup",
		"Tenant",
	)
	graph.MustAddE(
		"spectrum_alerts",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   protectgroup.SpectrumAlertsTable,
			Columns: []string{protectgroup.SpectrumAlertsColumn},
			Bidi:    false,
		},
		"ProtectGroup",
		"SpectrumAlert",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   skylinedos.TenantTable,
			Columns: []string{skylinedos.TenantColumn},
			Bidi:    false,
		},
		"SkylineDos",
		"Tenant",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.TenantTable,
			Columns: []string{socgroupticket.TenantColumn},
			Bidi:    false,
		},
		"SocGroupTicket",
		"Tenant",
	)
	graph.MustAddE(
		"user",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.UserTable,
			Columns: []string{socgroupticket.UserColumn},
			Bidi:    false,
		},
		"SocGroupTicket",
		"User",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumalert.TenantTable,
			Columns: []string{spectrumalert.TenantColumn},
			Bidi:    false,
		},
		"SpectrumAlert",
		"Tenant",
	)
	graph.MustAddE(
		"spectrum_datas",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.SpectrumDatasTable,
			Columns: []string{spectrumalert.SpectrumDatasColumn},
			Bidi:    false,
		},
		"SpectrumAlert",
		"SpectrumData",
	)
	graph.MustAddE(
		"clean_datas",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.CleanDatasTable,
			Columns: []string{spectrumalert.CleanDatasColumn},
			Bidi:    false,
		},
		"SpectrumAlert",
		"CleanData",
	)
	graph.MustAddE(
		"protect_group",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.ProtectGroupTable,
			Columns: []string{spectrumalert.ProtectGroupColumn},
			Bidi:    false,
		},
		"SpectrumAlert",
		"ProtectGroup",
	)
	graph.MustAddE(
		"strategy",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.StrategyTable,
			Columns: []string{spectrumalert.StrategyColumn},
			Bidi:    false,
		},
		"SpectrumAlert",
		"Strategy",
	)
	graph.MustAddE(
		"wofang_ticket",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.WofangTicketTable,
			Columns: []string{spectrumalert.WofangTicketColumn},
			Bidi:    false,
		},
		"SpectrumAlert",
		"Wofang",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumdata.TenantTable,
			Columns: []string{spectrumdata.TenantColumn},
			Bidi:    false,
		},
		"SpectrumData",
		"Tenant",
	)
	graph.MustAddE(
		"spectrum_alert",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumdata.SpectrumAlertTable,
			Columns: []string{spectrumdata.SpectrumAlertColumn},
			Bidi:    false,
		},
		"SpectrumData",
		"SpectrumAlert",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   strategy.TenantTable,
			Columns: []string{strategy.TenantColumn},
			Bidi:    false,
		},
		"Strategy",
		"Tenant",
	)
	graph.MustAddE(
		"strategy_alerts",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   strategy.StrategyAlertsTable,
			Columns: []string{strategy.StrategyAlertsColumn},
			Bidi:    false,
		},
		"Strategy",
		"SpectrumAlert",
	)
	graph.MustAddE(
		"groups",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.GroupsTable,
			Columns: user.GroupsPrimaryKey,
			Bidi:    false,
		},
		"User",
		"Group",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.TenantTable,
			Columns: []string{wofang.TenantColumn},
			Bidi:    false,
		},
		"Wofang",
		"Tenant",
	)
	graph.MustAddE(
		"user",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.UserTable,
			Columns: []string{wofang.UserColumn},
			Bidi:    false,
		},
		"Wofang",
		"User",
	)
	graph.MustAddE(
		"spectrum_alerts",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.SpectrumAlertsTable,
			Columns: []string{wofang.SpectrumAlertsColumn},
			Bidi:    false,
		},
		"Wofang",
		"SpectrumAlert",
	)
	graph.MustAddE(
		"matrix_spectrum_alerts",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.MatrixSpectrumAlertsTable,
			Columns: []string{wofang.MatrixSpectrumAlertsColumn},
			Bidi:    false,
		},
		"Wofang",
		"MatrixSpectrumAlert",
	)
	graph.MustAddE(
		"tenant",
		&sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofangalert.TenantTable,
			Columns: []string{wofangalert.TenantColumn},
			Bidi:    false,
		},
		"WofangAlert",
		"Tenant",
	)
	return graph
}()

// predicateAdder wraps the addPredicate method.
// All update, update-one and query builders implement this interface.
type predicateAdder interface {
	addPredicate(func(s *sql.Selector))
}

// addPredicate implements the predicateAdder interface.
func (crq *CasbinRuleQuery) addPredicate(pred func(s *sql.Selector)) {
	crq.predicates = append(crq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the CasbinRuleQuery builder.
func (crq *CasbinRuleQuery) Filter() *CasbinRuleFilter {
	return &CasbinRuleFilter{config: crq.config, predicateAdder: crq}
}

// addPredicate implements the predicateAdder interface.
func (m *CasbinRuleMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the CasbinRuleMutation builder.
func (m *CasbinRuleMutation) Filter() *CasbinRuleFilter {
	return &CasbinRuleFilter{config: m.config, predicateAdder: m}
}

// CasbinRuleFilter provides a generic filtering capability at runtime for CasbinRuleQuery.
type CasbinRuleFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *CasbinRuleFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[0].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *CasbinRuleFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(casbinrule.FieldID))
}

// WhereType applies the entql string predicate on the type field.
func (f *CasbinRuleFilter) WhereType(p entql.StringP) {
	f.Where(p.Field(casbinrule.FieldType))
}

// WhereSub applies the entql string predicate on the sub field.
func (f *CasbinRuleFilter) WhereSub(p entql.StringP) {
	f.Where(p.Field(casbinrule.FieldSub))
}

// WhereDom applies the entql string predicate on the dom field.
func (f *CasbinRuleFilter) WhereDom(p entql.StringP) {
	f.Where(p.Field(casbinrule.FieldDom))
}

// WhereObj applies the entql string predicate on the obj field.
func (f *CasbinRuleFilter) WhereObj(p entql.StringP) {
	f.Where(p.Field(casbinrule.FieldObj))
}

// WhereAct applies the entql string predicate on the act field.
func (f *CasbinRuleFilter) WhereAct(p entql.StringP) {
	f.Where(p.Field(casbinrule.FieldAct))
}

// WhereV4 applies the entql string predicate on the v4 field.
func (f *CasbinRuleFilter) WhereV4(p entql.StringP) {
	f.Where(p.Field(casbinrule.FieldV4))
}

// WhereV5 applies the entql string predicate on the v5 field.
func (f *CasbinRuleFilter) WhereV5(p entql.StringP) {
	f.Where(p.Field(casbinrule.FieldV5))
}

// addPredicate implements the predicateAdder interface.
func (cdq *CleanDataQuery) addPredicate(pred func(s *sql.Selector)) {
	cdq.predicates = append(cdq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the CleanDataQuery builder.
func (cdq *CleanDataQuery) Filter() *CleanDataFilter {
	return &CleanDataFilter{config: cdq.config, predicateAdder: cdq}
}

// addPredicate implements the predicateAdder interface.
func (m *CleanDataMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the CleanDataMutation builder.
func (m *CleanDataMutation) Filter() *CleanDataFilter {
	return &CleanDataFilter{config: m.config, predicateAdder: m}
}

// CleanDataFilter provides a generic filtering capability at runtime for CleanDataQuery.
type CleanDataFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *CleanDataFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[1].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *CleanDataFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(cleandata.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *CleanDataFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(cleandata.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *CleanDataFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(cleandata.FieldCreatedAt))
}

// WhereSpectrumAlertID applies the entql int predicate on the spectrum_alert_id field.
func (f *CleanDataFilter) WhereSpectrumAlertID(p entql.IntP) {
	f.Where(p.Field(cleandata.FieldSpectrumAlertID))
}

// WhereIP applies the entql string predicate on the ip field.
func (f *CleanDataFilter) WhereIP(p entql.StringP) {
	f.Where(p.Field(cleandata.FieldIP))
}

// WhereTime applies the entql time.Time predicate on the time field.
func (f *CleanDataFilter) WhereTime(p entql.TimeP) {
	f.Where(p.Field(cleandata.FieldTime))
}

// WhereInBps applies the entql int64 predicate on the in_bps field.
func (f *CleanDataFilter) WhereInBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInBps))
}

// WhereOutBps applies the entql int64 predicate on the out_bps field.
func (f *CleanDataFilter) WhereOutBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutBps))
}

// WhereInPps applies the entql int64 predicate on the in_pps field.
func (f *CleanDataFilter) WhereInPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInPps))
}

// WhereOutPps applies the entql int64 predicate on the out_pps field.
func (f *CleanDataFilter) WhereOutPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutPps))
}

// WhereInAckPps applies the entql int64 predicate on the in_ack_pps field.
func (f *CleanDataFilter) WhereInAckPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInAckPps))
}

// WhereOutAckPps applies the entql int64 predicate on the out_ack_pps field.
func (f *CleanDataFilter) WhereOutAckPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutAckPps))
}

// WhereInAckBps applies the entql int64 predicate on the in_ack_bps field.
func (f *CleanDataFilter) WhereInAckBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInAckBps))
}

// WhereOutAckBps applies the entql int64 predicate on the out_ack_bps field.
func (f *CleanDataFilter) WhereOutAckBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutAckBps))
}

// WhereInSynPps applies the entql int64 predicate on the in_syn_pps field.
func (f *CleanDataFilter) WhereInSynPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInSynPps))
}

// WhereOutSynPps applies the entql int64 predicate on the out_syn_pps field.
func (f *CleanDataFilter) WhereOutSynPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutSynPps))
}

// WhereInUDPPps applies the entql int64 predicate on the in_udp_pps field.
func (f *CleanDataFilter) WhereInUDPPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInUDPPps))
}

// WhereOutUDPPps applies the entql int64 predicate on the out_udp_pps field.
func (f *CleanDataFilter) WhereOutUDPPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutUDPPps))
}

// WhereInUDPBps applies the entql int64 predicate on the in_udp_bps field.
func (f *CleanDataFilter) WhereInUDPBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInUDPBps))
}

// WhereOutUDPBps applies the entql int64 predicate on the out_udp_bps field.
func (f *CleanDataFilter) WhereOutUDPBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutUDPBps))
}

// WhereInIcmpPps applies the entql int64 predicate on the in_icmp_pps field.
func (f *CleanDataFilter) WhereInIcmpPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInIcmpPps))
}

// WhereInIcmpBps applies the entql int64 predicate on the in_icmp_bps field.
func (f *CleanDataFilter) WhereInIcmpBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInIcmpBps))
}

// WhereOutIcmpBps applies the entql int64 predicate on the out_icmp_bps field.
func (f *CleanDataFilter) WhereOutIcmpBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutIcmpBps))
}

// WhereOutIcmpPps applies the entql int64 predicate on the out_icmp_pps field.
func (f *CleanDataFilter) WhereOutIcmpPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutIcmpPps))
}

// WhereInDNSPps applies the entql int64 predicate on the in_dns_pps field.
func (f *CleanDataFilter) WhereInDNSPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInDNSPps))
}

// WhereOutDNSPps applies the entql int64 predicate on the out_dns_pps field.
func (f *CleanDataFilter) WhereOutDNSPps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutDNSPps))
}

// WhereInDNSBps applies the entql int64 predicate on the in_dns_bps field.
func (f *CleanDataFilter) WhereInDNSBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldInDNSBps))
}

// WhereOutDNSBps applies the entql int64 predicate on the out_dns_bps field.
func (f *CleanDataFilter) WhereOutDNSBps(p entql.Int64P) {
	f.Where(p.Field(cleandata.FieldOutDNSBps))
}

// WhereCFilterID applies the entql int predicate on the c_filter_id field.
func (f *CleanDataFilter) WhereCFilterID(p entql.IntP) {
	f.Where(p.Field(cleandata.FieldCFilterID))
}

// WhereAttackFlags applies the entql int predicate on the attack_flags field.
func (f *CleanDataFilter) WhereAttackFlags(p entql.IntP) {
	f.Where(p.Field(cleandata.FieldAttackFlags))
}

// WhereCount applies the entql int predicate on the count field.
func (f *CleanDataFilter) WhereCount(p entql.IntP) {
	f.Where(p.Field(cleandata.FieldCount))
}

// WhereIPType applies the entql int predicate on the ip_type field.
func (f *CleanDataFilter) WhereIPType(p entql.IntP) {
	f.Where(p.Field(cleandata.FieldIPType))
}

// WhereCFilter applies the entql string predicate on the c_filter field.
func (f *CleanDataFilter) WhereCFilter(p entql.StringP) {
	f.Where(p.Field(cleandata.FieldCFilter))
}

// WhereHost applies the entql string predicate on the host field.
func (f *CleanDataFilter) WhereHost(p entql.StringP) {
	f.Where(p.Field(cleandata.FieldHost))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *CleanDataFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *CleanDataFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasSpectrumAlert applies a predicate to check if query has an edge spectrum_alert.
func (f *CleanDataFilter) WhereHasSpectrumAlert() {
	f.Where(entql.HasEdge("spectrum_alert"))
}

// WhereHasSpectrumAlertWith applies a predicate to check if query has an edge spectrum_alert with a given conditions (other predicates).
func (f *CleanDataFilter) WhereHasSpectrumAlertWith(preds ...predicate.SpectrumAlert) {
	f.Where(entql.HasEdgeWith("spectrum_alert", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (caq *CloudAlertQuery) addPredicate(pred func(s *sql.Selector)) {
	caq.predicates = append(caq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the CloudAlertQuery builder.
func (caq *CloudAlertQuery) Filter() *CloudAlertFilter {
	return &CloudAlertFilter{config: caq.config, predicateAdder: caq}
}

// addPredicate implements the predicateAdder interface.
func (m *CloudAlertMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the CloudAlertMutation builder.
func (m *CloudAlertMutation) Filter() *CloudAlertFilter {
	return &CloudAlertFilter{config: m.config, predicateAdder: m}
}

// CloudAlertFilter provides a generic filtering capability at runtime for CloudAlertQuery.
type CloudAlertFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *CloudAlertFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[2].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *CloudAlertFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *CloudAlertFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *CloudAlertFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(cloudalert.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *CloudAlertFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(cloudalert.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *CloudAlertFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(cloudalert.FieldRemark))
}

// WhereSrcIP applies the entql string predicate on the src_ip field.
func (f *CloudAlertFilter) WhereSrcIP(p entql.StringP) {
	f.Where(p.Field(cloudalert.FieldSrcIP))
}

// WhereSrcPort applies the entql int predicate on the src_port field.
func (f *CloudAlertFilter) WhereSrcPort(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldSrcPort))
}

// WhereDstIP applies the entql string predicate on the dst_ip field.
func (f *CloudAlertFilter) WhereDstIP(p entql.StringP) {
	f.Where(p.Field(cloudalert.FieldDstIP))
}

// WhereDstPort applies the entql int predicate on the dst_port field.
func (f *CloudAlertFilter) WhereDstPort(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldDstPort))
}

// WhereDefenceMode applies the entql int predicate on the defence_mode field.
func (f *CloudAlertFilter) WhereDefenceMode(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldDefenceMode))
}

// WhereFlowMode applies the entql int predicate on the flow_mode field.
func (f *CloudAlertFilter) WhereFlowMode(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldFlowMode))
}

// WhereTCPAckNum applies the entql string predicate on the tcp_ack_num field.
func (f *CloudAlertFilter) WhereTCPAckNum(p entql.StringP) {
	f.Where(p.Field(cloudalert.FieldTCPAckNum))
}

// WhereTCPSeqNum applies the entql string predicate on the tcp_seq_num field.
func (f *CloudAlertFilter) WhereTCPSeqNum(p entql.StringP) {
	f.Where(p.Field(cloudalert.FieldTCPSeqNum))
}

// WhereProtocol applies the entql int predicate on the protocol field.
func (f *CloudAlertFilter) WhereProtocol(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldProtocol))
}

// WhereDefenceLevel applies the entql int predicate on the defence_level field.
func (f *CloudAlertFilter) WhereDefenceLevel(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldDefenceLevel))
}

// WhereMaxPps applies the entql int64 predicate on the max_pps field.
func (f *CloudAlertFilter) WhereMaxPps(p entql.Int64P) {
	f.Where(p.Field(cloudalert.FieldMaxPps))
}

// WhereMaxAttackPps applies the entql int64 predicate on the max_attack_pps field.
func (f *CloudAlertFilter) WhereMaxAttackPps(p entql.Int64P) {
	f.Where(p.Field(cloudalert.FieldMaxAttackPps))
}

// WhereOverlimitPktCount applies the entql int predicate on the overlimit_pkt_count field.
func (f *CloudAlertFilter) WhereOverlimitPktCount(p entql.IntP) {
	f.Where(p.Field(cloudalert.FieldOverlimitPktCount))
}

// WhereStartTime applies the entql time.Time predicate on the start_time field.
func (f *CloudAlertFilter) WhereStartTime(p entql.TimeP) {
	f.Where(p.Field(cloudalert.FieldStartTime))
}

// WhereEndTime applies the entql time.Time predicate on the end_time field.
func (f *CloudAlertFilter) WhereEndTime(p entql.TimeP) {
	f.Where(p.Field(cloudalert.FieldEndTime))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *CloudAlertFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *CloudAlertFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasCloudflowDatas applies a predicate to check if query has an edge cloudflow_datas.
func (f *CloudAlertFilter) WhereHasCloudflowDatas() {
	f.Where(entql.HasEdge("cloudflow_datas"))
}

// WhereHasCloudflowDatasWith applies a predicate to check if query has an edge cloudflow_datas with a given conditions (other predicates).
func (f *CloudAlertFilter) WhereHasCloudflowDatasWith(preds ...predicate.CloudFlowData) {
	f.Where(entql.HasEdgeWith("cloudflow_datas", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (cadq *CloudAttackDataQuery) addPredicate(pred func(s *sql.Selector)) {
	cadq.predicates = append(cadq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the CloudAttackDataQuery builder.
func (cadq *CloudAttackDataQuery) Filter() *CloudAttackDataFilter {
	return &CloudAttackDataFilter{config: cadq.config, predicateAdder: cadq}
}

// addPredicate implements the predicateAdder interface.
func (m *CloudAttackDataMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the CloudAttackDataMutation builder.
func (m *CloudAttackDataMutation) Filter() *CloudAttackDataFilter {
	return &CloudAttackDataFilter{config: m.config, predicateAdder: m}
}

// CloudAttackDataFilter provides a generic filtering capability at runtime for CloudAttackDataQuery.
type CloudAttackDataFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *CloudAttackDataFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[3].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *CloudAttackDataFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(cloudattackdata.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *CloudAttackDataFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(cloudattackdata.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *CloudAttackDataFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(cloudattackdata.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *CloudAttackDataFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(cloudattackdata.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *CloudAttackDataFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(cloudattackdata.FieldRemark))
}

// WhereSrcIP applies the entql string predicate on the src_ip field.
func (f *CloudAttackDataFilter) WhereSrcIP(p entql.StringP) {
	f.Where(p.Field(cloudattackdata.FieldSrcIP))
}

// WhereSrcPort applies the entql int predicate on the src_port field.
func (f *CloudAttackDataFilter) WhereSrcPort(p entql.IntP) {
	f.Where(p.Field(cloudattackdata.FieldSrcPort))
}

// WhereDstIP applies the entql string predicate on the dst_ip field.
func (f *CloudAttackDataFilter) WhereDstIP(p entql.StringP) {
	f.Where(p.Field(cloudattackdata.FieldDstIP))
}

// WhereDstPort applies the entql int predicate on the dst_port field.
func (f *CloudAttackDataFilter) WhereDstPort(p entql.IntP) {
	f.Where(p.Field(cloudattackdata.FieldDstPort))
}

// WhereProtocol applies the entql int predicate on the protocol field.
func (f *CloudAttackDataFilter) WhereProtocol(p entql.IntP) {
	f.Where(p.Field(cloudattackdata.FieldProtocol))
}

// WhereCurrentAttackPps applies the entql int64 predicate on the current_attack_pps field.
func (f *CloudAttackDataFilter) WhereCurrentAttackPps(p entql.Int64P) {
	f.Where(p.Field(cloudattackdata.FieldCurrentAttackPps))
}

// WhereStartTime applies the entql time.Time predicate on the start_time field.
func (f *CloudAttackDataFilter) WhereStartTime(p entql.TimeP) {
	f.Where(p.Field(cloudattackdata.FieldStartTime))
}

// WhereEndTime applies the entql time.Time predicate on the end_time field.
func (f *CloudAttackDataFilter) WhereEndTime(p entql.TimeP) {
	f.Where(p.Field(cloudattackdata.FieldEndTime))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *CloudAttackDataFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *CloudAttackDataFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (cfdq *CloudFlowDataQuery) addPredicate(pred func(s *sql.Selector)) {
	cfdq.predicates = append(cfdq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the CloudFlowDataQuery builder.
func (cfdq *CloudFlowDataQuery) Filter() *CloudFlowDataFilter {
	return &CloudFlowDataFilter{config: cfdq.config, predicateAdder: cfdq}
}

// addPredicate implements the predicateAdder interface.
func (m *CloudFlowDataMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the CloudFlowDataMutation builder.
func (m *CloudFlowDataMutation) Filter() *CloudFlowDataFilter {
	return &CloudFlowDataFilter{config: m.config, predicateAdder: m}
}

// CloudFlowDataFilter provides a generic filtering capability at runtime for CloudFlowDataQuery.
type CloudFlowDataFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *CloudFlowDataFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[4].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *CloudFlowDataFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(cloudflowdata.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *CloudFlowDataFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(cloudflowdata.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *CloudFlowDataFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(cloudflowdata.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *CloudFlowDataFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(cloudflowdata.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *CloudFlowDataFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(cloudflowdata.FieldRemark))
}

// WhereCloudAlertID applies the entql int predicate on the cloud_alert_id field.
func (f *CloudFlowDataFilter) WhereCloudAlertID(p entql.IntP) {
	f.Where(p.Field(cloudflowdata.FieldCloudAlertID))
}

// WhereSrcIP applies the entql string predicate on the src_ip field.
func (f *CloudFlowDataFilter) WhereSrcIP(p entql.StringP) {
	f.Where(p.Field(cloudflowdata.FieldSrcIP))
}

// WhereSrcPort applies the entql int predicate on the src_port field.
func (f *CloudFlowDataFilter) WhereSrcPort(p entql.IntP) {
	f.Where(p.Field(cloudflowdata.FieldSrcPort))
}

// WhereDstIP applies the entql string predicate on the dst_ip field.
func (f *CloudFlowDataFilter) WhereDstIP(p entql.StringP) {
	f.Where(p.Field(cloudflowdata.FieldDstIP))
}

// WhereDstPort applies the entql int predicate on the dst_port field.
func (f *CloudFlowDataFilter) WhereDstPort(p entql.IntP) {
	f.Where(p.Field(cloudflowdata.FieldDstPort))
}

// WhereProtocol applies the entql int predicate on the protocol field.
func (f *CloudFlowDataFilter) WhereProtocol(p entql.IntP) {
	f.Where(p.Field(cloudflowdata.FieldProtocol))
}

// WhereMaxAttackPps applies the entql int64 predicate on the max_attack_pps field.
func (f *CloudFlowDataFilter) WhereMaxAttackPps(p entql.Int64P) {
	f.Where(p.Field(cloudflowdata.FieldMaxAttackPps))
}

// WhereFlowOverMaxPpsCount applies the entql int predicate on the flow_over_max_pps_count field.
func (f *CloudFlowDataFilter) WhereFlowOverMaxPpsCount(p entql.IntP) {
	f.Where(p.Field(cloudflowdata.FieldFlowOverMaxPpsCount))
}

// WhereStartTime applies the entql time.Time predicate on the start_time field.
func (f *CloudFlowDataFilter) WhereStartTime(p entql.TimeP) {
	f.Where(p.Field(cloudflowdata.FieldStartTime))
}

// WhereEndTime applies the entql time.Time predicate on the end_time field.
func (f *CloudFlowDataFilter) WhereEndTime(p entql.TimeP) {
	f.Where(p.Field(cloudflowdata.FieldEndTime))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *CloudFlowDataFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *CloudFlowDataFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasCloudAlert applies a predicate to check if query has an edge cloud_alert.
func (f *CloudFlowDataFilter) WhereHasCloudAlert() {
	f.Where(entql.HasEdge("cloud_alert"))
}

// WhereHasCloudAlertWith applies a predicate to check if query has an edge cloud_alert with a given conditions (other predicates).
func (f *CloudFlowDataFilter) WhereHasCloudAlertWith(preds ...predicate.CloudAlert) {
	f.Where(entql.HasEdgeWith("cloud_alert", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (dsq *DataSyncQuery) addPredicate(pred func(s *sql.Selector)) {
	dsq.predicates = append(dsq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the DataSyncQuery builder.
func (dsq *DataSyncQuery) Filter() *DataSyncFilter {
	return &DataSyncFilter{config: dsq.config, predicateAdder: dsq}
}

// addPredicate implements the predicateAdder interface.
func (m *DataSyncMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the DataSyncMutation builder.
func (m *DataSyncMutation) Filter() *DataSyncFilter {
	return &DataSyncFilter{config: m.config, predicateAdder: m}
}

// DataSyncFilter provides a generic filtering capability at runtime for DataSyncQuery.
type DataSyncFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *DataSyncFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[5].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *DataSyncFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(datasync.FieldID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *DataSyncFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(datasync.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *DataSyncFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(datasync.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *DataSyncFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(datasync.FieldRemark))
}

// WherePreDataList applies the entql json.RawMessage predicate on the pre_data_list field.
func (f *DataSyncFilter) WherePreDataList(p entql.BytesP) {
	f.Where(p.Field(datasync.FieldPreDataList))
}

// WhereDataList applies the entql json.RawMessage predicate on the data_list field.
func (f *DataSyncFilter) WhereDataList(p entql.BytesP) {
	f.Where(p.Field(datasync.FieldDataList))
}

// WhereDataType applies the entql string predicate on the data_type field.
func (f *DataSyncFilter) WhereDataType(p entql.StringP) {
	f.Where(p.Field(datasync.FieldDataType))
}

// WhereType applies the entql string predicate on the type field.
func (f *DataSyncFilter) WhereType(p entql.StringP) {
	f.Where(p.Field(datasync.FieldType))
}

// addPredicate implements the predicateAdder interface.
func (gq *GroupQuery) addPredicate(pred func(s *sql.Selector)) {
	gq.predicates = append(gq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the GroupQuery builder.
func (gq *GroupQuery) Filter() *GroupFilter {
	return &GroupFilter{config: gq.config, predicateAdder: gq}
}

// addPredicate implements the predicateAdder interface.
func (m *GroupMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the GroupMutation builder.
func (m *GroupMutation) Filter() *GroupFilter {
	return &GroupFilter{config: m.config, predicateAdder: m}
}

// GroupFilter provides a generic filtering capability at runtime for GroupQuery.
type GroupFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *GroupFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[6].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *GroupFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(group.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *GroupFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(group.FieldTenantID))
}

// WhereName applies the entql string predicate on the name field.
func (f *GroupFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(group.FieldName))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *GroupFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *GroupFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasUsers applies a predicate to check if query has an edge users.
func (f *GroupFilter) WhereHasUsers() {
	f.Where(entql.HasEdge("users"))
}

// WhereHasUsersWith applies a predicate to check if query has an edge users with a given conditions (other predicates).
func (f *GroupFilter) WhereHasUsersWith(preds ...predicate.User) {
	f.Where(entql.HasEdgeWith("users", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (msaq *MatrixSpectrumAlertQuery) addPredicate(pred func(s *sql.Selector)) {
	msaq.predicates = append(msaq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the MatrixSpectrumAlertQuery builder.
func (msaq *MatrixSpectrumAlertQuery) Filter() *MatrixSpectrumAlertFilter {
	return &MatrixSpectrumAlertFilter{config: msaq.config, predicateAdder: msaq}
}

// addPredicate implements the predicateAdder interface.
func (m *MatrixSpectrumAlertMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the MatrixSpectrumAlertMutation builder.
func (m *MatrixSpectrumAlertMutation) Filter() *MatrixSpectrumAlertFilter {
	return &MatrixSpectrumAlertFilter{config: m.config, predicateAdder: m}
}

// MatrixSpectrumAlertFilter provides a generic filtering capability at runtime for MatrixSpectrumAlertQuery.
type MatrixSpectrumAlertFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *MatrixSpectrumAlertFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[7].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *MatrixSpectrumAlertFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(matrixspectrumalert.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *MatrixSpectrumAlertFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(matrixspectrumalert.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *MatrixSpectrumAlertFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(matrixspectrumalert.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *MatrixSpectrumAlertFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(matrixspectrumalert.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *MatrixSpectrumAlertFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(matrixspectrumalert.FieldRemark))
}

// WhereWofangID applies the entql int predicate on the wofang_id field.
func (f *MatrixSpectrumAlertFilter) WhereWofangID(p entql.IntP) {
	f.Where(p.Field(matrixspectrumalert.FieldWofangID))
}

// WhereMatrixStrategyID applies the entql int predicate on the matrix_strategy_id field.
func (f *MatrixSpectrumAlertFilter) WhereMatrixStrategyID(p entql.IntP) {
	f.Where(p.Field(matrixspectrumalert.FieldMatrixStrategyID))
}

// WhereIPList applies the entql json.RawMessage predicate on the ip_list field.
func (f *MatrixSpectrumAlertFilter) WhereIPList(p entql.BytesP) {
	f.Where(p.Field(matrixspectrumalert.FieldIPList))
}

// WhereRegion applies the entql string predicate on the region field.
func (f *MatrixSpectrumAlertFilter) WhereRegion(p entql.StringP) {
	f.Where(p.Field(matrixspectrumalert.FieldRegion))
}

// WhereNetType applies the entql string predicate on the net_type field.
func (f *MatrixSpectrumAlertFilter) WhereNetType(p entql.StringP) {
	f.Where(p.Field(matrixspectrumalert.FieldNetType))
}

// WhereIsp applies the entql string predicate on the isp field.
func (f *MatrixSpectrumAlertFilter) WhereIsp(p entql.StringP) {
	f.Where(p.Field(matrixspectrumalert.FieldIsp))
}

// WhereStartTime applies the entql time.Time predicate on the start_time field.
func (f *MatrixSpectrumAlertFilter) WhereStartTime(p entql.TimeP) {
	f.Where(p.Field(matrixspectrumalert.FieldStartTime))
}

// WhereEndTime applies the entql time.Time predicate on the end_time field.
func (f *MatrixSpectrumAlertFilter) WhereEndTime(p entql.TimeP) {
	f.Where(p.Field(matrixspectrumalert.FieldEndTime))
}

// WhereAttackType applies the entql string predicate on the attack_type field.
func (f *MatrixSpectrumAlertFilter) WhereAttackType(p entql.StringP) {
	f.Where(p.Field(matrixspectrumalert.FieldAttackType))
}

// WhereBps applies the entql int64 predicate on the bps field.
func (f *MatrixSpectrumAlertFilter) WhereBps(p entql.Int64P) {
	f.Where(p.Field(matrixspectrumalert.FieldBps))
}

// WhereAttackInfo applies the entql json.RawMessage predicate on the attack_info field.
func (f *MatrixSpectrumAlertFilter) WhereAttackInfo(p entql.BytesP) {
	f.Where(p.Field(matrixspectrumalert.FieldAttackInfo))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *MatrixSpectrumAlertFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *MatrixSpectrumAlertFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasMatrixSpectrumDatas applies a predicate to check if query has an edge matrix_spectrum_datas.
func (f *MatrixSpectrumAlertFilter) WhereHasMatrixSpectrumDatas() {
	f.Where(entql.HasEdge("matrix_spectrum_datas"))
}

// WhereHasMatrixSpectrumDatasWith applies a predicate to check if query has an edge matrix_spectrum_datas with a given conditions (other predicates).
func (f *MatrixSpectrumAlertFilter) WhereHasMatrixSpectrumDatasWith(preds ...predicate.MatrixSpectrumData) {
	f.Where(entql.HasEdgeWith("matrix_spectrum_datas", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasMatrixStrategy applies a predicate to check if query has an edge matrix_strategy.
func (f *MatrixSpectrumAlertFilter) WhereHasMatrixStrategy() {
	f.Where(entql.HasEdge("matrix_strategy"))
}

// WhereHasMatrixStrategyWith applies a predicate to check if query has an edge matrix_strategy with a given conditions (other predicates).
func (f *MatrixSpectrumAlertFilter) WhereHasMatrixStrategyWith(preds ...predicate.MatrixStrategy) {
	f.Where(entql.HasEdgeWith("matrix_strategy", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasWofangTicket applies a predicate to check if query has an edge wofang_ticket.
func (f *MatrixSpectrumAlertFilter) WhereHasWofangTicket() {
	f.Where(entql.HasEdge("wofang_ticket"))
}

// WhereHasWofangTicketWith applies a predicate to check if query has an edge wofang_ticket with a given conditions (other predicates).
func (f *MatrixSpectrumAlertFilter) WhereHasWofangTicketWith(preds ...predicate.Wofang) {
	f.Where(entql.HasEdgeWith("wofang_ticket", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (msdq *MatrixSpectrumDataQuery) addPredicate(pred func(s *sql.Selector)) {
	msdq.predicates = append(msdq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the MatrixSpectrumDataQuery builder.
func (msdq *MatrixSpectrumDataQuery) Filter() *MatrixSpectrumDataFilter {
	return &MatrixSpectrumDataFilter{config: msdq.config, predicateAdder: msdq}
}

// addPredicate implements the predicateAdder interface.
func (m *MatrixSpectrumDataMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the MatrixSpectrumDataMutation builder.
func (m *MatrixSpectrumDataMutation) Filter() *MatrixSpectrumDataFilter {
	return &MatrixSpectrumDataFilter{config: m.config, predicateAdder: m}
}

// MatrixSpectrumDataFilter provides a generic filtering capability at runtime for MatrixSpectrumDataQuery.
type MatrixSpectrumDataFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *MatrixSpectrumDataFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[8].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *MatrixSpectrumDataFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(matrixspectrumdata.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *MatrixSpectrumDataFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(matrixspectrumdata.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *MatrixSpectrumDataFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(matrixspectrumdata.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *MatrixSpectrumDataFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(matrixspectrumdata.FieldUpdatedAt))
}

// WhereMatrixSpectrumAlertID applies the entql int predicate on the matrix_spectrum_alert_id field.
func (f *MatrixSpectrumDataFilter) WhereMatrixSpectrumAlertID(p entql.IntP) {
	f.Where(p.Field(matrixspectrumdata.FieldMatrixSpectrumAlertID))
}

// WhereRegion applies the entql string predicate on the region field.
func (f *MatrixSpectrumDataFilter) WhereRegion(p entql.StringP) {
	f.Where(p.Field(matrixspectrumdata.FieldRegion))
}

// WhereNetType applies the entql string predicate on the net_type field.
func (f *MatrixSpectrumDataFilter) WhereNetType(p entql.StringP) {
	f.Where(p.Field(matrixspectrumdata.FieldNetType))
}

// WhereIsp applies the entql string predicate on the isp field.
func (f *MatrixSpectrumDataFilter) WhereIsp(p entql.StringP) {
	f.Where(p.Field(matrixspectrumdata.FieldIsp))
}

// WhereBps applies the entql int64 predicate on the bps field.
func (f *MatrixSpectrumDataFilter) WhereBps(p entql.Int64P) {
	f.Where(p.Field(matrixspectrumdata.FieldBps))
}

// WhereTime applies the entql time.Time predicate on the time field.
func (f *MatrixSpectrumDataFilter) WhereTime(p entql.TimeP) {
	f.Where(p.Field(matrixspectrumdata.FieldTime))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *MatrixSpectrumDataFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *MatrixSpectrumDataFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasMatrixSpectrumAlert applies a predicate to check if query has an edge matrix_spectrum_alert.
func (f *MatrixSpectrumDataFilter) WhereHasMatrixSpectrumAlert() {
	f.Where(entql.HasEdge("matrix_spectrum_alert"))
}

// WhereHasMatrixSpectrumAlertWith applies a predicate to check if query has an edge matrix_spectrum_alert with a given conditions (other predicates).
func (f *MatrixSpectrumDataFilter) WhereHasMatrixSpectrumAlertWith(preds ...predicate.MatrixSpectrumAlert) {
	f.Where(entql.HasEdgeWith("matrix_spectrum_alert", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (msq *MatrixStrategyQuery) addPredicate(pred func(s *sql.Selector)) {
	msq.predicates = append(msq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the MatrixStrategyQuery builder.
func (msq *MatrixStrategyQuery) Filter() *MatrixStrategyFilter {
	return &MatrixStrategyFilter{config: msq.config, predicateAdder: msq}
}

// addPredicate implements the predicateAdder interface.
func (m *MatrixStrategyMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the MatrixStrategyMutation builder.
func (m *MatrixStrategyMutation) Filter() *MatrixStrategyFilter {
	return &MatrixStrategyFilter{config: m.config, predicateAdder: m}
}

// MatrixStrategyFilter provides a generic filtering capability at runtime for MatrixStrategyQuery.
type MatrixStrategyFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *MatrixStrategyFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[9].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *MatrixStrategyFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(matrixstrategy.FieldID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *MatrixStrategyFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(matrixstrategy.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *MatrixStrategyFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(matrixstrategy.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *MatrixStrategyFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(matrixstrategy.FieldRemark))
}

// WhereName applies the entql string predicate on the name field.
func (f *MatrixStrategyFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(matrixstrategy.FieldName))
}

// WhereRegion applies the entql string predicate on the region field.
func (f *MatrixStrategyFilter) WhereRegion(p entql.StringP) {
	f.Where(p.Field(matrixstrategy.FieldRegion))
}

// WhereNetType applies the entql string predicate on the net_type field.
func (f *MatrixStrategyFilter) WhereNetType(p entql.StringP) {
	f.Where(p.Field(matrixstrategy.FieldNetType))
}

// WhereIsp applies the entql string predicate on the isp field.
func (f *MatrixStrategyFilter) WhereIsp(p entql.StringP) {
	f.Where(p.Field(matrixstrategy.FieldIsp))
}

// WhereMonitorBps applies the entql int64 predicate on the monitor_bps field.
func (f *MatrixStrategyFilter) WhereMonitorBps(p entql.Int64P) {
	f.Where(p.Field(matrixstrategy.FieldMonitorBps))
}

// WhereDragBps applies the entql int64 predicate on the drag_bps field.
func (f *MatrixStrategyFilter) WhereDragBps(p entql.Int64P) {
	f.Where(p.Field(matrixstrategy.FieldDragBps))
}

// WhereDragType applies the entql int predicate on the drag_type field.
func (f *MatrixStrategyFilter) WhereDragType(p entql.IntP) {
	f.Where(p.Field(matrixstrategy.FieldDragType))
}

// WhereHasMatrixStrategyAlerts applies a predicate to check if query has an edge matrix_strategy_alerts.
func (f *MatrixStrategyFilter) WhereHasMatrixStrategyAlerts() {
	f.Where(entql.HasEdge("matrix_strategy_alerts"))
}

// WhereHasMatrixStrategyAlertsWith applies a predicate to check if query has an edge matrix_strategy_alerts with a given conditions (other predicates).
func (f *MatrixStrategyFilter) WhereHasMatrixStrategyAlertsWith(preds ...predicate.MatrixSpectrumAlert) {
	f.Where(entql.HasEdgeWith("matrix_strategy_alerts", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (nq *NotifyQuery) addPredicate(pred func(s *sql.Selector)) {
	nq.predicates = append(nq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the NotifyQuery builder.
func (nq *NotifyQuery) Filter() *NotifyFilter {
	return &NotifyFilter{config: nq.config, predicateAdder: nq}
}

// addPredicate implements the predicateAdder interface.
func (m *NotifyMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the NotifyMutation builder.
func (m *NotifyMutation) Filter() *NotifyFilter {
	return &NotifyFilter{config: m.config, predicateAdder: m}
}

// NotifyFilter provides a generic filtering capability at runtime for NotifyQuery.
type NotifyFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *NotifyFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[10].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *NotifyFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(notify.FieldID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *NotifyFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(notify.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *NotifyFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(notify.FieldUpdatedAt))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *NotifyFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(notify.FieldTenantID))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *NotifyFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(notify.FieldRemark))
}

// WhereName applies the entql string predicate on the name field.
func (f *NotifyFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(notify.FieldName))
}

// WherePopo applies the entql bool predicate on the popo field.
func (f *NotifyFilter) WherePopo(p entql.BoolP) {
	f.Where(p.Field(notify.FieldPopo))
}

// WhereEmail applies the entql bool predicate on the email field.
func (f *NotifyFilter) WhereEmail(p entql.BoolP) {
	f.Where(p.Field(notify.FieldEmail))
}

// WhereSms applies the entql bool predicate on the sms field.
func (f *NotifyFilter) WhereSms(p entql.BoolP) {
	f.Where(p.Field(notify.FieldSms))
}

// WherePhone applies the entql bool predicate on the phone field.
func (f *NotifyFilter) WherePhone(p entql.BoolP) {
	f.Where(p.Field(notify.FieldPhone))
}

// WherePopoGroups applies the entql json.RawMessage predicate on the popo_groups field.
func (f *NotifyFilter) WherePopoGroups(p entql.BytesP) {
	f.Where(p.Field(notify.FieldPopoGroups))
}

// WhereEmails applies the entql json.RawMessage predicate on the emails field.
func (f *NotifyFilter) WhereEmails(p entql.BytesP) {
	f.Where(p.Field(notify.FieldEmails))
}

// WherePhones applies the entql json.RawMessage predicate on the phones field.
func (f *NotifyFilter) WherePhones(p entql.BytesP) {
	f.Where(p.Field(notify.FieldPhones))
}

// WhereIPWhitelists applies the entql json.RawMessage predicate on the ip_whitelists field.
func (f *NotifyFilter) WhereIPWhitelists(p entql.BytesP) {
	f.Where(p.Field(notify.FieldIPWhitelists))
}

// WhereSystem applies the entql bool predicate on the system field.
func (f *NotifyFilter) WhereSystem(p entql.BoolP) {
	f.Where(p.Field(notify.FieldSystem))
}

// WhereEnabled applies the entql bool predicate on the enabled field.
func (f *NotifyFilter) WhereEnabled(p entql.BoolP) {
	f.Where(p.Field(notify.FieldEnabled))
}

// WhereSaNotifyPopo applies the entql bool predicate on the sa_notify_popo field.
func (f *NotifyFilter) WhereSaNotifyPopo(p entql.BoolP) {
	f.Where(p.Field(notify.FieldSaNotifyPopo))
}

// WhereSaNotifyEmail applies the entql bool predicate on the sa_notify_email field.
func (f *NotifyFilter) WhereSaNotifyEmail(p entql.BoolP) {
	f.Where(p.Field(notify.FieldSaNotifyEmail))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *NotifyFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *NotifyFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (pgq *ProtectGroupQuery) addPredicate(pred func(s *sql.Selector)) {
	pgq.predicates = append(pgq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the ProtectGroupQuery builder.
func (pgq *ProtectGroupQuery) Filter() *ProtectGroupFilter {
	return &ProtectGroupFilter{config: pgq.config, predicateAdder: pgq}
}

// addPredicate implements the predicateAdder interface.
func (m *ProtectGroupMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the ProtectGroupMutation builder.
func (m *ProtectGroupMutation) Filter() *ProtectGroupFilter {
	return &ProtectGroupFilter{config: m.config, predicateAdder: m}
}

// ProtectGroupFilter provides a generic filtering capability at runtime for ProtectGroupQuery.
type ProtectGroupFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *ProtectGroupFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[11].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *ProtectGroupFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(protectgroup.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *ProtectGroupFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(protectgroup.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *ProtectGroupFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(protectgroup.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *ProtectGroupFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(protectgroup.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *ProtectGroupFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(protectgroup.FieldRemark))
}

// WhereGroupName applies the entql string predicate on the group_name field.
func (f *ProtectGroupFilter) WhereGroupName(p entql.StringP) {
	f.Where(p.Field(protectgroup.FieldGroupName))
}

// WhereGroupID applies the entql int64 predicate on the group_id field.
func (f *ProtectGroupFilter) WhereGroupID(p entql.Int64P) {
	f.Where(p.Field(protectgroup.FieldGroupID))
}

// WhereType applies the entql int predicate on the type field.
func (f *ProtectGroupFilter) WhereType(p entql.IntP) {
	f.Where(p.Field(protectgroup.FieldType))
}

// WhereIPList applies the entql json.RawMessage predicate on the ip_list field.
func (f *ProtectGroupFilter) WhereIPList(p entql.BytesP) {
	f.Where(p.Field(protectgroup.FieldIPList))
}

// WhereExpandIP applies the entql string predicate on the expand_ip field.
func (f *ProtectGroupFilter) WhereExpandIP(p entql.StringP) {
	f.Where(p.Field(protectgroup.FieldExpandIP))
}

// WhereMonitorInfo applies the entql json.RawMessage predicate on the monitor_info field.
func (f *ProtectGroupFilter) WhereMonitorInfo(p entql.BytesP) {
	f.Where(p.Field(protectgroup.FieldMonitorInfo))
}

// WhereDragInfo applies the entql json.RawMessage predicate on the drag_info field.
func (f *ProtectGroupFilter) WhereDragInfo(p entql.BytesP) {
	f.Where(p.Field(protectgroup.FieldDragInfo))
}

// WhereNds4Config applies the entql json.RawMessage predicate on the nds4_config field.
func (f *ProtectGroupFilter) WhereNds4Config(p entql.BytesP) {
	f.Where(p.Field(protectgroup.FieldNds4Config))
}

// WhereNds6Config applies the entql json.RawMessage predicate on the nds6_config field.
func (f *ProtectGroupFilter) WhereNds6Config(p entql.BytesP) {
	f.Where(p.Field(protectgroup.FieldNds6Config))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *ProtectGroupFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *ProtectGroupFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasSpectrumAlerts applies a predicate to check if query has an edge spectrum_alerts.
func (f *ProtectGroupFilter) WhereHasSpectrumAlerts() {
	f.Where(entql.HasEdge("spectrum_alerts"))
}

// WhereHasSpectrumAlertsWith applies a predicate to check if query has an edge spectrum_alerts with a given conditions (other predicates).
func (f *ProtectGroupFilter) WhereHasSpectrumAlertsWith(preds ...predicate.SpectrumAlert) {
	f.Where(entql.HasEdgeWith("spectrum_alerts", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (sdq *SkylineDosQuery) addPredicate(pred func(s *sql.Selector)) {
	sdq.predicates = append(sdq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the SkylineDosQuery builder.
func (sdq *SkylineDosQuery) Filter() *SkylineDosFilter {
	return &SkylineDosFilter{config: sdq.config, predicateAdder: sdq}
}

// addPredicate implements the predicateAdder interface.
func (m *SkylineDosMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the SkylineDosMutation builder.
func (m *SkylineDosMutation) Filter() *SkylineDosFilter {
	return &SkylineDosFilter{config: m.config, predicateAdder: m}
}

// SkylineDosFilter provides a generic filtering capability at runtime for SkylineDosQuery.
type SkylineDosFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *SkylineDosFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[12].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *SkylineDosFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(skylinedos.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *SkylineDosFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(skylinedos.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *SkylineDosFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(skylinedos.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *SkylineDosFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(skylinedos.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *SkylineDosFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(skylinedos.FieldRemark))
}

// WhereStartTime applies the entql time.Time predicate on the start_time field.
func (f *SkylineDosFilter) WhereStartTime(p entql.TimeP) {
	f.Where(p.Field(skylinedos.FieldStartTime))
}

// WhereEndTime applies the entql time.Time predicate on the end_time field.
func (f *SkylineDosFilter) WhereEndTime(p entql.TimeP) {
	f.Where(p.Field(skylinedos.FieldEndTime))
}

// WhereRegion applies the entql string predicate on the region field.
func (f *SkylineDosFilter) WhereRegion(p entql.StringP) {
	f.Where(p.Field(skylinedos.FieldRegion))
}

// WhereResource applies the entql string predicate on the resource field.
func (f *SkylineDosFilter) WhereResource(p entql.StringP) {
	f.Where(p.Field(skylinedos.FieldResource))
}

// WhereResourceType applies the entql string predicate on the resource_type field.
func (f *SkylineDosFilter) WhereResourceType(p entql.StringP) {
	f.Where(p.Field(skylinedos.FieldResourceType))
}

// WhereVectorTypes applies the entql json.RawMessage predicate on the vector_types field.
func (f *SkylineDosFilter) WhereVectorTypes(p entql.BytesP) {
	f.Where(p.Field(skylinedos.FieldVectorTypes))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *SkylineDosFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(skylinedos.FieldStatus))
}

// WhereAttackID applies the entql string predicate on the attack_id field.
func (f *SkylineDosFilter) WhereAttackID(p entql.StringP) {
	f.Where(p.Field(skylinedos.FieldAttackID))
}

// WhereAttackCounters applies the entql json.RawMessage predicate on the attack_counters field.
func (f *SkylineDosFilter) WhereAttackCounters(p entql.BytesP) {
	f.Where(p.Field(skylinedos.FieldAttackCounters))
}

// WhereProject applies the entql string predicate on the project field.
func (f *SkylineDosFilter) WhereProject(p entql.StringP) {
	f.Where(p.Field(skylinedos.FieldProject))
}

// WhereDurationTime applies the entql int64 predicate on the duration_time field.
func (f *SkylineDosFilter) WhereDurationTime(p entql.Int64P) {
	f.Where(p.Field(skylinedos.FieldDurationTime))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *SkylineDosFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *SkylineDosFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (sgtq *SocGroupTicketQuery) addPredicate(pred func(s *sql.Selector)) {
	sgtq.predicates = append(sgtq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the SocGroupTicketQuery builder.
func (sgtq *SocGroupTicketQuery) Filter() *SocGroupTicketFilter {
	return &SocGroupTicketFilter{config: sgtq.config, predicateAdder: sgtq}
}

// addPredicate implements the predicateAdder interface.
func (m *SocGroupTicketMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the SocGroupTicketMutation builder.
func (m *SocGroupTicketMutation) Filter() *SocGroupTicketFilter {
	return &SocGroupTicketFilter{config: m.config, predicateAdder: m}
}

// SocGroupTicketFilter provides a generic filtering capability at runtime for SocGroupTicketQuery.
type SocGroupTicketFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *SocGroupTicketFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[13].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *SocGroupTicketFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(socgroupticket.FieldID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *SocGroupTicketFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(socgroupticket.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *SocGroupTicketFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(socgroupticket.FieldUpdatedAt))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *SocGroupTicketFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(socgroupticket.FieldTenantID))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *SocGroupTicketFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(socgroupticket.FieldRemark))
}

// WhereName applies the entql string predicate on the name field.
func (f *SocGroupTicketFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(socgroupticket.FieldName))
}

// WhereType applies the entql string predicate on the type field.
func (f *SocGroupTicketFilter) WhereType(p entql.StringP) {
	f.Where(p.Field(socgroupticket.FieldType))
}

// WhereDescription applies the entql string predicate on the description field.
func (f *SocGroupTicketFilter) WhereDescription(p entql.StringP) {
	f.Where(p.Field(socgroupticket.FieldDescription))
}

// WhereFollowList applies the entql json.RawMessage predicate on the follow_list field.
func (f *SocGroupTicketFilter) WhereFollowList(p entql.BytesP) {
	f.Where(p.Field(socgroupticket.FieldFollowList))
}

// WhereDepartmentID applies the entql int predicate on the department_id field.
func (f *SocGroupTicketFilter) WhereDepartmentID(p entql.IntP) {
	f.Where(p.Field(socgroupticket.FieldDepartmentID))
}

// WhereIPList applies the entql json.RawMessage predicate on the ip_list field.
func (f *SocGroupTicketFilter) WhereIPList(p entql.BytesP) {
	f.Where(p.Field(socgroupticket.FieldIPList))
}

// WhereMinBandwidth applies the entql float32 predicate on the min_bandwidth field.
func (f *SocGroupTicketFilter) WhereMinBandwidth(p entql.Float32P) {
	f.Where(p.Field(socgroupticket.FieldMinBandwidth))
}

// WhereDivertType applies the entql int predicate on the divert_type field.
func (f *SocGroupTicketFilter) WhereDivertType(p entql.IntP) {
	f.Where(p.Field(socgroupticket.FieldDivertType))
}

// WhereOpType applies the entql int predicate on the op_type field.
func (f *SocGroupTicketFilter) WhereOpType(p entql.IntP) {
	f.Where(p.Field(socgroupticket.FieldOpType))
}

// WhereOpTime applies the entql time.Time predicate on the op_time field.
func (f *SocGroupTicketFilter) WhereOpTime(p entql.TimeP) {
	f.Where(p.Field(socgroupticket.FieldOpTime))
}

// WhereConfigType applies the entql int predicate on the config_type field.
func (f *SocGroupTicketFilter) WhereConfigType(p entql.IntP) {
	f.Where(p.Field(socgroupticket.FieldConfigType))
}

// WhereConfigArgs applies the entql string predicate on the config_args field.
func (f *SocGroupTicketFilter) WhereConfigArgs(p entql.StringP) {
	f.Where(p.Field(socgroupticket.FieldConfigArgs))
}

// WhereProductName applies the entql string predicate on the product_name field.
func (f *SocGroupTicketFilter) WhereProductName(p entql.StringP) {
	f.Where(p.Field(socgroupticket.FieldProductName))
}

// WhereProductCode applies the entql string predicate on the product_code field.
func (f *SocGroupTicketFilter) WhereProductCode(p entql.StringP) {
	f.Where(p.Field(socgroupticket.FieldProductCode))
}

// WhereContactList applies the entql json.RawMessage predicate on the contact_list field.
func (f *SocGroupTicketFilter) WhereContactList(p entql.BytesP) {
	f.Where(p.Field(socgroupticket.FieldContactList))
}

// WhereGroupTicketID applies the entql int predicate on the group_ticket_id field.
func (f *SocGroupTicketFilter) WhereGroupTicketID(p entql.IntP) {
	f.Where(p.Field(socgroupticket.FieldGroupTicketID))
}

// WhereErrorInfo applies the entql string predicate on the error_info field.
func (f *SocGroupTicketFilter) WhereErrorInfo(p entql.StringP) {
	f.Where(p.Field(socgroupticket.FieldErrorInfo))
}

// WhereCreateUserID applies the entql int predicate on the create_user_id field.
func (f *SocGroupTicketFilter) WhereCreateUserID(p entql.IntP) {
	f.Where(p.Field(socgroupticket.FieldCreateUserID))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *SocGroupTicketFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *SocGroupTicketFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasUser applies a predicate to check if query has an edge user.
func (f *SocGroupTicketFilter) WhereHasUser() {
	f.Where(entql.HasEdge("user"))
}

// WhereHasUserWith applies a predicate to check if query has an edge user with a given conditions (other predicates).
func (f *SocGroupTicketFilter) WhereHasUserWith(preds ...predicate.User) {
	f.Where(entql.HasEdgeWith("user", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (saq *SpectrumAlertQuery) addPredicate(pred func(s *sql.Selector)) {
	saq.predicates = append(saq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the SpectrumAlertQuery builder.
func (saq *SpectrumAlertQuery) Filter() *SpectrumAlertFilter {
	return &SpectrumAlertFilter{config: saq.config, predicateAdder: saq}
}

// addPredicate implements the predicateAdder interface.
func (m *SpectrumAlertMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the SpectrumAlertMutation builder.
func (m *SpectrumAlertMutation) Filter() *SpectrumAlertFilter {
	return &SpectrumAlertFilter{config: m.config, predicateAdder: m}
}

// SpectrumAlertFilter provides a generic filtering capability at runtime for SpectrumAlertQuery.
type SpectrumAlertFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *SpectrumAlertFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[14].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *SpectrumAlertFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(spectrumalert.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *SpectrumAlertFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(spectrumalert.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *SpectrumAlertFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(spectrumalert.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *SpectrumAlertFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(spectrumalert.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *SpectrumAlertFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(spectrumalert.FieldRemark))
}

// WhereProtectGroupID applies the entql int predicate on the protect_group_id field.
func (f *SpectrumAlertFilter) WhereProtectGroupID(p entql.IntP) {
	f.Where(p.Field(spectrumalert.FieldProtectGroupID))
}

// WhereStrategyID applies the entql int predicate on the strategy_id field.
func (f *SpectrumAlertFilter) WhereStrategyID(p entql.IntP) {
	f.Where(p.Field(spectrumalert.FieldStrategyID))
}

// WhereWofangID applies the entql int predicate on the wofang_id field.
func (f *SpectrumAlertFilter) WhereWofangID(p entql.IntP) {
	f.Where(p.Field(spectrumalert.FieldWofangID))
}

// WhereProtectStatus applies the entql json.RawMessage predicate on the protect_status field.
func (f *SpectrumAlertFilter) WhereProtectStatus(p entql.BytesP) {
	f.Where(p.Field(spectrumalert.FieldProtectStatus))
}

// WhereIP applies the entql string predicate on the ip field.
func (f *SpectrumAlertFilter) WhereIP(p entql.StringP) {
	f.Where(p.Field(spectrumalert.FieldIP))
}

// WhereStartTime applies the entql time.Time predicate on the start_time field.
func (f *SpectrumAlertFilter) WhereStartTime(p entql.TimeP) {
	f.Where(p.Field(spectrumalert.FieldStartTime))
}

// WhereEndTime applies the entql time.Time predicate on the end_time field.
func (f *SpectrumAlertFilter) WhereEndTime(p entql.TimeP) {
	f.Where(p.Field(spectrumalert.FieldEndTime))
}

// WhereAttackType applies the entql string predicate on the attack_type field.
func (f *SpectrumAlertFilter) WhereAttackType(p entql.StringP) {
	f.Where(p.Field(spectrumalert.FieldAttackType))
}

// WhereMaxPps applies the entql int64 predicate on the max_pps field.
func (f *SpectrumAlertFilter) WhereMaxPps(p entql.Int64P) {
	f.Where(p.Field(spectrumalert.FieldMaxPps))
}

// WhereMaxBps applies the entql int64 predicate on the max_bps field.
func (f *SpectrumAlertFilter) WhereMaxBps(p entql.Int64P) {
	f.Where(p.Field(spectrumalert.FieldMaxBps))
}

// WhereAttackInfo applies the entql json.RawMessage predicate on the attack_info field.
func (f *SpectrumAlertFilter) WhereAttackInfo(p entql.BytesP) {
	f.Where(p.Field(spectrumalert.FieldAttackInfo))
}

// WhereIspCode applies the entql int predicate on the isp_code field.
func (f *SpectrumAlertFilter) WhereIspCode(p entql.IntP) {
	f.Where(p.Field(spectrumalert.FieldIspCode))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *SpectrumAlertFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *SpectrumAlertFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasSpectrumDatas applies a predicate to check if query has an edge spectrum_datas.
func (f *SpectrumAlertFilter) WhereHasSpectrumDatas() {
	f.Where(entql.HasEdge("spectrum_datas"))
}

// WhereHasSpectrumDatasWith applies a predicate to check if query has an edge spectrum_datas with a given conditions (other predicates).
func (f *SpectrumAlertFilter) WhereHasSpectrumDatasWith(preds ...predicate.SpectrumData) {
	f.Where(entql.HasEdgeWith("spectrum_datas", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasCleanDatas applies a predicate to check if query has an edge clean_datas.
func (f *SpectrumAlertFilter) WhereHasCleanDatas() {
	f.Where(entql.HasEdge("clean_datas"))
}

// WhereHasCleanDatasWith applies a predicate to check if query has an edge clean_datas with a given conditions (other predicates).
func (f *SpectrumAlertFilter) WhereHasCleanDatasWith(preds ...predicate.CleanData) {
	f.Where(entql.HasEdgeWith("clean_datas", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasProtectGroup applies a predicate to check if query has an edge protect_group.
func (f *SpectrumAlertFilter) WhereHasProtectGroup() {
	f.Where(entql.HasEdge("protect_group"))
}

// WhereHasProtectGroupWith applies a predicate to check if query has an edge protect_group with a given conditions (other predicates).
func (f *SpectrumAlertFilter) WhereHasProtectGroupWith(preds ...predicate.ProtectGroup) {
	f.Where(entql.HasEdgeWith("protect_group", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasStrategy applies a predicate to check if query has an edge strategy.
func (f *SpectrumAlertFilter) WhereHasStrategy() {
	f.Where(entql.HasEdge("strategy"))
}

// WhereHasStrategyWith applies a predicate to check if query has an edge strategy with a given conditions (other predicates).
func (f *SpectrumAlertFilter) WhereHasStrategyWith(preds ...predicate.Strategy) {
	f.Where(entql.HasEdgeWith("strategy", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasWofangTicket applies a predicate to check if query has an edge wofang_ticket.
func (f *SpectrumAlertFilter) WhereHasWofangTicket() {
	f.Where(entql.HasEdge("wofang_ticket"))
}

// WhereHasWofangTicketWith applies a predicate to check if query has an edge wofang_ticket with a given conditions (other predicates).
func (f *SpectrumAlertFilter) WhereHasWofangTicketWith(preds ...predicate.Wofang) {
	f.Where(entql.HasEdgeWith("wofang_ticket", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (sdq *SpectrumDataQuery) addPredicate(pred func(s *sql.Selector)) {
	sdq.predicates = append(sdq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the SpectrumDataQuery builder.
func (sdq *SpectrumDataQuery) Filter() *SpectrumDataFilter {
	return &SpectrumDataFilter{config: sdq.config, predicateAdder: sdq}
}

// addPredicate implements the predicateAdder interface.
func (m *SpectrumDataMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the SpectrumDataMutation builder.
func (m *SpectrumDataMutation) Filter() *SpectrumDataFilter {
	return &SpectrumDataFilter{config: m.config, predicateAdder: m}
}

// SpectrumDataFilter provides a generic filtering capability at runtime for SpectrumDataQuery.
type SpectrumDataFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *SpectrumDataFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[15].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *SpectrumDataFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(spectrumdata.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *SpectrumDataFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(spectrumdata.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *SpectrumDataFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(spectrumdata.FieldCreatedAt))
}

// WhereSpectrumAlertID applies the entql int predicate on the spectrum_alert_id field.
func (f *SpectrumDataFilter) WhereSpectrumAlertID(p entql.IntP) {
	f.Where(p.Field(spectrumdata.FieldSpectrumAlertID))
}

// WhereIP applies the entql string predicate on the ip field.
func (f *SpectrumDataFilter) WhereIP(p entql.StringP) {
	f.Where(p.Field(spectrumdata.FieldIP))
}

// WhereTime applies the entql time.Time predicate on the time field.
func (f *SpectrumDataFilter) WhereTime(p entql.TimeP) {
	f.Where(p.Field(spectrumdata.FieldTime))
}

// WhereMonitorID applies the entql int predicate on the monitor_id field.
func (f *SpectrumDataFilter) WhereMonitorID(p entql.IntP) {
	f.Where(p.Field(spectrumdata.FieldMonitorID))
}

// WhereDataType applies the entql int predicate on the data_type field.
func (f *SpectrumDataFilter) WhereDataType(p entql.IntP) {
	f.Where(p.Field(spectrumdata.FieldDataType))
}

// WhereBps applies the entql int64 predicate on the bps field.
func (f *SpectrumDataFilter) WhereBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldBps))
}

// WherePps applies the entql int64 predicate on the pps field.
func (f *SpectrumDataFilter) WherePps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldPps))
}

// WhereSynBps applies the entql int64 predicate on the syn_bps field.
func (f *SpectrumDataFilter) WhereSynBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldSynBps))
}

// WhereSynPps applies the entql int64 predicate on the syn_pps field.
func (f *SpectrumDataFilter) WhereSynPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldSynPps))
}

// WhereAckBps applies the entql int64 predicate on the ack_bps field.
func (f *SpectrumDataFilter) WhereAckBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldAckBps))
}

// WhereAckPps applies the entql int64 predicate on the ack_pps field.
func (f *SpectrumDataFilter) WhereAckPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldAckPps))
}

// WhereSynAckBps applies the entql int64 predicate on the syn_ack_bps field.
func (f *SpectrumDataFilter) WhereSynAckBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldSynAckBps))
}

// WhereSynAckPps applies the entql int64 predicate on the syn_ack_pps field.
func (f *SpectrumDataFilter) WhereSynAckPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldSynAckPps))
}

// WhereIcmpBps applies the entql int64 predicate on the icmp_bps field.
func (f *SpectrumDataFilter) WhereIcmpBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldIcmpBps))
}

// WhereIcmpPps applies the entql int64 predicate on the icmp_pps field.
func (f *SpectrumDataFilter) WhereIcmpPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldIcmpPps))
}

// WhereSmallPps applies the entql int64 predicate on the small_pps field.
func (f *SpectrumDataFilter) WhereSmallPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldSmallPps))
}

// WhereNtpPps applies the entql int64 predicate on the ntp_pps field.
func (f *SpectrumDataFilter) WhereNtpPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldNtpPps))
}

// WhereNtpBps applies the entql int64 predicate on the ntp_bps field.
func (f *SpectrumDataFilter) WhereNtpBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldNtpBps))
}

// WhereDNSQueryPps applies the entql int64 predicate on the dns_query_pps field.
func (f *SpectrumDataFilter) WhereDNSQueryPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldDNSQueryPps))
}

// WhereDNSQueryBps applies the entql int64 predicate on the dns_query_bps field.
func (f *SpectrumDataFilter) WhereDNSQueryBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldDNSQueryBps))
}

// WhereDNSAnswerPps applies the entql int64 predicate on the dns_answer_pps field.
func (f *SpectrumDataFilter) WhereDNSAnswerPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldDNSAnswerPps))
}

// WhereDNSAnswerBps applies the entql int64 predicate on the dns_answer_bps field.
func (f *SpectrumDataFilter) WhereDNSAnswerBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldDNSAnswerBps))
}

// WhereSsdpBps applies the entql int64 predicate on the ssdp_bps field.
func (f *SpectrumDataFilter) WhereSsdpBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldSsdpBps))
}

// WhereSsdpPps applies the entql int64 predicate on the ssdp_pps field.
func (f *SpectrumDataFilter) WhereSsdpPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldSsdpPps))
}

// WhereUDPPps applies the entql int64 predicate on the udp_pps field.
func (f *SpectrumDataFilter) WhereUDPPps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldUDPPps))
}

// WhereUDPBps applies the entql int64 predicate on the udp_bps field.
func (f *SpectrumDataFilter) WhereUDPBps(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldUDPBps))
}

// WhereQPS applies the entql int64 predicate on the qps field.
func (f *SpectrumDataFilter) WhereQPS(p entql.Int64P) {
	f.Where(p.Field(spectrumdata.FieldQPS))
}

// WhereReceiveCount applies the entql int predicate on the receive_count field.
func (f *SpectrumDataFilter) WhereReceiveCount(p entql.IntP) {
	f.Where(p.Field(spectrumdata.FieldReceiveCount))
}

// WhereIPType applies the entql int predicate on the ip_type field.
func (f *SpectrumDataFilter) WhereIPType(p entql.IntP) {
	f.Where(p.Field(spectrumdata.FieldIPType))
}

// WhereMonitor applies the entql string predicate on the monitor field.
func (f *SpectrumDataFilter) WhereMonitor(p entql.StringP) {
	f.Where(p.Field(spectrumdata.FieldMonitor))
}

// WhereProduct applies the entql string predicate on the product field.
func (f *SpectrumDataFilter) WhereProduct(p entql.StringP) {
	f.Where(p.Field(spectrumdata.FieldProduct))
}

// WhereHost applies the entql string predicate on the host field.
func (f *SpectrumDataFilter) WhereHost(p entql.StringP) {
	f.Where(p.Field(spectrumdata.FieldHost))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *SpectrumDataFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *SpectrumDataFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasSpectrumAlert applies a predicate to check if query has an edge spectrum_alert.
func (f *SpectrumDataFilter) WhereHasSpectrumAlert() {
	f.Where(entql.HasEdge("spectrum_alert"))
}

// WhereHasSpectrumAlertWith applies a predicate to check if query has an edge spectrum_alert with a given conditions (other predicates).
func (f *SpectrumDataFilter) WhereHasSpectrumAlertWith(preds ...predicate.SpectrumAlert) {
	f.Where(entql.HasEdgeWith("spectrum_alert", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (sq *StrategyQuery) addPredicate(pred func(s *sql.Selector)) {
	sq.predicates = append(sq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the StrategyQuery builder.
func (sq *StrategyQuery) Filter() *StrategyFilter {
	return &StrategyFilter{config: sq.config, predicateAdder: sq}
}

// addPredicate implements the predicateAdder interface.
func (m *StrategyMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the StrategyMutation builder.
func (m *StrategyMutation) Filter() *StrategyFilter {
	return &StrategyFilter{config: m.config, predicateAdder: m}
}

// StrategyFilter provides a generic filtering capability at runtime for StrategyQuery.
type StrategyFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *StrategyFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[16].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *StrategyFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(strategy.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *StrategyFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(strategy.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *StrategyFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(strategy.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *StrategyFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(strategy.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *StrategyFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(strategy.FieldRemark))
}

// WhereName applies the entql string predicate on the name field.
func (f *StrategyFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(strategy.FieldName))
}

// WhereType applies the entql int predicate on the type field.
func (f *StrategyFilter) WhereType(p entql.IntP) {
	f.Where(p.Field(strategy.FieldType))
}

// WhereEnabled applies the entql bool predicate on the enabled field.
func (f *StrategyFilter) WhereEnabled(p entql.BoolP) {
	f.Where(p.Field(strategy.FieldEnabled))
}

// WhereSystem applies the entql bool predicate on the system field.
func (f *StrategyFilter) WhereSystem(p entql.BoolP) {
	f.Where(p.Field(strategy.FieldSystem))
}

// WhereBps applies the entql int64 predicate on the bps field.
func (f *StrategyFilter) WhereBps(p entql.Int64P) {
	f.Where(p.Field(strategy.FieldBps))
}

// WherePps applies the entql int64 predicate on the pps field.
func (f *StrategyFilter) WherePps(p entql.Int64P) {
	f.Where(p.Field(strategy.FieldPps))
}

// WhereBpsCount applies the entql int predicate on the bps_count field.
func (f *StrategyFilter) WhereBpsCount(p entql.IntP) {
	f.Where(p.Field(strategy.FieldBpsCount))
}

// WherePpsCount applies the entql int predicate on the pps_count field.
func (f *StrategyFilter) WherePpsCount(p entql.IntP) {
	f.Where(p.Field(strategy.FieldPpsCount))
}

// WhereIspCode applies the entql int predicate on the isp_code field.
func (f *StrategyFilter) WhereIspCode(p entql.IntP) {
	f.Where(p.Field(strategy.FieldIspCode))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *StrategyFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *StrategyFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasStrategyAlerts applies a predicate to check if query has an edge strategy_alerts.
func (f *StrategyFilter) WhereHasStrategyAlerts() {
	f.Where(entql.HasEdge("strategy_alerts"))
}

// WhereHasStrategyAlertsWith applies a predicate to check if query has an edge strategy_alerts with a given conditions (other predicates).
func (f *StrategyFilter) WhereHasStrategyAlertsWith(preds ...predicate.SpectrumAlert) {
	f.Where(entql.HasEdgeWith("strategy_alerts", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (saq *SystemApiQuery) addPredicate(pred func(s *sql.Selector)) {
	saq.predicates = append(saq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the SystemApiQuery builder.
func (saq *SystemApiQuery) Filter() *SystemApiFilter {
	return &SystemApiFilter{config: saq.config, predicateAdder: saq}
}

// addPredicate implements the predicateAdder interface.
func (m *SystemApiMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the SystemApiMutation builder.
func (m *SystemApiMutation) Filter() *SystemApiFilter {
	return &SystemApiFilter{config: m.config, predicateAdder: m}
}

// SystemApiFilter provides a generic filtering capability at runtime for SystemApiQuery.
type SystemApiFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *SystemApiFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[17].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *SystemApiFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(systemapi.FieldID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *SystemApiFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(systemapi.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *SystemApiFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(systemapi.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *SystemApiFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(systemapi.FieldRemark))
}

// WhereName applies the entql string predicate on the name field.
func (f *SystemApiFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(systemapi.FieldName))
}

// WherePath applies the entql string predicate on the path field.
func (f *SystemApiFilter) WherePath(p entql.StringP) {
	f.Where(p.Field(systemapi.FieldPath))
}

// WhereHTTPMethod applies the entql string predicate on the http_method field.
func (f *SystemApiFilter) WhereHTTPMethod(p entql.StringP) {
	f.Where(p.Field(systemapi.FieldHTTPMethod))
}

// WhereRoles applies the entql json.RawMessage predicate on the roles field.
func (f *SystemApiFilter) WhereRoles(p entql.BytesP) {
	f.Where(p.Field(systemapi.FieldRoles))
}

// WherePublic applies the entql bool predicate on the public field.
func (f *SystemApiFilter) WherePublic(p entql.BoolP) {
	f.Where(p.Field(systemapi.FieldPublic))
}

// WhereSa applies the entql bool predicate on the sa field.
func (f *SystemApiFilter) WhereSa(p entql.BoolP) {
	f.Where(p.Field(systemapi.FieldSa))
}

// addPredicate implements the predicateAdder interface.
func (scq *SystemConfigQuery) addPredicate(pred func(s *sql.Selector)) {
	scq.predicates = append(scq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the SystemConfigQuery builder.
func (scq *SystemConfigQuery) Filter() *SystemConfigFilter {
	return &SystemConfigFilter{config: scq.config, predicateAdder: scq}
}

// addPredicate implements the predicateAdder interface.
func (m *SystemConfigMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the SystemConfigMutation builder.
func (m *SystemConfigMutation) Filter() *SystemConfigFilter {
	return &SystemConfigFilter{config: m.config, predicateAdder: m}
}

// SystemConfigFilter provides a generic filtering capability at runtime for SystemConfigQuery.
type SystemConfigFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *SystemConfigFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[18].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *SystemConfigFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(systemconfig.FieldID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *SystemConfigFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(systemconfig.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *SystemConfigFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(systemconfig.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *SystemConfigFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(systemconfig.FieldRemark))
}

// WhereWofangTestIP applies the entql string predicate on the wofang_test_ip field.
func (f *SystemConfigFilter) WhereWofangTestIP(p entql.StringP) {
	f.Where(p.Field(systemconfig.FieldWofangTestIP))
}

// WhereNotifyPhones applies the entql json.RawMessage predicate on the notify_phones field.
func (f *SystemConfigFilter) WhereNotifyPhones(p entql.BytesP) {
	f.Where(p.Field(systemconfig.FieldNotifyPhones))
}

// WhereNotifyEmails applies the entql json.RawMessage predicate on the notify_emails field.
func (f *SystemConfigFilter) WhereNotifyEmails(p entql.BytesP) {
	f.Where(p.Field(systemconfig.FieldNotifyEmails))
}

// WhereNotifyScenes applies the entql json.RawMessage predicate on the notify_scenes field.
func (f *SystemConfigFilter) WhereNotifyScenes(p entql.BytesP) {
	f.Where(p.Field(systemconfig.FieldNotifyScenes))
}

// WhereIPWhitelists applies the entql json.RawMessage predicate on the ip_whitelists field.
func (f *SystemConfigFilter) WhereIPWhitelists(p entql.BytesP) {
	f.Where(p.Field(systemconfig.FieldIPWhitelists))
}

// addPredicate implements the predicateAdder interface.
func (tq *TenantQuery) addPredicate(pred func(s *sql.Selector)) {
	tq.predicates = append(tq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the TenantQuery builder.
func (tq *TenantQuery) Filter() *TenantFilter {
	return &TenantFilter{config: tq.config, predicateAdder: tq}
}

// addPredicate implements the predicateAdder interface.
func (m *TenantMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the TenantMutation builder.
func (m *TenantMutation) Filter() *TenantFilter {
	return &TenantFilter{config: m.config, predicateAdder: m}
}

// TenantFilter provides a generic filtering capability at runtime for TenantQuery.
type TenantFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *TenantFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[19].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *TenantFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(tenant.FieldID))
}

// WhereName applies the entql string predicate on the name field.
func (f *TenantFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(tenant.FieldName))
}

// WhereCode applies the entql string predicate on the code field.
func (f *TenantFilter) WhereCode(p entql.StringP) {
	f.Where(p.Field(tenant.FieldCode))
}

// WhereOffline applies the entql bool predicate on the offline field.
func (f *TenantFilter) WhereOffline(p entql.BoolP) {
	f.Where(p.Field(tenant.FieldOffline))
}

// WhereIsdefend applies the entql bool predicate on the isdefend field.
func (f *TenantFilter) WhereIsdefend(p entql.BoolP) {
	f.Where(p.Field(tenant.FieldIsdefend))
}

// addPredicate implements the predicateAdder interface.
func (uq *UserQuery) addPredicate(pred func(s *sql.Selector)) {
	uq.predicates = append(uq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the UserQuery builder.
func (uq *UserQuery) Filter() *UserFilter {
	return &UserFilter{config: uq.config, predicateAdder: uq}
}

// addPredicate implements the predicateAdder interface.
func (m *UserMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the UserMutation builder.
func (m *UserMutation) Filter() *UserFilter {
	return &UserFilter{config: m.config, predicateAdder: m}
}

// UserFilter provides a generic filtering capability at runtime for UserQuery.
type UserFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *UserFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[20].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *UserFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(user.FieldID))
}

// WhereValid applies the entql bool predicate on the valid field.
func (f *UserFilter) WhereValid(p entql.BoolP) {
	f.Where(p.Field(user.FieldValid))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *UserFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(user.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *UserFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(user.FieldUpdatedAt))
}

// WhereName applies the entql string predicate on the name field.
func (f *UserFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(user.FieldName))
}

// WherePassword applies the entql string predicate on the password field.
func (f *UserFilter) WherePassword(p entql.StringP) {
	f.Where(p.Field(user.FieldPassword))
}

// WhereSuperAdmin applies the entql bool predicate on the super_admin field.
func (f *UserFilter) WhereSuperAdmin(p entql.BoolP) {
	f.Where(p.Field(user.FieldSuperAdmin))
}

// WhereUpdateAuth applies the entql bool predicate on the update_auth field.
func (f *UserFilter) WhereUpdateAuth(p entql.BoolP) {
	f.Where(p.Field(user.FieldUpdateAuth))
}

// WhereHasGroups applies a predicate to check if query has an edge groups.
func (f *UserFilter) WhereHasGroups() {
	f.Where(entql.HasEdge("groups"))
}

// WhereHasGroupsWith applies a predicate to check if query has an edge groups with a given conditions (other predicates).
func (f *UserFilter) WhereHasGroupsWith(preds ...predicate.Group) {
	f.Where(entql.HasEdgeWith("groups", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (uolq *UserOperationLogQuery) addPredicate(pred func(s *sql.Selector)) {
	uolq.predicates = append(uolq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the UserOperationLogQuery builder.
func (uolq *UserOperationLogQuery) Filter() *UserOperationLogFilter {
	return &UserOperationLogFilter{config: uolq.config, predicateAdder: uolq}
}

// addPredicate implements the predicateAdder interface.
func (m *UserOperationLogMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the UserOperationLogMutation builder.
func (m *UserOperationLogMutation) Filter() *UserOperationLogFilter {
	return &UserOperationLogFilter{config: m.config, predicateAdder: m}
}

// UserOperationLogFilter provides a generic filtering capability at runtime for UserOperationLogQuery.
type UserOperationLogFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *UserOperationLogFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[21].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *UserOperationLogFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(useroperationlog.FieldID))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *UserOperationLogFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(useroperationlog.FieldRemark))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *UserOperationLogFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(useroperationlog.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *UserOperationLogFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(useroperationlog.FieldUpdatedAt))
}

// WhereUsername applies the entql string predicate on the username field.
func (f *UserOperationLogFilter) WhereUsername(p entql.StringP) {
	f.Where(p.Field(useroperationlog.FieldUsername))
}

// WhereMethod applies the entql string predicate on the method field.
func (f *UserOperationLogFilter) WhereMethod(p entql.StringP) {
	f.Where(p.Field(useroperationlog.FieldMethod))
}

// WhereRequestID applies the entql string predicate on the request_id field.
func (f *UserOperationLogFilter) WhereRequestID(p entql.StringP) {
	f.Where(p.Field(useroperationlog.FieldRequestID))
}

// WhereURI applies the entql string predicate on the uri field.
func (f *UserOperationLogFilter) WhereURI(p entql.StringP) {
	f.Where(p.Field(useroperationlog.FieldURI))
}

// WhereRequestBody applies the entql string predicate on the request_body field.
func (f *UserOperationLogFilter) WhereRequestBody(p entql.StringP) {
	f.Where(p.Field(useroperationlog.FieldRequestBody))
}

// WhereProject applies the entql string predicate on the project field.
func (f *UserOperationLogFilter) WhereProject(p entql.StringP) {
	f.Where(p.Field(useroperationlog.FieldProject))
}

// addPredicate implements the predicateAdder interface.
func (wq *WofangQuery) addPredicate(pred func(s *sql.Selector)) {
	wq.predicates = append(wq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the WofangQuery builder.
func (wq *WofangQuery) Filter() *WofangFilter {
	return &WofangFilter{config: wq.config, predicateAdder: wq}
}

// addPredicate implements the predicateAdder interface.
func (m *WofangMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the WofangMutation builder.
func (m *WofangMutation) Filter() *WofangFilter {
	return &WofangFilter{config: m.config, predicateAdder: m}
}

// WofangFilter provides a generic filtering capability at runtime for WofangQuery.
type WofangFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *WofangFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[22].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *WofangFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(wofang.FieldID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *WofangFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(wofang.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *WofangFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(wofang.FieldUpdatedAt))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *WofangFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(wofang.FieldTenantID))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *WofangFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(wofang.FieldRemark))
}

// WhereName applies the entql string predicate on the name field.
func (f *WofangFilter) WhereName(p entql.StringP) {
	f.Where(p.Field(wofang.FieldName))
}

// WhereIP applies the entql string predicate on the ip field.
func (f *WofangFilter) WhereIP(p entql.StringP) {
	f.Where(p.Field(wofang.FieldIP))
}

// WhereType applies the entql string predicate on the type field.
func (f *WofangFilter) WhereType(p entql.StringP) {
	f.Where(p.Field(wofang.FieldType))
}

// WhereUnDragSecond applies the entql int predicate on the un_drag_second field.
func (f *WofangFilter) WhereUnDragSecond(p entql.IntP) {
	f.Where(p.Field(wofang.FieldUnDragSecond))
}

// WhereStartTime applies the entql time.Time predicate on the start_time field.
func (f *WofangFilter) WhereStartTime(p entql.TimeP) {
	f.Where(p.Field(wofang.FieldStartTime))
}

// WhereErrorInfo applies the entql string predicate on the error_info field.
func (f *WofangFilter) WhereErrorInfo(p entql.StringP) {
	f.Where(p.Field(wofang.FieldErrorInfo))
}

// WhereStatus applies the entql string predicate on the status field.
func (f *WofangFilter) WhereStatus(p entql.StringP) {
	f.Where(p.Field(wofang.FieldStatus))
}

// WhereCreateUserID applies the entql int predicate on the create_user_id field.
func (f *WofangFilter) WhereCreateUserID(p entql.IntP) {
	f.Where(p.Field(wofang.FieldCreateUserID))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *WofangFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *WofangFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasUser applies a predicate to check if query has an edge user.
func (f *WofangFilter) WhereHasUser() {
	f.Where(entql.HasEdge("user"))
}

// WhereHasUserWith applies a predicate to check if query has an edge user with a given conditions (other predicates).
func (f *WofangFilter) WhereHasUserWith(preds ...predicate.User) {
	f.Where(entql.HasEdgeWith("user", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasSpectrumAlerts applies a predicate to check if query has an edge spectrum_alerts.
func (f *WofangFilter) WhereHasSpectrumAlerts() {
	f.Where(entql.HasEdge("spectrum_alerts"))
}

// WhereHasSpectrumAlertsWith applies a predicate to check if query has an edge spectrum_alerts with a given conditions (other predicates).
func (f *WofangFilter) WhereHasSpectrumAlertsWith(preds ...predicate.SpectrumAlert) {
	f.Where(entql.HasEdgeWith("spectrum_alerts", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// WhereHasMatrixSpectrumAlerts applies a predicate to check if query has an edge matrix_spectrum_alerts.
func (f *WofangFilter) WhereHasMatrixSpectrumAlerts() {
	f.Where(entql.HasEdge("matrix_spectrum_alerts"))
}

// WhereHasMatrixSpectrumAlertsWith applies a predicate to check if query has an edge matrix_spectrum_alerts with a given conditions (other predicates).
func (f *WofangFilter) WhereHasMatrixSpectrumAlertsWith(preds ...predicate.MatrixSpectrumAlert) {
	f.Where(entql.HasEdgeWith("matrix_spectrum_alerts", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}

// addPredicate implements the predicateAdder interface.
func (waq *WofangAlertQuery) addPredicate(pred func(s *sql.Selector)) {
	waq.predicates = append(waq.predicates, pred)
}

// Filter returns a Filter implementation to apply filters on the WofangAlertQuery builder.
func (waq *WofangAlertQuery) Filter() *WofangAlertFilter {
	return &WofangAlertFilter{config: waq.config, predicateAdder: waq}
}

// addPredicate implements the predicateAdder interface.
func (m *WofangAlertMutation) addPredicate(pred func(s *sql.Selector)) {
	m.predicates = append(m.predicates, pred)
}

// Filter returns an entql.Where implementation to apply filters on the WofangAlertMutation builder.
func (m *WofangAlertMutation) Filter() *WofangAlertFilter {
	return &WofangAlertFilter{config: m.config, predicateAdder: m}
}

// WofangAlertFilter provides a generic filtering capability at runtime for WofangAlertQuery.
type WofangAlertFilter struct {
	predicateAdder
	config
}

// Where applies the entql predicate on the query filter.
func (f *WofangAlertFilter) Where(p entql.P) {
	f.addPredicate(func(s *sql.Selector) {
		if err := schemaGraph.EvalP(schemaGraph.Nodes[23].Type, p, s); err != nil {
			s.AddError(err)
		}
	})
}

// WhereID applies the entql int predicate on the id field.
func (f *WofangAlertFilter) WhereID(p entql.IntP) {
	f.Where(p.Field(wofangalert.FieldID))
}

// WhereTenantID applies the entql int predicate on the tenant_id field.
func (f *WofangAlertFilter) WhereTenantID(p entql.IntP) {
	f.Where(p.Field(wofangalert.FieldTenantID))
}

// WhereCreatedAt applies the entql time.Time predicate on the created_at field.
func (f *WofangAlertFilter) WhereCreatedAt(p entql.TimeP) {
	f.Where(p.Field(wofangalert.FieldCreatedAt))
}

// WhereUpdatedAt applies the entql time.Time predicate on the updated_at field.
func (f *WofangAlertFilter) WhereUpdatedAt(p entql.TimeP) {
	f.Where(p.Field(wofangalert.FieldUpdatedAt))
}

// WhereRemark applies the entql string predicate on the remark field.
func (f *WofangAlertFilter) WhereRemark(p entql.StringP) {
	f.Where(p.Field(wofangalert.FieldRemark))
}

// WhereAttackStatus applies the entql int predicate on the attack_status field.
func (f *WofangAlertFilter) WhereAttackStatus(p entql.IntP) {
	f.Where(p.Field(wofangalert.FieldAttackStatus))
}

// WhereAttackType applies the entql json.RawMessage predicate on the attack_type field.
func (f *WofangAlertFilter) WhereAttackType(p entql.BytesP) {
	f.Where(p.Field(wofangalert.FieldAttackType))
}

// WhereDeviceIP applies the entql string predicate on the device_ip field.
func (f *WofangAlertFilter) WhereDeviceIP(p entql.StringP) {
	f.Where(p.Field(wofangalert.FieldDeviceIP))
}

// WhereZoneIP applies the entql string predicate on the zone_ip field.
func (f *WofangAlertFilter) WhereZoneIP(p entql.StringP) {
	f.Where(p.Field(wofangalert.FieldZoneIP))
}

// WhereAttackID applies the entql int predicate on the attack_id field.
func (f *WofangAlertFilter) WhereAttackID(p entql.IntP) {
	f.Where(p.Field(wofangalert.FieldAttackID))
}

// WhereStartTime applies the entql time.Time predicate on the start_time field.
func (f *WofangAlertFilter) WhereStartTime(p entql.TimeP) {
	f.Where(p.Field(wofangalert.FieldStartTime))
}

// WhereEndTime applies the entql time.Time predicate on the end_time field.
func (f *WofangAlertFilter) WhereEndTime(p entql.TimeP) {
	f.Where(p.Field(wofangalert.FieldEndTime))
}

// WhereMaxDropBps applies the entql int64 predicate on the max_drop_bps field.
func (f *WofangAlertFilter) WhereMaxDropBps(p entql.Int64P) {
	f.Where(p.Field(wofangalert.FieldMaxDropBps))
}

// WhereMaxInBps applies the entql int64 predicate on the max_in_bps field.
func (f *WofangAlertFilter) WhereMaxInBps(p entql.Int64P) {
	f.Where(p.Field(wofangalert.FieldMaxInBps))
}

// WhereHasTenant applies a predicate to check if query has an edge tenant.
func (f *WofangAlertFilter) WhereHasTenant() {
	f.Where(entql.HasEdge("tenant"))
}

// WhereHasTenantWith applies a predicate to check if query has an edge tenant with a given conditions (other predicates).
func (f *WofangAlertFilter) WhereHasTenantWith(preds ...predicate.Tenant) {
	f.Where(entql.HasEdgeWith("tenant", sqlgraph.WrapFunc(func(s *sql.Selector) {
		for _, p := range preds {
			p(s)
		}
	})))
}
