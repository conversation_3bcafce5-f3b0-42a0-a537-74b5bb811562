// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudAlertUpdate is the builder for updating CloudAlert entities.
type CloudAlertUpdate struct {
	config
	hooks    []Hook
	mutation *CloudAlertMutation
}

// Where appends a list predicates to the CloudAlertUpdate builder.
func (cau *CloudAlertUpdate) Where(ps ...predicate.CloudAlert) *CloudAlertUpdate {
	cau.mutation.Where(ps...)
	return cau
}

// SetTenantID sets the "tenant_id" field.
func (cau *CloudAlertUpdate) SetTenantID(i int) *CloudAlertUpdate {
	cau.mutation.SetTenantID(i)
	return cau
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableTenantID(i *int) *CloudAlertUpdate {
	if i != nil {
		cau.SetTenantID(*i)
	}
	return cau
}

// ClearTenantID clears the value of the "tenant_id" field.
func (cau *CloudAlertUpdate) ClearTenantID() *CloudAlertUpdate {
	cau.mutation.ClearTenantID()
	return cau
}

// SetUpdatedAt sets the "updated_at" field.
func (cau *CloudAlertUpdate) SetUpdatedAt(t time.Time) *CloudAlertUpdate {
	cau.mutation.SetUpdatedAt(t)
	return cau
}

// SetRemark sets the "remark" field.
func (cau *CloudAlertUpdate) SetRemark(s string) *CloudAlertUpdate {
	cau.mutation.SetRemark(s)
	return cau
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableRemark(s *string) *CloudAlertUpdate {
	if s != nil {
		cau.SetRemark(*s)
	}
	return cau
}

// ClearRemark clears the value of the "remark" field.
func (cau *CloudAlertUpdate) ClearRemark() *CloudAlertUpdate {
	cau.mutation.ClearRemark()
	return cau
}

// SetSrcIP sets the "src_ip" field.
func (cau *CloudAlertUpdate) SetSrcIP(s string) *CloudAlertUpdate {
	cau.mutation.SetSrcIP(s)
	return cau
}

// SetNillableSrcIP sets the "src_ip" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableSrcIP(s *string) *CloudAlertUpdate {
	if s != nil {
		cau.SetSrcIP(*s)
	}
	return cau
}

// SetSrcPort sets the "src_port" field.
func (cau *CloudAlertUpdate) SetSrcPort(i int) *CloudAlertUpdate {
	cau.mutation.ResetSrcPort()
	cau.mutation.SetSrcPort(i)
	return cau
}

// SetNillableSrcPort sets the "src_port" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableSrcPort(i *int) *CloudAlertUpdate {
	if i != nil {
		cau.SetSrcPort(*i)
	}
	return cau
}

// AddSrcPort adds i to the "src_port" field.
func (cau *CloudAlertUpdate) AddSrcPort(i int) *CloudAlertUpdate {
	cau.mutation.AddSrcPort(i)
	return cau
}

// SetDstIP sets the "dst_ip" field.
func (cau *CloudAlertUpdate) SetDstIP(s string) *CloudAlertUpdate {
	cau.mutation.SetDstIP(s)
	return cau
}

// SetNillableDstIP sets the "dst_ip" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableDstIP(s *string) *CloudAlertUpdate {
	if s != nil {
		cau.SetDstIP(*s)
	}
	return cau
}

// SetDstPort sets the "dst_port" field.
func (cau *CloudAlertUpdate) SetDstPort(i int) *CloudAlertUpdate {
	cau.mutation.ResetDstPort()
	cau.mutation.SetDstPort(i)
	return cau
}

// SetNillableDstPort sets the "dst_port" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableDstPort(i *int) *CloudAlertUpdate {
	if i != nil {
		cau.SetDstPort(*i)
	}
	return cau
}

// AddDstPort adds i to the "dst_port" field.
func (cau *CloudAlertUpdate) AddDstPort(i int) *CloudAlertUpdate {
	cau.mutation.AddDstPort(i)
	return cau
}

// SetDefenceMode sets the "defence_mode" field.
func (cau *CloudAlertUpdate) SetDefenceMode(i int) *CloudAlertUpdate {
	cau.mutation.ResetDefenceMode()
	cau.mutation.SetDefenceMode(i)
	return cau
}

// SetNillableDefenceMode sets the "defence_mode" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableDefenceMode(i *int) *CloudAlertUpdate {
	if i != nil {
		cau.SetDefenceMode(*i)
	}
	return cau
}

// AddDefenceMode adds i to the "defence_mode" field.
func (cau *CloudAlertUpdate) AddDefenceMode(i int) *CloudAlertUpdate {
	cau.mutation.AddDefenceMode(i)
	return cau
}

// SetFlowMode sets the "flow_mode" field.
func (cau *CloudAlertUpdate) SetFlowMode(i int) *CloudAlertUpdate {
	cau.mutation.ResetFlowMode()
	cau.mutation.SetFlowMode(i)
	return cau
}

// SetNillableFlowMode sets the "flow_mode" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableFlowMode(i *int) *CloudAlertUpdate {
	if i != nil {
		cau.SetFlowMode(*i)
	}
	return cau
}

// AddFlowMode adds i to the "flow_mode" field.
func (cau *CloudAlertUpdate) AddFlowMode(i int) *CloudAlertUpdate {
	cau.mutation.AddFlowMode(i)
	return cau
}

// SetTCPAckNum sets the "tcp_ack_num" field.
func (cau *CloudAlertUpdate) SetTCPAckNum(s string) *CloudAlertUpdate {
	cau.mutation.SetTCPAckNum(s)
	return cau
}

// SetNillableTCPAckNum sets the "tcp_ack_num" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableTCPAckNum(s *string) *CloudAlertUpdate {
	if s != nil {
		cau.SetTCPAckNum(*s)
	}
	return cau
}

// SetTCPSeqNum sets the "tcp_seq_num" field.
func (cau *CloudAlertUpdate) SetTCPSeqNum(s string) *CloudAlertUpdate {
	cau.mutation.SetTCPSeqNum(s)
	return cau
}

// SetNillableTCPSeqNum sets the "tcp_seq_num" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableTCPSeqNum(s *string) *CloudAlertUpdate {
	if s != nil {
		cau.SetTCPSeqNum(*s)
	}
	return cau
}

// SetProtocol sets the "protocol" field.
func (cau *CloudAlertUpdate) SetProtocol(i int) *CloudAlertUpdate {
	cau.mutation.ResetProtocol()
	cau.mutation.SetProtocol(i)
	return cau
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableProtocol(i *int) *CloudAlertUpdate {
	if i != nil {
		cau.SetProtocol(*i)
	}
	return cau
}

// AddProtocol adds i to the "protocol" field.
func (cau *CloudAlertUpdate) AddProtocol(i int) *CloudAlertUpdate {
	cau.mutation.AddProtocol(i)
	return cau
}

// SetDefenceLevel sets the "defence_level" field.
func (cau *CloudAlertUpdate) SetDefenceLevel(i int) *CloudAlertUpdate {
	cau.mutation.ResetDefenceLevel()
	cau.mutation.SetDefenceLevel(i)
	return cau
}

// SetNillableDefenceLevel sets the "defence_level" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableDefenceLevel(i *int) *CloudAlertUpdate {
	if i != nil {
		cau.SetDefenceLevel(*i)
	}
	return cau
}

// AddDefenceLevel adds i to the "defence_level" field.
func (cau *CloudAlertUpdate) AddDefenceLevel(i int) *CloudAlertUpdate {
	cau.mutation.AddDefenceLevel(i)
	return cau
}

// SetMaxPps sets the "max_pps" field.
func (cau *CloudAlertUpdate) SetMaxPps(i int64) *CloudAlertUpdate {
	cau.mutation.ResetMaxPps()
	cau.mutation.SetMaxPps(i)
	return cau
}

// SetNillableMaxPps sets the "max_pps" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableMaxPps(i *int64) *CloudAlertUpdate {
	if i != nil {
		cau.SetMaxPps(*i)
	}
	return cau
}

// AddMaxPps adds i to the "max_pps" field.
func (cau *CloudAlertUpdate) AddMaxPps(i int64) *CloudAlertUpdate {
	cau.mutation.AddMaxPps(i)
	return cau
}

// SetMaxAttackPps sets the "max_attack_pps" field.
func (cau *CloudAlertUpdate) SetMaxAttackPps(i int64) *CloudAlertUpdate {
	cau.mutation.ResetMaxAttackPps()
	cau.mutation.SetMaxAttackPps(i)
	return cau
}

// SetNillableMaxAttackPps sets the "max_attack_pps" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableMaxAttackPps(i *int64) *CloudAlertUpdate {
	if i != nil {
		cau.SetMaxAttackPps(*i)
	}
	return cau
}

// AddMaxAttackPps adds i to the "max_attack_pps" field.
func (cau *CloudAlertUpdate) AddMaxAttackPps(i int64) *CloudAlertUpdate {
	cau.mutation.AddMaxAttackPps(i)
	return cau
}

// SetOverlimitPktCount sets the "overlimit_pkt_count" field.
func (cau *CloudAlertUpdate) SetOverlimitPktCount(i int) *CloudAlertUpdate {
	cau.mutation.ResetOverlimitPktCount()
	cau.mutation.SetOverlimitPktCount(i)
	return cau
}

// SetNillableOverlimitPktCount sets the "overlimit_pkt_count" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableOverlimitPktCount(i *int) *CloudAlertUpdate {
	if i != nil {
		cau.SetOverlimitPktCount(*i)
	}
	return cau
}

// AddOverlimitPktCount adds i to the "overlimit_pkt_count" field.
func (cau *CloudAlertUpdate) AddOverlimitPktCount(i int) *CloudAlertUpdate {
	cau.mutation.AddOverlimitPktCount(i)
	return cau
}

// SetStartTime sets the "start_time" field.
func (cau *CloudAlertUpdate) SetStartTime(t time.Time) *CloudAlertUpdate {
	cau.mutation.SetStartTime(t)
	return cau
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableStartTime(t *time.Time) *CloudAlertUpdate {
	if t != nil {
		cau.SetStartTime(*t)
	}
	return cau
}

// SetEndTime sets the "end_time" field.
func (cau *CloudAlertUpdate) SetEndTime(t time.Time) *CloudAlertUpdate {
	cau.mutation.SetEndTime(t)
	return cau
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (cau *CloudAlertUpdate) SetNillableEndTime(t *time.Time) *CloudAlertUpdate {
	if t != nil {
		cau.SetEndTime(*t)
	}
	return cau
}

// ClearEndTime clears the value of the "end_time" field.
func (cau *CloudAlertUpdate) ClearEndTime() *CloudAlertUpdate {
	cau.mutation.ClearEndTime()
	return cau
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cau *CloudAlertUpdate) SetTenant(t *Tenant) *CloudAlertUpdate {
	return cau.SetTenantID(t.ID)
}

// AddCloudflowDataIDs adds the "cloudflow_datas" edge to the CloudFlowData entity by IDs.
func (cau *CloudAlertUpdate) AddCloudflowDataIDs(ids ...int) *CloudAlertUpdate {
	cau.mutation.AddCloudflowDataIDs(ids...)
	return cau
}

// AddCloudflowDatas adds the "cloudflow_datas" edges to the CloudFlowData entity.
func (cau *CloudAlertUpdate) AddCloudflowDatas(c ...*CloudFlowData) *CloudAlertUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cau.AddCloudflowDataIDs(ids...)
}

// Mutation returns the CloudAlertMutation object of the builder.
func (cau *CloudAlertUpdate) Mutation() *CloudAlertMutation {
	return cau.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (cau *CloudAlertUpdate) ClearTenant() *CloudAlertUpdate {
	cau.mutation.ClearTenant()
	return cau
}

// ClearCloudflowDatas clears all "cloudflow_datas" edges to the CloudFlowData entity.
func (cau *CloudAlertUpdate) ClearCloudflowDatas() *CloudAlertUpdate {
	cau.mutation.ClearCloudflowDatas()
	return cau
}

// RemoveCloudflowDataIDs removes the "cloudflow_datas" edge to CloudFlowData entities by IDs.
func (cau *CloudAlertUpdate) RemoveCloudflowDataIDs(ids ...int) *CloudAlertUpdate {
	cau.mutation.RemoveCloudflowDataIDs(ids...)
	return cau
}

// RemoveCloudflowDatas removes "cloudflow_datas" edges to CloudFlowData entities.
func (cau *CloudAlertUpdate) RemoveCloudflowDatas(c ...*CloudFlowData) *CloudAlertUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cau.RemoveCloudflowDataIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cau *CloudAlertUpdate) Save(ctx context.Context) (int, error) {
	if err := cau.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, cau.sqlSave, cau.mutation, cau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cau *CloudAlertUpdate) SaveX(ctx context.Context) int {
	affected, err := cau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cau *CloudAlertUpdate) Exec(ctx context.Context) error {
	_, err := cau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cau *CloudAlertUpdate) ExecX(ctx context.Context) {
	if err := cau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cau *CloudAlertUpdate) defaults() error {
	if _, ok := cau.mutation.UpdatedAt(); !ok {
		if cloudalert.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudalert.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudalert.UpdateDefaultUpdatedAt()
		cau.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cau *CloudAlertUpdate) check() error {
	if v, ok := cau.mutation.Remark(); ok {
		if err := cloudalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudAlert.remark": %w`, err)}
		}
	}
	return nil
}

func (cau *CloudAlertUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := cau.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(cloudalert.Table, cloudalert.Columns, sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt))
	if ps := cau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cau.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudalert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := cau.mutation.Remark(); ok {
		_spec.SetField(cloudalert.FieldRemark, field.TypeString, value)
	}
	if cau.mutation.RemarkCleared() {
		_spec.ClearField(cloudalert.FieldRemark, field.TypeString)
	}
	if value, ok := cau.mutation.SrcIP(); ok {
		_spec.SetField(cloudalert.FieldSrcIP, field.TypeString, value)
	}
	if value, ok := cau.mutation.SrcPort(); ok {
		_spec.SetField(cloudalert.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cau.mutation.AddedSrcPort(); ok {
		_spec.AddField(cloudalert.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cau.mutation.DstIP(); ok {
		_spec.SetField(cloudalert.FieldDstIP, field.TypeString, value)
	}
	if value, ok := cau.mutation.DstPort(); ok {
		_spec.SetField(cloudalert.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cau.mutation.AddedDstPort(); ok {
		_spec.AddField(cloudalert.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cau.mutation.DefenceMode(); ok {
		_spec.SetField(cloudalert.FieldDefenceMode, field.TypeInt, value)
	}
	if value, ok := cau.mutation.AddedDefenceMode(); ok {
		_spec.AddField(cloudalert.FieldDefenceMode, field.TypeInt, value)
	}
	if value, ok := cau.mutation.FlowMode(); ok {
		_spec.SetField(cloudalert.FieldFlowMode, field.TypeInt, value)
	}
	if value, ok := cau.mutation.AddedFlowMode(); ok {
		_spec.AddField(cloudalert.FieldFlowMode, field.TypeInt, value)
	}
	if value, ok := cau.mutation.TCPAckNum(); ok {
		_spec.SetField(cloudalert.FieldTCPAckNum, field.TypeString, value)
	}
	if value, ok := cau.mutation.TCPSeqNum(); ok {
		_spec.SetField(cloudalert.FieldTCPSeqNum, field.TypeString, value)
	}
	if value, ok := cau.mutation.Protocol(); ok {
		_spec.SetField(cloudalert.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cau.mutation.AddedProtocol(); ok {
		_spec.AddField(cloudalert.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cau.mutation.DefenceLevel(); ok {
		_spec.SetField(cloudalert.FieldDefenceLevel, field.TypeInt, value)
	}
	if value, ok := cau.mutation.AddedDefenceLevel(); ok {
		_spec.AddField(cloudalert.FieldDefenceLevel, field.TypeInt, value)
	}
	if value, ok := cau.mutation.MaxPps(); ok {
		_spec.SetField(cloudalert.FieldMaxPps, field.TypeInt64, value)
	}
	if value, ok := cau.mutation.AddedMaxPps(); ok {
		_spec.AddField(cloudalert.FieldMaxPps, field.TypeInt64, value)
	}
	if value, ok := cau.mutation.MaxAttackPps(); ok {
		_spec.SetField(cloudalert.FieldMaxAttackPps, field.TypeInt64, value)
	}
	if value, ok := cau.mutation.AddedMaxAttackPps(); ok {
		_spec.AddField(cloudalert.FieldMaxAttackPps, field.TypeInt64, value)
	}
	if value, ok := cau.mutation.OverlimitPktCount(); ok {
		_spec.SetField(cloudalert.FieldOverlimitPktCount, field.TypeInt, value)
	}
	if value, ok := cau.mutation.AddedOverlimitPktCount(); ok {
		_spec.AddField(cloudalert.FieldOverlimitPktCount, field.TypeInt, value)
	}
	if value, ok := cau.mutation.StartTime(); ok {
		_spec.SetField(cloudalert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := cau.mutation.EndTime(); ok {
		_spec.SetField(cloudalert.FieldEndTime, field.TypeTime, value)
	}
	if cau.mutation.EndTimeCleared() {
		_spec.ClearField(cloudalert.FieldEndTime, field.TypeTime)
	}
	if cau.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudalert.TenantTable,
			Columns: []string{cloudalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cau.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudalert.TenantTable,
			Columns: []string{cloudalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if cau.mutation.CloudflowDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   cloudalert.CloudflowDatasTable,
			Columns: []string{cloudalert.CloudflowDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cau.mutation.RemovedCloudflowDatasIDs(); len(nodes) > 0 && !cau.mutation.CloudflowDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   cloudalert.CloudflowDatasTable,
			Columns: []string{cloudalert.CloudflowDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cau.mutation.CloudflowDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   cloudalert.CloudflowDatasTable,
			Columns: []string{cloudalert.CloudflowDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cloudalert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cau.mutation.done = true
	return n, nil
}

// CloudAlertUpdateOne is the builder for updating a single CloudAlert entity.
type CloudAlertUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CloudAlertMutation
}

// SetTenantID sets the "tenant_id" field.
func (cauo *CloudAlertUpdateOne) SetTenantID(i int) *CloudAlertUpdateOne {
	cauo.mutation.SetTenantID(i)
	return cauo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableTenantID(i *int) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetTenantID(*i)
	}
	return cauo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (cauo *CloudAlertUpdateOne) ClearTenantID() *CloudAlertUpdateOne {
	cauo.mutation.ClearTenantID()
	return cauo
}

// SetUpdatedAt sets the "updated_at" field.
func (cauo *CloudAlertUpdateOne) SetUpdatedAt(t time.Time) *CloudAlertUpdateOne {
	cauo.mutation.SetUpdatedAt(t)
	return cauo
}

// SetRemark sets the "remark" field.
func (cauo *CloudAlertUpdateOne) SetRemark(s string) *CloudAlertUpdateOne {
	cauo.mutation.SetRemark(s)
	return cauo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableRemark(s *string) *CloudAlertUpdateOne {
	if s != nil {
		cauo.SetRemark(*s)
	}
	return cauo
}

// ClearRemark clears the value of the "remark" field.
func (cauo *CloudAlertUpdateOne) ClearRemark() *CloudAlertUpdateOne {
	cauo.mutation.ClearRemark()
	return cauo
}

// SetSrcIP sets the "src_ip" field.
func (cauo *CloudAlertUpdateOne) SetSrcIP(s string) *CloudAlertUpdateOne {
	cauo.mutation.SetSrcIP(s)
	return cauo
}

// SetNillableSrcIP sets the "src_ip" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableSrcIP(s *string) *CloudAlertUpdateOne {
	if s != nil {
		cauo.SetSrcIP(*s)
	}
	return cauo
}

// SetSrcPort sets the "src_port" field.
func (cauo *CloudAlertUpdateOne) SetSrcPort(i int) *CloudAlertUpdateOne {
	cauo.mutation.ResetSrcPort()
	cauo.mutation.SetSrcPort(i)
	return cauo
}

// SetNillableSrcPort sets the "src_port" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableSrcPort(i *int) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetSrcPort(*i)
	}
	return cauo
}

// AddSrcPort adds i to the "src_port" field.
func (cauo *CloudAlertUpdateOne) AddSrcPort(i int) *CloudAlertUpdateOne {
	cauo.mutation.AddSrcPort(i)
	return cauo
}

// SetDstIP sets the "dst_ip" field.
func (cauo *CloudAlertUpdateOne) SetDstIP(s string) *CloudAlertUpdateOne {
	cauo.mutation.SetDstIP(s)
	return cauo
}

// SetNillableDstIP sets the "dst_ip" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableDstIP(s *string) *CloudAlertUpdateOne {
	if s != nil {
		cauo.SetDstIP(*s)
	}
	return cauo
}

// SetDstPort sets the "dst_port" field.
func (cauo *CloudAlertUpdateOne) SetDstPort(i int) *CloudAlertUpdateOne {
	cauo.mutation.ResetDstPort()
	cauo.mutation.SetDstPort(i)
	return cauo
}

// SetNillableDstPort sets the "dst_port" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableDstPort(i *int) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetDstPort(*i)
	}
	return cauo
}

// AddDstPort adds i to the "dst_port" field.
func (cauo *CloudAlertUpdateOne) AddDstPort(i int) *CloudAlertUpdateOne {
	cauo.mutation.AddDstPort(i)
	return cauo
}

// SetDefenceMode sets the "defence_mode" field.
func (cauo *CloudAlertUpdateOne) SetDefenceMode(i int) *CloudAlertUpdateOne {
	cauo.mutation.ResetDefenceMode()
	cauo.mutation.SetDefenceMode(i)
	return cauo
}

// SetNillableDefenceMode sets the "defence_mode" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableDefenceMode(i *int) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetDefenceMode(*i)
	}
	return cauo
}

// AddDefenceMode adds i to the "defence_mode" field.
func (cauo *CloudAlertUpdateOne) AddDefenceMode(i int) *CloudAlertUpdateOne {
	cauo.mutation.AddDefenceMode(i)
	return cauo
}

// SetFlowMode sets the "flow_mode" field.
func (cauo *CloudAlertUpdateOne) SetFlowMode(i int) *CloudAlertUpdateOne {
	cauo.mutation.ResetFlowMode()
	cauo.mutation.SetFlowMode(i)
	return cauo
}

// SetNillableFlowMode sets the "flow_mode" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableFlowMode(i *int) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetFlowMode(*i)
	}
	return cauo
}

// AddFlowMode adds i to the "flow_mode" field.
func (cauo *CloudAlertUpdateOne) AddFlowMode(i int) *CloudAlertUpdateOne {
	cauo.mutation.AddFlowMode(i)
	return cauo
}

// SetTCPAckNum sets the "tcp_ack_num" field.
func (cauo *CloudAlertUpdateOne) SetTCPAckNum(s string) *CloudAlertUpdateOne {
	cauo.mutation.SetTCPAckNum(s)
	return cauo
}

// SetNillableTCPAckNum sets the "tcp_ack_num" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableTCPAckNum(s *string) *CloudAlertUpdateOne {
	if s != nil {
		cauo.SetTCPAckNum(*s)
	}
	return cauo
}

// SetTCPSeqNum sets the "tcp_seq_num" field.
func (cauo *CloudAlertUpdateOne) SetTCPSeqNum(s string) *CloudAlertUpdateOne {
	cauo.mutation.SetTCPSeqNum(s)
	return cauo
}

// SetNillableTCPSeqNum sets the "tcp_seq_num" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableTCPSeqNum(s *string) *CloudAlertUpdateOne {
	if s != nil {
		cauo.SetTCPSeqNum(*s)
	}
	return cauo
}

// SetProtocol sets the "protocol" field.
func (cauo *CloudAlertUpdateOne) SetProtocol(i int) *CloudAlertUpdateOne {
	cauo.mutation.ResetProtocol()
	cauo.mutation.SetProtocol(i)
	return cauo
}

// SetNillableProtocol sets the "protocol" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableProtocol(i *int) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetProtocol(*i)
	}
	return cauo
}

// AddProtocol adds i to the "protocol" field.
func (cauo *CloudAlertUpdateOne) AddProtocol(i int) *CloudAlertUpdateOne {
	cauo.mutation.AddProtocol(i)
	return cauo
}

// SetDefenceLevel sets the "defence_level" field.
func (cauo *CloudAlertUpdateOne) SetDefenceLevel(i int) *CloudAlertUpdateOne {
	cauo.mutation.ResetDefenceLevel()
	cauo.mutation.SetDefenceLevel(i)
	return cauo
}

// SetNillableDefenceLevel sets the "defence_level" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableDefenceLevel(i *int) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetDefenceLevel(*i)
	}
	return cauo
}

// AddDefenceLevel adds i to the "defence_level" field.
func (cauo *CloudAlertUpdateOne) AddDefenceLevel(i int) *CloudAlertUpdateOne {
	cauo.mutation.AddDefenceLevel(i)
	return cauo
}

// SetMaxPps sets the "max_pps" field.
func (cauo *CloudAlertUpdateOne) SetMaxPps(i int64) *CloudAlertUpdateOne {
	cauo.mutation.ResetMaxPps()
	cauo.mutation.SetMaxPps(i)
	return cauo
}

// SetNillableMaxPps sets the "max_pps" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableMaxPps(i *int64) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetMaxPps(*i)
	}
	return cauo
}

// AddMaxPps adds i to the "max_pps" field.
func (cauo *CloudAlertUpdateOne) AddMaxPps(i int64) *CloudAlertUpdateOne {
	cauo.mutation.AddMaxPps(i)
	return cauo
}

// SetMaxAttackPps sets the "max_attack_pps" field.
func (cauo *CloudAlertUpdateOne) SetMaxAttackPps(i int64) *CloudAlertUpdateOne {
	cauo.mutation.ResetMaxAttackPps()
	cauo.mutation.SetMaxAttackPps(i)
	return cauo
}

// SetNillableMaxAttackPps sets the "max_attack_pps" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableMaxAttackPps(i *int64) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetMaxAttackPps(*i)
	}
	return cauo
}

// AddMaxAttackPps adds i to the "max_attack_pps" field.
func (cauo *CloudAlertUpdateOne) AddMaxAttackPps(i int64) *CloudAlertUpdateOne {
	cauo.mutation.AddMaxAttackPps(i)
	return cauo
}

// SetOverlimitPktCount sets the "overlimit_pkt_count" field.
func (cauo *CloudAlertUpdateOne) SetOverlimitPktCount(i int) *CloudAlertUpdateOne {
	cauo.mutation.ResetOverlimitPktCount()
	cauo.mutation.SetOverlimitPktCount(i)
	return cauo
}

// SetNillableOverlimitPktCount sets the "overlimit_pkt_count" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableOverlimitPktCount(i *int) *CloudAlertUpdateOne {
	if i != nil {
		cauo.SetOverlimitPktCount(*i)
	}
	return cauo
}

// AddOverlimitPktCount adds i to the "overlimit_pkt_count" field.
func (cauo *CloudAlertUpdateOne) AddOverlimitPktCount(i int) *CloudAlertUpdateOne {
	cauo.mutation.AddOverlimitPktCount(i)
	return cauo
}

// SetStartTime sets the "start_time" field.
func (cauo *CloudAlertUpdateOne) SetStartTime(t time.Time) *CloudAlertUpdateOne {
	cauo.mutation.SetStartTime(t)
	return cauo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableStartTime(t *time.Time) *CloudAlertUpdateOne {
	if t != nil {
		cauo.SetStartTime(*t)
	}
	return cauo
}

// SetEndTime sets the "end_time" field.
func (cauo *CloudAlertUpdateOne) SetEndTime(t time.Time) *CloudAlertUpdateOne {
	cauo.mutation.SetEndTime(t)
	return cauo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (cauo *CloudAlertUpdateOne) SetNillableEndTime(t *time.Time) *CloudAlertUpdateOne {
	if t != nil {
		cauo.SetEndTime(*t)
	}
	return cauo
}

// ClearEndTime clears the value of the "end_time" field.
func (cauo *CloudAlertUpdateOne) ClearEndTime() *CloudAlertUpdateOne {
	cauo.mutation.ClearEndTime()
	return cauo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cauo *CloudAlertUpdateOne) SetTenant(t *Tenant) *CloudAlertUpdateOne {
	return cauo.SetTenantID(t.ID)
}

// AddCloudflowDataIDs adds the "cloudflow_datas" edge to the CloudFlowData entity by IDs.
func (cauo *CloudAlertUpdateOne) AddCloudflowDataIDs(ids ...int) *CloudAlertUpdateOne {
	cauo.mutation.AddCloudflowDataIDs(ids...)
	return cauo
}

// AddCloudflowDatas adds the "cloudflow_datas" edges to the CloudFlowData entity.
func (cauo *CloudAlertUpdateOne) AddCloudflowDatas(c ...*CloudFlowData) *CloudAlertUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cauo.AddCloudflowDataIDs(ids...)
}

// Mutation returns the CloudAlertMutation object of the builder.
func (cauo *CloudAlertUpdateOne) Mutation() *CloudAlertMutation {
	return cauo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (cauo *CloudAlertUpdateOne) ClearTenant() *CloudAlertUpdateOne {
	cauo.mutation.ClearTenant()
	return cauo
}

// ClearCloudflowDatas clears all "cloudflow_datas" edges to the CloudFlowData entity.
func (cauo *CloudAlertUpdateOne) ClearCloudflowDatas() *CloudAlertUpdateOne {
	cauo.mutation.ClearCloudflowDatas()
	return cauo
}

// RemoveCloudflowDataIDs removes the "cloudflow_datas" edge to CloudFlowData entities by IDs.
func (cauo *CloudAlertUpdateOne) RemoveCloudflowDataIDs(ids ...int) *CloudAlertUpdateOne {
	cauo.mutation.RemoveCloudflowDataIDs(ids...)
	return cauo
}

// RemoveCloudflowDatas removes "cloudflow_datas" edges to CloudFlowData entities.
func (cauo *CloudAlertUpdateOne) RemoveCloudflowDatas(c ...*CloudFlowData) *CloudAlertUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cauo.RemoveCloudflowDataIDs(ids...)
}

// Where appends a list predicates to the CloudAlertUpdate builder.
func (cauo *CloudAlertUpdateOne) Where(ps ...predicate.CloudAlert) *CloudAlertUpdateOne {
	cauo.mutation.Where(ps...)
	return cauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cauo *CloudAlertUpdateOne) Select(field string, fields ...string) *CloudAlertUpdateOne {
	cauo.fields = append([]string{field}, fields...)
	return cauo
}

// Save executes the query and returns the updated CloudAlert entity.
func (cauo *CloudAlertUpdateOne) Save(ctx context.Context) (*CloudAlert, error) {
	if err := cauo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, cauo.sqlSave, cauo.mutation, cauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cauo *CloudAlertUpdateOne) SaveX(ctx context.Context) *CloudAlert {
	node, err := cauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cauo *CloudAlertUpdateOne) Exec(ctx context.Context) error {
	_, err := cauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cauo *CloudAlertUpdateOne) ExecX(ctx context.Context) {
	if err := cauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cauo *CloudAlertUpdateOne) defaults() error {
	if _, ok := cauo.mutation.UpdatedAt(); !ok {
		if cloudalert.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudalert.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudalert.UpdateDefaultUpdatedAt()
		cauo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cauo *CloudAlertUpdateOne) check() error {
	if v, ok := cauo.mutation.Remark(); ok {
		if err := cloudalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudAlert.remark": %w`, err)}
		}
	}
	return nil
}

func (cauo *CloudAlertUpdateOne) sqlSave(ctx context.Context) (_node *CloudAlert, err error) {
	if err := cauo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(cloudalert.Table, cloudalert.Columns, sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt))
	id, ok := cauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CloudAlert.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cloudalert.FieldID)
		for _, f := range fields {
			if !cloudalert.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != cloudalert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cauo.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudalert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := cauo.mutation.Remark(); ok {
		_spec.SetField(cloudalert.FieldRemark, field.TypeString, value)
	}
	if cauo.mutation.RemarkCleared() {
		_spec.ClearField(cloudalert.FieldRemark, field.TypeString)
	}
	if value, ok := cauo.mutation.SrcIP(); ok {
		_spec.SetField(cloudalert.FieldSrcIP, field.TypeString, value)
	}
	if value, ok := cauo.mutation.SrcPort(); ok {
		_spec.SetField(cloudalert.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.AddedSrcPort(); ok {
		_spec.AddField(cloudalert.FieldSrcPort, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.DstIP(); ok {
		_spec.SetField(cloudalert.FieldDstIP, field.TypeString, value)
	}
	if value, ok := cauo.mutation.DstPort(); ok {
		_spec.SetField(cloudalert.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.AddedDstPort(); ok {
		_spec.AddField(cloudalert.FieldDstPort, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.DefenceMode(); ok {
		_spec.SetField(cloudalert.FieldDefenceMode, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.AddedDefenceMode(); ok {
		_spec.AddField(cloudalert.FieldDefenceMode, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.FlowMode(); ok {
		_spec.SetField(cloudalert.FieldFlowMode, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.AddedFlowMode(); ok {
		_spec.AddField(cloudalert.FieldFlowMode, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.TCPAckNum(); ok {
		_spec.SetField(cloudalert.FieldTCPAckNum, field.TypeString, value)
	}
	if value, ok := cauo.mutation.TCPSeqNum(); ok {
		_spec.SetField(cloudalert.FieldTCPSeqNum, field.TypeString, value)
	}
	if value, ok := cauo.mutation.Protocol(); ok {
		_spec.SetField(cloudalert.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.AddedProtocol(); ok {
		_spec.AddField(cloudalert.FieldProtocol, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.DefenceLevel(); ok {
		_spec.SetField(cloudalert.FieldDefenceLevel, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.AddedDefenceLevel(); ok {
		_spec.AddField(cloudalert.FieldDefenceLevel, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.MaxPps(); ok {
		_spec.SetField(cloudalert.FieldMaxPps, field.TypeInt64, value)
	}
	if value, ok := cauo.mutation.AddedMaxPps(); ok {
		_spec.AddField(cloudalert.FieldMaxPps, field.TypeInt64, value)
	}
	if value, ok := cauo.mutation.MaxAttackPps(); ok {
		_spec.SetField(cloudalert.FieldMaxAttackPps, field.TypeInt64, value)
	}
	if value, ok := cauo.mutation.AddedMaxAttackPps(); ok {
		_spec.AddField(cloudalert.FieldMaxAttackPps, field.TypeInt64, value)
	}
	if value, ok := cauo.mutation.OverlimitPktCount(); ok {
		_spec.SetField(cloudalert.FieldOverlimitPktCount, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.AddedOverlimitPktCount(); ok {
		_spec.AddField(cloudalert.FieldOverlimitPktCount, field.TypeInt, value)
	}
	if value, ok := cauo.mutation.StartTime(); ok {
		_spec.SetField(cloudalert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := cauo.mutation.EndTime(); ok {
		_spec.SetField(cloudalert.FieldEndTime, field.TypeTime, value)
	}
	if cauo.mutation.EndTimeCleared() {
		_spec.ClearField(cloudalert.FieldEndTime, field.TypeTime)
	}
	if cauo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudalert.TenantTable,
			Columns: []string{cloudalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cauo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudalert.TenantTable,
			Columns: []string{cloudalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if cauo.mutation.CloudflowDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   cloudalert.CloudflowDatasTable,
			Columns: []string{cloudalert.CloudflowDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cauo.mutation.RemovedCloudflowDatasIDs(); len(nodes) > 0 && !cauo.mutation.CloudflowDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   cloudalert.CloudflowDatasTable,
			Columns: []string{cloudalert.CloudflowDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cauo.mutation.CloudflowDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   cloudalert.CloudflowDatasTable,
			Columns: []string{cloudalert.CloudflowDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &CloudAlert{config: cauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{cloudalert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cauo.mutation.done = true
	return _node, nil
}
