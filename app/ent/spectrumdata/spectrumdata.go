// Code generated by ent, DO NOT EDIT.

package spectrumdata

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the spectrumdata type in the database.
	Label = "spectrum_data"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldSpectrumAlertID holds the string denoting the spectrum_alert_id field in the database.
	FieldSpectrumAlertID = "spectrum_alert_id"
	// FieldIP holds the string denoting the ip field in the database.
	FieldIP = "ip"
	// FieldTime holds the string denoting the time field in the database.
	FieldTime = "time"
	// FieldMonitorID holds the string denoting the monitor_id field in the database.
	FieldMonitorID = "monitor_id"
	// FieldDataType holds the string denoting the data_type field in the database.
	FieldDataType = "data_type"
	// FieldBps holds the string denoting the bps field in the database.
	FieldBps = "bps"
	// FieldPps holds the string denoting the pps field in the database.
	FieldPps = "pps"
	// FieldSynBps holds the string denoting the syn_bps field in the database.
	FieldSynBps = "syn_bps"
	// FieldSynPps holds the string denoting the syn_pps field in the database.
	FieldSynPps = "syn_pps"
	// FieldAckBps holds the string denoting the ack_bps field in the database.
	FieldAckBps = "ack_bps"
	// FieldAckPps holds the string denoting the ack_pps field in the database.
	FieldAckPps = "ack_pps"
	// FieldSynAckBps holds the string denoting the syn_ack_bps field in the database.
	FieldSynAckBps = "syn_ack_bps"
	// FieldSynAckPps holds the string denoting the syn_ack_pps field in the database.
	FieldSynAckPps = "syn_ack_pps"
	// FieldIcmpBps holds the string denoting the icmp_bps field in the database.
	FieldIcmpBps = "icmp_bps"
	// FieldIcmpPps holds the string denoting the icmp_pps field in the database.
	FieldIcmpPps = "icmp_pps"
	// FieldSmallPps holds the string denoting the small_pps field in the database.
	FieldSmallPps = "small_pps"
	// FieldNtpPps holds the string denoting the ntp_pps field in the database.
	FieldNtpPps = "ntp_pps"
	// FieldNtpBps holds the string denoting the ntp_bps field in the database.
	FieldNtpBps = "ntp_bps"
	// FieldDNSQueryPps holds the string denoting the dns_query_pps field in the database.
	FieldDNSQueryPps = "dns_query_pps"
	// FieldDNSQueryBps holds the string denoting the dns_query_bps field in the database.
	FieldDNSQueryBps = "dns_query_bps"
	// FieldDNSAnswerPps holds the string denoting the dns_answer_pps field in the database.
	FieldDNSAnswerPps = "dns_answer_pps"
	// FieldDNSAnswerBps holds the string denoting the dns_answer_bps field in the database.
	FieldDNSAnswerBps = "dns_answer_bps"
	// FieldSsdpBps holds the string denoting the ssdp_bps field in the database.
	FieldSsdpBps = "ssdp_bps"
	// FieldSsdpPps holds the string denoting the ssdp_pps field in the database.
	FieldSsdpPps = "ssdp_pps"
	// FieldUDPPps holds the string denoting the udp_pps field in the database.
	FieldUDPPps = "udp_pps"
	// FieldUDPBps holds the string denoting the udp_bps field in the database.
	FieldUDPBps = "udp_bps"
	// FieldQPS holds the string denoting the qps field in the database.
	FieldQPS = "qps"
	// FieldReceiveCount holds the string denoting the receive_count field in the database.
	FieldReceiveCount = "receive_count"
	// FieldIPType holds the string denoting the ip_type field in the database.
	FieldIPType = "ip_type"
	// FieldMonitor holds the string denoting the monitor field in the database.
	FieldMonitor = "monitor"
	// FieldProduct holds the string denoting the product field in the database.
	FieldProduct = "product"
	// FieldHost holds the string denoting the host field in the database.
	FieldHost = "host"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeSpectrumAlert holds the string denoting the spectrum_alert edge name in mutations.
	EdgeSpectrumAlert = "spectrum_alert"
	// Table holds the table name of the spectrumdata in the database.
	Table = "spectrum_data"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "spectrum_data"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// SpectrumAlertTable is the table that holds the spectrum_alert relation/edge.
	SpectrumAlertTable = "spectrum_data"
	// SpectrumAlertInverseTable is the table name for the SpectrumAlert entity.
	// It exists in this package in order to avoid circular dependency with the "spectrumalert" package.
	SpectrumAlertInverseTable = "spectrum_alerts"
	// SpectrumAlertColumn is the table column denoting the spectrum_alert relation/edge.
	SpectrumAlertColumn = "spectrum_alert_id"
)

// Columns holds all SQL columns for spectrumdata fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldSpectrumAlertID,
	FieldIP,
	FieldTime,
	FieldMonitorID,
	FieldDataType,
	FieldBps,
	FieldPps,
	FieldSynBps,
	FieldSynPps,
	FieldAckBps,
	FieldAckPps,
	FieldSynAckBps,
	FieldSynAckPps,
	FieldIcmpBps,
	FieldIcmpPps,
	FieldSmallPps,
	FieldNtpPps,
	FieldNtpBps,
	FieldDNSQueryPps,
	FieldDNSQueryBps,
	FieldDNSAnswerPps,
	FieldDNSAnswerBps,
	FieldSsdpBps,
	FieldSsdpPps,
	FieldUDPPps,
	FieldUDPBps,
	FieldQPS,
	FieldReceiveCount,
	FieldIPType,
	FieldMonitor,
	FieldProduct,
	FieldHost,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
)

// OrderOption defines the ordering options for the SpectrumData queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// BySpectrumAlertID orders the results by the spectrum_alert_id field.
func BySpectrumAlertID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSpectrumAlertID, opts...).ToFunc()
}

// ByIP orders the results by the ip field.
func ByIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIP, opts...).ToFunc()
}

// ByTime orders the results by the time field.
func ByTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTime, opts...).ToFunc()
}

// ByMonitorID orders the results by the monitor_id field.
func ByMonitorID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMonitorID, opts...).ToFunc()
}

// ByDataType orders the results by the data_type field.
func ByDataType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDataType, opts...).ToFunc()
}

// ByBps orders the results by the bps field.
func ByBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBps, opts...).ToFunc()
}

// ByPps orders the results by the pps field.
func ByPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPps, opts...).ToFunc()
}

// BySynBps orders the results by the syn_bps field.
func BySynBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSynBps, opts...).ToFunc()
}

// BySynPps orders the results by the syn_pps field.
func BySynPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSynPps, opts...).ToFunc()
}

// ByAckBps orders the results by the ack_bps field.
func ByAckBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAckBps, opts...).ToFunc()
}

// ByAckPps orders the results by the ack_pps field.
func ByAckPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAckPps, opts...).ToFunc()
}

// BySynAckBps orders the results by the syn_ack_bps field.
func BySynAckBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSynAckBps, opts...).ToFunc()
}

// BySynAckPps orders the results by the syn_ack_pps field.
func BySynAckPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSynAckPps, opts...).ToFunc()
}

// ByIcmpBps orders the results by the icmp_bps field.
func ByIcmpBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIcmpBps, opts...).ToFunc()
}

// ByIcmpPps orders the results by the icmp_pps field.
func ByIcmpPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIcmpPps, opts...).ToFunc()
}

// BySmallPps orders the results by the small_pps field.
func BySmallPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSmallPps, opts...).ToFunc()
}

// ByNtpPps orders the results by the ntp_pps field.
func ByNtpPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNtpPps, opts...).ToFunc()
}

// ByNtpBps orders the results by the ntp_bps field.
func ByNtpBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNtpBps, opts...).ToFunc()
}

// ByDNSQueryPps orders the results by the dns_query_pps field.
func ByDNSQueryPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDNSQueryPps, opts...).ToFunc()
}

// ByDNSQueryBps orders the results by the dns_query_bps field.
func ByDNSQueryBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDNSQueryBps, opts...).ToFunc()
}

// ByDNSAnswerPps orders the results by the dns_answer_pps field.
func ByDNSAnswerPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDNSAnswerPps, opts...).ToFunc()
}

// ByDNSAnswerBps orders the results by the dns_answer_bps field.
func ByDNSAnswerBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDNSAnswerBps, opts...).ToFunc()
}

// BySsdpBps orders the results by the ssdp_bps field.
func BySsdpBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSsdpBps, opts...).ToFunc()
}

// BySsdpPps orders the results by the ssdp_pps field.
func BySsdpPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSsdpPps, opts...).ToFunc()
}

// ByUDPPps orders the results by the udp_pps field.
func ByUDPPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUDPPps, opts...).ToFunc()
}

// ByUDPBps orders the results by the udp_bps field.
func ByUDPBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUDPBps, opts...).ToFunc()
}

// ByQPS orders the results by the qps field.
func ByQPS(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldQPS, opts...).ToFunc()
}

// ByReceiveCount orders the results by the receive_count field.
func ByReceiveCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReceiveCount, opts...).ToFunc()
}

// ByIPType orders the results by the ip_type field.
func ByIPType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIPType, opts...).ToFunc()
}

// ByMonitor orders the results by the monitor field.
func ByMonitor(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMonitor, opts...).ToFunc()
}

// ByProduct orders the results by the product field.
func ByProduct(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProduct, opts...).ToFunc()
}

// ByHost orders the results by the host field.
func ByHost(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHost, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// BySpectrumAlertField orders the results by spectrum_alert field.
func BySpectrumAlertField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSpectrumAlertStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newSpectrumAlertStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SpectrumAlertInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, SpectrumAlertTable, SpectrumAlertColumn),
	)
}
