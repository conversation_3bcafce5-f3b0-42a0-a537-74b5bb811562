// Code generated by ent, DO NOT EDIT.

package spectrumdata

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldCreatedAt, v))
}

// SpectrumAlertID applies equality check predicate on the "spectrum_alert_id" field. It's identical to SpectrumAlertIDEQ.
func SpectrumAlertID(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSpectrumAlertID, v))
}

// IP applies equality check predicate on the "ip" field. It's identical to IPEQ.
func IP(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldIP, v))
}

// Time applies equality check predicate on the "time" field. It's identical to TimeEQ.
func Time(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldTime, v))
}

// MonitorID applies equality check predicate on the "monitor_id" field. It's identical to MonitorIDEQ.
func MonitorID(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldMonitorID, v))
}

// DataType applies equality check predicate on the "data_type" field. It's identical to DataTypeEQ.
func DataType(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDataType, v))
}

// Bps applies equality check predicate on the "bps" field. It's identical to BpsEQ.
func Bps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldBps, v))
}

// Pps applies equality check predicate on the "pps" field. It's identical to PpsEQ.
func Pps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldPps, v))
}

// SynBps applies equality check predicate on the "syn_bps" field. It's identical to SynBpsEQ.
func SynBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSynBps, v))
}

// SynPps applies equality check predicate on the "syn_pps" field. It's identical to SynPpsEQ.
func SynPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSynPps, v))
}

// AckBps applies equality check predicate on the "ack_bps" field. It's identical to AckBpsEQ.
func AckBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldAckBps, v))
}

// AckPps applies equality check predicate on the "ack_pps" field. It's identical to AckPpsEQ.
func AckPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldAckPps, v))
}

// SynAckBps applies equality check predicate on the "syn_ack_bps" field. It's identical to SynAckBpsEQ.
func SynAckBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSynAckBps, v))
}

// SynAckPps applies equality check predicate on the "syn_ack_pps" field. It's identical to SynAckPpsEQ.
func SynAckPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSynAckPps, v))
}

// IcmpBps applies equality check predicate on the "icmp_bps" field. It's identical to IcmpBpsEQ.
func IcmpBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldIcmpBps, v))
}

// IcmpPps applies equality check predicate on the "icmp_pps" field. It's identical to IcmpPpsEQ.
func IcmpPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldIcmpPps, v))
}

// SmallPps applies equality check predicate on the "small_pps" field. It's identical to SmallPpsEQ.
func SmallPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSmallPps, v))
}

// NtpPps applies equality check predicate on the "ntp_pps" field. It's identical to NtpPpsEQ.
func NtpPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldNtpPps, v))
}

// NtpBps applies equality check predicate on the "ntp_bps" field. It's identical to NtpBpsEQ.
func NtpBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldNtpBps, v))
}

// DNSQueryPps applies equality check predicate on the "dns_query_pps" field. It's identical to DNSQueryPpsEQ.
func DNSQueryPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDNSQueryPps, v))
}

// DNSQueryBps applies equality check predicate on the "dns_query_bps" field. It's identical to DNSQueryBpsEQ.
func DNSQueryBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDNSQueryBps, v))
}

// DNSAnswerPps applies equality check predicate on the "dns_answer_pps" field. It's identical to DNSAnswerPpsEQ.
func DNSAnswerPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDNSAnswerPps, v))
}

// DNSAnswerBps applies equality check predicate on the "dns_answer_bps" field. It's identical to DNSAnswerBpsEQ.
func DNSAnswerBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDNSAnswerBps, v))
}

// SsdpBps applies equality check predicate on the "ssdp_bps" field. It's identical to SsdpBpsEQ.
func SsdpBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSsdpBps, v))
}

// SsdpPps applies equality check predicate on the "ssdp_pps" field. It's identical to SsdpPpsEQ.
func SsdpPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSsdpPps, v))
}

// UDPPps applies equality check predicate on the "udp_pps" field. It's identical to UDPPpsEQ.
func UDPPps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldUDPPps, v))
}

// UDPBps applies equality check predicate on the "udp_bps" field. It's identical to UDPBpsEQ.
func UDPBps(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldUDPBps, v))
}

// QPS applies equality check predicate on the "qps" field. It's identical to QPSEQ.
func QPS(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldQPS, v))
}

// ReceiveCount applies equality check predicate on the "receive_count" field. It's identical to ReceiveCountEQ.
func ReceiveCount(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldReceiveCount, v))
}

// IPType applies equality check predicate on the "ip_type" field. It's identical to IPTypeEQ.
func IPType(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldIPType, v))
}

// Monitor applies equality check predicate on the "monitor" field. It's identical to MonitorEQ.
func Monitor(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldMonitor, v))
}

// Product applies equality check predicate on the "product" field. It's identical to ProductEQ.
func Product(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldProduct, v))
}

// Host applies equality check predicate on the "host" field. It's identical to HostEQ.
func Host(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldHost, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldCreatedAt, v))
}

// SpectrumAlertIDEQ applies the EQ predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSpectrumAlertID, v))
}

// SpectrumAlertIDNEQ applies the NEQ predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDNEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldSpectrumAlertID, v))
}

// SpectrumAlertIDIn applies the In predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldSpectrumAlertID, vs...))
}

// SpectrumAlertIDNotIn applies the NotIn predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDNotIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldSpectrumAlertID, vs...))
}

// SpectrumAlertIDIsNil applies the IsNil predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDIsNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIsNull(FieldSpectrumAlertID))
}

// SpectrumAlertIDNotNil applies the NotNil predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDNotNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotNull(FieldSpectrumAlertID))
}

// IPEQ applies the EQ predicate on the "ip" field.
func IPEQ(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldIP, v))
}

// IPNEQ applies the NEQ predicate on the "ip" field.
func IPNEQ(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldIP, v))
}

// IPIn applies the In predicate on the "ip" field.
func IPIn(vs ...string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldIP, vs...))
}

// IPNotIn applies the NotIn predicate on the "ip" field.
func IPNotIn(vs ...string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldIP, vs...))
}

// IPGT applies the GT predicate on the "ip" field.
func IPGT(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldIP, v))
}

// IPGTE applies the GTE predicate on the "ip" field.
func IPGTE(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldIP, v))
}

// IPLT applies the LT predicate on the "ip" field.
func IPLT(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldIP, v))
}

// IPLTE applies the LTE predicate on the "ip" field.
func IPLTE(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldIP, v))
}

// IPContains applies the Contains predicate on the "ip" field.
func IPContains(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldContains(FieldIP, v))
}

// IPHasPrefix applies the HasPrefix predicate on the "ip" field.
func IPHasPrefix(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldHasPrefix(FieldIP, v))
}

// IPHasSuffix applies the HasSuffix predicate on the "ip" field.
func IPHasSuffix(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldHasSuffix(FieldIP, v))
}

// IPEqualFold applies the EqualFold predicate on the "ip" field.
func IPEqualFold(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEqualFold(FieldIP, v))
}

// IPContainsFold applies the ContainsFold predicate on the "ip" field.
func IPContainsFold(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldContainsFold(FieldIP, v))
}

// TimeEQ applies the EQ predicate on the "time" field.
func TimeEQ(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldTime, v))
}

// TimeNEQ applies the NEQ predicate on the "time" field.
func TimeNEQ(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldTime, v))
}

// TimeIn applies the In predicate on the "time" field.
func TimeIn(vs ...time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldTime, vs...))
}

// TimeNotIn applies the NotIn predicate on the "time" field.
func TimeNotIn(vs ...time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldTime, vs...))
}

// TimeGT applies the GT predicate on the "time" field.
func TimeGT(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldTime, v))
}

// TimeGTE applies the GTE predicate on the "time" field.
func TimeGTE(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldTime, v))
}

// TimeLT applies the LT predicate on the "time" field.
func TimeLT(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldTime, v))
}

// TimeLTE applies the LTE predicate on the "time" field.
func TimeLTE(v time.Time) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldTime, v))
}

// MonitorIDEQ applies the EQ predicate on the "monitor_id" field.
func MonitorIDEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldMonitorID, v))
}

// MonitorIDNEQ applies the NEQ predicate on the "monitor_id" field.
func MonitorIDNEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldMonitorID, v))
}

// MonitorIDIn applies the In predicate on the "monitor_id" field.
func MonitorIDIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldMonitorID, vs...))
}

// MonitorIDNotIn applies the NotIn predicate on the "monitor_id" field.
func MonitorIDNotIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldMonitorID, vs...))
}

// MonitorIDGT applies the GT predicate on the "monitor_id" field.
func MonitorIDGT(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldMonitorID, v))
}

// MonitorIDGTE applies the GTE predicate on the "monitor_id" field.
func MonitorIDGTE(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldMonitorID, v))
}

// MonitorIDLT applies the LT predicate on the "monitor_id" field.
func MonitorIDLT(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldMonitorID, v))
}

// MonitorIDLTE applies the LTE predicate on the "monitor_id" field.
func MonitorIDLTE(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldMonitorID, v))
}

// DataTypeEQ applies the EQ predicate on the "data_type" field.
func DataTypeEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDataType, v))
}

// DataTypeNEQ applies the NEQ predicate on the "data_type" field.
func DataTypeNEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldDataType, v))
}

// DataTypeIn applies the In predicate on the "data_type" field.
func DataTypeIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldDataType, vs...))
}

// DataTypeNotIn applies the NotIn predicate on the "data_type" field.
func DataTypeNotIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldDataType, vs...))
}

// DataTypeGT applies the GT predicate on the "data_type" field.
func DataTypeGT(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldDataType, v))
}

// DataTypeGTE applies the GTE predicate on the "data_type" field.
func DataTypeGTE(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldDataType, v))
}

// DataTypeLT applies the LT predicate on the "data_type" field.
func DataTypeLT(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldDataType, v))
}

// DataTypeLTE applies the LTE predicate on the "data_type" field.
func DataTypeLTE(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldDataType, v))
}

// BpsEQ applies the EQ predicate on the "bps" field.
func BpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldBps, v))
}

// BpsNEQ applies the NEQ predicate on the "bps" field.
func BpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldBps, v))
}

// BpsIn applies the In predicate on the "bps" field.
func BpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldBps, vs...))
}

// BpsNotIn applies the NotIn predicate on the "bps" field.
func BpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldBps, vs...))
}

// BpsGT applies the GT predicate on the "bps" field.
func BpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldBps, v))
}

// BpsGTE applies the GTE predicate on the "bps" field.
func BpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldBps, v))
}

// BpsLT applies the LT predicate on the "bps" field.
func BpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldBps, v))
}

// BpsLTE applies the LTE predicate on the "bps" field.
func BpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldBps, v))
}

// PpsEQ applies the EQ predicate on the "pps" field.
func PpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldPps, v))
}

// PpsNEQ applies the NEQ predicate on the "pps" field.
func PpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldPps, v))
}

// PpsIn applies the In predicate on the "pps" field.
func PpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldPps, vs...))
}

// PpsNotIn applies the NotIn predicate on the "pps" field.
func PpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldPps, vs...))
}

// PpsGT applies the GT predicate on the "pps" field.
func PpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldPps, v))
}

// PpsGTE applies the GTE predicate on the "pps" field.
func PpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldPps, v))
}

// PpsLT applies the LT predicate on the "pps" field.
func PpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldPps, v))
}

// PpsLTE applies the LTE predicate on the "pps" field.
func PpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldPps, v))
}

// SynBpsEQ applies the EQ predicate on the "syn_bps" field.
func SynBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSynBps, v))
}

// SynBpsNEQ applies the NEQ predicate on the "syn_bps" field.
func SynBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldSynBps, v))
}

// SynBpsIn applies the In predicate on the "syn_bps" field.
func SynBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldSynBps, vs...))
}

// SynBpsNotIn applies the NotIn predicate on the "syn_bps" field.
func SynBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldSynBps, vs...))
}

// SynBpsGT applies the GT predicate on the "syn_bps" field.
func SynBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldSynBps, v))
}

// SynBpsGTE applies the GTE predicate on the "syn_bps" field.
func SynBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldSynBps, v))
}

// SynBpsLT applies the LT predicate on the "syn_bps" field.
func SynBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldSynBps, v))
}

// SynBpsLTE applies the LTE predicate on the "syn_bps" field.
func SynBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldSynBps, v))
}

// SynPpsEQ applies the EQ predicate on the "syn_pps" field.
func SynPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSynPps, v))
}

// SynPpsNEQ applies the NEQ predicate on the "syn_pps" field.
func SynPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldSynPps, v))
}

// SynPpsIn applies the In predicate on the "syn_pps" field.
func SynPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldSynPps, vs...))
}

// SynPpsNotIn applies the NotIn predicate on the "syn_pps" field.
func SynPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldSynPps, vs...))
}

// SynPpsGT applies the GT predicate on the "syn_pps" field.
func SynPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldSynPps, v))
}

// SynPpsGTE applies the GTE predicate on the "syn_pps" field.
func SynPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldSynPps, v))
}

// SynPpsLT applies the LT predicate on the "syn_pps" field.
func SynPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldSynPps, v))
}

// SynPpsLTE applies the LTE predicate on the "syn_pps" field.
func SynPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldSynPps, v))
}

// AckBpsEQ applies the EQ predicate on the "ack_bps" field.
func AckBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldAckBps, v))
}

// AckBpsNEQ applies the NEQ predicate on the "ack_bps" field.
func AckBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldAckBps, v))
}

// AckBpsIn applies the In predicate on the "ack_bps" field.
func AckBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldAckBps, vs...))
}

// AckBpsNotIn applies the NotIn predicate on the "ack_bps" field.
func AckBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldAckBps, vs...))
}

// AckBpsGT applies the GT predicate on the "ack_bps" field.
func AckBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldAckBps, v))
}

// AckBpsGTE applies the GTE predicate on the "ack_bps" field.
func AckBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldAckBps, v))
}

// AckBpsLT applies the LT predicate on the "ack_bps" field.
func AckBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldAckBps, v))
}

// AckBpsLTE applies the LTE predicate on the "ack_bps" field.
func AckBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldAckBps, v))
}

// AckPpsEQ applies the EQ predicate on the "ack_pps" field.
func AckPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldAckPps, v))
}

// AckPpsNEQ applies the NEQ predicate on the "ack_pps" field.
func AckPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldAckPps, v))
}

// AckPpsIn applies the In predicate on the "ack_pps" field.
func AckPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldAckPps, vs...))
}

// AckPpsNotIn applies the NotIn predicate on the "ack_pps" field.
func AckPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldAckPps, vs...))
}

// AckPpsGT applies the GT predicate on the "ack_pps" field.
func AckPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldAckPps, v))
}

// AckPpsGTE applies the GTE predicate on the "ack_pps" field.
func AckPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldAckPps, v))
}

// AckPpsLT applies the LT predicate on the "ack_pps" field.
func AckPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldAckPps, v))
}

// AckPpsLTE applies the LTE predicate on the "ack_pps" field.
func AckPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldAckPps, v))
}

// SynAckBpsEQ applies the EQ predicate on the "syn_ack_bps" field.
func SynAckBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSynAckBps, v))
}

// SynAckBpsNEQ applies the NEQ predicate on the "syn_ack_bps" field.
func SynAckBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldSynAckBps, v))
}

// SynAckBpsIn applies the In predicate on the "syn_ack_bps" field.
func SynAckBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldSynAckBps, vs...))
}

// SynAckBpsNotIn applies the NotIn predicate on the "syn_ack_bps" field.
func SynAckBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldSynAckBps, vs...))
}

// SynAckBpsGT applies the GT predicate on the "syn_ack_bps" field.
func SynAckBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldSynAckBps, v))
}

// SynAckBpsGTE applies the GTE predicate on the "syn_ack_bps" field.
func SynAckBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldSynAckBps, v))
}

// SynAckBpsLT applies the LT predicate on the "syn_ack_bps" field.
func SynAckBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldSynAckBps, v))
}

// SynAckBpsLTE applies the LTE predicate on the "syn_ack_bps" field.
func SynAckBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldSynAckBps, v))
}

// SynAckPpsEQ applies the EQ predicate on the "syn_ack_pps" field.
func SynAckPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSynAckPps, v))
}

// SynAckPpsNEQ applies the NEQ predicate on the "syn_ack_pps" field.
func SynAckPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldSynAckPps, v))
}

// SynAckPpsIn applies the In predicate on the "syn_ack_pps" field.
func SynAckPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldSynAckPps, vs...))
}

// SynAckPpsNotIn applies the NotIn predicate on the "syn_ack_pps" field.
func SynAckPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldSynAckPps, vs...))
}

// SynAckPpsGT applies the GT predicate on the "syn_ack_pps" field.
func SynAckPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldSynAckPps, v))
}

// SynAckPpsGTE applies the GTE predicate on the "syn_ack_pps" field.
func SynAckPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldSynAckPps, v))
}

// SynAckPpsLT applies the LT predicate on the "syn_ack_pps" field.
func SynAckPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldSynAckPps, v))
}

// SynAckPpsLTE applies the LTE predicate on the "syn_ack_pps" field.
func SynAckPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldSynAckPps, v))
}

// IcmpBpsEQ applies the EQ predicate on the "icmp_bps" field.
func IcmpBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldIcmpBps, v))
}

// IcmpBpsNEQ applies the NEQ predicate on the "icmp_bps" field.
func IcmpBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldIcmpBps, v))
}

// IcmpBpsIn applies the In predicate on the "icmp_bps" field.
func IcmpBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldIcmpBps, vs...))
}

// IcmpBpsNotIn applies the NotIn predicate on the "icmp_bps" field.
func IcmpBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldIcmpBps, vs...))
}

// IcmpBpsGT applies the GT predicate on the "icmp_bps" field.
func IcmpBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldIcmpBps, v))
}

// IcmpBpsGTE applies the GTE predicate on the "icmp_bps" field.
func IcmpBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldIcmpBps, v))
}

// IcmpBpsLT applies the LT predicate on the "icmp_bps" field.
func IcmpBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldIcmpBps, v))
}

// IcmpBpsLTE applies the LTE predicate on the "icmp_bps" field.
func IcmpBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldIcmpBps, v))
}

// IcmpPpsEQ applies the EQ predicate on the "icmp_pps" field.
func IcmpPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldIcmpPps, v))
}

// IcmpPpsNEQ applies the NEQ predicate on the "icmp_pps" field.
func IcmpPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldIcmpPps, v))
}

// IcmpPpsIn applies the In predicate on the "icmp_pps" field.
func IcmpPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldIcmpPps, vs...))
}

// IcmpPpsNotIn applies the NotIn predicate on the "icmp_pps" field.
func IcmpPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldIcmpPps, vs...))
}

// IcmpPpsGT applies the GT predicate on the "icmp_pps" field.
func IcmpPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldIcmpPps, v))
}

// IcmpPpsGTE applies the GTE predicate on the "icmp_pps" field.
func IcmpPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldIcmpPps, v))
}

// IcmpPpsLT applies the LT predicate on the "icmp_pps" field.
func IcmpPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldIcmpPps, v))
}

// IcmpPpsLTE applies the LTE predicate on the "icmp_pps" field.
func IcmpPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldIcmpPps, v))
}

// SmallPpsEQ applies the EQ predicate on the "small_pps" field.
func SmallPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSmallPps, v))
}

// SmallPpsNEQ applies the NEQ predicate on the "small_pps" field.
func SmallPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldSmallPps, v))
}

// SmallPpsIn applies the In predicate on the "small_pps" field.
func SmallPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldSmallPps, vs...))
}

// SmallPpsNotIn applies the NotIn predicate on the "small_pps" field.
func SmallPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldSmallPps, vs...))
}

// SmallPpsGT applies the GT predicate on the "small_pps" field.
func SmallPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldSmallPps, v))
}

// SmallPpsGTE applies the GTE predicate on the "small_pps" field.
func SmallPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldSmallPps, v))
}

// SmallPpsLT applies the LT predicate on the "small_pps" field.
func SmallPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldSmallPps, v))
}

// SmallPpsLTE applies the LTE predicate on the "small_pps" field.
func SmallPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldSmallPps, v))
}

// NtpPpsEQ applies the EQ predicate on the "ntp_pps" field.
func NtpPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldNtpPps, v))
}

// NtpPpsNEQ applies the NEQ predicate on the "ntp_pps" field.
func NtpPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldNtpPps, v))
}

// NtpPpsIn applies the In predicate on the "ntp_pps" field.
func NtpPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldNtpPps, vs...))
}

// NtpPpsNotIn applies the NotIn predicate on the "ntp_pps" field.
func NtpPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldNtpPps, vs...))
}

// NtpPpsGT applies the GT predicate on the "ntp_pps" field.
func NtpPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldNtpPps, v))
}

// NtpPpsGTE applies the GTE predicate on the "ntp_pps" field.
func NtpPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldNtpPps, v))
}

// NtpPpsLT applies the LT predicate on the "ntp_pps" field.
func NtpPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldNtpPps, v))
}

// NtpPpsLTE applies the LTE predicate on the "ntp_pps" field.
func NtpPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldNtpPps, v))
}

// NtpBpsEQ applies the EQ predicate on the "ntp_bps" field.
func NtpBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldNtpBps, v))
}

// NtpBpsNEQ applies the NEQ predicate on the "ntp_bps" field.
func NtpBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldNtpBps, v))
}

// NtpBpsIn applies the In predicate on the "ntp_bps" field.
func NtpBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldNtpBps, vs...))
}

// NtpBpsNotIn applies the NotIn predicate on the "ntp_bps" field.
func NtpBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldNtpBps, vs...))
}

// NtpBpsGT applies the GT predicate on the "ntp_bps" field.
func NtpBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldNtpBps, v))
}

// NtpBpsGTE applies the GTE predicate on the "ntp_bps" field.
func NtpBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldNtpBps, v))
}

// NtpBpsLT applies the LT predicate on the "ntp_bps" field.
func NtpBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldNtpBps, v))
}

// NtpBpsLTE applies the LTE predicate on the "ntp_bps" field.
func NtpBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldNtpBps, v))
}

// DNSQueryPpsEQ applies the EQ predicate on the "dns_query_pps" field.
func DNSQueryPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDNSQueryPps, v))
}

// DNSQueryPpsNEQ applies the NEQ predicate on the "dns_query_pps" field.
func DNSQueryPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldDNSQueryPps, v))
}

// DNSQueryPpsIn applies the In predicate on the "dns_query_pps" field.
func DNSQueryPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldDNSQueryPps, vs...))
}

// DNSQueryPpsNotIn applies the NotIn predicate on the "dns_query_pps" field.
func DNSQueryPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldDNSQueryPps, vs...))
}

// DNSQueryPpsGT applies the GT predicate on the "dns_query_pps" field.
func DNSQueryPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldDNSQueryPps, v))
}

// DNSQueryPpsGTE applies the GTE predicate on the "dns_query_pps" field.
func DNSQueryPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldDNSQueryPps, v))
}

// DNSQueryPpsLT applies the LT predicate on the "dns_query_pps" field.
func DNSQueryPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldDNSQueryPps, v))
}

// DNSQueryPpsLTE applies the LTE predicate on the "dns_query_pps" field.
func DNSQueryPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldDNSQueryPps, v))
}

// DNSQueryBpsEQ applies the EQ predicate on the "dns_query_bps" field.
func DNSQueryBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDNSQueryBps, v))
}

// DNSQueryBpsNEQ applies the NEQ predicate on the "dns_query_bps" field.
func DNSQueryBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldDNSQueryBps, v))
}

// DNSQueryBpsIn applies the In predicate on the "dns_query_bps" field.
func DNSQueryBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldDNSQueryBps, vs...))
}

// DNSQueryBpsNotIn applies the NotIn predicate on the "dns_query_bps" field.
func DNSQueryBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldDNSQueryBps, vs...))
}

// DNSQueryBpsGT applies the GT predicate on the "dns_query_bps" field.
func DNSQueryBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldDNSQueryBps, v))
}

// DNSQueryBpsGTE applies the GTE predicate on the "dns_query_bps" field.
func DNSQueryBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldDNSQueryBps, v))
}

// DNSQueryBpsLT applies the LT predicate on the "dns_query_bps" field.
func DNSQueryBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldDNSQueryBps, v))
}

// DNSQueryBpsLTE applies the LTE predicate on the "dns_query_bps" field.
func DNSQueryBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldDNSQueryBps, v))
}

// DNSAnswerPpsEQ applies the EQ predicate on the "dns_answer_pps" field.
func DNSAnswerPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDNSAnswerPps, v))
}

// DNSAnswerPpsNEQ applies the NEQ predicate on the "dns_answer_pps" field.
func DNSAnswerPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldDNSAnswerPps, v))
}

// DNSAnswerPpsIn applies the In predicate on the "dns_answer_pps" field.
func DNSAnswerPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldDNSAnswerPps, vs...))
}

// DNSAnswerPpsNotIn applies the NotIn predicate on the "dns_answer_pps" field.
func DNSAnswerPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldDNSAnswerPps, vs...))
}

// DNSAnswerPpsGT applies the GT predicate on the "dns_answer_pps" field.
func DNSAnswerPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldDNSAnswerPps, v))
}

// DNSAnswerPpsGTE applies the GTE predicate on the "dns_answer_pps" field.
func DNSAnswerPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldDNSAnswerPps, v))
}

// DNSAnswerPpsLT applies the LT predicate on the "dns_answer_pps" field.
func DNSAnswerPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldDNSAnswerPps, v))
}

// DNSAnswerPpsLTE applies the LTE predicate on the "dns_answer_pps" field.
func DNSAnswerPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldDNSAnswerPps, v))
}

// DNSAnswerBpsEQ applies the EQ predicate on the "dns_answer_bps" field.
func DNSAnswerBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldDNSAnswerBps, v))
}

// DNSAnswerBpsNEQ applies the NEQ predicate on the "dns_answer_bps" field.
func DNSAnswerBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldDNSAnswerBps, v))
}

// DNSAnswerBpsIn applies the In predicate on the "dns_answer_bps" field.
func DNSAnswerBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldDNSAnswerBps, vs...))
}

// DNSAnswerBpsNotIn applies the NotIn predicate on the "dns_answer_bps" field.
func DNSAnswerBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldDNSAnswerBps, vs...))
}

// DNSAnswerBpsGT applies the GT predicate on the "dns_answer_bps" field.
func DNSAnswerBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldDNSAnswerBps, v))
}

// DNSAnswerBpsGTE applies the GTE predicate on the "dns_answer_bps" field.
func DNSAnswerBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldDNSAnswerBps, v))
}

// DNSAnswerBpsLT applies the LT predicate on the "dns_answer_bps" field.
func DNSAnswerBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldDNSAnswerBps, v))
}

// DNSAnswerBpsLTE applies the LTE predicate on the "dns_answer_bps" field.
func DNSAnswerBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldDNSAnswerBps, v))
}

// SsdpBpsEQ applies the EQ predicate on the "ssdp_bps" field.
func SsdpBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSsdpBps, v))
}

// SsdpBpsNEQ applies the NEQ predicate on the "ssdp_bps" field.
func SsdpBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldSsdpBps, v))
}

// SsdpBpsIn applies the In predicate on the "ssdp_bps" field.
func SsdpBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldSsdpBps, vs...))
}

// SsdpBpsNotIn applies the NotIn predicate on the "ssdp_bps" field.
func SsdpBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldSsdpBps, vs...))
}

// SsdpBpsGT applies the GT predicate on the "ssdp_bps" field.
func SsdpBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldSsdpBps, v))
}

// SsdpBpsGTE applies the GTE predicate on the "ssdp_bps" field.
func SsdpBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldSsdpBps, v))
}

// SsdpBpsLT applies the LT predicate on the "ssdp_bps" field.
func SsdpBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldSsdpBps, v))
}

// SsdpBpsLTE applies the LTE predicate on the "ssdp_bps" field.
func SsdpBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldSsdpBps, v))
}

// SsdpPpsEQ applies the EQ predicate on the "ssdp_pps" field.
func SsdpPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldSsdpPps, v))
}

// SsdpPpsNEQ applies the NEQ predicate on the "ssdp_pps" field.
func SsdpPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldSsdpPps, v))
}

// SsdpPpsIn applies the In predicate on the "ssdp_pps" field.
func SsdpPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldSsdpPps, vs...))
}

// SsdpPpsNotIn applies the NotIn predicate on the "ssdp_pps" field.
func SsdpPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldSsdpPps, vs...))
}

// SsdpPpsGT applies the GT predicate on the "ssdp_pps" field.
func SsdpPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldSsdpPps, v))
}

// SsdpPpsGTE applies the GTE predicate on the "ssdp_pps" field.
func SsdpPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldSsdpPps, v))
}

// SsdpPpsLT applies the LT predicate on the "ssdp_pps" field.
func SsdpPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldSsdpPps, v))
}

// SsdpPpsLTE applies the LTE predicate on the "ssdp_pps" field.
func SsdpPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldSsdpPps, v))
}

// UDPPpsEQ applies the EQ predicate on the "udp_pps" field.
func UDPPpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldUDPPps, v))
}

// UDPPpsNEQ applies the NEQ predicate on the "udp_pps" field.
func UDPPpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldUDPPps, v))
}

// UDPPpsIn applies the In predicate on the "udp_pps" field.
func UDPPpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldUDPPps, vs...))
}

// UDPPpsNotIn applies the NotIn predicate on the "udp_pps" field.
func UDPPpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldUDPPps, vs...))
}

// UDPPpsGT applies the GT predicate on the "udp_pps" field.
func UDPPpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldUDPPps, v))
}

// UDPPpsGTE applies the GTE predicate on the "udp_pps" field.
func UDPPpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldUDPPps, v))
}

// UDPPpsLT applies the LT predicate on the "udp_pps" field.
func UDPPpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldUDPPps, v))
}

// UDPPpsLTE applies the LTE predicate on the "udp_pps" field.
func UDPPpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldUDPPps, v))
}

// UDPBpsEQ applies the EQ predicate on the "udp_bps" field.
func UDPBpsEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldUDPBps, v))
}

// UDPBpsNEQ applies the NEQ predicate on the "udp_bps" field.
func UDPBpsNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldUDPBps, v))
}

// UDPBpsIn applies the In predicate on the "udp_bps" field.
func UDPBpsIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldUDPBps, vs...))
}

// UDPBpsNotIn applies the NotIn predicate on the "udp_bps" field.
func UDPBpsNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldUDPBps, vs...))
}

// UDPBpsGT applies the GT predicate on the "udp_bps" field.
func UDPBpsGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldUDPBps, v))
}

// UDPBpsGTE applies the GTE predicate on the "udp_bps" field.
func UDPBpsGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldUDPBps, v))
}

// UDPBpsLT applies the LT predicate on the "udp_bps" field.
func UDPBpsLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldUDPBps, v))
}

// UDPBpsLTE applies the LTE predicate on the "udp_bps" field.
func UDPBpsLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldUDPBps, v))
}

// QPSEQ applies the EQ predicate on the "qps" field.
func QPSEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldQPS, v))
}

// QPSNEQ applies the NEQ predicate on the "qps" field.
func QPSNEQ(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldQPS, v))
}

// QPSIn applies the In predicate on the "qps" field.
func QPSIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldQPS, vs...))
}

// QPSNotIn applies the NotIn predicate on the "qps" field.
func QPSNotIn(vs ...int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldQPS, vs...))
}

// QPSGT applies the GT predicate on the "qps" field.
func QPSGT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldQPS, v))
}

// QPSGTE applies the GTE predicate on the "qps" field.
func QPSGTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldQPS, v))
}

// QPSLT applies the LT predicate on the "qps" field.
func QPSLT(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldQPS, v))
}

// QPSLTE applies the LTE predicate on the "qps" field.
func QPSLTE(v int64) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldQPS, v))
}

// ReceiveCountEQ applies the EQ predicate on the "receive_count" field.
func ReceiveCountEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldReceiveCount, v))
}

// ReceiveCountNEQ applies the NEQ predicate on the "receive_count" field.
func ReceiveCountNEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldReceiveCount, v))
}

// ReceiveCountIn applies the In predicate on the "receive_count" field.
func ReceiveCountIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldReceiveCount, vs...))
}

// ReceiveCountNotIn applies the NotIn predicate on the "receive_count" field.
func ReceiveCountNotIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldReceiveCount, vs...))
}

// ReceiveCountGT applies the GT predicate on the "receive_count" field.
func ReceiveCountGT(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldReceiveCount, v))
}

// ReceiveCountGTE applies the GTE predicate on the "receive_count" field.
func ReceiveCountGTE(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldReceiveCount, v))
}

// ReceiveCountLT applies the LT predicate on the "receive_count" field.
func ReceiveCountLT(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldReceiveCount, v))
}

// ReceiveCountLTE applies the LTE predicate on the "receive_count" field.
func ReceiveCountLTE(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldReceiveCount, v))
}

// IPTypeEQ applies the EQ predicate on the "ip_type" field.
func IPTypeEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldIPType, v))
}

// IPTypeNEQ applies the NEQ predicate on the "ip_type" field.
func IPTypeNEQ(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldIPType, v))
}

// IPTypeIn applies the In predicate on the "ip_type" field.
func IPTypeIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldIPType, vs...))
}

// IPTypeNotIn applies the NotIn predicate on the "ip_type" field.
func IPTypeNotIn(vs ...int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldIPType, vs...))
}

// IPTypeGT applies the GT predicate on the "ip_type" field.
func IPTypeGT(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldIPType, v))
}

// IPTypeGTE applies the GTE predicate on the "ip_type" field.
func IPTypeGTE(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldIPType, v))
}

// IPTypeLT applies the LT predicate on the "ip_type" field.
func IPTypeLT(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldIPType, v))
}

// IPTypeLTE applies the LTE predicate on the "ip_type" field.
func IPTypeLTE(v int) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldIPType, v))
}

// MonitorEQ applies the EQ predicate on the "monitor" field.
func MonitorEQ(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldMonitor, v))
}

// MonitorNEQ applies the NEQ predicate on the "monitor" field.
func MonitorNEQ(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldMonitor, v))
}

// MonitorIn applies the In predicate on the "monitor" field.
func MonitorIn(vs ...string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldMonitor, vs...))
}

// MonitorNotIn applies the NotIn predicate on the "monitor" field.
func MonitorNotIn(vs ...string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldMonitor, vs...))
}

// MonitorGT applies the GT predicate on the "monitor" field.
func MonitorGT(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldMonitor, v))
}

// MonitorGTE applies the GTE predicate on the "monitor" field.
func MonitorGTE(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldMonitor, v))
}

// MonitorLT applies the LT predicate on the "monitor" field.
func MonitorLT(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldMonitor, v))
}

// MonitorLTE applies the LTE predicate on the "monitor" field.
func MonitorLTE(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldMonitor, v))
}

// MonitorContains applies the Contains predicate on the "monitor" field.
func MonitorContains(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldContains(FieldMonitor, v))
}

// MonitorHasPrefix applies the HasPrefix predicate on the "monitor" field.
func MonitorHasPrefix(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldHasPrefix(FieldMonitor, v))
}

// MonitorHasSuffix applies the HasSuffix predicate on the "monitor" field.
func MonitorHasSuffix(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldHasSuffix(FieldMonitor, v))
}

// MonitorIsNil applies the IsNil predicate on the "monitor" field.
func MonitorIsNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIsNull(FieldMonitor))
}

// MonitorNotNil applies the NotNil predicate on the "monitor" field.
func MonitorNotNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotNull(FieldMonitor))
}

// MonitorEqualFold applies the EqualFold predicate on the "monitor" field.
func MonitorEqualFold(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEqualFold(FieldMonitor, v))
}

// MonitorContainsFold applies the ContainsFold predicate on the "monitor" field.
func MonitorContainsFold(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldContainsFold(FieldMonitor, v))
}

// ProductEQ applies the EQ predicate on the "product" field.
func ProductEQ(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldProduct, v))
}

// ProductNEQ applies the NEQ predicate on the "product" field.
func ProductNEQ(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldProduct, v))
}

// ProductIn applies the In predicate on the "product" field.
func ProductIn(vs ...string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldProduct, vs...))
}

// ProductNotIn applies the NotIn predicate on the "product" field.
func ProductNotIn(vs ...string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldProduct, vs...))
}

// ProductGT applies the GT predicate on the "product" field.
func ProductGT(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldProduct, v))
}

// ProductGTE applies the GTE predicate on the "product" field.
func ProductGTE(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldProduct, v))
}

// ProductLT applies the LT predicate on the "product" field.
func ProductLT(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldProduct, v))
}

// ProductLTE applies the LTE predicate on the "product" field.
func ProductLTE(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldProduct, v))
}

// ProductContains applies the Contains predicate on the "product" field.
func ProductContains(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldContains(FieldProduct, v))
}

// ProductHasPrefix applies the HasPrefix predicate on the "product" field.
func ProductHasPrefix(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldHasPrefix(FieldProduct, v))
}

// ProductHasSuffix applies the HasSuffix predicate on the "product" field.
func ProductHasSuffix(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldHasSuffix(FieldProduct, v))
}

// ProductIsNil applies the IsNil predicate on the "product" field.
func ProductIsNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIsNull(FieldProduct))
}

// ProductNotNil applies the NotNil predicate on the "product" field.
func ProductNotNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotNull(FieldProduct))
}

// ProductEqualFold applies the EqualFold predicate on the "product" field.
func ProductEqualFold(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEqualFold(FieldProduct, v))
}

// ProductContainsFold applies the ContainsFold predicate on the "product" field.
func ProductContainsFold(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldContainsFold(FieldProduct, v))
}

// HostEQ applies the EQ predicate on the "host" field.
func HostEQ(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEQ(FieldHost, v))
}

// HostNEQ applies the NEQ predicate on the "host" field.
func HostNEQ(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNEQ(FieldHost, v))
}

// HostIn applies the In predicate on the "host" field.
func HostIn(vs ...string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIn(FieldHost, vs...))
}

// HostNotIn applies the NotIn predicate on the "host" field.
func HostNotIn(vs ...string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotIn(FieldHost, vs...))
}

// HostGT applies the GT predicate on the "host" field.
func HostGT(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGT(FieldHost, v))
}

// HostGTE applies the GTE predicate on the "host" field.
func HostGTE(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldGTE(FieldHost, v))
}

// HostLT applies the LT predicate on the "host" field.
func HostLT(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLT(FieldHost, v))
}

// HostLTE applies the LTE predicate on the "host" field.
func HostLTE(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldLTE(FieldHost, v))
}

// HostContains applies the Contains predicate on the "host" field.
func HostContains(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldContains(FieldHost, v))
}

// HostHasPrefix applies the HasPrefix predicate on the "host" field.
func HostHasPrefix(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldHasPrefix(FieldHost, v))
}

// HostHasSuffix applies the HasSuffix predicate on the "host" field.
func HostHasSuffix(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldHasSuffix(FieldHost, v))
}

// HostIsNil applies the IsNil predicate on the "host" field.
func HostIsNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldIsNull(FieldHost))
}

// HostNotNil applies the NotNil predicate on the "host" field.
func HostNotNil() predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldNotNull(FieldHost))
}

// HostEqualFold applies the EqualFold predicate on the "host" field.
func HostEqualFold(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldEqualFold(FieldHost, v))
}

// HostContainsFold applies the ContainsFold predicate on the "host" field.
func HostContainsFold(v string) predicate.SpectrumData {
	return predicate.SpectrumData(sql.FieldContainsFold(FieldHost, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.SpectrumData {
	return predicate.SpectrumData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.SpectrumData {
	return predicate.SpectrumData(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasSpectrumAlert applies the HasEdge predicate on the "spectrum_alert" edge.
func HasSpectrumAlert() predicate.SpectrumData {
	return predicate.SpectrumData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, SpectrumAlertTable, SpectrumAlertColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSpectrumAlertWith applies the HasEdge predicate on the "spectrum_alert" edge with a given conditions (other predicates).
func HasSpectrumAlertWith(preds ...predicate.SpectrumAlert) predicate.SpectrumData {
	return predicate.SpectrumData(func(s *sql.Selector) {
		step := newSpectrumAlertStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SpectrumData) predicate.SpectrumData {
	return predicate.SpectrumData(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SpectrumData) predicate.SpectrumData {
	return predicate.SpectrumData(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SpectrumData) predicate.SpectrumData {
	return predicate.SpectrumData(sql.NotPredicates(p))
}
