// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"meta/app/ent/wofangalert"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WofangAlertUpdate is the builder for updating WofangAlert entities.
type WofangAlertUpdate struct {
	config
	hooks    []Hook
	mutation *WofangAlertMutation
}

// Where appends a list predicates to the WofangAlertUpdate builder.
func (wau *WofangAlertUpdate) Where(ps ...predicate.WofangAlert) *WofangAlertUpdate {
	wau.mutation.Where(ps...)
	return wau
}

// SetTenantID sets the "tenant_id" field.
func (wau *WofangAlertUpdate) SetTenantID(i int) *WofangAlertUpdate {
	wau.mutation.SetTenantID(i)
	return wau
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableTenantID(i *int) *WofangAlertUpdate {
	if i != nil {
		wau.SetTenantID(*i)
	}
	return wau
}

// ClearTenantID clears the value of the "tenant_id" field.
func (wau *WofangAlertUpdate) ClearTenantID() *WofangAlertUpdate {
	wau.mutation.ClearTenantID()
	return wau
}

// SetUpdatedAt sets the "updated_at" field.
func (wau *WofangAlertUpdate) SetUpdatedAt(t time.Time) *WofangAlertUpdate {
	wau.mutation.SetUpdatedAt(t)
	return wau
}

// SetRemark sets the "remark" field.
func (wau *WofangAlertUpdate) SetRemark(s string) *WofangAlertUpdate {
	wau.mutation.SetRemark(s)
	return wau
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableRemark(s *string) *WofangAlertUpdate {
	if s != nil {
		wau.SetRemark(*s)
	}
	return wau
}

// ClearRemark clears the value of the "remark" field.
func (wau *WofangAlertUpdate) ClearRemark() *WofangAlertUpdate {
	wau.mutation.ClearRemark()
	return wau
}

// SetAttackStatus sets the "attack_status" field.
func (wau *WofangAlertUpdate) SetAttackStatus(i int) *WofangAlertUpdate {
	wau.mutation.ResetAttackStatus()
	wau.mutation.SetAttackStatus(i)
	return wau
}

// SetNillableAttackStatus sets the "attack_status" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableAttackStatus(i *int) *WofangAlertUpdate {
	if i != nil {
		wau.SetAttackStatus(*i)
	}
	return wau
}

// AddAttackStatus adds i to the "attack_status" field.
func (wau *WofangAlertUpdate) AddAttackStatus(i int) *WofangAlertUpdate {
	wau.mutation.AddAttackStatus(i)
	return wau
}

// SetAttackType sets the "attack_type" field.
func (wau *WofangAlertUpdate) SetAttackType(s *[]string) *WofangAlertUpdate {
	wau.mutation.SetAttackType(s)
	return wau
}

// ClearAttackType clears the value of the "attack_type" field.
func (wau *WofangAlertUpdate) ClearAttackType() *WofangAlertUpdate {
	wau.mutation.ClearAttackType()
	return wau
}

// SetDeviceIP sets the "device_ip" field.
func (wau *WofangAlertUpdate) SetDeviceIP(s string) *WofangAlertUpdate {
	wau.mutation.SetDeviceIP(s)
	return wau
}

// SetNillableDeviceIP sets the "device_ip" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableDeviceIP(s *string) *WofangAlertUpdate {
	if s != nil {
		wau.SetDeviceIP(*s)
	}
	return wau
}

// SetZoneIP sets the "zone_ip" field.
func (wau *WofangAlertUpdate) SetZoneIP(s string) *WofangAlertUpdate {
	wau.mutation.SetZoneIP(s)
	return wau
}

// SetNillableZoneIP sets the "zone_ip" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableZoneIP(s *string) *WofangAlertUpdate {
	if s != nil {
		wau.SetZoneIP(*s)
	}
	return wau
}

// SetAttackID sets the "attack_id" field.
func (wau *WofangAlertUpdate) SetAttackID(i int) *WofangAlertUpdate {
	wau.mutation.ResetAttackID()
	wau.mutation.SetAttackID(i)
	return wau
}

// SetNillableAttackID sets the "attack_id" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableAttackID(i *int) *WofangAlertUpdate {
	if i != nil {
		wau.SetAttackID(*i)
	}
	return wau
}

// AddAttackID adds i to the "attack_id" field.
func (wau *WofangAlertUpdate) AddAttackID(i int) *WofangAlertUpdate {
	wau.mutation.AddAttackID(i)
	return wau
}

// SetStartTime sets the "start_time" field.
func (wau *WofangAlertUpdate) SetStartTime(t time.Time) *WofangAlertUpdate {
	wau.mutation.SetStartTime(t)
	return wau
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableStartTime(t *time.Time) *WofangAlertUpdate {
	if t != nil {
		wau.SetStartTime(*t)
	}
	return wau
}

// SetEndTime sets the "end_time" field.
func (wau *WofangAlertUpdate) SetEndTime(t time.Time) *WofangAlertUpdate {
	wau.mutation.SetEndTime(t)
	return wau
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableEndTime(t *time.Time) *WofangAlertUpdate {
	if t != nil {
		wau.SetEndTime(*t)
	}
	return wau
}

// ClearEndTime clears the value of the "end_time" field.
func (wau *WofangAlertUpdate) ClearEndTime() *WofangAlertUpdate {
	wau.mutation.ClearEndTime()
	return wau
}

// SetMaxDropBps sets the "max_drop_bps" field.
func (wau *WofangAlertUpdate) SetMaxDropBps(i int64) *WofangAlertUpdate {
	wau.mutation.ResetMaxDropBps()
	wau.mutation.SetMaxDropBps(i)
	return wau
}

// SetNillableMaxDropBps sets the "max_drop_bps" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableMaxDropBps(i *int64) *WofangAlertUpdate {
	if i != nil {
		wau.SetMaxDropBps(*i)
	}
	return wau
}

// AddMaxDropBps adds i to the "max_drop_bps" field.
func (wau *WofangAlertUpdate) AddMaxDropBps(i int64) *WofangAlertUpdate {
	wau.mutation.AddMaxDropBps(i)
	return wau
}

// SetMaxInBps sets the "max_in_bps" field.
func (wau *WofangAlertUpdate) SetMaxInBps(i int64) *WofangAlertUpdate {
	wau.mutation.ResetMaxInBps()
	wau.mutation.SetMaxInBps(i)
	return wau
}

// SetNillableMaxInBps sets the "max_in_bps" field if the given value is not nil.
func (wau *WofangAlertUpdate) SetNillableMaxInBps(i *int64) *WofangAlertUpdate {
	if i != nil {
		wau.SetMaxInBps(*i)
	}
	return wau
}

// AddMaxInBps adds i to the "max_in_bps" field.
func (wau *WofangAlertUpdate) AddMaxInBps(i int64) *WofangAlertUpdate {
	wau.mutation.AddMaxInBps(i)
	return wau
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (wau *WofangAlertUpdate) SetTenant(t *Tenant) *WofangAlertUpdate {
	return wau.SetTenantID(t.ID)
}

// Mutation returns the WofangAlertMutation object of the builder.
func (wau *WofangAlertUpdate) Mutation() *WofangAlertMutation {
	return wau.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (wau *WofangAlertUpdate) ClearTenant() *WofangAlertUpdate {
	wau.mutation.ClearTenant()
	return wau
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wau *WofangAlertUpdate) Save(ctx context.Context) (int, error) {
	if err := wau.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, wau.sqlSave, wau.mutation, wau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wau *WofangAlertUpdate) SaveX(ctx context.Context) int {
	affected, err := wau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wau *WofangAlertUpdate) Exec(ctx context.Context) error {
	_, err := wau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wau *WofangAlertUpdate) ExecX(ctx context.Context) {
	if err := wau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wau *WofangAlertUpdate) defaults() error {
	if _, ok := wau.mutation.UpdatedAt(); !ok {
		if wofangalert.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized wofangalert.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := wofangalert.UpdateDefaultUpdatedAt()
		wau.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (wau *WofangAlertUpdate) check() error {
	if v, ok := wau.mutation.Remark(); ok {
		if err := wofangalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "WofangAlert.remark": %w`, err)}
		}
	}
	return nil
}

func (wau *WofangAlertUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := wau.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(wofangalert.Table, wofangalert.Columns, sqlgraph.NewFieldSpec(wofangalert.FieldID, field.TypeInt))
	if ps := wau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wau.mutation.UpdatedAt(); ok {
		_spec.SetField(wofangalert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wau.mutation.Remark(); ok {
		_spec.SetField(wofangalert.FieldRemark, field.TypeString, value)
	}
	if wau.mutation.RemarkCleared() {
		_spec.ClearField(wofangalert.FieldRemark, field.TypeString)
	}
	if value, ok := wau.mutation.AttackStatus(); ok {
		_spec.SetField(wofangalert.FieldAttackStatus, field.TypeInt, value)
	}
	if value, ok := wau.mutation.AddedAttackStatus(); ok {
		_spec.AddField(wofangalert.FieldAttackStatus, field.TypeInt, value)
	}
	if value, ok := wau.mutation.AttackType(); ok {
		_spec.SetField(wofangalert.FieldAttackType, field.TypeJSON, value)
	}
	if wau.mutation.AttackTypeCleared() {
		_spec.ClearField(wofangalert.FieldAttackType, field.TypeJSON)
	}
	if value, ok := wau.mutation.DeviceIP(); ok {
		_spec.SetField(wofangalert.FieldDeviceIP, field.TypeString, value)
	}
	if value, ok := wau.mutation.ZoneIP(); ok {
		_spec.SetField(wofangalert.FieldZoneIP, field.TypeString, value)
	}
	if value, ok := wau.mutation.AttackID(); ok {
		_spec.SetField(wofangalert.FieldAttackID, field.TypeInt, value)
	}
	if value, ok := wau.mutation.AddedAttackID(); ok {
		_spec.AddField(wofangalert.FieldAttackID, field.TypeInt, value)
	}
	if value, ok := wau.mutation.StartTime(); ok {
		_spec.SetField(wofangalert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := wau.mutation.EndTime(); ok {
		_spec.SetField(wofangalert.FieldEndTime, field.TypeTime, value)
	}
	if wau.mutation.EndTimeCleared() {
		_spec.ClearField(wofangalert.FieldEndTime, field.TypeTime)
	}
	if value, ok := wau.mutation.MaxDropBps(); ok {
		_spec.SetField(wofangalert.FieldMaxDropBps, field.TypeInt64, value)
	}
	if value, ok := wau.mutation.AddedMaxDropBps(); ok {
		_spec.AddField(wofangalert.FieldMaxDropBps, field.TypeInt64, value)
	}
	if value, ok := wau.mutation.MaxInBps(); ok {
		_spec.SetField(wofangalert.FieldMaxInBps, field.TypeInt64, value)
	}
	if value, ok := wau.mutation.AddedMaxInBps(); ok {
		_spec.AddField(wofangalert.FieldMaxInBps, field.TypeInt64, value)
	}
	if wau.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofangalert.TenantTable,
			Columns: []string{wofangalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wau.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofangalert.TenantTable,
			Columns: []string{wofangalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, wau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wofangalert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wau.mutation.done = true
	return n, nil
}

// WofangAlertUpdateOne is the builder for updating a single WofangAlert entity.
type WofangAlertUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *WofangAlertMutation
}

// SetTenantID sets the "tenant_id" field.
func (wauo *WofangAlertUpdateOne) SetTenantID(i int) *WofangAlertUpdateOne {
	wauo.mutation.SetTenantID(i)
	return wauo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableTenantID(i *int) *WofangAlertUpdateOne {
	if i != nil {
		wauo.SetTenantID(*i)
	}
	return wauo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (wauo *WofangAlertUpdateOne) ClearTenantID() *WofangAlertUpdateOne {
	wauo.mutation.ClearTenantID()
	return wauo
}

// SetUpdatedAt sets the "updated_at" field.
func (wauo *WofangAlertUpdateOne) SetUpdatedAt(t time.Time) *WofangAlertUpdateOne {
	wauo.mutation.SetUpdatedAt(t)
	return wauo
}

// SetRemark sets the "remark" field.
func (wauo *WofangAlertUpdateOne) SetRemark(s string) *WofangAlertUpdateOne {
	wauo.mutation.SetRemark(s)
	return wauo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableRemark(s *string) *WofangAlertUpdateOne {
	if s != nil {
		wauo.SetRemark(*s)
	}
	return wauo
}

// ClearRemark clears the value of the "remark" field.
func (wauo *WofangAlertUpdateOne) ClearRemark() *WofangAlertUpdateOne {
	wauo.mutation.ClearRemark()
	return wauo
}

// SetAttackStatus sets the "attack_status" field.
func (wauo *WofangAlertUpdateOne) SetAttackStatus(i int) *WofangAlertUpdateOne {
	wauo.mutation.ResetAttackStatus()
	wauo.mutation.SetAttackStatus(i)
	return wauo
}

// SetNillableAttackStatus sets the "attack_status" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableAttackStatus(i *int) *WofangAlertUpdateOne {
	if i != nil {
		wauo.SetAttackStatus(*i)
	}
	return wauo
}

// AddAttackStatus adds i to the "attack_status" field.
func (wauo *WofangAlertUpdateOne) AddAttackStatus(i int) *WofangAlertUpdateOne {
	wauo.mutation.AddAttackStatus(i)
	return wauo
}

// SetAttackType sets the "attack_type" field.
func (wauo *WofangAlertUpdateOne) SetAttackType(s *[]string) *WofangAlertUpdateOne {
	wauo.mutation.SetAttackType(s)
	return wauo
}

// ClearAttackType clears the value of the "attack_type" field.
func (wauo *WofangAlertUpdateOne) ClearAttackType() *WofangAlertUpdateOne {
	wauo.mutation.ClearAttackType()
	return wauo
}

// SetDeviceIP sets the "device_ip" field.
func (wauo *WofangAlertUpdateOne) SetDeviceIP(s string) *WofangAlertUpdateOne {
	wauo.mutation.SetDeviceIP(s)
	return wauo
}

// SetNillableDeviceIP sets the "device_ip" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableDeviceIP(s *string) *WofangAlertUpdateOne {
	if s != nil {
		wauo.SetDeviceIP(*s)
	}
	return wauo
}

// SetZoneIP sets the "zone_ip" field.
func (wauo *WofangAlertUpdateOne) SetZoneIP(s string) *WofangAlertUpdateOne {
	wauo.mutation.SetZoneIP(s)
	return wauo
}

// SetNillableZoneIP sets the "zone_ip" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableZoneIP(s *string) *WofangAlertUpdateOne {
	if s != nil {
		wauo.SetZoneIP(*s)
	}
	return wauo
}

// SetAttackID sets the "attack_id" field.
func (wauo *WofangAlertUpdateOne) SetAttackID(i int) *WofangAlertUpdateOne {
	wauo.mutation.ResetAttackID()
	wauo.mutation.SetAttackID(i)
	return wauo
}

// SetNillableAttackID sets the "attack_id" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableAttackID(i *int) *WofangAlertUpdateOne {
	if i != nil {
		wauo.SetAttackID(*i)
	}
	return wauo
}

// AddAttackID adds i to the "attack_id" field.
func (wauo *WofangAlertUpdateOne) AddAttackID(i int) *WofangAlertUpdateOne {
	wauo.mutation.AddAttackID(i)
	return wauo
}

// SetStartTime sets the "start_time" field.
func (wauo *WofangAlertUpdateOne) SetStartTime(t time.Time) *WofangAlertUpdateOne {
	wauo.mutation.SetStartTime(t)
	return wauo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableStartTime(t *time.Time) *WofangAlertUpdateOne {
	if t != nil {
		wauo.SetStartTime(*t)
	}
	return wauo
}

// SetEndTime sets the "end_time" field.
func (wauo *WofangAlertUpdateOne) SetEndTime(t time.Time) *WofangAlertUpdateOne {
	wauo.mutation.SetEndTime(t)
	return wauo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableEndTime(t *time.Time) *WofangAlertUpdateOne {
	if t != nil {
		wauo.SetEndTime(*t)
	}
	return wauo
}

// ClearEndTime clears the value of the "end_time" field.
func (wauo *WofangAlertUpdateOne) ClearEndTime() *WofangAlertUpdateOne {
	wauo.mutation.ClearEndTime()
	return wauo
}

// SetMaxDropBps sets the "max_drop_bps" field.
func (wauo *WofangAlertUpdateOne) SetMaxDropBps(i int64) *WofangAlertUpdateOne {
	wauo.mutation.ResetMaxDropBps()
	wauo.mutation.SetMaxDropBps(i)
	return wauo
}

// SetNillableMaxDropBps sets the "max_drop_bps" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableMaxDropBps(i *int64) *WofangAlertUpdateOne {
	if i != nil {
		wauo.SetMaxDropBps(*i)
	}
	return wauo
}

// AddMaxDropBps adds i to the "max_drop_bps" field.
func (wauo *WofangAlertUpdateOne) AddMaxDropBps(i int64) *WofangAlertUpdateOne {
	wauo.mutation.AddMaxDropBps(i)
	return wauo
}

// SetMaxInBps sets the "max_in_bps" field.
func (wauo *WofangAlertUpdateOne) SetMaxInBps(i int64) *WofangAlertUpdateOne {
	wauo.mutation.ResetMaxInBps()
	wauo.mutation.SetMaxInBps(i)
	return wauo
}

// SetNillableMaxInBps sets the "max_in_bps" field if the given value is not nil.
func (wauo *WofangAlertUpdateOne) SetNillableMaxInBps(i *int64) *WofangAlertUpdateOne {
	if i != nil {
		wauo.SetMaxInBps(*i)
	}
	return wauo
}

// AddMaxInBps adds i to the "max_in_bps" field.
func (wauo *WofangAlertUpdateOne) AddMaxInBps(i int64) *WofangAlertUpdateOne {
	wauo.mutation.AddMaxInBps(i)
	return wauo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (wauo *WofangAlertUpdateOne) SetTenant(t *Tenant) *WofangAlertUpdateOne {
	return wauo.SetTenantID(t.ID)
}

// Mutation returns the WofangAlertMutation object of the builder.
func (wauo *WofangAlertUpdateOne) Mutation() *WofangAlertMutation {
	return wauo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (wauo *WofangAlertUpdateOne) ClearTenant() *WofangAlertUpdateOne {
	wauo.mutation.ClearTenant()
	return wauo
}

// Where appends a list predicates to the WofangAlertUpdate builder.
func (wauo *WofangAlertUpdateOne) Where(ps ...predicate.WofangAlert) *WofangAlertUpdateOne {
	wauo.mutation.Where(ps...)
	return wauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wauo *WofangAlertUpdateOne) Select(field string, fields ...string) *WofangAlertUpdateOne {
	wauo.fields = append([]string{field}, fields...)
	return wauo
}

// Save executes the query and returns the updated WofangAlert entity.
func (wauo *WofangAlertUpdateOne) Save(ctx context.Context) (*WofangAlert, error) {
	if err := wauo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, wauo.sqlSave, wauo.mutation, wauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wauo *WofangAlertUpdateOne) SaveX(ctx context.Context) *WofangAlert {
	node, err := wauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wauo *WofangAlertUpdateOne) Exec(ctx context.Context) error {
	_, err := wauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wauo *WofangAlertUpdateOne) ExecX(ctx context.Context) {
	if err := wauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wauo *WofangAlertUpdateOne) defaults() error {
	if _, ok := wauo.mutation.UpdatedAt(); !ok {
		if wofangalert.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized wofangalert.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := wofangalert.UpdateDefaultUpdatedAt()
		wauo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (wauo *WofangAlertUpdateOne) check() error {
	if v, ok := wauo.mutation.Remark(); ok {
		if err := wofangalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "WofangAlert.remark": %w`, err)}
		}
	}
	return nil
}

func (wauo *WofangAlertUpdateOne) sqlSave(ctx context.Context) (_node *WofangAlert, err error) {
	if err := wauo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(wofangalert.Table, wofangalert.Columns, sqlgraph.NewFieldSpec(wofangalert.FieldID, field.TypeInt))
	id, ok := wauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "WofangAlert.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wofangalert.FieldID)
		for _, f := range fields {
			if !wofangalert.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != wofangalert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wauo.mutation.UpdatedAt(); ok {
		_spec.SetField(wofangalert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wauo.mutation.Remark(); ok {
		_spec.SetField(wofangalert.FieldRemark, field.TypeString, value)
	}
	if wauo.mutation.RemarkCleared() {
		_spec.ClearField(wofangalert.FieldRemark, field.TypeString)
	}
	if value, ok := wauo.mutation.AttackStatus(); ok {
		_spec.SetField(wofangalert.FieldAttackStatus, field.TypeInt, value)
	}
	if value, ok := wauo.mutation.AddedAttackStatus(); ok {
		_spec.AddField(wofangalert.FieldAttackStatus, field.TypeInt, value)
	}
	if value, ok := wauo.mutation.AttackType(); ok {
		_spec.SetField(wofangalert.FieldAttackType, field.TypeJSON, value)
	}
	if wauo.mutation.AttackTypeCleared() {
		_spec.ClearField(wofangalert.FieldAttackType, field.TypeJSON)
	}
	if value, ok := wauo.mutation.DeviceIP(); ok {
		_spec.SetField(wofangalert.FieldDeviceIP, field.TypeString, value)
	}
	if value, ok := wauo.mutation.ZoneIP(); ok {
		_spec.SetField(wofangalert.FieldZoneIP, field.TypeString, value)
	}
	if value, ok := wauo.mutation.AttackID(); ok {
		_spec.SetField(wofangalert.FieldAttackID, field.TypeInt, value)
	}
	if value, ok := wauo.mutation.AddedAttackID(); ok {
		_spec.AddField(wofangalert.FieldAttackID, field.TypeInt, value)
	}
	if value, ok := wauo.mutation.StartTime(); ok {
		_spec.SetField(wofangalert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := wauo.mutation.EndTime(); ok {
		_spec.SetField(wofangalert.FieldEndTime, field.TypeTime, value)
	}
	if wauo.mutation.EndTimeCleared() {
		_spec.ClearField(wofangalert.FieldEndTime, field.TypeTime)
	}
	if value, ok := wauo.mutation.MaxDropBps(); ok {
		_spec.SetField(wofangalert.FieldMaxDropBps, field.TypeInt64, value)
	}
	if value, ok := wauo.mutation.AddedMaxDropBps(); ok {
		_spec.AddField(wofangalert.FieldMaxDropBps, field.TypeInt64, value)
	}
	if value, ok := wauo.mutation.MaxInBps(); ok {
		_spec.SetField(wofangalert.FieldMaxInBps, field.TypeInt64, value)
	}
	if value, ok := wauo.mutation.AddedMaxInBps(); ok {
		_spec.AddField(wofangalert.FieldMaxInBps, field.TypeInt64, value)
	}
	if wauo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofangalert.TenantTable,
			Columns: []string{wofangalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wauo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofangalert.TenantTable,
			Columns: []string{wofangalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &WofangAlert{config: wauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wofangalert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wauo.mutation.done = true
	return _node, nil
}
