// Code generated by ent, DO NOT EDIT.

package strategy

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the strategy type in the database.
	Label = "strategy"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldEnabled holds the string denoting the enabled field in the database.
	FieldEnabled = "enabled"
	// FieldSystem holds the string denoting the system field in the database.
	FieldSystem = "system"
	// FieldBps holds the string denoting the bps field in the database.
	FieldBps = "bps"
	// FieldPps holds the string denoting the pps field in the database.
	FieldPps = "pps"
	// FieldBpsCount holds the string denoting the bps_count field in the database.
	FieldBpsCount = "bps_count"
	// FieldPpsCount holds the string denoting the pps_count field in the database.
	FieldPpsCount = "pps_count"
	// FieldIspCode holds the string denoting the isp_code field in the database.
	FieldIspCode = "isp_code"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeStrategyAlerts holds the string denoting the strategy_alerts edge name in mutations.
	EdgeStrategyAlerts = "strategy_alerts"
	// Table holds the table name of the strategy in the database.
	Table = "strategies"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "strategies"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// StrategyAlertsTable is the table that holds the strategy_alerts relation/edge.
	StrategyAlertsTable = "spectrum_alerts"
	// StrategyAlertsInverseTable is the table name for the SpectrumAlert entity.
	// It exists in this package in order to avoid circular dependency with the "spectrumalert" package.
	StrategyAlertsInverseTable = "spectrum_alerts"
	// StrategyAlertsColumn is the table column denoting the strategy_alerts relation/edge.
	StrategyAlertsColumn = "strategy_id"
)

// Columns holds all SQL columns for strategy fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldName,
	FieldType,
	FieldEnabled,
	FieldSystem,
	FieldBps,
	FieldPps,
	FieldBpsCount,
	FieldPpsCount,
	FieldIspCode,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the Strategy queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByEnabled orders the results by the enabled field.
func ByEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnabled, opts...).ToFunc()
}

// BySystem orders the results by the system field.
func BySystem(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSystem, opts...).ToFunc()
}

// ByBps orders the results by the bps field.
func ByBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBps, opts...).ToFunc()
}

// ByPps orders the results by the pps field.
func ByPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPps, opts...).ToFunc()
}

// ByBpsCount orders the results by the bps_count field.
func ByBpsCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBpsCount, opts...).ToFunc()
}

// ByPpsCount orders the results by the pps_count field.
func ByPpsCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPpsCount, opts...).ToFunc()
}

// ByIspCode orders the results by the isp_code field.
func ByIspCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIspCode, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// ByStrategyAlertsCount orders the results by strategy_alerts count.
func ByStrategyAlertsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newStrategyAlertsStep(), opts...)
	}
}

// ByStrategyAlerts orders the results by strategy_alerts terms.
func ByStrategyAlerts(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newStrategyAlertsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newStrategyAlertsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(StrategyAlertsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, StrategyAlertsTable, StrategyAlertsColumn),
	)
}
