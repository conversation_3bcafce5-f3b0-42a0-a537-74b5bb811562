// Code generated by ent, DO NOT EDIT.

package strategy

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldRemark, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldName, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldType, v))
}

// Enabled applies equality check predicate on the "enabled" field. It's identical to EnabledEQ.
func Enabled(v bool) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldEnabled, v))
}

// System applies equality check predicate on the "system" field. It's identical to SystemEQ.
func System(v bool) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldSystem, v))
}

// Bps applies equality check predicate on the "bps" field. It's identical to BpsEQ.
func Bps(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldBps, v))
}

// Pps applies equality check predicate on the "pps" field. It's identical to PpsEQ.
func Pps(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldPps, v))
}

// BpsCount applies equality check predicate on the "bps_count" field. It's identical to BpsCountEQ.
func BpsCount(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldBpsCount, v))
}

// PpsCount applies equality check predicate on the "pps_count" field. It's identical to PpsCountEQ.
func PpsCount(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldPpsCount, v))
}

// IspCode applies equality check predicate on the "isp_code" field. It's identical to IspCodeEQ.
func IspCode(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldIspCode, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.Strategy {
	return predicate.Strategy(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.Strategy {
	return predicate.Strategy(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.Strategy {
	return predicate.Strategy(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.Strategy {
	return predicate.Strategy(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldContainsFold(FieldRemark, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Strategy {
	return predicate.Strategy(sql.FieldContainsFold(FieldName, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldType, v))
}

// EnabledEQ applies the EQ predicate on the "enabled" field.
func EnabledEQ(v bool) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldEnabled, v))
}

// EnabledNEQ applies the NEQ predicate on the "enabled" field.
func EnabledNEQ(v bool) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldEnabled, v))
}

// SystemEQ applies the EQ predicate on the "system" field.
func SystemEQ(v bool) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldSystem, v))
}

// SystemNEQ applies the NEQ predicate on the "system" field.
func SystemNEQ(v bool) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldSystem, v))
}

// BpsEQ applies the EQ predicate on the "bps" field.
func BpsEQ(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldBps, v))
}

// BpsNEQ applies the NEQ predicate on the "bps" field.
func BpsNEQ(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldBps, v))
}

// BpsIn applies the In predicate on the "bps" field.
func BpsIn(vs ...int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldBps, vs...))
}

// BpsNotIn applies the NotIn predicate on the "bps" field.
func BpsNotIn(vs ...int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldBps, vs...))
}

// BpsGT applies the GT predicate on the "bps" field.
func BpsGT(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldBps, v))
}

// BpsGTE applies the GTE predicate on the "bps" field.
func BpsGTE(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldBps, v))
}

// BpsLT applies the LT predicate on the "bps" field.
func BpsLT(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldBps, v))
}

// BpsLTE applies the LTE predicate on the "bps" field.
func BpsLTE(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldBps, v))
}

// PpsEQ applies the EQ predicate on the "pps" field.
func PpsEQ(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldPps, v))
}

// PpsNEQ applies the NEQ predicate on the "pps" field.
func PpsNEQ(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldPps, v))
}

// PpsIn applies the In predicate on the "pps" field.
func PpsIn(vs ...int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldPps, vs...))
}

// PpsNotIn applies the NotIn predicate on the "pps" field.
func PpsNotIn(vs ...int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldPps, vs...))
}

// PpsGT applies the GT predicate on the "pps" field.
func PpsGT(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldPps, v))
}

// PpsGTE applies the GTE predicate on the "pps" field.
func PpsGTE(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldPps, v))
}

// PpsLT applies the LT predicate on the "pps" field.
func PpsLT(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldPps, v))
}

// PpsLTE applies the LTE predicate on the "pps" field.
func PpsLTE(v int64) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldPps, v))
}

// BpsCountEQ applies the EQ predicate on the "bps_count" field.
func BpsCountEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldBpsCount, v))
}

// BpsCountNEQ applies the NEQ predicate on the "bps_count" field.
func BpsCountNEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldBpsCount, v))
}

// BpsCountIn applies the In predicate on the "bps_count" field.
func BpsCountIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldBpsCount, vs...))
}

// BpsCountNotIn applies the NotIn predicate on the "bps_count" field.
func BpsCountNotIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldBpsCount, vs...))
}

// BpsCountGT applies the GT predicate on the "bps_count" field.
func BpsCountGT(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldBpsCount, v))
}

// BpsCountGTE applies the GTE predicate on the "bps_count" field.
func BpsCountGTE(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldBpsCount, v))
}

// BpsCountLT applies the LT predicate on the "bps_count" field.
func BpsCountLT(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldBpsCount, v))
}

// BpsCountLTE applies the LTE predicate on the "bps_count" field.
func BpsCountLTE(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldBpsCount, v))
}

// PpsCountEQ applies the EQ predicate on the "pps_count" field.
func PpsCountEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldPpsCount, v))
}

// PpsCountNEQ applies the NEQ predicate on the "pps_count" field.
func PpsCountNEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldPpsCount, v))
}

// PpsCountIn applies the In predicate on the "pps_count" field.
func PpsCountIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldPpsCount, vs...))
}

// PpsCountNotIn applies the NotIn predicate on the "pps_count" field.
func PpsCountNotIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldPpsCount, vs...))
}

// PpsCountGT applies the GT predicate on the "pps_count" field.
func PpsCountGT(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldPpsCount, v))
}

// PpsCountGTE applies the GTE predicate on the "pps_count" field.
func PpsCountGTE(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldPpsCount, v))
}

// PpsCountLT applies the LT predicate on the "pps_count" field.
func PpsCountLT(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldPpsCount, v))
}

// PpsCountLTE applies the LTE predicate on the "pps_count" field.
func PpsCountLTE(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldPpsCount, v))
}

// IspCodeEQ applies the EQ predicate on the "isp_code" field.
func IspCodeEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldEQ(FieldIspCode, v))
}

// IspCodeNEQ applies the NEQ predicate on the "isp_code" field.
func IspCodeNEQ(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNEQ(FieldIspCode, v))
}

// IspCodeIn applies the In predicate on the "isp_code" field.
func IspCodeIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldIn(FieldIspCode, vs...))
}

// IspCodeNotIn applies the NotIn predicate on the "isp_code" field.
func IspCodeNotIn(vs ...int) predicate.Strategy {
	return predicate.Strategy(sql.FieldNotIn(FieldIspCode, vs...))
}

// IspCodeGT applies the GT predicate on the "isp_code" field.
func IspCodeGT(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGT(FieldIspCode, v))
}

// IspCodeGTE applies the GTE predicate on the "isp_code" field.
func IspCodeGTE(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldGTE(FieldIspCode, v))
}

// IspCodeLT applies the LT predicate on the "isp_code" field.
func IspCodeLT(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLT(FieldIspCode, v))
}

// IspCodeLTE applies the LTE predicate on the "isp_code" field.
func IspCodeLTE(v int) predicate.Strategy {
	return predicate.Strategy(sql.FieldLTE(FieldIspCode, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.Strategy {
	return predicate.Strategy(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.Strategy {
	return predicate.Strategy(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasStrategyAlerts applies the HasEdge predicate on the "strategy_alerts" edge.
func HasStrategyAlerts() predicate.Strategy {
	return predicate.Strategy(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, StrategyAlertsTable, StrategyAlertsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasStrategyAlertsWith applies the HasEdge predicate on the "strategy_alerts" edge with a given conditions (other predicates).
func HasStrategyAlertsWith(preds ...predicate.SpectrumAlert) predicate.Strategy {
	return predicate.Strategy(func(s *sql.Selector) {
		step := newStrategyAlertsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Strategy) predicate.Strategy {
	return predicate.Strategy(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Strategy) predicate.Strategy {
	return predicate.Strategy(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Strategy) predicate.Strategy {
	return predicate.Strategy(sql.NotPredicates(p))
}
