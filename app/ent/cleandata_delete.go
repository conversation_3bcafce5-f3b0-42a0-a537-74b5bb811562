// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/cleandata"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CleanDataDelete is the builder for deleting a CleanData entity.
type CleanDataDelete struct {
	config
	hooks    []Hook
	mutation *CleanDataMutation
}

// Where appends a list predicates to the CleanDataDelete builder.
func (cdd *CleanDataDelete) Where(ps ...predicate.CleanData) *CleanDataDelete {
	cdd.mutation.Where(ps...)
	return cdd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (cdd *CleanDataDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, cdd.sqlExec, cdd.mutation, cdd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (cdd *CleanDataDelete) ExecX(ctx context.Context) int {
	n, err := cdd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (cdd *CleanDataDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(cleandata.Table, sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt))
	if ps := cdd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, cdd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	cdd.mutation.done = true
	return affected, err
}

// CleanDataDeleteOne is the builder for deleting a single CleanData entity.
type CleanDataDeleteOne struct {
	cdd *CleanDataDelete
}

// Where appends a list predicates to the CleanDataDelete builder.
func (cddo *CleanDataDeleteOne) Where(ps ...predicate.CleanData) *CleanDataDeleteOne {
	cddo.cdd.mutation.Where(ps...)
	return cddo
}

// Exec executes the deletion query.
func (cddo *CleanDataDeleteOne) Exec(ctx context.Context) error {
	n, err := cddo.cdd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{cleandata.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (cddo *CleanDataDeleteOne) ExecX(ctx context.Context) {
	if err := cddo.Exec(ctx); err != nil {
		panic(err)
	}
}
