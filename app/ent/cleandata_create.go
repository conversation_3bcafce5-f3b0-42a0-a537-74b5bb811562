// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cleandata"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CleanDataCreate is the builder for creating a CleanData entity.
type CleanDataCreate struct {
	config
	mutation *CleanDataMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (cdc *CleanDataCreate) SetTenantID(i int) *CleanDataCreate {
	cdc.mutation.SetTenantID(i)
	return cdc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cdc *CleanDataCreate) SetNillableTenantID(i *int) *CleanDataCreate {
	if i != nil {
		cdc.SetTenantID(*i)
	}
	return cdc
}

// SetCreatedAt sets the "created_at" field.
func (cdc *CleanDataCreate) SetCreatedAt(t time.Time) *CleanDataCreate {
	cdc.mutation.SetCreatedAt(t)
	return cdc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (cdc *CleanDataCreate) SetNillableCreatedAt(t *time.Time) *CleanDataCreate {
	if t != nil {
		cdc.SetCreatedAt(*t)
	}
	return cdc
}

// SetSpectrumAlertID sets the "spectrum_alert_id" field.
func (cdc *CleanDataCreate) SetSpectrumAlertID(i int) *CleanDataCreate {
	cdc.mutation.SetSpectrumAlertID(i)
	return cdc
}

// SetNillableSpectrumAlertID sets the "spectrum_alert_id" field if the given value is not nil.
func (cdc *CleanDataCreate) SetNillableSpectrumAlertID(i *int) *CleanDataCreate {
	if i != nil {
		cdc.SetSpectrumAlertID(*i)
	}
	return cdc
}

// SetIP sets the "ip" field.
func (cdc *CleanDataCreate) SetIP(s string) *CleanDataCreate {
	cdc.mutation.SetIP(s)
	return cdc
}

// SetTime sets the "time" field.
func (cdc *CleanDataCreate) SetTime(t time.Time) *CleanDataCreate {
	cdc.mutation.SetTime(t)
	return cdc
}

// SetInBps sets the "in_bps" field.
func (cdc *CleanDataCreate) SetInBps(i int64) *CleanDataCreate {
	cdc.mutation.SetInBps(i)
	return cdc
}

// SetOutBps sets the "out_bps" field.
func (cdc *CleanDataCreate) SetOutBps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutBps(i)
	return cdc
}

// SetInPps sets the "in_pps" field.
func (cdc *CleanDataCreate) SetInPps(i int64) *CleanDataCreate {
	cdc.mutation.SetInPps(i)
	return cdc
}

// SetOutPps sets the "out_pps" field.
func (cdc *CleanDataCreate) SetOutPps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutPps(i)
	return cdc
}

// SetInAckPps sets the "in_ack_pps" field.
func (cdc *CleanDataCreate) SetInAckPps(i int64) *CleanDataCreate {
	cdc.mutation.SetInAckPps(i)
	return cdc
}

// SetOutAckPps sets the "out_ack_pps" field.
func (cdc *CleanDataCreate) SetOutAckPps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutAckPps(i)
	return cdc
}

// SetInAckBps sets the "in_ack_bps" field.
func (cdc *CleanDataCreate) SetInAckBps(i int64) *CleanDataCreate {
	cdc.mutation.SetInAckBps(i)
	return cdc
}

// SetOutAckBps sets the "out_ack_bps" field.
func (cdc *CleanDataCreate) SetOutAckBps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutAckBps(i)
	return cdc
}

// SetInSynPps sets the "in_syn_pps" field.
func (cdc *CleanDataCreate) SetInSynPps(i int64) *CleanDataCreate {
	cdc.mutation.SetInSynPps(i)
	return cdc
}

// SetOutSynPps sets the "out_syn_pps" field.
func (cdc *CleanDataCreate) SetOutSynPps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutSynPps(i)
	return cdc
}

// SetInUDPPps sets the "in_udp_pps" field.
func (cdc *CleanDataCreate) SetInUDPPps(i int64) *CleanDataCreate {
	cdc.mutation.SetInUDPPps(i)
	return cdc
}

// SetOutUDPPps sets the "out_udp_pps" field.
func (cdc *CleanDataCreate) SetOutUDPPps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutUDPPps(i)
	return cdc
}

// SetInUDPBps sets the "in_udp_bps" field.
func (cdc *CleanDataCreate) SetInUDPBps(i int64) *CleanDataCreate {
	cdc.mutation.SetInUDPBps(i)
	return cdc
}

// SetOutUDPBps sets the "out_udp_bps" field.
func (cdc *CleanDataCreate) SetOutUDPBps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutUDPBps(i)
	return cdc
}

// SetInIcmpPps sets the "in_icmp_pps" field.
func (cdc *CleanDataCreate) SetInIcmpPps(i int64) *CleanDataCreate {
	cdc.mutation.SetInIcmpPps(i)
	return cdc
}

// SetInIcmpBps sets the "in_icmp_bps" field.
func (cdc *CleanDataCreate) SetInIcmpBps(i int64) *CleanDataCreate {
	cdc.mutation.SetInIcmpBps(i)
	return cdc
}

// SetOutIcmpBps sets the "out_icmp_bps" field.
func (cdc *CleanDataCreate) SetOutIcmpBps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutIcmpBps(i)
	return cdc
}

// SetOutIcmpPps sets the "out_icmp_pps" field.
func (cdc *CleanDataCreate) SetOutIcmpPps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutIcmpPps(i)
	return cdc
}

// SetInDNSPps sets the "in_dns_pps" field.
func (cdc *CleanDataCreate) SetInDNSPps(i int64) *CleanDataCreate {
	cdc.mutation.SetInDNSPps(i)
	return cdc
}

// SetOutDNSPps sets the "out_dns_pps" field.
func (cdc *CleanDataCreate) SetOutDNSPps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutDNSPps(i)
	return cdc
}

// SetInDNSBps sets the "in_dns_bps" field.
func (cdc *CleanDataCreate) SetInDNSBps(i int64) *CleanDataCreate {
	cdc.mutation.SetInDNSBps(i)
	return cdc
}

// SetOutDNSBps sets the "out_dns_bps" field.
func (cdc *CleanDataCreate) SetOutDNSBps(i int64) *CleanDataCreate {
	cdc.mutation.SetOutDNSBps(i)
	return cdc
}

// SetCFilterID sets the "c_filter_id" field.
func (cdc *CleanDataCreate) SetCFilterID(i int) *CleanDataCreate {
	cdc.mutation.SetCFilterID(i)
	return cdc
}

// SetAttackFlags sets the "attack_flags" field.
func (cdc *CleanDataCreate) SetAttackFlags(i int) *CleanDataCreate {
	cdc.mutation.SetAttackFlags(i)
	return cdc
}

// SetCount sets the "count" field.
func (cdc *CleanDataCreate) SetCount(i int) *CleanDataCreate {
	cdc.mutation.SetCount(i)
	return cdc
}

// SetIPType sets the "ip_type" field.
func (cdc *CleanDataCreate) SetIPType(i int) *CleanDataCreate {
	cdc.mutation.SetIPType(i)
	return cdc
}

// SetCFilter sets the "c_filter" field.
func (cdc *CleanDataCreate) SetCFilter(s string) *CleanDataCreate {
	cdc.mutation.SetCFilter(s)
	return cdc
}

// SetNillableCFilter sets the "c_filter" field if the given value is not nil.
func (cdc *CleanDataCreate) SetNillableCFilter(s *string) *CleanDataCreate {
	if s != nil {
		cdc.SetCFilter(*s)
	}
	return cdc
}

// SetHost sets the "host" field.
func (cdc *CleanDataCreate) SetHost(s string) *CleanDataCreate {
	cdc.mutation.SetHost(s)
	return cdc
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (cdc *CleanDataCreate) SetNillableHost(s *string) *CleanDataCreate {
	if s != nil {
		cdc.SetHost(*s)
	}
	return cdc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cdc *CleanDataCreate) SetTenant(t *Tenant) *CleanDataCreate {
	return cdc.SetTenantID(t.ID)
}

// SetSpectrumAlert sets the "spectrum_alert" edge to the SpectrumAlert entity.
func (cdc *CleanDataCreate) SetSpectrumAlert(s *SpectrumAlert) *CleanDataCreate {
	return cdc.SetSpectrumAlertID(s.ID)
}

// Mutation returns the CleanDataMutation object of the builder.
func (cdc *CleanDataCreate) Mutation() *CleanDataMutation {
	return cdc.mutation
}

// Save creates the CleanData in the database.
func (cdc *CleanDataCreate) Save(ctx context.Context) (*CleanData, error) {
	if err := cdc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, cdc.sqlSave, cdc.mutation, cdc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cdc *CleanDataCreate) SaveX(ctx context.Context) *CleanData {
	v, err := cdc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cdc *CleanDataCreate) Exec(ctx context.Context) error {
	_, err := cdc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cdc *CleanDataCreate) ExecX(ctx context.Context) {
	if err := cdc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cdc *CleanDataCreate) defaults() error {
	if _, ok := cdc.mutation.CreatedAt(); !ok {
		if cleandata.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized cleandata.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := cleandata.DefaultCreatedAt()
		cdc.mutation.SetCreatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cdc *CleanDataCreate) check() error {
	if _, ok := cdc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "CleanData.created_at"`)}
	}
	if _, ok := cdc.mutation.IP(); !ok {
		return &ValidationError{Name: "ip", err: errors.New(`ent: missing required field "CleanData.ip"`)}
	}
	if _, ok := cdc.mutation.Time(); !ok {
		return &ValidationError{Name: "time", err: errors.New(`ent: missing required field "CleanData.time"`)}
	}
	if _, ok := cdc.mutation.InBps(); !ok {
		return &ValidationError{Name: "in_bps", err: errors.New(`ent: missing required field "CleanData.in_bps"`)}
	}
	if _, ok := cdc.mutation.OutBps(); !ok {
		return &ValidationError{Name: "out_bps", err: errors.New(`ent: missing required field "CleanData.out_bps"`)}
	}
	if _, ok := cdc.mutation.InPps(); !ok {
		return &ValidationError{Name: "in_pps", err: errors.New(`ent: missing required field "CleanData.in_pps"`)}
	}
	if _, ok := cdc.mutation.OutPps(); !ok {
		return &ValidationError{Name: "out_pps", err: errors.New(`ent: missing required field "CleanData.out_pps"`)}
	}
	if _, ok := cdc.mutation.InAckPps(); !ok {
		return &ValidationError{Name: "in_ack_pps", err: errors.New(`ent: missing required field "CleanData.in_ack_pps"`)}
	}
	if _, ok := cdc.mutation.OutAckPps(); !ok {
		return &ValidationError{Name: "out_ack_pps", err: errors.New(`ent: missing required field "CleanData.out_ack_pps"`)}
	}
	if _, ok := cdc.mutation.InAckBps(); !ok {
		return &ValidationError{Name: "in_ack_bps", err: errors.New(`ent: missing required field "CleanData.in_ack_bps"`)}
	}
	if _, ok := cdc.mutation.OutAckBps(); !ok {
		return &ValidationError{Name: "out_ack_bps", err: errors.New(`ent: missing required field "CleanData.out_ack_bps"`)}
	}
	if _, ok := cdc.mutation.InSynPps(); !ok {
		return &ValidationError{Name: "in_syn_pps", err: errors.New(`ent: missing required field "CleanData.in_syn_pps"`)}
	}
	if _, ok := cdc.mutation.OutSynPps(); !ok {
		return &ValidationError{Name: "out_syn_pps", err: errors.New(`ent: missing required field "CleanData.out_syn_pps"`)}
	}
	if _, ok := cdc.mutation.InUDPPps(); !ok {
		return &ValidationError{Name: "in_udp_pps", err: errors.New(`ent: missing required field "CleanData.in_udp_pps"`)}
	}
	if _, ok := cdc.mutation.OutUDPPps(); !ok {
		return &ValidationError{Name: "out_udp_pps", err: errors.New(`ent: missing required field "CleanData.out_udp_pps"`)}
	}
	if _, ok := cdc.mutation.InUDPBps(); !ok {
		return &ValidationError{Name: "in_udp_bps", err: errors.New(`ent: missing required field "CleanData.in_udp_bps"`)}
	}
	if _, ok := cdc.mutation.OutUDPBps(); !ok {
		return &ValidationError{Name: "out_udp_bps", err: errors.New(`ent: missing required field "CleanData.out_udp_bps"`)}
	}
	if _, ok := cdc.mutation.InIcmpPps(); !ok {
		return &ValidationError{Name: "in_icmp_pps", err: errors.New(`ent: missing required field "CleanData.in_icmp_pps"`)}
	}
	if _, ok := cdc.mutation.InIcmpBps(); !ok {
		return &ValidationError{Name: "in_icmp_bps", err: errors.New(`ent: missing required field "CleanData.in_icmp_bps"`)}
	}
	if _, ok := cdc.mutation.OutIcmpBps(); !ok {
		return &ValidationError{Name: "out_icmp_bps", err: errors.New(`ent: missing required field "CleanData.out_icmp_bps"`)}
	}
	if _, ok := cdc.mutation.OutIcmpPps(); !ok {
		return &ValidationError{Name: "out_icmp_pps", err: errors.New(`ent: missing required field "CleanData.out_icmp_pps"`)}
	}
	if _, ok := cdc.mutation.InDNSPps(); !ok {
		return &ValidationError{Name: "in_dns_pps", err: errors.New(`ent: missing required field "CleanData.in_dns_pps"`)}
	}
	if _, ok := cdc.mutation.OutDNSPps(); !ok {
		return &ValidationError{Name: "out_dns_pps", err: errors.New(`ent: missing required field "CleanData.out_dns_pps"`)}
	}
	if _, ok := cdc.mutation.InDNSBps(); !ok {
		return &ValidationError{Name: "in_dns_bps", err: errors.New(`ent: missing required field "CleanData.in_dns_bps"`)}
	}
	if _, ok := cdc.mutation.OutDNSBps(); !ok {
		return &ValidationError{Name: "out_dns_bps", err: errors.New(`ent: missing required field "CleanData.out_dns_bps"`)}
	}
	if _, ok := cdc.mutation.CFilterID(); !ok {
		return &ValidationError{Name: "c_filter_id", err: errors.New(`ent: missing required field "CleanData.c_filter_id"`)}
	}
	if _, ok := cdc.mutation.AttackFlags(); !ok {
		return &ValidationError{Name: "attack_flags", err: errors.New(`ent: missing required field "CleanData.attack_flags"`)}
	}
	if _, ok := cdc.mutation.Count(); !ok {
		return &ValidationError{Name: "count", err: errors.New(`ent: missing required field "CleanData.count"`)}
	}
	if _, ok := cdc.mutation.IPType(); !ok {
		return &ValidationError{Name: "ip_type", err: errors.New(`ent: missing required field "CleanData.ip_type"`)}
	}
	return nil
}

func (cdc *CleanDataCreate) sqlSave(ctx context.Context) (*CleanData, error) {
	if err := cdc.check(); err != nil {
		return nil, err
	}
	_node, _spec := cdc.createSpec()
	if err := sqlgraph.CreateNode(ctx, cdc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	cdc.mutation.id = &_node.ID
	cdc.mutation.done = true
	return _node, nil
}

func (cdc *CleanDataCreate) createSpec() (*CleanData, *sqlgraph.CreateSpec) {
	var (
		_node = &CleanData{config: cdc.config}
		_spec = sqlgraph.NewCreateSpec(cleandata.Table, sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt))
	)
	if value, ok := cdc.mutation.CreatedAt(); ok {
		_spec.SetField(cleandata.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := cdc.mutation.IP(); ok {
		_spec.SetField(cleandata.FieldIP, field.TypeString, value)
		_node.IP = value
	}
	if value, ok := cdc.mutation.Time(); ok {
		_spec.SetField(cleandata.FieldTime, field.TypeTime, value)
		_node.Time = value
	}
	if value, ok := cdc.mutation.InBps(); ok {
		_spec.SetField(cleandata.FieldInBps, field.TypeInt64, value)
		_node.InBps = value
	}
	if value, ok := cdc.mutation.OutBps(); ok {
		_spec.SetField(cleandata.FieldOutBps, field.TypeInt64, value)
		_node.OutBps = value
	}
	if value, ok := cdc.mutation.InPps(); ok {
		_spec.SetField(cleandata.FieldInPps, field.TypeInt64, value)
		_node.InPps = value
	}
	if value, ok := cdc.mutation.OutPps(); ok {
		_spec.SetField(cleandata.FieldOutPps, field.TypeInt64, value)
		_node.OutPps = value
	}
	if value, ok := cdc.mutation.InAckPps(); ok {
		_spec.SetField(cleandata.FieldInAckPps, field.TypeInt64, value)
		_node.InAckPps = value
	}
	if value, ok := cdc.mutation.OutAckPps(); ok {
		_spec.SetField(cleandata.FieldOutAckPps, field.TypeInt64, value)
		_node.OutAckPps = value
	}
	if value, ok := cdc.mutation.InAckBps(); ok {
		_spec.SetField(cleandata.FieldInAckBps, field.TypeInt64, value)
		_node.InAckBps = value
	}
	if value, ok := cdc.mutation.OutAckBps(); ok {
		_spec.SetField(cleandata.FieldOutAckBps, field.TypeInt64, value)
		_node.OutAckBps = value
	}
	if value, ok := cdc.mutation.InSynPps(); ok {
		_spec.SetField(cleandata.FieldInSynPps, field.TypeInt64, value)
		_node.InSynPps = value
	}
	if value, ok := cdc.mutation.OutSynPps(); ok {
		_spec.SetField(cleandata.FieldOutSynPps, field.TypeInt64, value)
		_node.OutSynPps = value
	}
	if value, ok := cdc.mutation.InUDPPps(); ok {
		_spec.SetField(cleandata.FieldInUDPPps, field.TypeInt64, value)
		_node.InUDPPps = value
	}
	if value, ok := cdc.mutation.OutUDPPps(); ok {
		_spec.SetField(cleandata.FieldOutUDPPps, field.TypeInt64, value)
		_node.OutUDPPps = value
	}
	if value, ok := cdc.mutation.InUDPBps(); ok {
		_spec.SetField(cleandata.FieldInUDPBps, field.TypeInt64, value)
		_node.InUDPBps = value
	}
	if value, ok := cdc.mutation.OutUDPBps(); ok {
		_spec.SetField(cleandata.FieldOutUDPBps, field.TypeInt64, value)
		_node.OutUDPBps = value
	}
	if value, ok := cdc.mutation.InIcmpPps(); ok {
		_spec.SetField(cleandata.FieldInIcmpPps, field.TypeInt64, value)
		_node.InIcmpPps = value
	}
	if value, ok := cdc.mutation.InIcmpBps(); ok {
		_spec.SetField(cleandata.FieldInIcmpBps, field.TypeInt64, value)
		_node.InIcmpBps = value
	}
	if value, ok := cdc.mutation.OutIcmpBps(); ok {
		_spec.SetField(cleandata.FieldOutIcmpBps, field.TypeInt64, value)
		_node.OutIcmpBps = value
	}
	if value, ok := cdc.mutation.OutIcmpPps(); ok {
		_spec.SetField(cleandata.FieldOutIcmpPps, field.TypeInt64, value)
		_node.OutIcmpPps = value
	}
	if value, ok := cdc.mutation.InDNSPps(); ok {
		_spec.SetField(cleandata.FieldInDNSPps, field.TypeInt64, value)
		_node.InDNSPps = value
	}
	if value, ok := cdc.mutation.OutDNSPps(); ok {
		_spec.SetField(cleandata.FieldOutDNSPps, field.TypeInt64, value)
		_node.OutDNSPps = value
	}
	if value, ok := cdc.mutation.InDNSBps(); ok {
		_spec.SetField(cleandata.FieldInDNSBps, field.TypeInt64, value)
		_node.InDNSBps = value
	}
	if value, ok := cdc.mutation.OutDNSBps(); ok {
		_spec.SetField(cleandata.FieldOutDNSBps, field.TypeInt64, value)
		_node.OutDNSBps = value
	}
	if value, ok := cdc.mutation.CFilterID(); ok {
		_spec.SetField(cleandata.FieldCFilterID, field.TypeInt, value)
		_node.CFilterID = value
	}
	if value, ok := cdc.mutation.AttackFlags(); ok {
		_spec.SetField(cleandata.FieldAttackFlags, field.TypeInt, value)
		_node.AttackFlags = value
	}
	if value, ok := cdc.mutation.Count(); ok {
		_spec.SetField(cleandata.FieldCount, field.TypeInt, value)
		_node.Count = value
	}
	if value, ok := cdc.mutation.IPType(); ok {
		_spec.SetField(cleandata.FieldIPType, field.TypeInt, value)
		_node.IPType = value
	}
	if value, ok := cdc.mutation.CFilter(); ok {
		_spec.SetField(cleandata.FieldCFilter, field.TypeString, value)
		_node.CFilter = &value
	}
	if value, ok := cdc.mutation.Host(); ok {
		_spec.SetField(cleandata.FieldHost, field.TypeString, value)
		_node.Host = &value
	}
	if nodes := cdc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cleandata.TenantTable,
			Columns: []string{cleandata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := cdc.mutation.SpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cleandata.SpectrumAlertTable,
			Columns: []string{cleandata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.SpectrumAlertID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// CleanDataCreateBulk is the builder for creating many CleanData entities in bulk.
type CleanDataCreateBulk struct {
	config
	err      error
	builders []*CleanDataCreate
}

// Save creates the CleanData entities in the database.
func (cdcb *CleanDataCreateBulk) Save(ctx context.Context) ([]*CleanData, error) {
	if cdcb.err != nil {
		return nil, cdcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cdcb.builders))
	nodes := make([]*CleanData, len(cdcb.builders))
	mutators := make([]Mutator, len(cdcb.builders))
	for i := range cdcb.builders {
		func(i int, root context.Context) {
			builder := cdcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CleanDataMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cdcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cdcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cdcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cdcb *CleanDataCreateBulk) SaveX(ctx context.Context) []*CleanData {
	v, err := cdcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cdcb *CleanDataCreateBulk) Exec(ctx context.Context) error {
	_, err := cdcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cdcb *CleanDataCreateBulk) ExecX(ctx context.Context) {
	if err := cdcb.Exec(ctx); err != nil {
		panic(err)
	}
}
