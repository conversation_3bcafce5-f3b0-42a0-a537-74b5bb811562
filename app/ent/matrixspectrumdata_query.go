// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumDataQuery is the builder for querying MatrixSpectrumData entities.
type MatrixSpectrumDataQuery struct {
	config
	ctx                     *QueryContext
	order                   []matrixspectrumdata.OrderOption
	inters                  []Interceptor
	predicates              []predicate.MatrixSpectrumData
	withTenant              *TenantQuery
	withMatrixSpectrumAlert *MatrixSpectrumAlertQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the MatrixSpectrumDataQuery builder.
func (msdq *MatrixSpectrumDataQuery) Where(ps ...predicate.MatrixSpectrumData) *MatrixSpectrumDataQuery {
	msdq.predicates = append(msdq.predicates, ps...)
	return msdq
}

// Limit the number of records to be returned by this query.
func (msdq *MatrixSpectrumDataQuery) Limit(limit int) *MatrixSpectrumDataQuery {
	msdq.ctx.Limit = &limit
	return msdq
}

// Offset to start from.
func (msdq *MatrixSpectrumDataQuery) Offset(offset int) *MatrixSpectrumDataQuery {
	msdq.ctx.Offset = &offset
	return msdq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (msdq *MatrixSpectrumDataQuery) Unique(unique bool) *MatrixSpectrumDataQuery {
	msdq.ctx.Unique = &unique
	return msdq
}

// Order specifies how the records should be ordered.
func (msdq *MatrixSpectrumDataQuery) Order(o ...matrixspectrumdata.OrderOption) *MatrixSpectrumDataQuery {
	msdq.order = append(msdq.order, o...)
	return msdq
}

// QueryTenant chains the current query on the "tenant" edge.
func (msdq *MatrixSpectrumDataQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: msdq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := msdq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := msdq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumdata.Table, matrixspectrumdata.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, matrixspectrumdata.TenantTable, matrixspectrumdata.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(msdq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryMatrixSpectrumAlert chains the current query on the "matrix_spectrum_alert" edge.
func (msdq *MatrixSpectrumDataQuery) QueryMatrixSpectrumAlert() *MatrixSpectrumAlertQuery {
	query := (&MatrixSpectrumAlertClient{config: msdq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := msdq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := msdq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(matrixspectrumdata.Table, matrixspectrumdata.FieldID, selector),
			sqlgraph.To(matrixspectrumalert.Table, matrixspectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, matrixspectrumdata.MatrixSpectrumAlertTable, matrixspectrumdata.MatrixSpectrumAlertColumn),
		)
		fromU = sqlgraph.SetNeighbors(msdq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first MatrixSpectrumData entity from the query.
// Returns a *NotFoundError when no MatrixSpectrumData was found.
func (msdq *MatrixSpectrumDataQuery) First(ctx context.Context) (*MatrixSpectrumData, error) {
	nodes, err := msdq.Limit(1).All(setContextOp(ctx, msdq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{matrixspectrumdata.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (msdq *MatrixSpectrumDataQuery) FirstX(ctx context.Context) *MatrixSpectrumData {
	node, err := msdq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first MatrixSpectrumData ID from the query.
// Returns a *NotFoundError when no MatrixSpectrumData ID was found.
func (msdq *MatrixSpectrumDataQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = msdq.Limit(1).IDs(setContextOp(ctx, msdq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{matrixspectrumdata.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (msdq *MatrixSpectrumDataQuery) FirstIDX(ctx context.Context) int {
	id, err := msdq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single MatrixSpectrumData entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one MatrixSpectrumData entity is found.
// Returns a *NotFoundError when no MatrixSpectrumData entities are found.
func (msdq *MatrixSpectrumDataQuery) Only(ctx context.Context) (*MatrixSpectrumData, error) {
	nodes, err := msdq.Limit(2).All(setContextOp(ctx, msdq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{matrixspectrumdata.Label}
	default:
		return nil, &NotSingularError{matrixspectrumdata.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (msdq *MatrixSpectrumDataQuery) OnlyX(ctx context.Context) *MatrixSpectrumData {
	node, err := msdq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only MatrixSpectrumData ID in the query.
// Returns a *NotSingularError when more than one MatrixSpectrumData ID is found.
// Returns a *NotFoundError when no entities are found.
func (msdq *MatrixSpectrumDataQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = msdq.Limit(2).IDs(setContextOp(ctx, msdq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{matrixspectrumdata.Label}
	default:
		err = &NotSingularError{matrixspectrumdata.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (msdq *MatrixSpectrumDataQuery) OnlyIDX(ctx context.Context) int {
	id, err := msdq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of MatrixSpectrumDataSlice.
func (msdq *MatrixSpectrumDataQuery) All(ctx context.Context) ([]*MatrixSpectrumData, error) {
	ctx = setContextOp(ctx, msdq.ctx, "All")
	if err := msdq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*MatrixSpectrumData, *MatrixSpectrumDataQuery]()
	return withInterceptors[[]*MatrixSpectrumData](ctx, msdq, qr, msdq.inters)
}

// AllX is like All, but panics if an error occurs.
func (msdq *MatrixSpectrumDataQuery) AllX(ctx context.Context) []*MatrixSpectrumData {
	nodes, err := msdq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of MatrixSpectrumData IDs.
func (msdq *MatrixSpectrumDataQuery) IDs(ctx context.Context) (ids []int, err error) {
	if msdq.ctx.Unique == nil && msdq.path != nil {
		msdq.Unique(true)
	}
	ctx = setContextOp(ctx, msdq.ctx, "IDs")
	if err = msdq.Select(matrixspectrumdata.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (msdq *MatrixSpectrumDataQuery) IDsX(ctx context.Context) []int {
	ids, err := msdq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (msdq *MatrixSpectrumDataQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, msdq.ctx, "Count")
	if err := msdq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, msdq, querierCount[*MatrixSpectrumDataQuery](), msdq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (msdq *MatrixSpectrumDataQuery) CountX(ctx context.Context) int {
	count, err := msdq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (msdq *MatrixSpectrumDataQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, msdq.ctx, "Exist")
	switch _, err := msdq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (msdq *MatrixSpectrumDataQuery) ExistX(ctx context.Context) bool {
	exist, err := msdq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the MatrixSpectrumDataQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (msdq *MatrixSpectrumDataQuery) Clone() *MatrixSpectrumDataQuery {
	if msdq == nil {
		return nil
	}
	return &MatrixSpectrumDataQuery{
		config:                  msdq.config,
		ctx:                     msdq.ctx.Clone(),
		order:                   append([]matrixspectrumdata.OrderOption{}, msdq.order...),
		inters:                  append([]Interceptor{}, msdq.inters...),
		predicates:              append([]predicate.MatrixSpectrumData{}, msdq.predicates...),
		withTenant:              msdq.withTenant.Clone(),
		withMatrixSpectrumAlert: msdq.withMatrixSpectrumAlert.Clone(),
		// clone intermediate query.
		sql:  msdq.sql.Clone(),
		path: msdq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (msdq *MatrixSpectrumDataQuery) WithTenant(opts ...func(*TenantQuery)) *MatrixSpectrumDataQuery {
	query := (&TenantClient{config: msdq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	msdq.withTenant = query
	return msdq
}

// WithMatrixSpectrumAlert tells the query-builder to eager-load the nodes that are connected to
// the "matrix_spectrum_alert" edge. The optional arguments are used to configure the query builder of the edge.
func (msdq *MatrixSpectrumDataQuery) WithMatrixSpectrumAlert(opts ...func(*MatrixSpectrumAlertQuery)) *MatrixSpectrumDataQuery {
	query := (&MatrixSpectrumAlertClient{config: msdq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	msdq.withMatrixSpectrumAlert = query
	return msdq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.MatrixSpectrumData.Query().
//		GroupBy(matrixspectrumdata.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (msdq *MatrixSpectrumDataQuery) GroupBy(field string, fields ...string) *MatrixSpectrumDataGroupBy {
	msdq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &MatrixSpectrumDataGroupBy{build: msdq}
	grbuild.flds = &msdq.ctx.Fields
	grbuild.label = matrixspectrumdata.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.MatrixSpectrumData.Query().
//		Select(matrixspectrumdata.FieldTenantID).
//		Scan(ctx, &v)
func (msdq *MatrixSpectrumDataQuery) Select(fields ...string) *MatrixSpectrumDataSelect {
	msdq.ctx.Fields = append(msdq.ctx.Fields, fields...)
	sbuild := &MatrixSpectrumDataSelect{MatrixSpectrumDataQuery: msdq}
	sbuild.label = matrixspectrumdata.Label
	sbuild.flds, sbuild.scan = &msdq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a MatrixSpectrumDataSelect configured with the given aggregations.
func (msdq *MatrixSpectrumDataQuery) Aggregate(fns ...AggregateFunc) *MatrixSpectrumDataSelect {
	return msdq.Select().Aggregate(fns...)
}

func (msdq *MatrixSpectrumDataQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range msdq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, msdq); err != nil {
				return err
			}
		}
	}
	for _, f := range msdq.ctx.Fields {
		if !matrixspectrumdata.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if msdq.path != nil {
		prev, err := msdq.path(ctx)
		if err != nil {
			return err
		}
		msdq.sql = prev
	}
	if matrixspectrumdata.Policy == nil {
		return errors.New("ent: uninitialized matrixspectrumdata.Policy (forgotten import ent/runtime?)")
	}
	if err := matrixspectrumdata.Policy.EvalQuery(ctx, msdq); err != nil {
		return err
	}
	return nil
}

func (msdq *MatrixSpectrumDataQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*MatrixSpectrumData, error) {
	var (
		nodes       = []*MatrixSpectrumData{}
		_spec       = msdq.querySpec()
		loadedTypes = [2]bool{
			msdq.withTenant != nil,
			msdq.withMatrixSpectrumAlert != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*MatrixSpectrumData).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &MatrixSpectrumData{config: msdq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, msdq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := msdq.withTenant; query != nil {
		if err := msdq.loadTenant(ctx, query, nodes, nil,
			func(n *MatrixSpectrumData, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := msdq.withMatrixSpectrumAlert; query != nil {
		if err := msdq.loadMatrixSpectrumAlert(ctx, query, nodes, nil,
			func(n *MatrixSpectrumData, e *MatrixSpectrumAlert) { n.Edges.MatrixSpectrumAlert = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (msdq *MatrixSpectrumDataQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*MatrixSpectrumData, init func(*MatrixSpectrumData), assign func(*MatrixSpectrumData, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MatrixSpectrumData)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (msdq *MatrixSpectrumDataQuery) loadMatrixSpectrumAlert(ctx context.Context, query *MatrixSpectrumAlertQuery, nodes []*MatrixSpectrumData, init func(*MatrixSpectrumData), assign func(*MatrixSpectrumData, *MatrixSpectrumAlert)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*MatrixSpectrumData)
	for i := range nodes {
		if nodes[i].MatrixSpectrumAlertID == nil {
			continue
		}
		fk := *nodes[i].MatrixSpectrumAlertID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(matrixspectrumalert.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "matrix_spectrum_alert_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (msdq *MatrixSpectrumDataQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := msdq.querySpec()
	_spec.Node.Columns = msdq.ctx.Fields
	if len(msdq.ctx.Fields) > 0 {
		_spec.Unique = msdq.ctx.Unique != nil && *msdq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, msdq.driver, _spec)
}

func (msdq *MatrixSpectrumDataQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(matrixspectrumdata.Table, matrixspectrumdata.Columns, sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt))
	_spec.From = msdq.sql
	if unique := msdq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if msdq.path != nil {
		_spec.Unique = true
	}
	if fields := msdq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, matrixspectrumdata.FieldID)
		for i := range fields {
			if fields[i] != matrixspectrumdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if msdq.withTenant != nil {
			_spec.Node.AddColumnOnce(matrixspectrumdata.FieldTenantID)
		}
		if msdq.withMatrixSpectrumAlert != nil {
			_spec.Node.AddColumnOnce(matrixspectrumdata.FieldMatrixSpectrumAlertID)
		}
	}
	if ps := msdq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := msdq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := msdq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := msdq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (msdq *MatrixSpectrumDataQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(msdq.driver.Dialect())
	t1 := builder.Table(matrixspectrumdata.Table)
	columns := msdq.ctx.Fields
	if len(columns) == 0 {
		columns = matrixspectrumdata.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if msdq.sql != nil {
		selector = msdq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if msdq.ctx.Unique != nil && *msdq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range msdq.predicates {
		p(selector)
	}
	for _, p := range msdq.order {
		p(selector)
	}
	if offset := msdq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := msdq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// MatrixSpectrumDataGroupBy is the group-by builder for MatrixSpectrumData entities.
type MatrixSpectrumDataGroupBy struct {
	selector
	build *MatrixSpectrumDataQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (msdgb *MatrixSpectrumDataGroupBy) Aggregate(fns ...AggregateFunc) *MatrixSpectrumDataGroupBy {
	msdgb.fns = append(msdgb.fns, fns...)
	return msdgb
}

// Scan applies the selector query and scans the result into the given value.
func (msdgb *MatrixSpectrumDataGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, msdgb.build.ctx, "GroupBy")
	if err := msdgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MatrixSpectrumDataQuery, *MatrixSpectrumDataGroupBy](ctx, msdgb.build, msdgb, msdgb.build.inters, v)
}

func (msdgb *MatrixSpectrumDataGroupBy) sqlScan(ctx context.Context, root *MatrixSpectrumDataQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(msdgb.fns))
	for _, fn := range msdgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*msdgb.flds)+len(msdgb.fns))
		for _, f := range *msdgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*msdgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := msdgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// MatrixSpectrumDataSelect is the builder for selecting fields of MatrixSpectrumData entities.
type MatrixSpectrumDataSelect struct {
	*MatrixSpectrumDataQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (msds *MatrixSpectrumDataSelect) Aggregate(fns ...AggregateFunc) *MatrixSpectrumDataSelect {
	msds.fns = append(msds.fns, fns...)
	return msds
}

// Scan applies the selector query and scans the result into the given value.
func (msds *MatrixSpectrumDataSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, msds.ctx, "Select")
	if err := msds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MatrixSpectrumDataQuery, *MatrixSpectrumDataSelect](ctx, msds.MatrixSpectrumDataQuery, msds, msds.inters, v)
}

func (msds *MatrixSpectrumDataSelect) sqlScan(ctx context.Context, root *MatrixSpectrumDataQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(msds.fns))
	for _, fn := range msds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*msds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := msds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
