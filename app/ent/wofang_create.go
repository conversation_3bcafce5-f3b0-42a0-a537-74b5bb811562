// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/ent/wofang"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WofangCreate is the builder for creating a Wofang entity.
type WofangCreate struct {
	config
	mutation *WofangMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (wc *WofangCreate) SetCreatedAt(t time.Time) *WofangCreate {
	wc.mutation.SetCreatedAt(t)
	return wc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (wc *WofangCreate) SetNillableCreatedAt(t *time.Time) *WofangCreate {
	if t != nil {
		wc.SetCreatedAt(*t)
	}
	return wc
}

// SetUpdatedAt sets the "updated_at" field.
func (wc *WofangCreate) SetUpdatedAt(t time.Time) *WofangCreate {
	wc.mutation.SetUpdatedAt(t)
	return wc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (wc *WofangCreate) SetNillableUpdatedAt(t *time.Time) *WofangCreate {
	if t != nil {
		wc.SetUpdatedAt(*t)
	}
	return wc
}

// SetTenantID sets the "tenant_id" field.
func (wc *WofangCreate) SetTenantID(i int) *WofangCreate {
	wc.mutation.SetTenantID(i)
	return wc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (wc *WofangCreate) SetNillableTenantID(i *int) *WofangCreate {
	if i != nil {
		wc.SetTenantID(*i)
	}
	return wc
}

// SetRemark sets the "remark" field.
func (wc *WofangCreate) SetRemark(s string) *WofangCreate {
	wc.mutation.SetRemark(s)
	return wc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wc *WofangCreate) SetNillableRemark(s *string) *WofangCreate {
	if s != nil {
		wc.SetRemark(*s)
	}
	return wc
}

// SetName sets the "name" field.
func (wc *WofangCreate) SetName(s string) *WofangCreate {
	wc.mutation.SetName(s)
	return wc
}

// SetIP sets the "ip" field.
func (wc *WofangCreate) SetIP(s string) *WofangCreate {
	wc.mutation.SetIP(s)
	return wc
}

// SetType sets the "type" field.
func (wc *WofangCreate) SetType(s string) *WofangCreate {
	wc.mutation.SetType(s)
	return wc
}

// SetUnDragSecond sets the "un_drag_second" field.
func (wc *WofangCreate) SetUnDragSecond(i int) *WofangCreate {
	wc.mutation.SetUnDragSecond(i)
	return wc
}

// SetStartTime sets the "start_time" field.
func (wc *WofangCreate) SetStartTime(t time.Time) *WofangCreate {
	wc.mutation.SetStartTime(t)
	return wc
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (wc *WofangCreate) SetNillableStartTime(t *time.Time) *WofangCreate {
	if t != nil {
		wc.SetStartTime(*t)
	}
	return wc
}

// SetErrorInfo sets the "error_info" field.
func (wc *WofangCreate) SetErrorInfo(s string) *WofangCreate {
	wc.mutation.SetErrorInfo(s)
	return wc
}

// SetStatus sets the "status" field.
func (wc *WofangCreate) SetStatus(s string) *WofangCreate {
	wc.mutation.SetStatus(s)
	return wc
}

// SetCreateUserID sets the "create_user_id" field.
func (wc *WofangCreate) SetCreateUserID(i int) *WofangCreate {
	wc.mutation.SetCreateUserID(i)
	return wc
}

// SetNillableCreateUserID sets the "create_user_id" field if the given value is not nil.
func (wc *WofangCreate) SetNillableCreateUserID(i *int) *WofangCreate {
	if i != nil {
		wc.SetCreateUserID(*i)
	}
	return wc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (wc *WofangCreate) SetTenant(t *Tenant) *WofangCreate {
	return wc.SetTenantID(t.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (wc *WofangCreate) SetUserID(id int) *WofangCreate {
	wc.mutation.SetUserID(id)
	return wc
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (wc *WofangCreate) SetNillableUserID(id *int) *WofangCreate {
	if id != nil {
		wc = wc.SetUserID(*id)
	}
	return wc
}

// SetUser sets the "user" edge to the User entity.
func (wc *WofangCreate) SetUser(u *User) *WofangCreate {
	return wc.SetUserID(u.ID)
}

// AddSpectrumAlertIDs adds the "spectrum_alerts" edge to the SpectrumAlert entity by IDs.
func (wc *WofangCreate) AddSpectrumAlertIDs(ids ...int) *WofangCreate {
	wc.mutation.AddSpectrumAlertIDs(ids...)
	return wc
}

// AddSpectrumAlerts adds the "spectrum_alerts" edges to the SpectrumAlert entity.
func (wc *WofangCreate) AddSpectrumAlerts(s ...*SpectrumAlert) *WofangCreate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return wc.AddSpectrumAlertIDs(ids...)
}

// AddMatrixSpectrumAlertIDs adds the "matrix_spectrum_alerts" edge to the MatrixSpectrumAlert entity by IDs.
func (wc *WofangCreate) AddMatrixSpectrumAlertIDs(ids ...int) *WofangCreate {
	wc.mutation.AddMatrixSpectrumAlertIDs(ids...)
	return wc
}

// AddMatrixSpectrumAlerts adds the "matrix_spectrum_alerts" edges to the MatrixSpectrumAlert entity.
func (wc *WofangCreate) AddMatrixSpectrumAlerts(m ...*MatrixSpectrumAlert) *WofangCreate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return wc.AddMatrixSpectrumAlertIDs(ids...)
}

// Mutation returns the WofangMutation object of the builder.
func (wc *WofangCreate) Mutation() *WofangMutation {
	return wc.mutation
}

// Save creates the Wofang in the database.
func (wc *WofangCreate) Save(ctx context.Context) (*Wofang, error) {
	if err := wc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, wc.sqlSave, wc.mutation, wc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (wc *WofangCreate) SaveX(ctx context.Context) *Wofang {
	v, err := wc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wc *WofangCreate) Exec(ctx context.Context) error {
	_, err := wc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wc *WofangCreate) ExecX(ctx context.Context) {
	if err := wc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wc *WofangCreate) defaults() error {
	if _, ok := wc.mutation.CreatedAt(); !ok {
		if wofang.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized wofang.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := wofang.DefaultCreatedAt()
		wc.mutation.SetCreatedAt(v)
	}
	if _, ok := wc.mutation.UpdatedAt(); !ok {
		if wofang.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized wofang.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := wofang.DefaultUpdatedAt()
		wc.mutation.SetUpdatedAt(v)
	}
	if _, ok := wc.mutation.StartTime(); !ok {
		if wofang.DefaultStartTime == nil {
			return fmt.Errorf("ent: uninitialized wofang.DefaultStartTime (forgotten import ent/runtime?)")
		}
		v := wofang.DefaultStartTime()
		wc.mutation.SetStartTime(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (wc *WofangCreate) check() error {
	if _, ok := wc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Wofang.created_at"`)}
	}
	if _, ok := wc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Wofang.updated_at"`)}
	}
	if v, ok := wc.mutation.Remark(); ok {
		if err := wofang.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Wofang.remark": %w`, err)}
		}
	}
	if _, ok := wc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Wofang.name"`)}
	}
	if _, ok := wc.mutation.IP(); !ok {
		return &ValidationError{Name: "ip", err: errors.New(`ent: missing required field "Wofang.ip"`)}
	}
	if _, ok := wc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Wofang.type"`)}
	}
	if _, ok := wc.mutation.UnDragSecond(); !ok {
		return &ValidationError{Name: "un_drag_second", err: errors.New(`ent: missing required field "Wofang.un_drag_second"`)}
	}
	if _, ok := wc.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "Wofang.start_time"`)}
	}
	if _, ok := wc.mutation.ErrorInfo(); !ok {
		return &ValidationError{Name: "error_info", err: errors.New(`ent: missing required field "Wofang.error_info"`)}
	}
	if v, ok := wc.mutation.ErrorInfo(); ok {
		if err := wofang.ErrorInfoValidator(v); err != nil {
			return &ValidationError{Name: "error_info", err: fmt.Errorf(`ent: validator failed for field "Wofang.error_info": %w`, err)}
		}
	}
	if _, ok := wc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Wofang.status"`)}
	}
	return nil
}

func (wc *WofangCreate) sqlSave(ctx context.Context) (*Wofang, error) {
	if err := wc.check(); err != nil {
		return nil, err
	}
	_node, _spec := wc.createSpec()
	if err := sqlgraph.CreateNode(ctx, wc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	wc.mutation.id = &_node.ID
	wc.mutation.done = true
	return _node, nil
}

func (wc *WofangCreate) createSpec() (*Wofang, *sqlgraph.CreateSpec) {
	var (
		_node = &Wofang{config: wc.config}
		_spec = sqlgraph.NewCreateSpec(wofang.Table, sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt))
	)
	if value, ok := wc.mutation.CreatedAt(); ok {
		_spec.SetField(wofang.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := wc.mutation.UpdatedAt(); ok {
		_spec.SetField(wofang.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := wc.mutation.Remark(); ok {
		_spec.SetField(wofang.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := wc.mutation.Name(); ok {
		_spec.SetField(wofang.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := wc.mutation.IP(); ok {
		_spec.SetField(wofang.FieldIP, field.TypeString, value)
		_node.IP = value
	}
	if value, ok := wc.mutation.GetType(); ok {
		_spec.SetField(wofang.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := wc.mutation.UnDragSecond(); ok {
		_spec.SetField(wofang.FieldUnDragSecond, field.TypeInt, value)
		_node.UnDragSecond = value
	}
	if value, ok := wc.mutation.StartTime(); ok {
		_spec.SetField(wofang.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := wc.mutation.ErrorInfo(); ok {
		_spec.SetField(wofang.FieldErrorInfo, field.TypeString, value)
		_node.ErrorInfo = value
	}
	if value, ok := wc.mutation.Status(); ok {
		_spec.SetField(wofang.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if nodes := wc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.TenantTable,
			Columns: []string{wofang.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := wc.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.UserTable,
			Columns: []string{wofang.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.CreateUserID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := wc.mutation.SpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.SpectrumAlertsTable,
			Columns: []string{wofang.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := wc.mutation.MatrixSpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.MatrixSpectrumAlertsTable,
			Columns: []string{wofang.MatrixSpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// WofangCreateBulk is the builder for creating many Wofang entities in bulk.
type WofangCreateBulk struct {
	config
	err      error
	builders []*WofangCreate
}

// Save creates the Wofang entities in the database.
func (wcb *WofangCreateBulk) Save(ctx context.Context) ([]*Wofang, error) {
	if wcb.err != nil {
		return nil, wcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(wcb.builders))
	nodes := make([]*Wofang, len(wcb.builders))
	mutators := make([]Mutator, len(wcb.builders))
	for i := range wcb.builders {
		func(i int, root context.Context) {
			builder := wcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*WofangMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, wcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, wcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, wcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (wcb *WofangCreateBulk) SaveX(ctx context.Context) []*Wofang {
	v, err := wcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (wcb *WofangCreateBulk) Exec(ctx context.Context) error {
	_, err := wcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wcb *WofangCreateBulk) ExecX(ctx context.Context) {
	if err := wcb.Exec(ctx); err != nil {
		panic(err)
	}
}
