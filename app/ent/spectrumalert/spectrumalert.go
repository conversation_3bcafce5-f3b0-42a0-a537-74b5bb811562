// Code generated by ent, DO NOT EDIT.

package spectrumalert

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the spectrumalert type in the database.
	Label = "spectrum_alert"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldProtectGroupID holds the string denoting the protect_group_id field in the database.
	FieldProtectGroupID = "protect_group_id"
	// FieldStrategyID holds the string denoting the strategy_id field in the database.
	FieldStrategyID = "strategy_id"
	// FieldWofangID holds the string denoting the wofang_id field in the database.
	FieldWofangID = "wofang_id"
	// FieldProtectStatus holds the string denoting the protect_status field in the database.
	FieldProtectStatus = "protect_status"
	// FieldIP holds the string denoting the ip field in the database.
	FieldIP = "ip"
	// FieldStartTime holds the string denoting the start_time field in the database.
	FieldStartTime = "start_time"
	// FieldEndTime holds the string denoting the end_time field in the database.
	FieldEndTime = "end_time"
	// FieldAttackType holds the string denoting the attack_type field in the database.
	FieldAttackType = "attack_type"
	// FieldMaxPps holds the string denoting the max_pps field in the database.
	FieldMaxPps = "max_pps"
	// FieldMaxBps holds the string denoting the max_bps field in the database.
	FieldMaxBps = "max_bps"
	// FieldAttackInfo holds the string denoting the attack_info field in the database.
	FieldAttackInfo = "attack_info"
	// FieldIspCode holds the string denoting the isp_code field in the database.
	FieldIspCode = "isp_code"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeSpectrumDatas holds the string denoting the spectrum_datas edge name in mutations.
	EdgeSpectrumDatas = "spectrum_datas"
	// EdgeCleanDatas holds the string denoting the clean_datas edge name in mutations.
	EdgeCleanDatas = "clean_datas"
	// EdgeProtectGroup holds the string denoting the protect_group edge name in mutations.
	EdgeProtectGroup = "protect_group"
	// EdgeStrategy holds the string denoting the strategy edge name in mutations.
	EdgeStrategy = "strategy"
	// EdgeWofangTicket holds the string denoting the wofang_ticket edge name in mutations.
	EdgeWofangTicket = "wofang_ticket"
	// Table holds the table name of the spectrumalert in the database.
	Table = "spectrum_alerts"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "spectrum_alerts"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// SpectrumDatasTable is the table that holds the spectrum_datas relation/edge.
	SpectrumDatasTable = "spectrum_data"
	// SpectrumDatasInverseTable is the table name for the SpectrumData entity.
	// It exists in this package in order to avoid circular dependency with the "spectrumdata" package.
	SpectrumDatasInverseTable = "spectrum_data"
	// SpectrumDatasColumn is the table column denoting the spectrum_datas relation/edge.
	SpectrumDatasColumn = "spectrum_alert_id"
	// CleanDatasTable is the table that holds the clean_datas relation/edge.
	CleanDatasTable = "clean_data"
	// CleanDatasInverseTable is the table name for the CleanData entity.
	// It exists in this package in order to avoid circular dependency with the "cleandata" package.
	CleanDatasInverseTable = "clean_data"
	// CleanDatasColumn is the table column denoting the clean_datas relation/edge.
	CleanDatasColumn = "spectrum_alert_id"
	// ProtectGroupTable is the table that holds the protect_group relation/edge.
	ProtectGroupTable = "spectrum_alerts"
	// ProtectGroupInverseTable is the table name for the ProtectGroup entity.
	// It exists in this package in order to avoid circular dependency with the "protectgroup" package.
	ProtectGroupInverseTable = "protect_groups"
	// ProtectGroupColumn is the table column denoting the protect_group relation/edge.
	ProtectGroupColumn = "protect_group_id"
	// StrategyTable is the table that holds the strategy relation/edge.
	StrategyTable = "spectrum_alerts"
	// StrategyInverseTable is the table name for the Strategy entity.
	// It exists in this package in order to avoid circular dependency with the "strategy" package.
	StrategyInverseTable = "strategies"
	// StrategyColumn is the table column denoting the strategy relation/edge.
	StrategyColumn = "strategy_id"
	// WofangTicketTable is the table that holds the wofang_ticket relation/edge.
	WofangTicketTable = "spectrum_alerts"
	// WofangTicketInverseTable is the table name for the Wofang entity.
	// It exists in this package in order to avoid circular dependency with the "wofang" package.
	WofangTicketInverseTable = "wofangs"
	// WofangTicketColumn is the table column denoting the wofang_ticket relation/edge.
	WofangTicketColumn = "wofang_id"
)

// Columns holds all SQL columns for spectrumalert fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldProtectGroupID,
	FieldStrategyID,
	FieldWofangID,
	FieldProtectStatus,
	FieldIP,
	FieldStartTime,
	FieldEndTime,
	FieldAttackType,
	FieldMaxPps,
	FieldMaxBps,
	FieldAttackInfo,
	FieldIspCode,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
)

// OrderOption defines the ordering options for the SpectrumAlert queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByProtectGroupID orders the results by the protect_group_id field.
func ByProtectGroupID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProtectGroupID, opts...).ToFunc()
}

// ByStrategyID orders the results by the strategy_id field.
func ByStrategyID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStrategyID, opts...).ToFunc()
}

// ByWofangID orders the results by the wofang_id field.
func ByWofangID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWofangID, opts...).ToFunc()
}

// ByIP orders the results by the ip field.
func ByIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIP, opts...).ToFunc()
}

// ByStartTime orders the results by the start_time field.
func ByStartTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStartTime, opts...).ToFunc()
}

// ByEndTime orders the results by the end_time field.
func ByEndTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEndTime, opts...).ToFunc()
}

// ByAttackType orders the results by the attack_type field.
func ByAttackType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAttackType, opts...).ToFunc()
}

// ByMaxPps orders the results by the max_pps field.
func ByMaxPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxPps, opts...).ToFunc()
}

// ByMaxBps orders the results by the max_bps field.
func ByMaxBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxBps, opts...).ToFunc()
}

// ByIspCode orders the results by the isp_code field.
func ByIspCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIspCode, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// BySpectrumDatasCount orders the results by spectrum_datas count.
func BySpectrumDatasCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newSpectrumDatasStep(), opts...)
	}
}

// BySpectrumDatas orders the results by spectrum_datas terms.
func BySpectrumDatas(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSpectrumDatasStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByCleanDatasCount orders the results by clean_datas count.
func ByCleanDatasCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newCleanDatasStep(), opts...)
	}
}

// ByCleanDatas orders the results by clean_datas terms.
func ByCleanDatas(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCleanDatasStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByProtectGroupField orders the results by protect_group field.
func ByProtectGroupField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newProtectGroupStep(), sql.OrderByField(field, opts...))
	}
}

// ByStrategyField orders the results by strategy field.
func ByStrategyField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newStrategyStep(), sql.OrderByField(field, opts...))
	}
}

// ByWofangTicketField orders the results by wofang_ticket field.
func ByWofangTicketField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newWofangTicketStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newSpectrumDatasStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SpectrumDatasInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, SpectrumDatasTable, SpectrumDatasColumn),
	)
}
func newCleanDatasStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CleanDatasInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, CleanDatasTable, CleanDatasColumn),
	)
}
func newProtectGroupStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ProtectGroupInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, ProtectGroupTable, ProtectGroupColumn),
	)
}
func newStrategyStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(StrategyInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, StrategyTable, StrategyColumn),
	)
}
func newWofangTicketStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(WofangTicketInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, WofangTicketTable, WofangTicketColumn),
	)
}
