// Code generated by ent, DO NOT EDIT.

package spectrumalert

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldRemark, v))
}

// ProtectGroupID applies equality check predicate on the "protect_group_id" field. It's identical to ProtectGroupIDEQ.
func ProtectGroupID(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldProtectGroupID, v))
}

// StrategyID applies equality check predicate on the "strategy_id" field. It's identical to StrategyIDEQ.
func StrategyID(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldStrategyID, v))
}

// WofangID applies equality check predicate on the "wofang_id" field. It's identical to WofangIDEQ.
func WofangID(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldWofangID, v))
}

// IP applies equality check predicate on the "ip" field. It's identical to IPEQ.
func IP(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldIP, v))
}

// StartTime applies equality check predicate on the "start_time" field. It's identical to StartTimeEQ.
func StartTime(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldStartTime, v))
}

// EndTime applies equality check predicate on the "end_time" field. It's identical to EndTimeEQ.
func EndTime(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldEndTime, v))
}

// AttackType applies equality check predicate on the "attack_type" field. It's identical to AttackTypeEQ.
func AttackType(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldAttackType, v))
}

// MaxPps applies equality check predicate on the "max_pps" field. It's identical to MaxPpsEQ.
func MaxPps(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldMaxPps, v))
}

// MaxBps applies equality check predicate on the "max_bps" field. It's identical to MaxBpsEQ.
func MaxBps(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldMaxBps, v))
}

// IspCode applies equality check predicate on the "isp_code" field. It's identical to IspCodeEQ.
func IspCode(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldIspCode, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldContainsFold(FieldRemark, v))
}

// ProtectGroupIDEQ applies the EQ predicate on the "protect_group_id" field.
func ProtectGroupIDEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldProtectGroupID, v))
}

// ProtectGroupIDNEQ applies the NEQ predicate on the "protect_group_id" field.
func ProtectGroupIDNEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldProtectGroupID, v))
}

// ProtectGroupIDIn applies the In predicate on the "protect_group_id" field.
func ProtectGroupIDIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldProtectGroupID, vs...))
}

// ProtectGroupIDNotIn applies the NotIn predicate on the "protect_group_id" field.
func ProtectGroupIDNotIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldProtectGroupID, vs...))
}

// ProtectGroupIDIsNil applies the IsNil predicate on the "protect_group_id" field.
func ProtectGroupIDIsNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIsNull(FieldProtectGroupID))
}

// ProtectGroupIDNotNil applies the NotNil predicate on the "protect_group_id" field.
func ProtectGroupIDNotNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotNull(FieldProtectGroupID))
}

// StrategyIDEQ applies the EQ predicate on the "strategy_id" field.
func StrategyIDEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldStrategyID, v))
}

// StrategyIDNEQ applies the NEQ predicate on the "strategy_id" field.
func StrategyIDNEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldStrategyID, v))
}

// StrategyIDIn applies the In predicate on the "strategy_id" field.
func StrategyIDIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldStrategyID, vs...))
}

// StrategyIDNotIn applies the NotIn predicate on the "strategy_id" field.
func StrategyIDNotIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldStrategyID, vs...))
}

// StrategyIDIsNil applies the IsNil predicate on the "strategy_id" field.
func StrategyIDIsNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIsNull(FieldStrategyID))
}

// StrategyIDNotNil applies the NotNil predicate on the "strategy_id" field.
func StrategyIDNotNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotNull(FieldStrategyID))
}

// WofangIDEQ applies the EQ predicate on the "wofang_id" field.
func WofangIDEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldWofangID, v))
}

// WofangIDNEQ applies the NEQ predicate on the "wofang_id" field.
func WofangIDNEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldWofangID, v))
}

// WofangIDIn applies the In predicate on the "wofang_id" field.
func WofangIDIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldWofangID, vs...))
}

// WofangIDNotIn applies the NotIn predicate on the "wofang_id" field.
func WofangIDNotIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldWofangID, vs...))
}

// WofangIDIsNil applies the IsNil predicate on the "wofang_id" field.
func WofangIDIsNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIsNull(FieldWofangID))
}

// WofangIDNotNil applies the NotNil predicate on the "wofang_id" field.
func WofangIDNotNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotNull(FieldWofangID))
}

// ProtectStatusIsNil applies the IsNil predicate on the "protect_status" field.
func ProtectStatusIsNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIsNull(FieldProtectStatus))
}

// ProtectStatusNotNil applies the NotNil predicate on the "protect_status" field.
func ProtectStatusNotNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotNull(FieldProtectStatus))
}

// IPEQ applies the EQ predicate on the "ip" field.
func IPEQ(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldIP, v))
}

// IPNEQ applies the NEQ predicate on the "ip" field.
func IPNEQ(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldIP, v))
}

// IPIn applies the In predicate on the "ip" field.
func IPIn(vs ...string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldIP, vs...))
}

// IPNotIn applies the NotIn predicate on the "ip" field.
func IPNotIn(vs ...string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldIP, vs...))
}

// IPGT applies the GT predicate on the "ip" field.
func IPGT(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldIP, v))
}

// IPGTE applies the GTE predicate on the "ip" field.
func IPGTE(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldIP, v))
}

// IPLT applies the LT predicate on the "ip" field.
func IPLT(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldIP, v))
}

// IPLTE applies the LTE predicate on the "ip" field.
func IPLTE(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldIP, v))
}

// IPContains applies the Contains predicate on the "ip" field.
func IPContains(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldContains(FieldIP, v))
}

// IPHasPrefix applies the HasPrefix predicate on the "ip" field.
func IPHasPrefix(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldHasPrefix(FieldIP, v))
}

// IPHasSuffix applies the HasSuffix predicate on the "ip" field.
func IPHasSuffix(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldHasSuffix(FieldIP, v))
}

// IPEqualFold applies the EqualFold predicate on the "ip" field.
func IPEqualFold(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEqualFold(FieldIP, v))
}

// IPContainsFold applies the ContainsFold predicate on the "ip" field.
func IPContainsFold(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldContainsFold(FieldIP, v))
}

// StartTimeEQ applies the EQ predicate on the "start_time" field.
func StartTimeEQ(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldStartTime, v))
}

// StartTimeNEQ applies the NEQ predicate on the "start_time" field.
func StartTimeNEQ(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldStartTime, v))
}

// StartTimeIn applies the In predicate on the "start_time" field.
func StartTimeIn(vs ...time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldStartTime, vs...))
}

// StartTimeNotIn applies the NotIn predicate on the "start_time" field.
func StartTimeNotIn(vs ...time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldStartTime, vs...))
}

// StartTimeGT applies the GT predicate on the "start_time" field.
func StartTimeGT(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldStartTime, v))
}

// StartTimeGTE applies the GTE predicate on the "start_time" field.
func StartTimeGTE(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldStartTime, v))
}

// StartTimeLT applies the LT predicate on the "start_time" field.
func StartTimeLT(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldStartTime, v))
}

// StartTimeLTE applies the LTE predicate on the "start_time" field.
func StartTimeLTE(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldStartTime, v))
}

// EndTimeEQ applies the EQ predicate on the "end_time" field.
func EndTimeEQ(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldEndTime, v))
}

// EndTimeNEQ applies the NEQ predicate on the "end_time" field.
func EndTimeNEQ(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldEndTime, v))
}

// EndTimeIn applies the In predicate on the "end_time" field.
func EndTimeIn(vs ...time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldEndTime, vs...))
}

// EndTimeNotIn applies the NotIn predicate on the "end_time" field.
func EndTimeNotIn(vs ...time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldEndTime, vs...))
}

// EndTimeGT applies the GT predicate on the "end_time" field.
func EndTimeGT(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldEndTime, v))
}

// EndTimeGTE applies the GTE predicate on the "end_time" field.
func EndTimeGTE(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldEndTime, v))
}

// EndTimeLT applies the LT predicate on the "end_time" field.
func EndTimeLT(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldEndTime, v))
}

// EndTimeLTE applies the LTE predicate on the "end_time" field.
func EndTimeLTE(v time.Time) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldEndTime, v))
}

// EndTimeIsNil applies the IsNil predicate on the "end_time" field.
func EndTimeIsNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIsNull(FieldEndTime))
}

// EndTimeNotNil applies the NotNil predicate on the "end_time" field.
func EndTimeNotNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotNull(FieldEndTime))
}

// AttackTypeEQ applies the EQ predicate on the "attack_type" field.
func AttackTypeEQ(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldAttackType, v))
}

// AttackTypeNEQ applies the NEQ predicate on the "attack_type" field.
func AttackTypeNEQ(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldAttackType, v))
}

// AttackTypeIn applies the In predicate on the "attack_type" field.
func AttackTypeIn(vs ...string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldAttackType, vs...))
}

// AttackTypeNotIn applies the NotIn predicate on the "attack_type" field.
func AttackTypeNotIn(vs ...string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldAttackType, vs...))
}

// AttackTypeGT applies the GT predicate on the "attack_type" field.
func AttackTypeGT(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldAttackType, v))
}

// AttackTypeGTE applies the GTE predicate on the "attack_type" field.
func AttackTypeGTE(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldAttackType, v))
}

// AttackTypeLT applies the LT predicate on the "attack_type" field.
func AttackTypeLT(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldAttackType, v))
}

// AttackTypeLTE applies the LTE predicate on the "attack_type" field.
func AttackTypeLTE(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldAttackType, v))
}

// AttackTypeContains applies the Contains predicate on the "attack_type" field.
func AttackTypeContains(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldContains(FieldAttackType, v))
}

// AttackTypeHasPrefix applies the HasPrefix predicate on the "attack_type" field.
func AttackTypeHasPrefix(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldHasPrefix(FieldAttackType, v))
}

// AttackTypeHasSuffix applies the HasSuffix predicate on the "attack_type" field.
func AttackTypeHasSuffix(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldHasSuffix(FieldAttackType, v))
}

// AttackTypeEqualFold applies the EqualFold predicate on the "attack_type" field.
func AttackTypeEqualFold(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEqualFold(FieldAttackType, v))
}

// AttackTypeContainsFold applies the ContainsFold predicate on the "attack_type" field.
func AttackTypeContainsFold(v string) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldContainsFold(FieldAttackType, v))
}

// MaxPpsEQ applies the EQ predicate on the "max_pps" field.
func MaxPpsEQ(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldMaxPps, v))
}

// MaxPpsNEQ applies the NEQ predicate on the "max_pps" field.
func MaxPpsNEQ(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldMaxPps, v))
}

// MaxPpsIn applies the In predicate on the "max_pps" field.
func MaxPpsIn(vs ...int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldMaxPps, vs...))
}

// MaxPpsNotIn applies the NotIn predicate on the "max_pps" field.
func MaxPpsNotIn(vs ...int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldMaxPps, vs...))
}

// MaxPpsGT applies the GT predicate on the "max_pps" field.
func MaxPpsGT(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldMaxPps, v))
}

// MaxPpsGTE applies the GTE predicate on the "max_pps" field.
func MaxPpsGTE(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldMaxPps, v))
}

// MaxPpsLT applies the LT predicate on the "max_pps" field.
func MaxPpsLT(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldMaxPps, v))
}

// MaxPpsLTE applies the LTE predicate on the "max_pps" field.
func MaxPpsLTE(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldMaxPps, v))
}

// MaxBpsEQ applies the EQ predicate on the "max_bps" field.
func MaxBpsEQ(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldMaxBps, v))
}

// MaxBpsNEQ applies the NEQ predicate on the "max_bps" field.
func MaxBpsNEQ(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldMaxBps, v))
}

// MaxBpsIn applies the In predicate on the "max_bps" field.
func MaxBpsIn(vs ...int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldMaxBps, vs...))
}

// MaxBpsNotIn applies the NotIn predicate on the "max_bps" field.
func MaxBpsNotIn(vs ...int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldMaxBps, vs...))
}

// MaxBpsGT applies the GT predicate on the "max_bps" field.
func MaxBpsGT(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldMaxBps, v))
}

// MaxBpsGTE applies the GTE predicate on the "max_bps" field.
func MaxBpsGTE(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldMaxBps, v))
}

// MaxBpsLT applies the LT predicate on the "max_bps" field.
func MaxBpsLT(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldMaxBps, v))
}

// MaxBpsLTE applies the LTE predicate on the "max_bps" field.
func MaxBpsLTE(v int64) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldMaxBps, v))
}

// AttackInfoIsNil applies the IsNil predicate on the "attack_info" field.
func AttackInfoIsNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIsNull(FieldAttackInfo))
}

// AttackInfoNotNil applies the NotNil predicate on the "attack_info" field.
func AttackInfoNotNil() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotNull(FieldAttackInfo))
}

// IspCodeEQ applies the EQ predicate on the "isp_code" field.
func IspCodeEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldEQ(FieldIspCode, v))
}

// IspCodeNEQ applies the NEQ predicate on the "isp_code" field.
func IspCodeNEQ(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNEQ(FieldIspCode, v))
}

// IspCodeIn applies the In predicate on the "isp_code" field.
func IspCodeIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldIn(FieldIspCode, vs...))
}

// IspCodeNotIn applies the NotIn predicate on the "isp_code" field.
func IspCodeNotIn(vs ...int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldNotIn(FieldIspCode, vs...))
}

// IspCodeGT applies the GT predicate on the "isp_code" field.
func IspCodeGT(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGT(FieldIspCode, v))
}

// IspCodeGTE applies the GTE predicate on the "isp_code" field.
func IspCodeGTE(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldGTE(FieldIspCode, v))
}

// IspCodeLT applies the LT predicate on the "isp_code" field.
func IspCodeLT(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLT(FieldIspCode, v))
}

// IspCodeLTE applies the LTE predicate on the "isp_code" field.
func IspCodeLTE(v int) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.FieldLTE(FieldIspCode, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasSpectrumDatas applies the HasEdge predicate on the "spectrum_datas" edge.
func HasSpectrumDatas() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, SpectrumDatasTable, SpectrumDatasColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSpectrumDatasWith applies the HasEdge predicate on the "spectrum_datas" edge with a given conditions (other predicates).
func HasSpectrumDatasWith(preds ...predicate.SpectrumData) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := newSpectrumDatasStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasCleanDatas applies the HasEdge predicate on the "clean_datas" edge.
func HasCleanDatas() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, CleanDatasTable, CleanDatasColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasCleanDatasWith applies the HasEdge predicate on the "clean_datas" edge with a given conditions (other predicates).
func HasCleanDatasWith(preds ...predicate.CleanData) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := newCleanDatasStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasProtectGroup applies the HasEdge predicate on the "protect_group" edge.
func HasProtectGroup() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ProtectGroupTable, ProtectGroupColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasProtectGroupWith applies the HasEdge predicate on the "protect_group" edge with a given conditions (other predicates).
func HasProtectGroupWith(preds ...predicate.ProtectGroup) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := newProtectGroupStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasStrategy applies the HasEdge predicate on the "strategy" edge.
func HasStrategy() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, StrategyTable, StrategyColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasStrategyWith applies the HasEdge predicate on the "strategy" edge with a given conditions (other predicates).
func HasStrategyWith(preds ...predicate.Strategy) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := newStrategyStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasWofangTicket applies the HasEdge predicate on the "wofang_ticket" edge.
func HasWofangTicket() predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, WofangTicketTable, WofangTicketColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasWofangTicketWith applies the HasEdge predicate on the "wofang_ticket" edge with a given conditions (other predicates).
func HasWofangTicketWith(preds ...predicate.Wofang) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(func(s *sql.Selector) {
		step := newWofangTicketStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SpectrumAlert) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SpectrumAlert) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SpectrumAlert) predicate.SpectrumAlert {
	return predicate.SpectrumAlert(sql.NotPredicates(p))
}
