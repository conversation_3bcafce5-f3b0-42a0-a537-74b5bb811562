// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/casbinrule"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CasbinRuleUpdate is the builder for updating CasbinRule entities.
type CasbinRuleUpdate struct {
	config
	hooks    []Hook
	mutation *CasbinRuleMutation
}

// Where appends a list predicates to the CasbinRuleUpdate builder.
func (cru *CasbinRuleUpdate) Where(ps ...predicate.CasbinRule) *CasbinRuleUpdate {
	cru.mutation.Where(ps...)
	return cru
}

// SetType sets the "type" field.
func (cru *CasbinRuleUpdate) SetType(s string) *CasbinRuleUpdate {
	cru.mutation.SetType(s)
	return cru
}

// SetNillableType sets the "type" field if the given value is not nil.
func (cru *CasbinRuleUpdate) SetNillableType(s *string) *CasbinRuleUpdate {
	if s != nil {
		cru.SetType(*s)
	}
	return cru
}

// SetSub sets the "sub" field.
func (cru *CasbinRuleUpdate) SetSub(s string) *CasbinRuleUpdate {
	cru.mutation.SetSub(s)
	return cru
}

// SetNillableSub sets the "sub" field if the given value is not nil.
func (cru *CasbinRuleUpdate) SetNillableSub(s *string) *CasbinRuleUpdate {
	if s != nil {
		cru.SetSub(*s)
	}
	return cru
}

// SetDom sets the "dom" field.
func (cru *CasbinRuleUpdate) SetDom(s string) *CasbinRuleUpdate {
	cru.mutation.SetDom(s)
	return cru
}

// SetNillableDom sets the "dom" field if the given value is not nil.
func (cru *CasbinRuleUpdate) SetNillableDom(s *string) *CasbinRuleUpdate {
	if s != nil {
		cru.SetDom(*s)
	}
	return cru
}

// SetObj sets the "obj" field.
func (cru *CasbinRuleUpdate) SetObj(s string) *CasbinRuleUpdate {
	cru.mutation.SetObj(s)
	return cru
}

// SetNillableObj sets the "obj" field if the given value is not nil.
func (cru *CasbinRuleUpdate) SetNillableObj(s *string) *CasbinRuleUpdate {
	if s != nil {
		cru.SetObj(*s)
	}
	return cru
}

// SetAct sets the "act" field.
func (cru *CasbinRuleUpdate) SetAct(s string) *CasbinRuleUpdate {
	cru.mutation.SetAct(s)
	return cru
}

// SetNillableAct sets the "act" field if the given value is not nil.
func (cru *CasbinRuleUpdate) SetNillableAct(s *string) *CasbinRuleUpdate {
	if s != nil {
		cru.SetAct(*s)
	}
	return cru
}

// SetV4 sets the "v4" field.
func (cru *CasbinRuleUpdate) SetV4(s string) *CasbinRuleUpdate {
	cru.mutation.SetV4(s)
	return cru
}

// SetNillableV4 sets the "v4" field if the given value is not nil.
func (cru *CasbinRuleUpdate) SetNillableV4(s *string) *CasbinRuleUpdate {
	if s != nil {
		cru.SetV4(*s)
	}
	return cru
}

// SetV5 sets the "v5" field.
func (cru *CasbinRuleUpdate) SetV5(s string) *CasbinRuleUpdate {
	cru.mutation.SetV5(s)
	return cru
}

// SetNillableV5 sets the "v5" field if the given value is not nil.
func (cru *CasbinRuleUpdate) SetNillableV5(s *string) *CasbinRuleUpdate {
	if s != nil {
		cru.SetV5(*s)
	}
	return cru
}

// Mutation returns the CasbinRuleMutation object of the builder.
func (cru *CasbinRuleUpdate) Mutation() *CasbinRuleMutation {
	return cru.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cru *CasbinRuleUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, cru.sqlSave, cru.mutation, cru.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cru *CasbinRuleUpdate) SaveX(ctx context.Context) int {
	affected, err := cru.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cru *CasbinRuleUpdate) Exec(ctx context.Context) error {
	_, err := cru.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cru *CasbinRuleUpdate) ExecX(ctx context.Context) {
	if err := cru.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cru *CasbinRuleUpdate) check() error {
	if v, ok := cru.mutation.GetType(); ok {
		if err := casbinrule.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.type": %w`, err)}
		}
	}
	if v, ok := cru.mutation.Sub(); ok {
		if err := casbinrule.SubValidator(v); err != nil {
			return &ValidationError{Name: "sub", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.sub": %w`, err)}
		}
	}
	if v, ok := cru.mutation.Dom(); ok {
		if err := casbinrule.DomValidator(v); err != nil {
			return &ValidationError{Name: "dom", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.dom": %w`, err)}
		}
	}
	if v, ok := cru.mutation.Obj(); ok {
		if err := casbinrule.ObjValidator(v); err != nil {
			return &ValidationError{Name: "obj", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.obj": %w`, err)}
		}
	}
	return nil
}

func (cru *CasbinRuleUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := cru.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(casbinrule.Table, casbinrule.Columns, sqlgraph.NewFieldSpec(casbinrule.FieldID, field.TypeInt))
	if ps := cru.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cru.mutation.GetType(); ok {
		_spec.SetField(casbinrule.FieldType, field.TypeString, value)
	}
	if value, ok := cru.mutation.Sub(); ok {
		_spec.SetField(casbinrule.FieldSub, field.TypeString, value)
	}
	if value, ok := cru.mutation.Dom(); ok {
		_spec.SetField(casbinrule.FieldDom, field.TypeString, value)
	}
	if value, ok := cru.mutation.Obj(); ok {
		_spec.SetField(casbinrule.FieldObj, field.TypeString, value)
	}
	if value, ok := cru.mutation.Act(); ok {
		_spec.SetField(casbinrule.FieldAct, field.TypeString, value)
	}
	if value, ok := cru.mutation.V4(); ok {
		_spec.SetField(casbinrule.FieldV4, field.TypeString, value)
	}
	if value, ok := cru.mutation.V5(); ok {
		_spec.SetField(casbinrule.FieldV5, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cru.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{casbinrule.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cru.mutation.done = true
	return n, nil
}

// CasbinRuleUpdateOne is the builder for updating a single CasbinRule entity.
type CasbinRuleUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *CasbinRuleMutation
}

// SetType sets the "type" field.
func (cruo *CasbinRuleUpdateOne) SetType(s string) *CasbinRuleUpdateOne {
	cruo.mutation.SetType(s)
	return cruo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (cruo *CasbinRuleUpdateOne) SetNillableType(s *string) *CasbinRuleUpdateOne {
	if s != nil {
		cruo.SetType(*s)
	}
	return cruo
}

// SetSub sets the "sub" field.
func (cruo *CasbinRuleUpdateOne) SetSub(s string) *CasbinRuleUpdateOne {
	cruo.mutation.SetSub(s)
	return cruo
}

// SetNillableSub sets the "sub" field if the given value is not nil.
func (cruo *CasbinRuleUpdateOne) SetNillableSub(s *string) *CasbinRuleUpdateOne {
	if s != nil {
		cruo.SetSub(*s)
	}
	return cruo
}

// SetDom sets the "dom" field.
func (cruo *CasbinRuleUpdateOne) SetDom(s string) *CasbinRuleUpdateOne {
	cruo.mutation.SetDom(s)
	return cruo
}

// SetNillableDom sets the "dom" field if the given value is not nil.
func (cruo *CasbinRuleUpdateOne) SetNillableDom(s *string) *CasbinRuleUpdateOne {
	if s != nil {
		cruo.SetDom(*s)
	}
	return cruo
}

// SetObj sets the "obj" field.
func (cruo *CasbinRuleUpdateOne) SetObj(s string) *CasbinRuleUpdateOne {
	cruo.mutation.SetObj(s)
	return cruo
}

// SetNillableObj sets the "obj" field if the given value is not nil.
func (cruo *CasbinRuleUpdateOne) SetNillableObj(s *string) *CasbinRuleUpdateOne {
	if s != nil {
		cruo.SetObj(*s)
	}
	return cruo
}

// SetAct sets the "act" field.
func (cruo *CasbinRuleUpdateOne) SetAct(s string) *CasbinRuleUpdateOne {
	cruo.mutation.SetAct(s)
	return cruo
}

// SetNillableAct sets the "act" field if the given value is not nil.
func (cruo *CasbinRuleUpdateOne) SetNillableAct(s *string) *CasbinRuleUpdateOne {
	if s != nil {
		cruo.SetAct(*s)
	}
	return cruo
}

// SetV4 sets the "v4" field.
func (cruo *CasbinRuleUpdateOne) SetV4(s string) *CasbinRuleUpdateOne {
	cruo.mutation.SetV4(s)
	return cruo
}

// SetNillableV4 sets the "v4" field if the given value is not nil.
func (cruo *CasbinRuleUpdateOne) SetNillableV4(s *string) *CasbinRuleUpdateOne {
	if s != nil {
		cruo.SetV4(*s)
	}
	return cruo
}

// SetV5 sets the "v5" field.
func (cruo *CasbinRuleUpdateOne) SetV5(s string) *CasbinRuleUpdateOne {
	cruo.mutation.SetV5(s)
	return cruo
}

// SetNillableV5 sets the "v5" field if the given value is not nil.
func (cruo *CasbinRuleUpdateOne) SetNillableV5(s *string) *CasbinRuleUpdateOne {
	if s != nil {
		cruo.SetV5(*s)
	}
	return cruo
}

// Mutation returns the CasbinRuleMutation object of the builder.
func (cruo *CasbinRuleUpdateOne) Mutation() *CasbinRuleMutation {
	return cruo.mutation
}

// Where appends a list predicates to the CasbinRuleUpdate builder.
func (cruo *CasbinRuleUpdateOne) Where(ps ...predicate.CasbinRule) *CasbinRuleUpdateOne {
	cruo.mutation.Where(ps...)
	return cruo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cruo *CasbinRuleUpdateOne) Select(field string, fields ...string) *CasbinRuleUpdateOne {
	cruo.fields = append([]string{field}, fields...)
	return cruo
}

// Save executes the query and returns the updated CasbinRule entity.
func (cruo *CasbinRuleUpdateOne) Save(ctx context.Context) (*CasbinRule, error) {
	return withHooks(ctx, cruo.sqlSave, cruo.mutation, cruo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cruo *CasbinRuleUpdateOne) SaveX(ctx context.Context) *CasbinRule {
	node, err := cruo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cruo *CasbinRuleUpdateOne) Exec(ctx context.Context) error {
	_, err := cruo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cruo *CasbinRuleUpdateOne) ExecX(ctx context.Context) {
	if err := cruo.Exec(ctx); err != nil {
		panic(err)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cruo *CasbinRuleUpdateOne) check() error {
	if v, ok := cruo.mutation.GetType(); ok {
		if err := casbinrule.TypeValidator(v); err != nil {
			return &ValidationError{Name: "type", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.type": %w`, err)}
		}
	}
	if v, ok := cruo.mutation.Sub(); ok {
		if err := casbinrule.SubValidator(v); err != nil {
			return &ValidationError{Name: "sub", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.sub": %w`, err)}
		}
	}
	if v, ok := cruo.mutation.Dom(); ok {
		if err := casbinrule.DomValidator(v); err != nil {
			return &ValidationError{Name: "dom", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.dom": %w`, err)}
		}
	}
	if v, ok := cruo.mutation.Obj(); ok {
		if err := casbinrule.ObjValidator(v); err != nil {
			return &ValidationError{Name: "obj", err: fmt.Errorf(`ent: validator failed for field "CasbinRule.obj": %w`, err)}
		}
	}
	return nil
}

func (cruo *CasbinRuleUpdateOne) sqlSave(ctx context.Context) (_node *CasbinRule, err error) {
	if err := cruo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(casbinrule.Table, casbinrule.Columns, sqlgraph.NewFieldSpec(casbinrule.FieldID, field.TypeInt))
	id, ok := cruo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "CasbinRule.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cruo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, casbinrule.FieldID)
		for _, f := range fields {
			if !casbinrule.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != casbinrule.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cruo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cruo.mutation.GetType(); ok {
		_spec.SetField(casbinrule.FieldType, field.TypeString, value)
	}
	if value, ok := cruo.mutation.Sub(); ok {
		_spec.SetField(casbinrule.FieldSub, field.TypeString, value)
	}
	if value, ok := cruo.mutation.Dom(); ok {
		_spec.SetField(casbinrule.FieldDom, field.TypeString, value)
	}
	if value, ok := cruo.mutation.Obj(); ok {
		_spec.SetField(casbinrule.FieldObj, field.TypeString, value)
	}
	if value, ok := cruo.mutation.Act(); ok {
		_spec.SetField(casbinrule.FieldAct, field.TypeString, value)
	}
	if value, ok := cruo.mutation.V4(); ok {
		_spec.SetField(casbinrule.FieldV4, field.TypeString, value)
	}
	if value, ok := cruo.mutation.V5(); ok {
		_spec.SetField(casbinrule.FieldV5, field.TypeString, value)
	}
	_node = &CasbinRule{config: cruo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cruo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{casbinrule.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cruo.mutation.done = true
	return _node, nil
}
