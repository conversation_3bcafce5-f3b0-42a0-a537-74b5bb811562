// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/notify"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotifyUpdate is the builder for updating Notify entities.
type NotifyUpdate struct {
	config
	hooks    []Hook
	mutation *NotifyMutation
}

// Where appends a list predicates to the NotifyUpdate builder.
func (nu *NotifyUpdate) Where(ps ...predicate.Notify) *NotifyUpdate {
	nu.mutation.Where(ps...)
	return nu
}

// SetUpdatedAt sets the "updated_at" field.
func (nu *NotifyUpdate) SetUpdatedAt(t time.Time) *NotifyUpdate {
	nu.mutation.SetUpdatedAt(t)
	return nu
}

// SetTenantID sets the "tenant_id" field.
func (nu *NotifyUpdate) SetTenantID(i int) *NotifyUpdate {
	nu.mutation.SetTenantID(i)
	return nu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableTenantID(i *int) *NotifyUpdate {
	if i != nil {
		nu.SetTenantID(*i)
	}
	return nu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (nu *NotifyUpdate) ClearTenantID() *NotifyUpdate {
	nu.mutation.ClearTenantID()
	return nu
}

// SetRemark sets the "remark" field.
func (nu *NotifyUpdate) SetRemark(s string) *NotifyUpdate {
	nu.mutation.SetRemark(s)
	return nu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableRemark(s *string) *NotifyUpdate {
	if s != nil {
		nu.SetRemark(*s)
	}
	return nu
}

// ClearRemark clears the value of the "remark" field.
func (nu *NotifyUpdate) ClearRemark() *NotifyUpdate {
	nu.mutation.ClearRemark()
	return nu
}

// SetName sets the "name" field.
func (nu *NotifyUpdate) SetName(s string) *NotifyUpdate {
	nu.mutation.SetName(s)
	return nu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableName(s *string) *NotifyUpdate {
	if s != nil {
		nu.SetName(*s)
	}
	return nu
}

// SetPopo sets the "popo" field.
func (nu *NotifyUpdate) SetPopo(b bool) *NotifyUpdate {
	nu.mutation.SetPopo(b)
	return nu
}

// SetNillablePopo sets the "popo" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillablePopo(b *bool) *NotifyUpdate {
	if b != nil {
		nu.SetPopo(*b)
	}
	return nu
}

// SetEmail sets the "email" field.
func (nu *NotifyUpdate) SetEmail(b bool) *NotifyUpdate {
	nu.mutation.SetEmail(b)
	return nu
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableEmail(b *bool) *NotifyUpdate {
	if b != nil {
		nu.SetEmail(*b)
	}
	return nu
}

// SetSms sets the "sms" field.
func (nu *NotifyUpdate) SetSms(b bool) *NotifyUpdate {
	nu.mutation.SetSms(b)
	return nu
}

// SetNillableSms sets the "sms" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableSms(b *bool) *NotifyUpdate {
	if b != nil {
		nu.SetSms(*b)
	}
	return nu
}

// SetPhone sets the "phone" field.
func (nu *NotifyUpdate) SetPhone(b bool) *NotifyUpdate {
	nu.mutation.SetPhone(b)
	return nu
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillablePhone(b *bool) *NotifyUpdate {
	if b != nil {
		nu.SetPhone(*b)
	}
	return nu
}

// SetPopoGroups sets the "popo_groups" field.
func (nu *NotifyUpdate) SetPopoGroups(s *[]string) *NotifyUpdate {
	nu.mutation.SetPopoGroups(s)
	return nu
}

// SetEmails sets the "emails" field.
func (nu *NotifyUpdate) SetEmails(s *[]string) *NotifyUpdate {
	nu.mutation.SetEmails(s)
	return nu
}

// SetPhones sets the "phones" field.
func (nu *NotifyUpdate) SetPhones(s *[]string) *NotifyUpdate {
	nu.mutation.SetPhones(s)
	return nu
}

// SetIPWhitelists sets the "ip_whitelists" field.
func (nu *NotifyUpdate) SetIPWhitelists(s *[]string) *NotifyUpdate {
	nu.mutation.SetIPWhitelists(s)
	return nu
}

// SetSystem sets the "system" field.
func (nu *NotifyUpdate) SetSystem(b bool) *NotifyUpdate {
	nu.mutation.SetSystem(b)
	return nu
}

// SetNillableSystem sets the "system" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableSystem(b *bool) *NotifyUpdate {
	if b != nil {
		nu.SetSystem(*b)
	}
	return nu
}

// SetEnabled sets the "enabled" field.
func (nu *NotifyUpdate) SetEnabled(b bool) *NotifyUpdate {
	nu.mutation.SetEnabled(b)
	return nu
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableEnabled(b *bool) *NotifyUpdate {
	if b != nil {
		nu.SetEnabled(*b)
	}
	return nu
}

// SetSaNotifyPopo sets the "sa_notify_popo" field.
func (nu *NotifyUpdate) SetSaNotifyPopo(b bool) *NotifyUpdate {
	nu.mutation.SetSaNotifyPopo(b)
	return nu
}

// SetNillableSaNotifyPopo sets the "sa_notify_popo" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableSaNotifyPopo(b *bool) *NotifyUpdate {
	if b != nil {
		nu.SetSaNotifyPopo(*b)
	}
	return nu
}

// SetSaNotifyEmail sets the "sa_notify_email" field.
func (nu *NotifyUpdate) SetSaNotifyEmail(b bool) *NotifyUpdate {
	nu.mutation.SetSaNotifyEmail(b)
	return nu
}

// SetNillableSaNotifyEmail sets the "sa_notify_email" field if the given value is not nil.
func (nu *NotifyUpdate) SetNillableSaNotifyEmail(b *bool) *NotifyUpdate {
	if b != nil {
		nu.SetSaNotifyEmail(*b)
	}
	return nu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (nu *NotifyUpdate) SetTenant(t *Tenant) *NotifyUpdate {
	return nu.SetTenantID(t.ID)
}

// Mutation returns the NotifyMutation object of the builder.
func (nu *NotifyUpdate) Mutation() *NotifyMutation {
	return nu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (nu *NotifyUpdate) ClearTenant() *NotifyUpdate {
	nu.mutation.ClearTenant()
	return nu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (nu *NotifyUpdate) Save(ctx context.Context) (int, error) {
	if err := nu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, nu.sqlSave, nu.mutation, nu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nu *NotifyUpdate) SaveX(ctx context.Context) int {
	affected, err := nu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (nu *NotifyUpdate) Exec(ctx context.Context) error {
	_, err := nu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nu *NotifyUpdate) ExecX(ctx context.Context) {
	if err := nu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (nu *NotifyUpdate) defaults() error {
	if _, ok := nu.mutation.UpdatedAt(); !ok {
		if notify.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized notify.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := notify.UpdateDefaultUpdatedAt()
		nu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (nu *NotifyUpdate) check() error {
	if v, ok := nu.mutation.Remark(); ok {
		if err := notify.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Notify.remark": %w`, err)}
		}
	}
	return nil
}

func (nu *NotifyUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := nu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(notify.Table, notify.Columns, sqlgraph.NewFieldSpec(notify.FieldID, field.TypeInt))
	if ps := nu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := nu.mutation.UpdatedAt(); ok {
		_spec.SetField(notify.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := nu.mutation.Remark(); ok {
		_spec.SetField(notify.FieldRemark, field.TypeString, value)
	}
	if nu.mutation.RemarkCleared() {
		_spec.ClearField(notify.FieldRemark, field.TypeString)
	}
	if value, ok := nu.mutation.Name(); ok {
		_spec.SetField(notify.FieldName, field.TypeString, value)
	}
	if value, ok := nu.mutation.Popo(); ok {
		_spec.SetField(notify.FieldPopo, field.TypeBool, value)
	}
	if value, ok := nu.mutation.Email(); ok {
		_spec.SetField(notify.FieldEmail, field.TypeBool, value)
	}
	if value, ok := nu.mutation.Sms(); ok {
		_spec.SetField(notify.FieldSms, field.TypeBool, value)
	}
	if value, ok := nu.mutation.Phone(); ok {
		_spec.SetField(notify.FieldPhone, field.TypeBool, value)
	}
	if value, ok := nu.mutation.PopoGroups(); ok {
		_spec.SetField(notify.FieldPopoGroups, field.TypeJSON, value)
	}
	if value, ok := nu.mutation.Emails(); ok {
		_spec.SetField(notify.FieldEmails, field.TypeJSON, value)
	}
	if value, ok := nu.mutation.Phones(); ok {
		_spec.SetField(notify.FieldPhones, field.TypeJSON, value)
	}
	if value, ok := nu.mutation.IPWhitelists(); ok {
		_spec.SetField(notify.FieldIPWhitelists, field.TypeJSON, value)
	}
	if value, ok := nu.mutation.System(); ok {
		_spec.SetField(notify.FieldSystem, field.TypeBool, value)
	}
	if value, ok := nu.mutation.Enabled(); ok {
		_spec.SetField(notify.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := nu.mutation.SaNotifyPopo(); ok {
		_spec.SetField(notify.FieldSaNotifyPopo, field.TypeBool, value)
	}
	if value, ok := nu.mutation.SaNotifyEmail(); ok {
		_spec.SetField(notify.FieldSaNotifyEmail, field.TypeBool, value)
	}
	if nu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   notify.TenantTable,
			Columns: []string{notify.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := nu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   notify.TenantTable,
			Columns: []string{notify.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, nu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notify.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	nu.mutation.done = true
	return n, nil
}

// NotifyUpdateOne is the builder for updating a single Notify entity.
type NotifyUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *NotifyMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (nuo *NotifyUpdateOne) SetUpdatedAt(t time.Time) *NotifyUpdateOne {
	nuo.mutation.SetUpdatedAt(t)
	return nuo
}

// SetTenantID sets the "tenant_id" field.
func (nuo *NotifyUpdateOne) SetTenantID(i int) *NotifyUpdateOne {
	nuo.mutation.SetTenantID(i)
	return nuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableTenantID(i *int) *NotifyUpdateOne {
	if i != nil {
		nuo.SetTenantID(*i)
	}
	return nuo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (nuo *NotifyUpdateOne) ClearTenantID() *NotifyUpdateOne {
	nuo.mutation.ClearTenantID()
	return nuo
}

// SetRemark sets the "remark" field.
func (nuo *NotifyUpdateOne) SetRemark(s string) *NotifyUpdateOne {
	nuo.mutation.SetRemark(s)
	return nuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableRemark(s *string) *NotifyUpdateOne {
	if s != nil {
		nuo.SetRemark(*s)
	}
	return nuo
}

// ClearRemark clears the value of the "remark" field.
func (nuo *NotifyUpdateOne) ClearRemark() *NotifyUpdateOne {
	nuo.mutation.ClearRemark()
	return nuo
}

// SetName sets the "name" field.
func (nuo *NotifyUpdateOne) SetName(s string) *NotifyUpdateOne {
	nuo.mutation.SetName(s)
	return nuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableName(s *string) *NotifyUpdateOne {
	if s != nil {
		nuo.SetName(*s)
	}
	return nuo
}

// SetPopo sets the "popo" field.
func (nuo *NotifyUpdateOne) SetPopo(b bool) *NotifyUpdateOne {
	nuo.mutation.SetPopo(b)
	return nuo
}

// SetNillablePopo sets the "popo" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillablePopo(b *bool) *NotifyUpdateOne {
	if b != nil {
		nuo.SetPopo(*b)
	}
	return nuo
}

// SetEmail sets the "email" field.
func (nuo *NotifyUpdateOne) SetEmail(b bool) *NotifyUpdateOne {
	nuo.mutation.SetEmail(b)
	return nuo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableEmail(b *bool) *NotifyUpdateOne {
	if b != nil {
		nuo.SetEmail(*b)
	}
	return nuo
}

// SetSms sets the "sms" field.
func (nuo *NotifyUpdateOne) SetSms(b bool) *NotifyUpdateOne {
	nuo.mutation.SetSms(b)
	return nuo
}

// SetNillableSms sets the "sms" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableSms(b *bool) *NotifyUpdateOne {
	if b != nil {
		nuo.SetSms(*b)
	}
	return nuo
}

// SetPhone sets the "phone" field.
func (nuo *NotifyUpdateOne) SetPhone(b bool) *NotifyUpdateOne {
	nuo.mutation.SetPhone(b)
	return nuo
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillablePhone(b *bool) *NotifyUpdateOne {
	if b != nil {
		nuo.SetPhone(*b)
	}
	return nuo
}

// SetPopoGroups sets the "popo_groups" field.
func (nuo *NotifyUpdateOne) SetPopoGroups(s *[]string) *NotifyUpdateOne {
	nuo.mutation.SetPopoGroups(s)
	return nuo
}

// SetEmails sets the "emails" field.
func (nuo *NotifyUpdateOne) SetEmails(s *[]string) *NotifyUpdateOne {
	nuo.mutation.SetEmails(s)
	return nuo
}

// SetPhones sets the "phones" field.
func (nuo *NotifyUpdateOne) SetPhones(s *[]string) *NotifyUpdateOne {
	nuo.mutation.SetPhones(s)
	return nuo
}

// SetIPWhitelists sets the "ip_whitelists" field.
func (nuo *NotifyUpdateOne) SetIPWhitelists(s *[]string) *NotifyUpdateOne {
	nuo.mutation.SetIPWhitelists(s)
	return nuo
}

// SetSystem sets the "system" field.
func (nuo *NotifyUpdateOne) SetSystem(b bool) *NotifyUpdateOne {
	nuo.mutation.SetSystem(b)
	return nuo
}

// SetNillableSystem sets the "system" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableSystem(b *bool) *NotifyUpdateOne {
	if b != nil {
		nuo.SetSystem(*b)
	}
	return nuo
}

// SetEnabled sets the "enabled" field.
func (nuo *NotifyUpdateOne) SetEnabled(b bool) *NotifyUpdateOne {
	nuo.mutation.SetEnabled(b)
	return nuo
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableEnabled(b *bool) *NotifyUpdateOne {
	if b != nil {
		nuo.SetEnabled(*b)
	}
	return nuo
}

// SetSaNotifyPopo sets the "sa_notify_popo" field.
func (nuo *NotifyUpdateOne) SetSaNotifyPopo(b bool) *NotifyUpdateOne {
	nuo.mutation.SetSaNotifyPopo(b)
	return nuo
}

// SetNillableSaNotifyPopo sets the "sa_notify_popo" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableSaNotifyPopo(b *bool) *NotifyUpdateOne {
	if b != nil {
		nuo.SetSaNotifyPopo(*b)
	}
	return nuo
}

// SetSaNotifyEmail sets the "sa_notify_email" field.
func (nuo *NotifyUpdateOne) SetSaNotifyEmail(b bool) *NotifyUpdateOne {
	nuo.mutation.SetSaNotifyEmail(b)
	return nuo
}

// SetNillableSaNotifyEmail sets the "sa_notify_email" field if the given value is not nil.
func (nuo *NotifyUpdateOne) SetNillableSaNotifyEmail(b *bool) *NotifyUpdateOne {
	if b != nil {
		nuo.SetSaNotifyEmail(*b)
	}
	return nuo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (nuo *NotifyUpdateOne) SetTenant(t *Tenant) *NotifyUpdateOne {
	return nuo.SetTenantID(t.ID)
}

// Mutation returns the NotifyMutation object of the builder.
func (nuo *NotifyUpdateOne) Mutation() *NotifyMutation {
	return nuo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (nuo *NotifyUpdateOne) ClearTenant() *NotifyUpdateOne {
	nuo.mutation.ClearTenant()
	return nuo
}

// Where appends a list predicates to the NotifyUpdate builder.
func (nuo *NotifyUpdateOne) Where(ps ...predicate.Notify) *NotifyUpdateOne {
	nuo.mutation.Where(ps...)
	return nuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (nuo *NotifyUpdateOne) Select(field string, fields ...string) *NotifyUpdateOne {
	nuo.fields = append([]string{field}, fields...)
	return nuo
}

// Save executes the query and returns the updated Notify entity.
func (nuo *NotifyUpdateOne) Save(ctx context.Context) (*Notify, error) {
	if err := nuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, nuo.sqlSave, nuo.mutation, nuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (nuo *NotifyUpdateOne) SaveX(ctx context.Context) *Notify {
	node, err := nuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (nuo *NotifyUpdateOne) Exec(ctx context.Context) error {
	_, err := nuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nuo *NotifyUpdateOne) ExecX(ctx context.Context) {
	if err := nuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (nuo *NotifyUpdateOne) defaults() error {
	if _, ok := nuo.mutation.UpdatedAt(); !ok {
		if notify.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized notify.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := notify.UpdateDefaultUpdatedAt()
		nuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (nuo *NotifyUpdateOne) check() error {
	if v, ok := nuo.mutation.Remark(); ok {
		if err := notify.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Notify.remark": %w`, err)}
		}
	}
	return nil
}

func (nuo *NotifyUpdateOne) sqlSave(ctx context.Context) (_node *Notify, err error) {
	if err := nuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(notify.Table, notify.Columns, sqlgraph.NewFieldSpec(notify.FieldID, field.TypeInt))
	id, ok := nuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Notify.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := nuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notify.FieldID)
		for _, f := range fields {
			if !notify.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != notify.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := nuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := nuo.mutation.UpdatedAt(); ok {
		_spec.SetField(notify.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := nuo.mutation.Remark(); ok {
		_spec.SetField(notify.FieldRemark, field.TypeString, value)
	}
	if nuo.mutation.RemarkCleared() {
		_spec.ClearField(notify.FieldRemark, field.TypeString)
	}
	if value, ok := nuo.mutation.Name(); ok {
		_spec.SetField(notify.FieldName, field.TypeString, value)
	}
	if value, ok := nuo.mutation.Popo(); ok {
		_spec.SetField(notify.FieldPopo, field.TypeBool, value)
	}
	if value, ok := nuo.mutation.Email(); ok {
		_spec.SetField(notify.FieldEmail, field.TypeBool, value)
	}
	if value, ok := nuo.mutation.Sms(); ok {
		_spec.SetField(notify.FieldSms, field.TypeBool, value)
	}
	if value, ok := nuo.mutation.Phone(); ok {
		_spec.SetField(notify.FieldPhone, field.TypeBool, value)
	}
	if value, ok := nuo.mutation.PopoGroups(); ok {
		_spec.SetField(notify.FieldPopoGroups, field.TypeJSON, value)
	}
	if value, ok := nuo.mutation.Emails(); ok {
		_spec.SetField(notify.FieldEmails, field.TypeJSON, value)
	}
	if value, ok := nuo.mutation.Phones(); ok {
		_spec.SetField(notify.FieldPhones, field.TypeJSON, value)
	}
	if value, ok := nuo.mutation.IPWhitelists(); ok {
		_spec.SetField(notify.FieldIPWhitelists, field.TypeJSON, value)
	}
	if value, ok := nuo.mutation.System(); ok {
		_spec.SetField(notify.FieldSystem, field.TypeBool, value)
	}
	if value, ok := nuo.mutation.Enabled(); ok {
		_spec.SetField(notify.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := nuo.mutation.SaNotifyPopo(); ok {
		_spec.SetField(notify.FieldSaNotifyPopo, field.TypeBool, value)
	}
	if value, ok := nuo.mutation.SaNotifyEmail(); ok {
		_spec.SetField(notify.FieldSaNotifyEmail, field.TypeBool, value)
	}
	if nuo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   notify.TenantTable,
			Columns: []string{notify.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := nuo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   notify.TenantTable,
			Columns: []string{notify.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Notify{config: nuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, nuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{notify.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	nuo.mutation.done = true
	return _node, nil
}
