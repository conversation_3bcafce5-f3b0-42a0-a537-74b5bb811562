// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cleandata"
	"meta/app/ent/protectgroup"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"
	"meta/app/entity/netease"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SpectrumAlertCreate is the builder for creating a SpectrumAlert entity.
type SpectrumAlertCreate struct {
	config
	mutation *SpectrumAlertMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (sac *SpectrumAlertCreate) SetTenantID(i int) *SpectrumAlertCreate {
	sac.mutation.SetTenantID(i)
	return sac
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableTenantID(i *int) *SpectrumAlertCreate {
	if i != nil {
		sac.SetTenantID(*i)
	}
	return sac
}

// SetCreatedAt sets the "created_at" field.
func (sac *SpectrumAlertCreate) SetCreatedAt(t time.Time) *SpectrumAlertCreate {
	sac.mutation.SetCreatedAt(t)
	return sac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableCreatedAt(t *time.Time) *SpectrumAlertCreate {
	if t != nil {
		sac.SetCreatedAt(*t)
	}
	return sac
}

// SetUpdatedAt sets the "updated_at" field.
func (sac *SpectrumAlertCreate) SetUpdatedAt(t time.Time) *SpectrumAlertCreate {
	sac.mutation.SetUpdatedAt(t)
	return sac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableUpdatedAt(t *time.Time) *SpectrumAlertCreate {
	if t != nil {
		sac.SetUpdatedAt(*t)
	}
	return sac
}

// SetRemark sets the "remark" field.
func (sac *SpectrumAlertCreate) SetRemark(s string) *SpectrumAlertCreate {
	sac.mutation.SetRemark(s)
	return sac
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableRemark(s *string) *SpectrumAlertCreate {
	if s != nil {
		sac.SetRemark(*s)
	}
	return sac
}

// SetProtectGroupID sets the "protect_group_id" field.
func (sac *SpectrumAlertCreate) SetProtectGroupID(i int) *SpectrumAlertCreate {
	sac.mutation.SetProtectGroupID(i)
	return sac
}

// SetNillableProtectGroupID sets the "protect_group_id" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableProtectGroupID(i *int) *SpectrumAlertCreate {
	if i != nil {
		sac.SetProtectGroupID(*i)
	}
	return sac
}

// SetStrategyID sets the "strategy_id" field.
func (sac *SpectrumAlertCreate) SetStrategyID(i int) *SpectrumAlertCreate {
	sac.mutation.SetStrategyID(i)
	return sac
}

// SetNillableStrategyID sets the "strategy_id" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableStrategyID(i *int) *SpectrumAlertCreate {
	if i != nil {
		sac.SetStrategyID(*i)
	}
	return sac
}

// SetWofangID sets the "wofang_id" field.
func (sac *SpectrumAlertCreate) SetWofangID(i int) *SpectrumAlertCreate {
	sac.mutation.SetWofangID(i)
	return sac
}

// SetNillableWofangID sets the "wofang_id" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableWofangID(i *int) *SpectrumAlertCreate {
	if i != nil {
		sac.SetWofangID(*i)
	}
	return sac
}

// SetProtectStatus sets the "protect_status" field.
func (sac *SpectrumAlertCreate) SetProtectStatus(i *[]int) *SpectrumAlertCreate {
	sac.mutation.SetProtectStatus(i)
	return sac
}

// SetIP sets the "ip" field.
func (sac *SpectrumAlertCreate) SetIP(s string) *SpectrumAlertCreate {
	sac.mutation.SetIP(s)
	return sac
}

// SetStartTime sets the "start_time" field.
func (sac *SpectrumAlertCreate) SetStartTime(t time.Time) *SpectrumAlertCreate {
	sac.mutation.SetStartTime(t)
	return sac
}

// SetEndTime sets the "end_time" field.
func (sac *SpectrumAlertCreate) SetEndTime(t time.Time) *SpectrumAlertCreate {
	sac.mutation.SetEndTime(t)
	return sac
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableEndTime(t *time.Time) *SpectrumAlertCreate {
	if t != nil {
		sac.SetEndTime(*t)
	}
	return sac
}

// SetAttackType sets the "attack_type" field.
func (sac *SpectrumAlertCreate) SetAttackType(s string) *SpectrumAlertCreate {
	sac.mutation.SetAttackType(s)
	return sac
}

// SetMaxPps sets the "max_pps" field.
func (sac *SpectrumAlertCreate) SetMaxPps(i int64) *SpectrumAlertCreate {
	sac.mutation.SetMaxPps(i)
	return sac
}

// SetMaxBps sets the "max_bps" field.
func (sac *SpectrumAlertCreate) SetMaxBps(i int64) *SpectrumAlertCreate {
	sac.mutation.SetMaxBps(i)
	return sac
}

// SetAttackInfo sets the "attack_info" field.
func (sac *SpectrumAlertCreate) SetAttackInfo(ni netease.AttackInfo) *SpectrumAlertCreate {
	sac.mutation.SetAttackInfo(ni)
	return sac
}

// SetNillableAttackInfo sets the "attack_info" field if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableAttackInfo(ni *netease.AttackInfo) *SpectrumAlertCreate {
	if ni != nil {
		sac.SetAttackInfo(*ni)
	}
	return sac
}

// SetIspCode sets the "isp_code" field.
func (sac *SpectrumAlertCreate) SetIspCode(i int) *SpectrumAlertCreate {
	sac.mutation.SetIspCode(i)
	return sac
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sac *SpectrumAlertCreate) SetTenant(t *Tenant) *SpectrumAlertCreate {
	return sac.SetTenantID(t.ID)
}

// AddSpectrumDataIDs adds the "spectrum_datas" edge to the SpectrumData entity by IDs.
func (sac *SpectrumAlertCreate) AddSpectrumDataIDs(ids ...int) *SpectrumAlertCreate {
	sac.mutation.AddSpectrumDataIDs(ids...)
	return sac
}

// AddSpectrumDatas adds the "spectrum_datas" edges to the SpectrumData entity.
func (sac *SpectrumAlertCreate) AddSpectrumDatas(s ...*SpectrumData) *SpectrumAlertCreate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return sac.AddSpectrumDataIDs(ids...)
}

// AddCleanDataIDs adds the "clean_datas" edge to the CleanData entity by IDs.
func (sac *SpectrumAlertCreate) AddCleanDataIDs(ids ...int) *SpectrumAlertCreate {
	sac.mutation.AddCleanDataIDs(ids...)
	return sac
}

// AddCleanDatas adds the "clean_datas" edges to the CleanData entity.
func (sac *SpectrumAlertCreate) AddCleanDatas(c ...*CleanData) *SpectrumAlertCreate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return sac.AddCleanDataIDs(ids...)
}

// SetProtectGroup sets the "protect_group" edge to the ProtectGroup entity.
func (sac *SpectrumAlertCreate) SetProtectGroup(p *ProtectGroup) *SpectrumAlertCreate {
	return sac.SetProtectGroupID(p.ID)
}

// SetStrategy sets the "strategy" edge to the Strategy entity.
func (sac *SpectrumAlertCreate) SetStrategy(s *Strategy) *SpectrumAlertCreate {
	return sac.SetStrategyID(s.ID)
}

// SetWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID.
func (sac *SpectrumAlertCreate) SetWofangTicketID(id int) *SpectrumAlertCreate {
	sac.mutation.SetWofangTicketID(id)
	return sac
}

// SetNillableWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID if the given value is not nil.
func (sac *SpectrumAlertCreate) SetNillableWofangTicketID(id *int) *SpectrumAlertCreate {
	if id != nil {
		sac = sac.SetWofangTicketID(*id)
	}
	return sac
}

// SetWofangTicket sets the "wofang_ticket" edge to the Wofang entity.
func (sac *SpectrumAlertCreate) SetWofangTicket(w *Wofang) *SpectrumAlertCreate {
	return sac.SetWofangTicketID(w.ID)
}

// Mutation returns the SpectrumAlertMutation object of the builder.
func (sac *SpectrumAlertCreate) Mutation() *SpectrumAlertMutation {
	return sac.mutation
}

// Save creates the SpectrumAlert in the database.
func (sac *SpectrumAlertCreate) Save(ctx context.Context) (*SpectrumAlert, error) {
	if err := sac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sac.sqlSave, sac.mutation, sac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sac *SpectrumAlertCreate) SaveX(ctx context.Context) *SpectrumAlert {
	v, err := sac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sac *SpectrumAlertCreate) Exec(ctx context.Context) error {
	_, err := sac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sac *SpectrumAlertCreate) ExecX(ctx context.Context) {
	if err := sac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sac *SpectrumAlertCreate) defaults() error {
	if _, ok := sac.mutation.CreatedAt(); !ok {
		if spectrumalert.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized spectrumalert.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := spectrumalert.DefaultCreatedAt()
		sac.mutation.SetCreatedAt(v)
	}
	if _, ok := sac.mutation.UpdatedAt(); !ok {
		if spectrumalert.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized spectrumalert.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := spectrumalert.DefaultUpdatedAt()
		sac.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sac *SpectrumAlertCreate) check() error {
	if _, ok := sac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "SpectrumAlert.created_at"`)}
	}
	if _, ok := sac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "SpectrumAlert.updated_at"`)}
	}
	if v, ok := sac.mutation.Remark(); ok {
		if err := spectrumalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SpectrumAlert.remark": %w`, err)}
		}
	}
	if _, ok := sac.mutation.IP(); !ok {
		return &ValidationError{Name: "ip", err: errors.New(`ent: missing required field "SpectrumAlert.ip"`)}
	}
	if _, ok := sac.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "SpectrumAlert.start_time"`)}
	}
	if _, ok := sac.mutation.AttackType(); !ok {
		return &ValidationError{Name: "attack_type", err: errors.New(`ent: missing required field "SpectrumAlert.attack_type"`)}
	}
	if _, ok := sac.mutation.MaxPps(); !ok {
		return &ValidationError{Name: "max_pps", err: errors.New(`ent: missing required field "SpectrumAlert.max_pps"`)}
	}
	if _, ok := sac.mutation.MaxBps(); !ok {
		return &ValidationError{Name: "max_bps", err: errors.New(`ent: missing required field "SpectrumAlert.max_bps"`)}
	}
	if _, ok := sac.mutation.IspCode(); !ok {
		return &ValidationError{Name: "isp_code", err: errors.New(`ent: missing required field "SpectrumAlert.isp_code"`)}
	}
	return nil
}

func (sac *SpectrumAlertCreate) sqlSave(ctx context.Context) (*SpectrumAlert, error) {
	if err := sac.check(); err != nil {
		return nil, err
	}
	_node, _spec := sac.createSpec()
	if err := sqlgraph.CreateNode(ctx, sac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sac.mutation.id = &_node.ID
	sac.mutation.done = true
	return _node, nil
}

func (sac *SpectrumAlertCreate) createSpec() (*SpectrumAlert, *sqlgraph.CreateSpec) {
	var (
		_node = &SpectrumAlert{config: sac.config}
		_spec = sqlgraph.NewCreateSpec(spectrumalert.Table, sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt))
	)
	if value, ok := sac.mutation.CreatedAt(); ok {
		_spec.SetField(spectrumalert.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sac.mutation.UpdatedAt(); ok {
		_spec.SetField(spectrumalert.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := sac.mutation.Remark(); ok {
		_spec.SetField(spectrumalert.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := sac.mutation.ProtectStatus(); ok {
		_spec.SetField(spectrumalert.FieldProtectStatus, field.TypeJSON, value)
		_node.ProtectStatus = value
	}
	if value, ok := sac.mutation.IP(); ok {
		_spec.SetField(spectrumalert.FieldIP, field.TypeString, value)
		_node.IP = value
	}
	if value, ok := sac.mutation.StartTime(); ok {
		_spec.SetField(spectrumalert.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := sac.mutation.EndTime(); ok {
		_spec.SetField(spectrumalert.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if value, ok := sac.mutation.AttackType(); ok {
		_spec.SetField(spectrumalert.FieldAttackType, field.TypeString, value)
		_node.AttackType = value
	}
	if value, ok := sac.mutation.MaxPps(); ok {
		_spec.SetField(spectrumalert.FieldMaxPps, field.TypeInt64, value)
		_node.MaxPps = value
	}
	if value, ok := sac.mutation.MaxBps(); ok {
		_spec.SetField(spectrumalert.FieldMaxBps, field.TypeInt64, value)
		_node.MaxBps = value
	}
	if value, ok := sac.mutation.AttackInfo(); ok {
		_spec.SetField(spectrumalert.FieldAttackInfo, field.TypeJSON, value)
		_node.AttackInfo = value
	}
	if value, ok := sac.mutation.IspCode(); ok {
		_spec.SetField(spectrumalert.FieldIspCode, field.TypeInt, value)
		_node.IspCode = value
	}
	if nodes := sac.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumalert.TenantTable,
			Columns: []string{spectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sac.mutation.SpectrumDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.SpectrumDatasTable,
			Columns: []string{spectrumalert.SpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sac.mutation.CleanDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.CleanDatasTable,
			Columns: []string{spectrumalert.CleanDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sac.mutation.ProtectGroupIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.ProtectGroupTable,
			Columns: []string{spectrumalert.ProtectGroupColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ProtectGroupID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sac.mutation.StrategyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.StrategyTable,
			Columns: []string{spectrumalert.StrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.StrategyID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sac.mutation.WofangTicketIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.WofangTicketTable,
			Columns: []string{spectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.WofangID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// SpectrumAlertCreateBulk is the builder for creating many SpectrumAlert entities in bulk.
type SpectrumAlertCreateBulk struct {
	config
	err      error
	builders []*SpectrumAlertCreate
}

// Save creates the SpectrumAlert entities in the database.
func (sacb *SpectrumAlertCreateBulk) Save(ctx context.Context) ([]*SpectrumAlert, error) {
	if sacb.err != nil {
		return nil, sacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(sacb.builders))
	nodes := make([]*SpectrumAlert, len(sacb.builders))
	mutators := make([]Mutator, len(sacb.builders))
	for i := range sacb.builders {
		func(i int, root context.Context) {
			builder := sacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SpectrumAlertMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, sacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, sacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, sacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (sacb *SpectrumAlertCreateBulk) SaveX(ctx context.Context) []*SpectrumAlert {
	v, err := sacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sacb *SpectrumAlertCreateBulk) Exec(ctx context.Context) error {
	_, err := sacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sacb *SpectrumAlertCreateBulk) ExecX(ctx context.Context) {
	if err := sacb.Exec(ctx); err != nil {
		panic(err)
	}
}
