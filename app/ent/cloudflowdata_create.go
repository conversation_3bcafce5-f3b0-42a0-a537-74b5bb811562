// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudFlowDataCreate is the builder for creating a CloudFlowData entity.
type CloudFlowDataCreate struct {
	config
	mutation *CloudFlowDataMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (cfdc *CloudFlowDataCreate) SetTenantID(i int) *CloudFlowDataCreate {
	cfdc.mutation.SetTenantID(i)
	return cfdc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cfdc *CloudFlowDataCreate) SetNillableTenantID(i *int) *CloudFlowDataCreate {
	if i != nil {
		cfdc.SetTenantID(*i)
	}
	return cfdc
}

// SetCreatedAt sets the "created_at" field.
func (cfdc *CloudFlowDataCreate) SetCreatedAt(t time.Time) *CloudFlowDataCreate {
	cfdc.mutation.SetCreatedAt(t)
	return cfdc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (cfdc *CloudFlowDataCreate) SetNillableCreatedAt(t *time.Time) *CloudFlowDataCreate {
	if t != nil {
		cfdc.SetCreatedAt(*t)
	}
	return cfdc
}

// SetUpdatedAt sets the "updated_at" field.
func (cfdc *CloudFlowDataCreate) SetUpdatedAt(t time.Time) *CloudFlowDataCreate {
	cfdc.mutation.SetUpdatedAt(t)
	return cfdc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (cfdc *CloudFlowDataCreate) SetNillableUpdatedAt(t *time.Time) *CloudFlowDataCreate {
	if t != nil {
		cfdc.SetUpdatedAt(*t)
	}
	return cfdc
}

// SetRemark sets the "remark" field.
func (cfdc *CloudFlowDataCreate) SetRemark(s string) *CloudFlowDataCreate {
	cfdc.mutation.SetRemark(s)
	return cfdc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (cfdc *CloudFlowDataCreate) SetNillableRemark(s *string) *CloudFlowDataCreate {
	if s != nil {
		cfdc.SetRemark(*s)
	}
	return cfdc
}

// SetCloudAlertID sets the "cloud_alert_id" field.
func (cfdc *CloudFlowDataCreate) SetCloudAlertID(i int) *CloudFlowDataCreate {
	cfdc.mutation.SetCloudAlertID(i)
	return cfdc
}

// SetNillableCloudAlertID sets the "cloud_alert_id" field if the given value is not nil.
func (cfdc *CloudFlowDataCreate) SetNillableCloudAlertID(i *int) *CloudFlowDataCreate {
	if i != nil {
		cfdc.SetCloudAlertID(*i)
	}
	return cfdc
}

// SetSrcIP sets the "src_ip" field.
func (cfdc *CloudFlowDataCreate) SetSrcIP(s string) *CloudFlowDataCreate {
	cfdc.mutation.SetSrcIP(s)
	return cfdc
}

// SetSrcPort sets the "src_port" field.
func (cfdc *CloudFlowDataCreate) SetSrcPort(i int) *CloudFlowDataCreate {
	cfdc.mutation.SetSrcPort(i)
	return cfdc
}

// SetDstIP sets the "dst_ip" field.
func (cfdc *CloudFlowDataCreate) SetDstIP(s string) *CloudFlowDataCreate {
	cfdc.mutation.SetDstIP(s)
	return cfdc
}

// SetDstPort sets the "dst_port" field.
func (cfdc *CloudFlowDataCreate) SetDstPort(i int) *CloudFlowDataCreate {
	cfdc.mutation.SetDstPort(i)
	return cfdc
}

// SetProtocol sets the "protocol" field.
func (cfdc *CloudFlowDataCreate) SetProtocol(i int) *CloudFlowDataCreate {
	cfdc.mutation.SetProtocol(i)
	return cfdc
}

// SetMaxAttackPps sets the "max_attack_pps" field.
func (cfdc *CloudFlowDataCreate) SetMaxAttackPps(i int64) *CloudFlowDataCreate {
	cfdc.mutation.SetMaxAttackPps(i)
	return cfdc
}

// SetFlowOverMaxPpsCount sets the "flow_over_max_pps_count" field.
func (cfdc *CloudFlowDataCreate) SetFlowOverMaxPpsCount(i int) *CloudFlowDataCreate {
	cfdc.mutation.SetFlowOverMaxPpsCount(i)
	return cfdc
}

// SetStartTime sets the "start_time" field.
func (cfdc *CloudFlowDataCreate) SetStartTime(t time.Time) *CloudFlowDataCreate {
	cfdc.mutation.SetStartTime(t)
	return cfdc
}

// SetEndTime sets the "end_time" field.
func (cfdc *CloudFlowDataCreate) SetEndTime(t time.Time) *CloudFlowDataCreate {
	cfdc.mutation.SetEndTime(t)
	return cfdc
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (cfdc *CloudFlowDataCreate) SetNillableEndTime(t *time.Time) *CloudFlowDataCreate {
	if t != nil {
		cfdc.SetEndTime(*t)
	}
	return cfdc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cfdc *CloudFlowDataCreate) SetTenant(t *Tenant) *CloudFlowDataCreate {
	return cfdc.SetTenantID(t.ID)
}

// SetCloudAlert sets the "cloud_alert" edge to the CloudAlert entity.
func (cfdc *CloudFlowDataCreate) SetCloudAlert(c *CloudAlert) *CloudFlowDataCreate {
	return cfdc.SetCloudAlertID(c.ID)
}

// Mutation returns the CloudFlowDataMutation object of the builder.
func (cfdc *CloudFlowDataCreate) Mutation() *CloudFlowDataMutation {
	return cfdc.mutation
}

// Save creates the CloudFlowData in the database.
func (cfdc *CloudFlowDataCreate) Save(ctx context.Context) (*CloudFlowData, error) {
	if err := cfdc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, cfdc.sqlSave, cfdc.mutation, cfdc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cfdc *CloudFlowDataCreate) SaveX(ctx context.Context) *CloudFlowData {
	v, err := cfdc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cfdc *CloudFlowDataCreate) Exec(ctx context.Context) error {
	_, err := cfdc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cfdc *CloudFlowDataCreate) ExecX(ctx context.Context) {
	if err := cfdc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cfdc *CloudFlowDataCreate) defaults() error {
	if _, ok := cfdc.mutation.CreatedAt(); !ok {
		if cloudflowdata.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudflowdata.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := cloudflowdata.DefaultCreatedAt()
		cfdc.mutation.SetCreatedAt(v)
	}
	if _, ok := cfdc.mutation.UpdatedAt(); !ok {
		if cloudflowdata.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudflowdata.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudflowdata.DefaultUpdatedAt()
		cfdc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cfdc *CloudFlowDataCreate) check() error {
	if _, ok := cfdc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "CloudFlowData.created_at"`)}
	}
	if _, ok := cfdc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "CloudFlowData.updated_at"`)}
	}
	if v, ok := cfdc.mutation.Remark(); ok {
		if err := cloudflowdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudFlowData.remark": %w`, err)}
		}
	}
	if _, ok := cfdc.mutation.SrcIP(); !ok {
		return &ValidationError{Name: "src_ip", err: errors.New(`ent: missing required field "CloudFlowData.src_ip"`)}
	}
	if _, ok := cfdc.mutation.SrcPort(); !ok {
		return &ValidationError{Name: "src_port", err: errors.New(`ent: missing required field "CloudFlowData.src_port"`)}
	}
	if _, ok := cfdc.mutation.DstIP(); !ok {
		return &ValidationError{Name: "dst_ip", err: errors.New(`ent: missing required field "CloudFlowData.dst_ip"`)}
	}
	if _, ok := cfdc.mutation.DstPort(); !ok {
		return &ValidationError{Name: "dst_port", err: errors.New(`ent: missing required field "CloudFlowData.dst_port"`)}
	}
	if _, ok := cfdc.mutation.Protocol(); !ok {
		return &ValidationError{Name: "protocol", err: errors.New(`ent: missing required field "CloudFlowData.protocol"`)}
	}
	if _, ok := cfdc.mutation.MaxAttackPps(); !ok {
		return &ValidationError{Name: "max_attack_pps", err: errors.New(`ent: missing required field "CloudFlowData.max_attack_pps"`)}
	}
	if _, ok := cfdc.mutation.FlowOverMaxPpsCount(); !ok {
		return &ValidationError{Name: "flow_over_max_pps_count", err: errors.New(`ent: missing required field "CloudFlowData.flow_over_max_pps_count"`)}
	}
	if _, ok := cfdc.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "CloudFlowData.start_time"`)}
	}
	return nil
}

func (cfdc *CloudFlowDataCreate) sqlSave(ctx context.Context) (*CloudFlowData, error) {
	if err := cfdc.check(); err != nil {
		return nil, err
	}
	_node, _spec := cfdc.createSpec()
	if err := sqlgraph.CreateNode(ctx, cfdc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	cfdc.mutation.id = &_node.ID
	cfdc.mutation.done = true
	return _node, nil
}

func (cfdc *CloudFlowDataCreate) createSpec() (*CloudFlowData, *sqlgraph.CreateSpec) {
	var (
		_node = &CloudFlowData{config: cfdc.config}
		_spec = sqlgraph.NewCreateSpec(cloudflowdata.Table, sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt))
	)
	if value, ok := cfdc.mutation.CreatedAt(); ok {
		_spec.SetField(cloudflowdata.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := cfdc.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudflowdata.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := cfdc.mutation.Remark(); ok {
		_spec.SetField(cloudflowdata.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := cfdc.mutation.SrcIP(); ok {
		_spec.SetField(cloudflowdata.FieldSrcIP, field.TypeString, value)
		_node.SrcIP = value
	}
	if value, ok := cfdc.mutation.SrcPort(); ok {
		_spec.SetField(cloudflowdata.FieldSrcPort, field.TypeInt, value)
		_node.SrcPort = value
	}
	if value, ok := cfdc.mutation.DstIP(); ok {
		_spec.SetField(cloudflowdata.FieldDstIP, field.TypeString, value)
		_node.DstIP = value
	}
	if value, ok := cfdc.mutation.DstPort(); ok {
		_spec.SetField(cloudflowdata.FieldDstPort, field.TypeInt, value)
		_node.DstPort = value
	}
	if value, ok := cfdc.mutation.Protocol(); ok {
		_spec.SetField(cloudflowdata.FieldProtocol, field.TypeInt, value)
		_node.Protocol = value
	}
	if value, ok := cfdc.mutation.MaxAttackPps(); ok {
		_spec.SetField(cloudflowdata.FieldMaxAttackPps, field.TypeInt64, value)
		_node.MaxAttackPps = value
	}
	if value, ok := cfdc.mutation.FlowOverMaxPpsCount(); ok {
		_spec.SetField(cloudflowdata.FieldFlowOverMaxPpsCount, field.TypeInt, value)
		_node.FlowOverMaxPpsCount = value
	}
	if value, ok := cfdc.mutation.StartTime(); ok {
		_spec.SetField(cloudflowdata.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := cfdc.mutation.EndTime(); ok {
		_spec.SetField(cloudflowdata.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if nodes := cfdc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudflowdata.TenantTable,
			Columns: []string{cloudflowdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := cfdc.mutation.CloudAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   cloudflowdata.CloudAlertTable,
			Columns: []string{cloudflowdata.CloudAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.CloudAlertID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// CloudFlowDataCreateBulk is the builder for creating many CloudFlowData entities in bulk.
type CloudFlowDataCreateBulk struct {
	config
	err      error
	builders []*CloudFlowDataCreate
}

// Save creates the CloudFlowData entities in the database.
func (cfdcb *CloudFlowDataCreateBulk) Save(ctx context.Context) ([]*CloudFlowData, error) {
	if cfdcb.err != nil {
		return nil, cfdcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cfdcb.builders))
	nodes := make([]*CloudFlowData, len(cfdcb.builders))
	mutators := make([]Mutator, len(cfdcb.builders))
	for i := range cfdcb.builders {
		func(i int, root context.Context) {
			builder := cfdcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CloudFlowDataMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cfdcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cfdcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cfdcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cfdcb *CloudFlowDataCreateBulk) SaveX(ctx context.Context) []*CloudFlowData {
	v, err := cfdcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cfdcb *CloudFlowDataCreateBulk) Exec(ctx context.Context) error {
	_, err := cfdcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cfdcb *CloudFlowDataCreateBulk) ExecX(ctx context.Context) {
	if err := cfdcb.Exec(ctx); err != nil {
		panic(err)
	}
}
