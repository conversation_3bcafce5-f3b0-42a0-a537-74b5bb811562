// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudAttackDataCreate is the builder for creating a CloudAttackData entity.
type CloudAttackDataCreate struct {
	config
	mutation *CloudAttackDataMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (cadc *CloudAttackDataCreate) SetTenantID(i int) *CloudAttackDataCreate {
	cadc.mutation.SetTenantID(i)
	return cadc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cadc *CloudAttackDataCreate) SetNillableTenantID(i *int) *CloudAttackDataCreate {
	if i != nil {
		cadc.SetTenantID(*i)
	}
	return cadc
}

// SetCreatedAt sets the "created_at" field.
func (cadc *CloudAttackDataCreate) SetCreatedAt(t time.Time) *CloudAttackDataCreate {
	cadc.mutation.SetCreatedAt(t)
	return cadc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (cadc *CloudAttackDataCreate) SetNillableCreatedAt(t *time.Time) *CloudAttackDataCreate {
	if t != nil {
		cadc.SetCreatedAt(*t)
	}
	return cadc
}

// SetUpdatedAt sets the "updated_at" field.
func (cadc *CloudAttackDataCreate) SetUpdatedAt(t time.Time) *CloudAttackDataCreate {
	cadc.mutation.SetUpdatedAt(t)
	return cadc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (cadc *CloudAttackDataCreate) SetNillableUpdatedAt(t *time.Time) *CloudAttackDataCreate {
	if t != nil {
		cadc.SetUpdatedAt(*t)
	}
	return cadc
}

// SetRemark sets the "remark" field.
func (cadc *CloudAttackDataCreate) SetRemark(s string) *CloudAttackDataCreate {
	cadc.mutation.SetRemark(s)
	return cadc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (cadc *CloudAttackDataCreate) SetNillableRemark(s *string) *CloudAttackDataCreate {
	if s != nil {
		cadc.SetRemark(*s)
	}
	return cadc
}

// SetSrcIP sets the "src_ip" field.
func (cadc *CloudAttackDataCreate) SetSrcIP(s string) *CloudAttackDataCreate {
	cadc.mutation.SetSrcIP(s)
	return cadc
}

// SetSrcPort sets the "src_port" field.
func (cadc *CloudAttackDataCreate) SetSrcPort(i int) *CloudAttackDataCreate {
	cadc.mutation.SetSrcPort(i)
	return cadc
}

// SetDstIP sets the "dst_ip" field.
func (cadc *CloudAttackDataCreate) SetDstIP(s string) *CloudAttackDataCreate {
	cadc.mutation.SetDstIP(s)
	return cadc
}

// SetDstPort sets the "dst_port" field.
func (cadc *CloudAttackDataCreate) SetDstPort(i int) *CloudAttackDataCreate {
	cadc.mutation.SetDstPort(i)
	return cadc
}

// SetProtocol sets the "protocol" field.
func (cadc *CloudAttackDataCreate) SetProtocol(i int) *CloudAttackDataCreate {
	cadc.mutation.SetProtocol(i)
	return cadc
}

// SetCurrentAttackPps sets the "current_attack_pps" field.
func (cadc *CloudAttackDataCreate) SetCurrentAttackPps(i int64) *CloudAttackDataCreate {
	cadc.mutation.SetCurrentAttackPps(i)
	return cadc
}

// SetStartTime sets the "start_time" field.
func (cadc *CloudAttackDataCreate) SetStartTime(t time.Time) *CloudAttackDataCreate {
	cadc.mutation.SetStartTime(t)
	return cadc
}

// SetEndTime sets the "end_time" field.
func (cadc *CloudAttackDataCreate) SetEndTime(t time.Time) *CloudAttackDataCreate {
	cadc.mutation.SetEndTime(t)
	return cadc
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (cadc *CloudAttackDataCreate) SetNillableEndTime(t *time.Time) *CloudAttackDataCreate {
	if t != nil {
		cadc.SetEndTime(*t)
	}
	return cadc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cadc *CloudAttackDataCreate) SetTenant(t *Tenant) *CloudAttackDataCreate {
	return cadc.SetTenantID(t.ID)
}

// Mutation returns the CloudAttackDataMutation object of the builder.
func (cadc *CloudAttackDataCreate) Mutation() *CloudAttackDataMutation {
	return cadc.mutation
}

// Save creates the CloudAttackData in the database.
func (cadc *CloudAttackDataCreate) Save(ctx context.Context) (*CloudAttackData, error) {
	if err := cadc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, cadc.sqlSave, cadc.mutation, cadc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cadc *CloudAttackDataCreate) SaveX(ctx context.Context) *CloudAttackData {
	v, err := cadc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cadc *CloudAttackDataCreate) Exec(ctx context.Context) error {
	_, err := cadc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cadc *CloudAttackDataCreate) ExecX(ctx context.Context) {
	if err := cadc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cadc *CloudAttackDataCreate) defaults() error {
	if _, ok := cadc.mutation.CreatedAt(); !ok {
		if cloudattackdata.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudattackdata.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := cloudattackdata.DefaultCreatedAt()
		cadc.mutation.SetCreatedAt(v)
	}
	if _, ok := cadc.mutation.UpdatedAt(); !ok {
		if cloudattackdata.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudattackdata.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudattackdata.DefaultUpdatedAt()
		cadc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cadc *CloudAttackDataCreate) check() error {
	if _, ok := cadc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "CloudAttackData.created_at"`)}
	}
	if _, ok := cadc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "CloudAttackData.updated_at"`)}
	}
	if v, ok := cadc.mutation.Remark(); ok {
		if err := cloudattackdata.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudAttackData.remark": %w`, err)}
		}
	}
	if _, ok := cadc.mutation.SrcIP(); !ok {
		return &ValidationError{Name: "src_ip", err: errors.New(`ent: missing required field "CloudAttackData.src_ip"`)}
	}
	if _, ok := cadc.mutation.SrcPort(); !ok {
		return &ValidationError{Name: "src_port", err: errors.New(`ent: missing required field "CloudAttackData.src_port"`)}
	}
	if _, ok := cadc.mutation.DstIP(); !ok {
		return &ValidationError{Name: "dst_ip", err: errors.New(`ent: missing required field "CloudAttackData.dst_ip"`)}
	}
	if _, ok := cadc.mutation.DstPort(); !ok {
		return &ValidationError{Name: "dst_port", err: errors.New(`ent: missing required field "CloudAttackData.dst_port"`)}
	}
	if _, ok := cadc.mutation.Protocol(); !ok {
		return &ValidationError{Name: "protocol", err: errors.New(`ent: missing required field "CloudAttackData.protocol"`)}
	}
	if _, ok := cadc.mutation.CurrentAttackPps(); !ok {
		return &ValidationError{Name: "current_attack_pps", err: errors.New(`ent: missing required field "CloudAttackData.current_attack_pps"`)}
	}
	if _, ok := cadc.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "CloudAttackData.start_time"`)}
	}
	return nil
}

func (cadc *CloudAttackDataCreate) sqlSave(ctx context.Context) (*CloudAttackData, error) {
	if err := cadc.check(); err != nil {
		return nil, err
	}
	_node, _spec := cadc.createSpec()
	if err := sqlgraph.CreateNode(ctx, cadc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	cadc.mutation.id = &_node.ID
	cadc.mutation.done = true
	return _node, nil
}

func (cadc *CloudAttackDataCreate) createSpec() (*CloudAttackData, *sqlgraph.CreateSpec) {
	var (
		_node = &CloudAttackData{config: cadc.config}
		_spec = sqlgraph.NewCreateSpec(cloudattackdata.Table, sqlgraph.NewFieldSpec(cloudattackdata.FieldID, field.TypeInt))
	)
	if value, ok := cadc.mutation.CreatedAt(); ok {
		_spec.SetField(cloudattackdata.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := cadc.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudattackdata.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := cadc.mutation.Remark(); ok {
		_spec.SetField(cloudattackdata.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := cadc.mutation.SrcIP(); ok {
		_spec.SetField(cloudattackdata.FieldSrcIP, field.TypeString, value)
		_node.SrcIP = value
	}
	if value, ok := cadc.mutation.SrcPort(); ok {
		_spec.SetField(cloudattackdata.FieldSrcPort, field.TypeInt, value)
		_node.SrcPort = value
	}
	if value, ok := cadc.mutation.DstIP(); ok {
		_spec.SetField(cloudattackdata.FieldDstIP, field.TypeString, value)
		_node.DstIP = value
	}
	if value, ok := cadc.mutation.DstPort(); ok {
		_spec.SetField(cloudattackdata.FieldDstPort, field.TypeInt, value)
		_node.DstPort = value
	}
	if value, ok := cadc.mutation.Protocol(); ok {
		_spec.SetField(cloudattackdata.FieldProtocol, field.TypeInt, value)
		_node.Protocol = value
	}
	if value, ok := cadc.mutation.CurrentAttackPps(); ok {
		_spec.SetField(cloudattackdata.FieldCurrentAttackPps, field.TypeInt64, value)
		_node.CurrentAttackPps = value
	}
	if value, ok := cadc.mutation.StartTime(); ok {
		_spec.SetField(cloudattackdata.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := cadc.mutation.EndTime(); ok {
		_spec.SetField(cloudattackdata.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if nodes := cadc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudattackdata.TenantTable,
			Columns: []string{cloudattackdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// CloudAttackDataCreateBulk is the builder for creating many CloudAttackData entities in bulk.
type CloudAttackDataCreateBulk struct {
	config
	err      error
	builders []*CloudAttackDataCreate
}

// Save creates the CloudAttackData entities in the database.
func (cadcb *CloudAttackDataCreateBulk) Save(ctx context.Context) ([]*CloudAttackData, error) {
	if cadcb.err != nil {
		return nil, cadcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cadcb.builders))
	nodes := make([]*CloudAttackData, len(cadcb.builders))
	mutators := make([]Mutator, len(cadcb.builders))
	for i := range cadcb.builders {
		func(i int, root context.Context) {
			builder := cadcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CloudAttackDataMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cadcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cadcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cadcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cadcb *CloudAttackDataCreateBulk) SaveX(ctx context.Context) []*CloudAttackData {
	v, err := cadcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cadcb *CloudAttackDataCreateBulk) Exec(ctx context.Context) error {
	_, err := cadcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cadcb *CloudAttackDataCreateBulk) ExecX(ctx context.Context) {
	if err := cadcb.Exec(ctx); err != nil {
		panic(err)
	}
}
