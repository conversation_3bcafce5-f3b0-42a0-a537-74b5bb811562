// Code generated by ent, DO NOT EDIT.

package cleandata

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldCreatedAt, v))
}

// SpectrumAlertID applies equality check predicate on the "spectrum_alert_id" field. It's identical to SpectrumAlertIDEQ.
func SpectrumAlertID(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldSpectrumAlertID, v))
}

// IP applies equality check predicate on the "ip" field. It's identical to IPEQ.
func IP(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldIP, v))
}

// Time applies equality check predicate on the "time" field. It's identical to TimeEQ.
func Time(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldTime, v))
}

// InBps applies equality check predicate on the "in_bps" field. It's identical to InBpsEQ.
func InBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInBps, v))
}

// OutBps applies equality check predicate on the "out_bps" field. It's identical to OutBpsEQ.
func OutBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutBps, v))
}

// InPps applies equality check predicate on the "in_pps" field. It's identical to InPpsEQ.
func InPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInPps, v))
}

// OutPps applies equality check predicate on the "out_pps" field. It's identical to OutPpsEQ.
func OutPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutPps, v))
}

// InAckPps applies equality check predicate on the "in_ack_pps" field. It's identical to InAckPpsEQ.
func InAckPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInAckPps, v))
}

// OutAckPps applies equality check predicate on the "out_ack_pps" field. It's identical to OutAckPpsEQ.
func OutAckPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutAckPps, v))
}

// InAckBps applies equality check predicate on the "in_ack_bps" field. It's identical to InAckBpsEQ.
func InAckBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInAckBps, v))
}

// OutAckBps applies equality check predicate on the "out_ack_bps" field. It's identical to OutAckBpsEQ.
func OutAckBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutAckBps, v))
}

// InSynPps applies equality check predicate on the "in_syn_pps" field. It's identical to InSynPpsEQ.
func InSynPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInSynPps, v))
}

// OutSynPps applies equality check predicate on the "out_syn_pps" field. It's identical to OutSynPpsEQ.
func OutSynPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutSynPps, v))
}

// InUDPPps applies equality check predicate on the "in_udp_pps" field. It's identical to InUDPPpsEQ.
func InUDPPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInUDPPps, v))
}

// OutUDPPps applies equality check predicate on the "out_udp_pps" field. It's identical to OutUDPPpsEQ.
func OutUDPPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutUDPPps, v))
}

// InUDPBps applies equality check predicate on the "in_udp_bps" field. It's identical to InUDPBpsEQ.
func InUDPBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInUDPBps, v))
}

// OutUDPBps applies equality check predicate on the "out_udp_bps" field. It's identical to OutUDPBpsEQ.
func OutUDPBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutUDPBps, v))
}

// InIcmpPps applies equality check predicate on the "in_icmp_pps" field. It's identical to InIcmpPpsEQ.
func InIcmpPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInIcmpPps, v))
}

// InIcmpBps applies equality check predicate on the "in_icmp_bps" field. It's identical to InIcmpBpsEQ.
func InIcmpBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInIcmpBps, v))
}

// OutIcmpBps applies equality check predicate on the "out_icmp_bps" field. It's identical to OutIcmpBpsEQ.
func OutIcmpBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutIcmpBps, v))
}

// OutIcmpPps applies equality check predicate on the "out_icmp_pps" field. It's identical to OutIcmpPpsEQ.
func OutIcmpPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutIcmpPps, v))
}

// InDNSPps applies equality check predicate on the "in_dns_pps" field. It's identical to InDNSPpsEQ.
func InDNSPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInDNSPps, v))
}

// OutDNSPps applies equality check predicate on the "out_dns_pps" field. It's identical to OutDNSPpsEQ.
func OutDNSPps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutDNSPps, v))
}

// InDNSBps applies equality check predicate on the "in_dns_bps" field. It's identical to InDNSBpsEQ.
func InDNSBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInDNSBps, v))
}

// OutDNSBps applies equality check predicate on the "out_dns_bps" field. It's identical to OutDNSBpsEQ.
func OutDNSBps(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutDNSBps, v))
}

// CFilterID applies equality check predicate on the "c_filter_id" field. It's identical to CFilterIDEQ.
func CFilterID(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldCFilterID, v))
}

// AttackFlags applies equality check predicate on the "attack_flags" field. It's identical to AttackFlagsEQ.
func AttackFlags(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldAttackFlags, v))
}

// Count applies equality check predicate on the "count" field. It's identical to CountEQ.
func Count(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldCount, v))
}

// IPType applies equality check predicate on the "ip_type" field. It's identical to IPTypeEQ.
func IPType(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldIPType, v))
}

// CFilter applies equality check predicate on the "c_filter" field. It's identical to CFilterEQ.
func CFilter(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldCFilter, v))
}

// Host applies equality check predicate on the "host" field. It's identical to HostEQ.
func Host(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldHost, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.CleanData {
	return predicate.CleanData(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.CleanData {
	return predicate.CleanData(sql.FieldNotNull(FieldTenantID))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldCreatedAt, v))
}

// SpectrumAlertIDEQ applies the EQ predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldSpectrumAlertID, v))
}

// SpectrumAlertIDNEQ applies the NEQ predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDNEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldSpectrumAlertID, v))
}

// SpectrumAlertIDIn applies the In predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldSpectrumAlertID, vs...))
}

// SpectrumAlertIDNotIn applies the NotIn predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDNotIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldSpectrumAlertID, vs...))
}

// SpectrumAlertIDIsNil applies the IsNil predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDIsNil() predicate.CleanData {
	return predicate.CleanData(sql.FieldIsNull(FieldSpectrumAlertID))
}

// SpectrumAlertIDNotNil applies the NotNil predicate on the "spectrum_alert_id" field.
func SpectrumAlertIDNotNil() predicate.CleanData {
	return predicate.CleanData(sql.FieldNotNull(FieldSpectrumAlertID))
}

// IPEQ applies the EQ predicate on the "ip" field.
func IPEQ(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldIP, v))
}

// IPNEQ applies the NEQ predicate on the "ip" field.
func IPNEQ(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldIP, v))
}

// IPIn applies the In predicate on the "ip" field.
func IPIn(vs ...string) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldIP, vs...))
}

// IPNotIn applies the NotIn predicate on the "ip" field.
func IPNotIn(vs ...string) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldIP, vs...))
}

// IPGT applies the GT predicate on the "ip" field.
func IPGT(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldIP, v))
}

// IPGTE applies the GTE predicate on the "ip" field.
func IPGTE(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldIP, v))
}

// IPLT applies the LT predicate on the "ip" field.
func IPLT(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldIP, v))
}

// IPLTE applies the LTE predicate on the "ip" field.
func IPLTE(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldIP, v))
}

// IPContains applies the Contains predicate on the "ip" field.
func IPContains(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldContains(FieldIP, v))
}

// IPHasPrefix applies the HasPrefix predicate on the "ip" field.
func IPHasPrefix(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldHasPrefix(FieldIP, v))
}

// IPHasSuffix applies the HasSuffix predicate on the "ip" field.
func IPHasSuffix(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldHasSuffix(FieldIP, v))
}

// IPEqualFold applies the EqualFold predicate on the "ip" field.
func IPEqualFold(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEqualFold(FieldIP, v))
}

// IPContainsFold applies the ContainsFold predicate on the "ip" field.
func IPContainsFold(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldContainsFold(FieldIP, v))
}

// TimeEQ applies the EQ predicate on the "time" field.
func TimeEQ(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldTime, v))
}

// TimeNEQ applies the NEQ predicate on the "time" field.
func TimeNEQ(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldTime, v))
}

// TimeIn applies the In predicate on the "time" field.
func TimeIn(vs ...time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldTime, vs...))
}

// TimeNotIn applies the NotIn predicate on the "time" field.
func TimeNotIn(vs ...time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldTime, vs...))
}

// TimeGT applies the GT predicate on the "time" field.
func TimeGT(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldTime, v))
}

// TimeGTE applies the GTE predicate on the "time" field.
func TimeGTE(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldTime, v))
}

// TimeLT applies the LT predicate on the "time" field.
func TimeLT(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldTime, v))
}

// TimeLTE applies the LTE predicate on the "time" field.
func TimeLTE(v time.Time) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldTime, v))
}

// InBpsEQ applies the EQ predicate on the "in_bps" field.
func InBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInBps, v))
}

// InBpsNEQ applies the NEQ predicate on the "in_bps" field.
func InBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInBps, v))
}

// InBpsIn applies the In predicate on the "in_bps" field.
func InBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInBps, vs...))
}

// InBpsNotIn applies the NotIn predicate on the "in_bps" field.
func InBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInBps, vs...))
}

// InBpsGT applies the GT predicate on the "in_bps" field.
func InBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInBps, v))
}

// InBpsGTE applies the GTE predicate on the "in_bps" field.
func InBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInBps, v))
}

// InBpsLT applies the LT predicate on the "in_bps" field.
func InBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInBps, v))
}

// InBpsLTE applies the LTE predicate on the "in_bps" field.
func InBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInBps, v))
}

// OutBpsEQ applies the EQ predicate on the "out_bps" field.
func OutBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutBps, v))
}

// OutBpsNEQ applies the NEQ predicate on the "out_bps" field.
func OutBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutBps, v))
}

// OutBpsIn applies the In predicate on the "out_bps" field.
func OutBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutBps, vs...))
}

// OutBpsNotIn applies the NotIn predicate on the "out_bps" field.
func OutBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutBps, vs...))
}

// OutBpsGT applies the GT predicate on the "out_bps" field.
func OutBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutBps, v))
}

// OutBpsGTE applies the GTE predicate on the "out_bps" field.
func OutBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutBps, v))
}

// OutBpsLT applies the LT predicate on the "out_bps" field.
func OutBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutBps, v))
}

// OutBpsLTE applies the LTE predicate on the "out_bps" field.
func OutBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutBps, v))
}

// InPpsEQ applies the EQ predicate on the "in_pps" field.
func InPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInPps, v))
}

// InPpsNEQ applies the NEQ predicate on the "in_pps" field.
func InPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInPps, v))
}

// InPpsIn applies the In predicate on the "in_pps" field.
func InPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInPps, vs...))
}

// InPpsNotIn applies the NotIn predicate on the "in_pps" field.
func InPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInPps, vs...))
}

// InPpsGT applies the GT predicate on the "in_pps" field.
func InPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInPps, v))
}

// InPpsGTE applies the GTE predicate on the "in_pps" field.
func InPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInPps, v))
}

// InPpsLT applies the LT predicate on the "in_pps" field.
func InPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInPps, v))
}

// InPpsLTE applies the LTE predicate on the "in_pps" field.
func InPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInPps, v))
}

// OutPpsEQ applies the EQ predicate on the "out_pps" field.
func OutPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutPps, v))
}

// OutPpsNEQ applies the NEQ predicate on the "out_pps" field.
func OutPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutPps, v))
}

// OutPpsIn applies the In predicate on the "out_pps" field.
func OutPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutPps, vs...))
}

// OutPpsNotIn applies the NotIn predicate on the "out_pps" field.
func OutPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutPps, vs...))
}

// OutPpsGT applies the GT predicate on the "out_pps" field.
func OutPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutPps, v))
}

// OutPpsGTE applies the GTE predicate on the "out_pps" field.
func OutPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutPps, v))
}

// OutPpsLT applies the LT predicate on the "out_pps" field.
func OutPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutPps, v))
}

// OutPpsLTE applies the LTE predicate on the "out_pps" field.
func OutPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutPps, v))
}

// InAckPpsEQ applies the EQ predicate on the "in_ack_pps" field.
func InAckPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInAckPps, v))
}

// InAckPpsNEQ applies the NEQ predicate on the "in_ack_pps" field.
func InAckPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInAckPps, v))
}

// InAckPpsIn applies the In predicate on the "in_ack_pps" field.
func InAckPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInAckPps, vs...))
}

// InAckPpsNotIn applies the NotIn predicate on the "in_ack_pps" field.
func InAckPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInAckPps, vs...))
}

// InAckPpsGT applies the GT predicate on the "in_ack_pps" field.
func InAckPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInAckPps, v))
}

// InAckPpsGTE applies the GTE predicate on the "in_ack_pps" field.
func InAckPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInAckPps, v))
}

// InAckPpsLT applies the LT predicate on the "in_ack_pps" field.
func InAckPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInAckPps, v))
}

// InAckPpsLTE applies the LTE predicate on the "in_ack_pps" field.
func InAckPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInAckPps, v))
}

// OutAckPpsEQ applies the EQ predicate on the "out_ack_pps" field.
func OutAckPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutAckPps, v))
}

// OutAckPpsNEQ applies the NEQ predicate on the "out_ack_pps" field.
func OutAckPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutAckPps, v))
}

// OutAckPpsIn applies the In predicate on the "out_ack_pps" field.
func OutAckPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutAckPps, vs...))
}

// OutAckPpsNotIn applies the NotIn predicate on the "out_ack_pps" field.
func OutAckPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutAckPps, vs...))
}

// OutAckPpsGT applies the GT predicate on the "out_ack_pps" field.
func OutAckPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutAckPps, v))
}

// OutAckPpsGTE applies the GTE predicate on the "out_ack_pps" field.
func OutAckPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutAckPps, v))
}

// OutAckPpsLT applies the LT predicate on the "out_ack_pps" field.
func OutAckPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutAckPps, v))
}

// OutAckPpsLTE applies the LTE predicate on the "out_ack_pps" field.
func OutAckPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutAckPps, v))
}

// InAckBpsEQ applies the EQ predicate on the "in_ack_bps" field.
func InAckBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInAckBps, v))
}

// InAckBpsNEQ applies the NEQ predicate on the "in_ack_bps" field.
func InAckBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInAckBps, v))
}

// InAckBpsIn applies the In predicate on the "in_ack_bps" field.
func InAckBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInAckBps, vs...))
}

// InAckBpsNotIn applies the NotIn predicate on the "in_ack_bps" field.
func InAckBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInAckBps, vs...))
}

// InAckBpsGT applies the GT predicate on the "in_ack_bps" field.
func InAckBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInAckBps, v))
}

// InAckBpsGTE applies the GTE predicate on the "in_ack_bps" field.
func InAckBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInAckBps, v))
}

// InAckBpsLT applies the LT predicate on the "in_ack_bps" field.
func InAckBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInAckBps, v))
}

// InAckBpsLTE applies the LTE predicate on the "in_ack_bps" field.
func InAckBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInAckBps, v))
}

// OutAckBpsEQ applies the EQ predicate on the "out_ack_bps" field.
func OutAckBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutAckBps, v))
}

// OutAckBpsNEQ applies the NEQ predicate on the "out_ack_bps" field.
func OutAckBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutAckBps, v))
}

// OutAckBpsIn applies the In predicate on the "out_ack_bps" field.
func OutAckBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutAckBps, vs...))
}

// OutAckBpsNotIn applies the NotIn predicate on the "out_ack_bps" field.
func OutAckBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutAckBps, vs...))
}

// OutAckBpsGT applies the GT predicate on the "out_ack_bps" field.
func OutAckBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutAckBps, v))
}

// OutAckBpsGTE applies the GTE predicate on the "out_ack_bps" field.
func OutAckBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutAckBps, v))
}

// OutAckBpsLT applies the LT predicate on the "out_ack_bps" field.
func OutAckBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutAckBps, v))
}

// OutAckBpsLTE applies the LTE predicate on the "out_ack_bps" field.
func OutAckBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutAckBps, v))
}

// InSynPpsEQ applies the EQ predicate on the "in_syn_pps" field.
func InSynPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInSynPps, v))
}

// InSynPpsNEQ applies the NEQ predicate on the "in_syn_pps" field.
func InSynPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInSynPps, v))
}

// InSynPpsIn applies the In predicate on the "in_syn_pps" field.
func InSynPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInSynPps, vs...))
}

// InSynPpsNotIn applies the NotIn predicate on the "in_syn_pps" field.
func InSynPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInSynPps, vs...))
}

// InSynPpsGT applies the GT predicate on the "in_syn_pps" field.
func InSynPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInSynPps, v))
}

// InSynPpsGTE applies the GTE predicate on the "in_syn_pps" field.
func InSynPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInSynPps, v))
}

// InSynPpsLT applies the LT predicate on the "in_syn_pps" field.
func InSynPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInSynPps, v))
}

// InSynPpsLTE applies the LTE predicate on the "in_syn_pps" field.
func InSynPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInSynPps, v))
}

// OutSynPpsEQ applies the EQ predicate on the "out_syn_pps" field.
func OutSynPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutSynPps, v))
}

// OutSynPpsNEQ applies the NEQ predicate on the "out_syn_pps" field.
func OutSynPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutSynPps, v))
}

// OutSynPpsIn applies the In predicate on the "out_syn_pps" field.
func OutSynPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutSynPps, vs...))
}

// OutSynPpsNotIn applies the NotIn predicate on the "out_syn_pps" field.
func OutSynPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutSynPps, vs...))
}

// OutSynPpsGT applies the GT predicate on the "out_syn_pps" field.
func OutSynPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutSynPps, v))
}

// OutSynPpsGTE applies the GTE predicate on the "out_syn_pps" field.
func OutSynPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutSynPps, v))
}

// OutSynPpsLT applies the LT predicate on the "out_syn_pps" field.
func OutSynPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutSynPps, v))
}

// OutSynPpsLTE applies the LTE predicate on the "out_syn_pps" field.
func OutSynPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutSynPps, v))
}

// InUDPPpsEQ applies the EQ predicate on the "in_udp_pps" field.
func InUDPPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInUDPPps, v))
}

// InUDPPpsNEQ applies the NEQ predicate on the "in_udp_pps" field.
func InUDPPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInUDPPps, v))
}

// InUDPPpsIn applies the In predicate on the "in_udp_pps" field.
func InUDPPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInUDPPps, vs...))
}

// InUDPPpsNotIn applies the NotIn predicate on the "in_udp_pps" field.
func InUDPPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInUDPPps, vs...))
}

// InUDPPpsGT applies the GT predicate on the "in_udp_pps" field.
func InUDPPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInUDPPps, v))
}

// InUDPPpsGTE applies the GTE predicate on the "in_udp_pps" field.
func InUDPPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInUDPPps, v))
}

// InUDPPpsLT applies the LT predicate on the "in_udp_pps" field.
func InUDPPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInUDPPps, v))
}

// InUDPPpsLTE applies the LTE predicate on the "in_udp_pps" field.
func InUDPPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInUDPPps, v))
}

// OutUDPPpsEQ applies the EQ predicate on the "out_udp_pps" field.
func OutUDPPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutUDPPps, v))
}

// OutUDPPpsNEQ applies the NEQ predicate on the "out_udp_pps" field.
func OutUDPPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutUDPPps, v))
}

// OutUDPPpsIn applies the In predicate on the "out_udp_pps" field.
func OutUDPPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutUDPPps, vs...))
}

// OutUDPPpsNotIn applies the NotIn predicate on the "out_udp_pps" field.
func OutUDPPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutUDPPps, vs...))
}

// OutUDPPpsGT applies the GT predicate on the "out_udp_pps" field.
func OutUDPPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutUDPPps, v))
}

// OutUDPPpsGTE applies the GTE predicate on the "out_udp_pps" field.
func OutUDPPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutUDPPps, v))
}

// OutUDPPpsLT applies the LT predicate on the "out_udp_pps" field.
func OutUDPPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutUDPPps, v))
}

// OutUDPPpsLTE applies the LTE predicate on the "out_udp_pps" field.
func OutUDPPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutUDPPps, v))
}

// InUDPBpsEQ applies the EQ predicate on the "in_udp_bps" field.
func InUDPBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInUDPBps, v))
}

// InUDPBpsNEQ applies the NEQ predicate on the "in_udp_bps" field.
func InUDPBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInUDPBps, v))
}

// InUDPBpsIn applies the In predicate on the "in_udp_bps" field.
func InUDPBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInUDPBps, vs...))
}

// InUDPBpsNotIn applies the NotIn predicate on the "in_udp_bps" field.
func InUDPBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInUDPBps, vs...))
}

// InUDPBpsGT applies the GT predicate on the "in_udp_bps" field.
func InUDPBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInUDPBps, v))
}

// InUDPBpsGTE applies the GTE predicate on the "in_udp_bps" field.
func InUDPBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInUDPBps, v))
}

// InUDPBpsLT applies the LT predicate on the "in_udp_bps" field.
func InUDPBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInUDPBps, v))
}

// InUDPBpsLTE applies the LTE predicate on the "in_udp_bps" field.
func InUDPBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInUDPBps, v))
}

// OutUDPBpsEQ applies the EQ predicate on the "out_udp_bps" field.
func OutUDPBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutUDPBps, v))
}

// OutUDPBpsNEQ applies the NEQ predicate on the "out_udp_bps" field.
func OutUDPBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutUDPBps, v))
}

// OutUDPBpsIn applies the In predicate on the "out_udp_bps" field.
func OutUDPBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutUDPBps, vs...))
}

// OutUDPBpsNotIn applies the NotIn predicate on the "out_udp_bps" field.
func OutUDPBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutUDPBps, vs...))
}

// OutUDPBpsGT applies the GT predicate on the "out_udp_bps" field.
func OutUDPBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutUDPBps, v))
}

// OutUDPBpsGTE applies the GTE predicate on the "out_udp_bps" field.
func OutUDPBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutUDPBps, v))
}

// OutUDPBpsLT applies the LT predicate on the "out_udp_bps" field.
func OutUDPBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutUDPBps, v))
}

// OutUDPBpsLTE applies the LTE predicate on the "out_udp_bps" field.
func OutUDPBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutUDPBps, v))
}

// InIcmpPpsEQ applies the EQ predicate on the "in_icmp_pps" field.
func InIcmpPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInIcmpPps, v))
}

// InIcmpPpsNEQ applies the NEQ predicate on the "in_icmp_pps" field.
func InIcmpPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInIcmpPps, v))
}

// InIcmpPpsIn applies the In predicate on the "in_icmp_pps" field.
func InIcmpPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInIcmpPps, vs...))
}

// InIcmpPpsNotIn applies the NotIn predicate on the "in_icmp_pps" field.
func InIcmpPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInIcmpPps, vs...))
}

// InIcmpPpsGT applies the GT predicate on the "in_icmp_pps" field.
func InIcmpPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInIcmpPps, v))
}

// InIcmpPpsGTE applies the GTE predicate on the "in_icmp_pps" field.
func InIcmpPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInIcmpPps, v))
}

// InIcmpPpsLT applies the LT predicate on the "in_icmp_pps" field.
func InIcmpPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInIcmpPps, v))
}

// InIcmpPpsLTE applies the LTE predicate on the "in_icmp_pps" field.
func InIcmpPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInIcmpPps, v))
}

// InIcmpBpsEQ applies the EQ predicate on the "in_icmp_bps" field.
func InIcmpBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInIcmpBps, v))
}

// InIcmpBpsNEQ applies the NEQ predicate on the "in_icmp_bps" field.
func InIcmpBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInIcmpBps, v))
}

// InIcmpBpsIn applies the In predicate on the "in_icmp_bps" field.
func InIcmpBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInIcmpBps, vs...))
}

// InIcmpBpsNotIn applies the NotIn predicate on the "in_icmp_bps" field.
func InIcmpBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInIcmpBps, vs...))
}

// InIcmpBpsGT applies the GT predicate on the "in_icmp_bps" field.
func InIcmpBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInIcmpBps, v))
}

// InIcmpBpsGTE applies the GTE predicate on the "in_icmp_bps" field.
func InIcmpBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInIcmpBps, v))
}

// InIcmpBpsLT applies the LT predicate on the "in_icmp_bps" field.
func InIcmpBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInIcmpBps, v))
}

// InIcmpBpsLTE applies the LTE predicate on the "in_icmp_bps" field.
func InIcmpBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInIcmpBps, v))
}

// OutIcmpBpsEQ applies the EQ predicate on the "out_icmp_bps" field.
func OutIcmpBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutIcmpBps, v))
}

// OutIcmpBpsNEQ applies the NEQ predicate on the "out_icmp_bps" field.
func OutIcmpBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutIcmpBps, v))
}

// OutIcmpBpsIn applies the In predicate on the "out_icmp_bps" field.
func OutIcmpBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutIcmpBps, vs...))
}

// OutIcmpBpsNotIn applies the NotIn predicate on the "out_icmp_bps" field.
func OutIcmpBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutIcmpBps, vs...))
}

// OutIcmpBpsGT applies the GT predicate on the "out_icmp_bps" field.
func OutIcmpBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutIcmpBps, v))
}

// OutIcmpBpsGTE applies the GTE predicate on the "out_icmp_bps" field.
func OutIcmpBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutIcmpBps, v))
}

// OutIcmpBpsLT applies the LT predicate on the "out_icmp_bps" field.
func OutIcmpBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutIcmpBps, v))
}

// OutIcmpBpsLTE applies the LTE predicate on the "out_icmp_bps" field.
func OutIcmpBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutIcmpBps, v))
}

// OutIcmpPpsEQ applies the EQ predicate on the "out_icmp_pps" field.
func OutIcmpPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutIcmpPps, v))
}

// OutIcmpPpsNEQ applies the NEQ predicate on the "out_icmp_pps" field.
func OutIcmpPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutIcmpPps, v))
}

// OutIcmpPpsIn applies the In predicate on the "out_icmp_pps" field.
func OutIcmpPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutIcmpPps, vs...))
}

// OutIcmpPpsNotIn applies the NotIn predicate on the "out_icmp_pps" field.
func OutIcmpPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutIcmpPps, vs...))
}

// OutIcmpPpsGT applies the GT predicate on the "out_icmp_pps" field.
func OutIcmpPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutIcmpPps, v))
}

// OutIcmpPpsGTE applies the GTE predicate on the "out_icmp_pps" field.
func OutIcmpPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutIcmpPps, v))
}

// OutIcmpPpsLT applies the LT predicate on the "out_icmp_pps" field.
func OutIcmpPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutIcmpPps, v))
}

// OutIcmpPpsLTE applies the LTE predicate on the "out_icmp_pps" field.
func OutIcmpPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutIcmpPps, v))
}

// InDNSPpsEQ applies the EQ predicate on the "in_dns_pps" field.
func InDNSPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInDNSPps, v))
}

// InDNSPpsNEQ applies the NEQ predicate on the "in_dns_pps" field.
func InDNSPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInDNSPps, v))
}

// InDNSPpsIn applies the In predicate on the "in_dns_pps" field.
func InDNSPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInDNSPps, vs...))
}

// InDNSPpsNotIn applies the NotIn predicate on the "in_dns_pps" field.
func InDNSPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInDNSPps, vs...))
}

// InDNSPpsGT applies the GT predicate on the "in_dns_pps" field.
func InDNSPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInDNSPps, v))
}

// InDNSPpsGTE applies the GTE predicate on the "in_dns_pps" field.
func InDNSPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInDNSPps, v))
}

// InDNSPpsLT applies the LT predicate on the "in_dns_pps" field.
func InDNSPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInDNSPps, v))
}

// InDNSPpsLTE applies the LTE predicate on the "in_dns_pps" field.
func InDNSPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInDNSPps, v))
}

// OutDNSPpsEQ applies the EQ predicate on the "out_dns_pps" field.
func OutDNSPpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutDNSPps, v))
}

// OutDNSPpsNEQ applies the NEQ predicate on the "out_dns_pps" field.
func OutDNSPpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutDNSPps, v))
}

// OutDNSPpsIn applies the In predicate on the "out_dns_pps" field.
func OutDNSPpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutDNSPps, vs...))
}

// OutDNSPpsNotIn applies the NotIn predicate on the "out_dns_pps" field.
func OutDNSPpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutDNSPps, vs...))
}

// OutDNSPpsGT applies the GT predicate on the "out_dns_pps" field.
func OutDNSPpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutDNSPps, v))
}

// OutDNSPpsGTE applies the GTE predicate on the "out_dns_pps" field.
func OutDNSPpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutDNSPps, v))
}

// OutDNSPpsLT applies the LT predicate on the "out_dns_pps" field.
func OutDNSPpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutDNSPps, v))
}

// OutDNSPpsLTE applies the LTE predicate on the "out_dns_pps" field.
func OutDNSPpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutDNSPps, v))
}

// InDNSBpsEQ applies the EQ predicate on the "in_dns_bps" field.
func InDNSBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldInDNSBps, v))
}

// InDNSBpsNEQ applies the NEQ predicate on the "in_dns_bps" field.
func InDNSBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldInDNSBps, v))
}

// InDNSBpsIn applies the In predicate on the "in_dns_bps" field.
func InDNSBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldInDNSBps, vs...))
}

// InDNSBpsNotIn applies the NotIn predicate on the "in_dns_bps" field.
func InDNSBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldInDNSBps, vs...))
}

// InDNSBpsGT applies the GT predicate on the "in_dns_bps" field.
func InDNSBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldInDNSBps, v))
}

// InDNSBpsGTE applies the GTE predicate on the "in_dns_bps" field.
func InDNSBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldInDNSBps, v))
}

// InDNSBpsLT applies the LT predicate on the "in_dns_bps" field.
func InDNSBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldInDNSBps, v))
}

// InDNSBpsLTE applies the LTE predicate on the "in_dns_bps" field.
func InDNSBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldInDNSBps, v))
}

// OutDNSBpsEQ applies the EQ predicate on the "out_dns_bps" field.
func OutDNSBpsEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldOutDNSBps, v))
}

// OutDNSBpsNEQ applies the NEQ predicate on the "out_dns_bps" field.
func OutDNSBpsNEQ(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldOutDNSBps, v))
}

// OutDNSBpsIn applies the In predicate on the "out_dns_bps" field.
func OutDNSBpsIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldOutDNSBps, vs...))
}

// OutDNSBpsNotIn applies the NotIn predicate on the "out_dns_bps" field.
func OutDNSBpsNotIn(vs ...int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldOutDNSBps, vs...))
}

// OutDNSBpsGT applies the GT predicate on the "out_dns_bps" field.
func OutDNSBpsGT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldOutDNSBps, v))
}

// OutDNSBpsGTE applies the GTE predicate on the "out_dns_bps" field.
func OutDNSBpsGTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldOutDNSBps, v))
}

// OutDNSBpsLT applies the LT predicate on the "out_dns_bps" field.
func OutDNSBpsLT(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldOutDNSBps, v))
}

// OutDNSBpsLTE applies the LTE predicate on the "out_dns_bps" field.
func OutDNSBpsLTE(v int64) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldOutDNSBps, v))
}

// CFilterIDEQ applies the EQ predicate on the "c_filter_id" field.
func CFilterIDEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldCFilterID, v))
}

// CFilterIDNEQ applies the NEQ predicate on the "c_filter_id" field.
func CFilterIDNEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldCFilterID, v))
}

// CFilterIDIn applies the In predicate on the "c_filter_id" field.
func CFilterIDIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldCFilterID, vs...))
}

// CFilterIDNotIn applies the NotIn predicate on the "c_filter_id" field.
func CFilterIDNotIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldCFilterID, vs...))
}

// CFilterIDGT applies the GT predicate on the "c_filter_id" field.
func CFilterIDGT(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldCFilterID, v))
}

// CFilterIDGTE applies the GTE predicate on the "c_filter_id" field.
func CFilterIDGTE(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldCFilterID, v))
}

// CFilterIDLT applies the LT predicate on the "c_filter_id" field.
func CFilterIDLT(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldCFilterID, v))
}

// CFilterIDLTE applies the LTE predicate on the "c_filter_id" field.
func CFilterIDLTE(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldCFilterID, v))
}

// AttackFlagsEQ applies the EQ predicate on the "attack_flags" field.
func AttackFlagsEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldAttackFlags, v))
}

// AttackFlagsNEQ applies the NEQ predicate on the "attack_flags" field.
func AttackFlagsNEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldAttackFlags, v))
}

// AttackFlagsIn applies the In predicate on the "attack_flags" field.
func AttackFlagsIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldAttackFlags, vs...))
}

// AttackFlagsNotIn applies the NotIn predicate on the "attack_flags" field.
func AttackFlagsNotIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldAttackFlags, vs...))
}

// AttackFlagsGT applies the GT predicate on the "attack_flags" field.
func AttackFlagsGT(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldAttackFlags, v))
}

// AttackFlagsGTE applies the GTE predicate on the "attack_flags" field.
func AttackFlagsGTE(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldAttackFlags, v))
}

// AttackFlagsLT applies the LT predicate on the "attack_flags" field.
func AttackFlagsLT(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldAttackFlags, v))
}

// AttackFlagsLTE applies the LTE predicate on the "attack_flags" field.
func AttackFlagsLTE(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldAttackFlags, v))
}

// CountEQ applies the EQ predicate on the "count" field.
func CountEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldCount, v))
}

// CountNEQ applies the NEQ predicate on the "count" field.
func CountNEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldCount, v))
}

// CountIn applies the In predicate on the "count" field.
func CountIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldCount, vs...))
}

// CountNotIn applies the NotIn predicate on the "count" field.
func CountNotIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldCount, vs...))
}

// CountGT applies the GT predicate on the "count" field.
func CountGT(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldCount, v))
}

// CountGTE applies the GTE predicate on the "count" field.
func CountGTE(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldCount, v))
}

// CountLT applies the LT predicate on the "count" field.
func CountLT(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldCount, v))
}

// CountLTE applies the LTE predicate on the "count" field.
func CountLTE(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldCount, v))
}

// IPTypeEQ applies the EQ predicate on the "ip_type" field.
func IPTypeEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldIPType, v))
}

// IPTypeNEQ applies the NEQ predicate on the "ip_type" field.
func IPTypeNEQ(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldIPType, v))
}

// IPTypeIn applies the In predicate on the "ip_type" field.
func IPTypeIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldIPType, vs...))
}

// IPTypeNotIn applies the NotIn predicate on the "ip_type" field.
func IPTypeNotIn(vs ...int) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldIPType, vs...))
}

// IPTypeGT applies the GT predicate on the "ip_type" field.
func IPTypeGT(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldIPType, v))
}

// IPTypeGTE applies the GTE predicate on the "ip_type" field.
func IPTypeGTE(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldIPType, v))
}

// IPTypeLT applies the LT predicate on the "ip_type" field.
func IPTypeLT(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldIPType, v))
}

// IPTypeLTE applies the LTE predicate on the "ip_type" field.
func IPTypeLTE(v int) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldIPType, v))
}

// CFilterEQ applies the EQ predicate on the "c_filter" field.
func CFilterEQ(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldCFilter, v))
}

// CFilterNEQ applies the NEQ predicate on the "c_filter" field.
func CFilterNEQ(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldCFilter, v))
}

// CFilterIn applies the In predicate on the "c_filter" field.
func CFilterIn(vs ...string) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldCFilter, vs...))
}

// CFilterNotIn applies the NotIn predicate on the "c_filter" field.
func CFilterNotIn(vs ...string) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldCFilter, vs...))
}

// CFilterGT applies the GT predicate on the "c_filter" field.
func CFilterGT(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldCFilter, v))
}

// CFilterGTE applies the GTE predicate on the "c_filter" field.
func CFilterGTE(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldCFilter, v))
}

// CFilterLT applies the LT predicate on the "c_filter" field.
func CFilterLT(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldCFilter, v))
}

// CFilterLTE applies the LTE predicate on the "c_filter" field.
func CFilterLTE(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldCFilter, v))
}

// CFilterContains applies the Contains predicate on the "c_filter" field.
func CFilterContains(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldContains(FieldCFilter, v))
}

// CFilterHasPrefix applies the HasPrefix predicate on the "c_filter" field.
func CFilterHasPrefix(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldHasPrefix(FieldCFilter, v))
}

// CFilterHasSuffix applies the HasSuffix predicate on the "c_filter" field.
func CFilterHasSuffix(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldHasSuffix(FieldCFilter, v))
}

// CFilterIsNil applies the IsNil predicate on the "c_filter" field.
func CFilterIsNil() predicate.CleanData {
	return predicate.CleanData(sql.FieldIsNull(FieldCFilter))
}

// CFilterNotNil applies the NotNil predicate on the "c_filter" field.
func CFilterNotNil() predicate.CleanData {
	return predicate.CleanData(sql.FieldNotNull(FieldCFilter))
}

// CFilterEqualFold applies the EqualFold predicate on the "c_filter" field.
func CFilterEqualFold(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEqualFold(FieldCFilter, v))
}

// CFilterContainsFold applies the ContainsFold predicate on the "c_filter" field.
func CFilterContainsFold(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldContainsFold(FieldCFilter, v))
}

// HostEQ applies the EQ predicate on the "host" field.
func HostEQ(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEQ(FieldHost, v))
}

// HostNEQ applies the NEQ predicate on the "host" field.
func HostNEQ(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldNEQ(FieldHost, v))
}

// HostIn applies the In predicate on the "host" field.
func HostIn(vs ...string) predicate.CleanData {
	return predicate.CleanData(sql.FieldIn(FieldHost, vs...))
}

// HostNotIn applies the NotIn predicate on the "host" field.
func HostNotIn(vs ...string) predicate.CleanData {
	return predicate.CleanData(sql.FieldNotIn(FieldHost, vs...))
}

// HostGT applies the GT predicate on the "host" field.
func HostGT(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldGT(FieldHost, v))
}

// HostGTE applies the GTE predicate on the "host" field.
func HostGTE(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldGTE(FieldHost, v))
}

// HostLT applies the LT predicate on the "host" field.
func HostLT(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldLT(FieldHost, v))
}

// HostLTE applies the LTE predicate on the "host" field.
func HostLTE(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldLTE(FieldHost, v))
}

// HostContains applies the Contains predicate on the "host" field.
func HostContains(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldContains(FieldHost, v))
}

// HostHasPrefix applies the HasPrefix predicate on the "host" field.
func HostHasPrefix(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldHasPrefix(FieldHost, v))
}

// HostHasSuffix applies the HasSuffix predicate on the "host" field.
func HostHasSuffix(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldHasSuffix(FieldHost, v))
}

// HostIsNil applies the IsNil predicate on the "host" field.
func HostIsNil() predicate.CleanData {
	return predicate.CleanData(sql.FieldIsNull(FieldHost))
}

// HostNotNil applies the NotNil predicate on the "host" field.
func HostNotNil() predicate.CleanData {
	return predicate.CleanData(sql.FieldNotNull(FieldHost))
}

// HostEqualFold applies the EqualFold predicate on the "host" field.
func HostEqualFold(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldEqualFold(FieldHost, v))
}

// HostContainsFold applies the ContainsFold predicate on the "host" field.
func HostContainsFold(v string) predicate.CleanData {
	return predicate.CleanData(sql.FieldContainsFold(FieldHost, v))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.CleanData {
	return predicate.CleanData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.CleanData {
	return predicate.CleanData(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasSpectrumAlert applies the HasEdge predicate on the "spectrum_alert" edge.
func HasSpectrumAlert() predicate.CleanData {
	return predicate.CleanData(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, SpectrumAlertTable, SpectrumAlertColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSpectrumAlertWith applies the HasEdge predicate on the "spectrum_alert" edge with a given conditions (other predicates).
func HasSpectrumAlertWith(preds ...predicate.SpectrumAlert) predicate.CleanData {
	return predicate.CleanData(func(s *sql.Selector) {
		step := newSpectrumAlertStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.CleanData) predicate.CleanData {
	return predicate.CleanData(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.CleanData) predicate.CleanData {
	return predicate.CleanData(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.CleanData) predicate.CleanData {
	return predicate.CleanData(sql.NotPredicates(p))
}
