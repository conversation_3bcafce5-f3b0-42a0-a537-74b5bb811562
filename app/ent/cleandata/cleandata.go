// Code generated by ent, DO NOT EDIT.

package cleandata

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the cleandata type in the database.
	Label = "clean_data"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldSpectrumAlertID holds the string denoting the spectrum_alert_id field in the database.
	FieldSpectrumAlertID = "spectrum_alert_id"
	// FieldIP holds the string denoting the ip field in the database.
	FieldIP = "ip"
	// FieldTime holds the string denoting the time field in the database.
	FieldTime = "time"
	// FieldInBps holds the string denoting the in_bps field in the database.
	FieldInBps = "in_bps"
	// FieldOutBps holds the string denoting the out_bps field in the database.
	FieldOutBps = "out_bps"
	// FieldInPps holds the string denoting the in_pps field in the database.
	FieldInPps = "in_pps"
	// FieldOutPps holds the string denoting the out_pps field in the database.
	FieldOutPps = "out_pps"
	// FieldInAckPps holds the string denoting the in_ack_pps field in the database.
	FieldInAckPps = "in_ack_pps"
	// FieldOutAckPps holds the string denoting the out_ack_pps field in the database.
	FieldOutAckPps = "out_ack_pps"
	// FieldInAckBps holds the string denoting the in_ack_bps field in the database.
	FieldInAckBps = "in_ack_bps"
	// FieldOutAckBps holds the string denoting the out_ack_bps field in the database.
	FieldOutAckBps = "out_ack_bps"
	// FieldInSynPps holds the string denoting the in_syn_pps field in the database.
	FieldInSynPps = "in_syn_pps"
	// FieldOutSynPps holds the string denoting the out_syn_pps field in the database.
	FieldOutSynPps = "out_syn_pps"
	// FieldInUDPPps holds the string denoting the in_udp_pps field in the database.
	FieldInUDPPps = "in_udp_pps"
	// FieldOutUDPPps holds the string denoting the out_udp_pps field in the database.
	FieldOutUDPPps = "out_udp_pps"
	// FieldInUDPBps holds the string denoting the in_udp_bps field in the database.
	FieldInUDPBps = "in_udp_bps"
	// FieldOutUDPBps holds the string denoting the out_udp_bps field in the database.
	FieldOutUDPBps = "out_udp_bps"
	// FieldInIcmpPps holds the string denoting the in_icmp_pps field in the database.
	FieldInIcmpPps = "in_icmp_pps"
	// FieldInIcmpBps holds the string denoting the in_icmp_bps field in the database.
	FieldInIcmpBps = "in_icmp_bps"
	// FieldOutIcmpBps holds the string denoting the out_icmp_bps field in the database.
	FieldOutIcmpBps = "out_icmp_bps"
	// FieldOutIcmpPps holds the string denoting the out_icmp_pps field in the database.
	FieldOutIcmpPps = "out_icmp_pps"
	// FieldInDNSPps holds the string denoting the in_dns_pps field in the database.
	FieldInDNSPps = "in_dns_pps"
	// FieldOutDNSPps holds the string denoting the out_dns_pps field in the database.
	FieldOutDNSPps = "out_dns_pps"
	// FieldInDNSBps holds the string denoting the in_dns_bps field in the database.
	FieldInDNSBps = "in_dns_bps"
	// FieldOutDNSBps holds the string denoting the out_dns_bps field in the database.
	FieldOutDNSBps = "out_dns_bps"
	// FieldCFilterID holds the string denoting the c_filter_id field in the database.
	FieldCFilterID = "c_filter_id"
	// FieldAttackFlags holds the string denoting the attack_flags field in the database.
	FieldAttackFlags = "attack_flags"
	// FieldCount holds the string denoting the count field in the database.
	FieldCount = "count"
	// FieldIPType holds the string denoting the ip_type field in the database.
	FieldIPType = "ip_type"
	// FieldCFilter holds the string denoting the c_filter field in the database.
	FieldCFilter = "c_filter"
	// FieldHost holds the string denoting the host field in the database.
	FieldHost = "host"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeSpectrumAlert holds the string denoting the spectrum_alert edge name in mutations.
	EdgeSpectrumAlert = "spectrum_alert"
	// Table holds the table name of the cleandata in the database.
	Table = "clean_data"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "clean_data"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// SpectrumAlertTable is the table that holds the spectrum_alert relation/edge.
	SpectrumAlertTable = "clean_data"
	// SpectrumAlertInverseTable is the table name for the SpectrumAlert entity.
	// It exists in this package in order to avoid circular dependency with the "spectrumalert" package.
	SpectrumAlertInverseTable = "spectrum_alerts"
	// SpectrumAlertColumn is the table column denoting the spectrum_alert relation/edge.
	SpectrumAlertColumn = "spectrum_alert_id"
)

// Columns holds all SQL columns for cleandata fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldSpectrumAlertID,
	FieldIP,
	FieldTime,
	FieldInBps,
	FieldOutBps,
	FieldInPps,
	FieldOutPps,
	FieldInAckPps,
	FieldOutAckPps,
	FieldInAckBps,
	FieldOutAckBps,
	FieldInSynPps,
	FieldOutSynPps,
	FieldInUDPPps,
	FieldOutUDPPps,
	FieldInUDPBps,
	FieldOutUDPBps,
	FieldInIcmpPps,
	FieldInIcmpBps,
	FieldOutIcmpBps,
	FieldOutIcmpPps,
	FieldInDNSPps,
	FieldOutDNSPps,
	FieldInDNSBps,
	FieldOutDNSBps,
	FieldCFilterID,
	FieldAttackFlags,
	FieldCount,
	FieldIPType,
	FieldCFilter,
	FieldHost,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
)

// OrderOption defines the ordering options for the CleanData queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// BySpectrumAlertID orders the results by the spectrum_alert_id field.
func BySpectrumAlertID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSpectrumAlertID, opts...).ToFunc()
}

// ByIP orders the results by the ip field.
func ByIP(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIP, opts...).ToFunc()
}

// ByTime orders the results by the time field.
func ByTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTime, opts...).ToFunc()
}

// ByInBps orders the results by the in_bps field.
func ByInBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInBps, opts...).ToFunc()
}

// ByOutBps orders the results by the out_bps field.
func ByOutBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutBps, opts...).ToFunc()
}

// ByInPps orders the results by the in_pps field.
func ByInPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInPps, opts...).ToFunc()
}

// ByOutPps orders the results by the out_pps field.
func ByOutPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutPps, opts...).ToFunc()
}

// ByInAckPps orders the results by the in_ack_pps field.
func ByInAckPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInAckPps, opts...).ToFunc()
}

// ByOutAckPps orders the results by the out_ack_pps field.
func ByOutAckPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutAckPps, opts...).ToFunc()
}

// ByInAckBps orders the results by the in_ack_bps field.
func ByInAckBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInAckBps, opts...).ToFunc()
}

// ByOutAckBps orders the results by the out_ack_bps field.
func ByOutAckBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutAckBps, opts...).ToFunc()
}

// ByInSynPps orders the results by the in_syn_pps field.
func ByInSynPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInSynPps, opts...).ToFunc()
}

// ByOutSynPps orders the results by the out_syn_pps field.
func ByOutSynPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutSynPps, opts...).ToFunc()
}

// ByInUDPPps orders the results by the in_udp_pps field.
func ByInUDPPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInUDPPps, opts...).ToFunc()
}

// ByOutUDPPps orders the results by the out_udp_pps field.
func ByOutUDPPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutUDPPps, opts...).ToFunc()
}

// ByInUDPBps orders the results by the in_udp_bps field.
func ByInUDPBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInUDPBps, opts...).ToFunc()
}

// ByOutUDPBps orders the results by the out_udp_bps field.
func ByOutUDPBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutUDPBps, opts...).ToFunc()
}

// ByInIcmpPps orders the results by the in_icmp_pps field.
func ByInIcmpPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInIcmpPps, opts...).ToFunc()
}

// ByInIcmpBps orders the results by the in_icmp_bps field.
func ByInIcmpBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInIcmpBps, opts...).ToFunc()
}

// ByOutIcmpBps orders the results by the out_icmp_bps field.
func ByOutIcmpBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutIcmpBps, opts...).ToFunc()
}

// ByOutIcmpPps orders the results by the out_icmp_pps field.
func ByOutIcmpPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutIcmpPps, opts...).ToFunc()
}

// ByInDNSPps orders the results by the in_dns_pps field.
func ByInDNSPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInDNSPps, opts...).ToFunc()
}

// ByOutDNSPps orders the results by the out_dns_pps field.
func ByOutDNSPps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutDNSPps, opts...).ToFunc()
}

// ByInDNSBps orders the results by the in_dns_bps field.
func ByInDNSBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInDNSBps, opts...).ToFunc()
}

// ByOutDNSBps orders the results by the out_dns_bps field.
func ByOutDNSBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOutDNSBps, opts...).ToFunc()
}

// ByCFilterID orders the results by the c_filter_id field.
func ByCFilterID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCFilterID, opts...).ToFunc()
}

// ByAttackFlags orders the results by the attack_flags field.
func ByAttackFlags(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAttackFlags, opts...).ToFunc()
}

// ByCount orders the results by the count field.
func ByCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCount, opts...).ToFunc()
}

// ByIPType orders the results by the ip_type field.
func ByIPType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIPType, opts...).ToFunc()
}

// ByCFilter orders the results by the c_filter field.
func ByCFilter(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCFilter, opts...).ToFunc()
}

// ByHost orders the results by the host field.
func ByHost(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHost, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// BySpectrumAlertField orders the results by spectrum_alert field.
func BySpectrumAlertField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSpectrumAlertStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newSpectrumAlertStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SpectrumAlertInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, SpectrumAlertTable, SpectrumAlertColumn),
	)
}
