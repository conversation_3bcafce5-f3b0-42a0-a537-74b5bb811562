// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/useroperationlog"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserOperationLogUpdate is the builder for updating UserOperationLog entities.
type UserOperationLogUpdate struct {
	config
	hooks    []Hook
	mutation *UserOperationLogMutation
}

// Where appends a list predicates to the UserOperationLogUpdate builder.
func (uolu *UserOperationLogUpdate) Where(ps ...predicate.UserOperationLog) *UserOperationLogUpdate {
	uolu.mutation.Where(ps...)
	return uolu
}

// SetRemark sets the "remark" field.
func (uolu *UserOperationLogUpdate) SetRemark(s string) *UserOperationLogUpdate {
	uolu.mutation.SetRemark(s)
	return uolu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (uolu *UserOperationLogUpdate) SetNillableRemark(s *string) *UserOperationLogUpdate {
	if s != nil {
		uolu.SetRemark(*s)
	}
	return uolu
}

// ClearRemark clears the value of the "remark" field.
func (uolu *UserOperationLogUpdate) ClearRemark() *UserOperationLogUpdate {
	uolu.mutation.ClearRemark()
	return uolu
}

// SetUpdatedAt sets the "updated_at" field.
func (uolu *UserOperationLogUpdate) SetUpdatedAt(t time.Time) *UserOperationLogUpdate {
	uolu.mutation.SetUpdatedAt(t)
	return uolu
}

// SetUsername sets the "username" field.
func (uolu *UserOperationLogUpdate) SetUsername(s string) *UserOperationLogUpdate {
	uolu.mutation.SetUsername(s)
	return uolu
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (uolu *UserOperationLogUpdate) SetNillableUsername(s *string) *UserOperationLogUpdate {
	if s != nil {
		uolu.SetUsername(*s)
	}
	return uolu
}

// SetMethod sets the "method" field.
func (uolu *UserOperationLogUpdate) SetMethod(s string) *UserOperationLogUpdate {
	uolu.mutation.SetMethod(s)
	return uolu
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (uolu *UserOperationLogUpdate) SetNillableMethod(s *string) *UserOperationLogUpdate {
	if s != nil {
		uolu.SetMethod(*s)
	}
	return uolu
}

// SetRequestID sets the "request_id" field.
func (uolu *UserOperationLogUpdate) SetRequestID(s string) *UserOperationLogUpdate {
	uolu.mutation.SetRequestID(s)
	return uolu
}

// SetNillableRequestID sets the "request_id" field if the given value is not nil.
func (uolu *UserOperationLogUpdate) SetNillableRequestID(s *string) *UserOperationLogUpdate {
	if s != nil {
		uolu.SetRequestID(*s)
	}
	return uolu
}

// SetURI sets the "uri" field.
func (uolu *UserOperationLogUpdate) SetURI(s string) *UserOperationLogUpdate {
	uolu.mutation.SetURI(s)
	return uolu
}

// SetNillableURI sets the "uri" field if the given value is not nil.
func (uolu *UserOperationLogUpdate) SetNillableURI(s *string) *UserOperationLogUpdate {
	if s != nil {
		uolu.SetURI(*s)
	}
	return uolu
}

// SetRequestBody sets the "request_body" field.
func (uolu *UserOperationLogUpdate) SetRequestBody(s string) *UserOperationLogUpdate {
	uolu.mutation.SetRequestBody(s)
	return uolu
}

// SetNillableRequestBody sets the "request_body" field if the given value is not nil.
func (uolu *UserOperationLogUpdate) SetNillableRequestBody(s *string) *UserOperationLogUpdate {
	if s != nil {
		uolu.SetRequestBody(*s)
	}
	return uolu
}

// SetProject sets the "project" field.
func (uolu *UserOperationLogUpdate) SetProject(s string) *UserOperationLogUpdate {
	uolu.mutation.SetProject(s)
	return uolu
}

// SetNillableProject sets the "project" field if the given value is not nil.
func (uolu *UserOperationLogUpdate) SetNillableProject(s *string) *UserOperationLogUpdate {
	if s != nil {
		uolu.SetProject(*s)
	}
	return uolu
}

// Mutation returns the UserOperationLogMutation object of the builder.
func (uolu *UserOperationLogUpdate) Mutation() *UserOperationLogMutation {
	return uolu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uolu *UserOperationLogUpdate) Save(ctx context.Context) (int, error) {
	uolu.defaults()
	return withHooks(ctx, uolu.sqlSave, uolu.mutation, uolu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uolu *UserOperationLogUpdate) SaveX(ctx context.Context) int {
	affected, err := uolu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uolu *UserOperationLogUpdate) Exec(ctx context.Context) error {
	_, err := uolu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uolu *UserOperationLogUpdate) ExecX(ctx context.Context) {
	if err := uolu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uolu *UserOperationLogUpdate) defaults() {
	if _, ok := uolu.mutation.UpdatedAt(); !ok {
		v := useroperationlog.UpdateDefaultUpdatedAt()
		uolu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uolu *UserOperationLogUpdate) check() error {
	if v, ok := uolu.mutation.Remark(); ok {
		if err := useroperationlog.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "UserOperationLog.remark": %w`, err)}
		}
	}
	if v, ok := uolu.mutation.URI(); ok {
		if err := useroperationlog.URIValidator(v); err != nil {
			return &ValidationError{Name: "uri", err: fmt.Errorf(`ent: validator failed for field "UserOperationLog.uri": %w`, err)}
		}
	}
	return nil
}

func (uolu *UserOperationLogUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uolu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(useroperationlog.Table, useroperationlog.Columns, sqlgraph.NewFieldSpec(useroperationlog.FieldID, field.TypeInt))
	if ps := uolu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uolu.mutation.Remark(); ok {
		_spec.SetField(useroperationlog.FieldRemark, field.TypeString, value)
	}
	if uolu.mutation.RemarkCleared() {
		_spec.ClearField(useroperationlog.FieldRemark, field.TypeString)
	}
	if value, ok := uolu.mutation.UpdatedAt(); ok {
		_spec.SetField(useroperationlog.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uolu.mutation.Username(); ok {
		_spec.SetField(useroperationlog.FieldUsername, field.TypeString, value)
	}
	if value, ok := uolu.mutation.Method(); ok {
		_spec.SetField(useroperationlog.FieldMethod, field.TypeString, value)
	}
	if value, ok := uolu.mutation.RequestID(); ok {
		_spec.SetField(useroperationlog.FieldRequestID, field.TypeString, value)
	}
	if value, ok := uolu.mutation.URI(); ok {
		_spec.SetField(useroperationlog.FieldURI, field.TypeString, value)
	}
	if value, ok := uolu.mutation.RequestBody(); ok {
		_spec.SetField(useroperationlog.FieldRequestBody, field.TypeString, value)
	}
	if value, ok := uolu.mutation.Project(); ok {
		_spec.SetField(useroperationlog.FieldProject, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, uolu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{useroperationlog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uolu.mutation.done = true
	return n, nil
}

// UserOperationLogUpdateOne is the builder for updating a single UserOperationLog entity.
type UserOperationLogUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UserOperationLogMutation
}

// SetRemark sets the "remark" field.
func (uoluo *UserOperationLogUpdateOne) SetRemark(s string) *UserOperationLogUpdateOne {
	uoluo.mutation.SetRemark(s)
	return uoluo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (uoluo *UserOperationLogUpdateOne) SetNillableRemark(s *string) *UserOperationLogUpdateOne {
	if s != nil {
		uoluo.SetRemark(*s)
	}
	return uoluo
}

// ClearRemark clears the value of the "remark" field.
func (uoluo *UserOperationLogUpdateOne) ClearRemark() *UserOperationLogUpdateOne {
	uoluo.mutation.ClearRemark()
	return uoluo
}

// SetUpdatedAt sets the "updated_at" field.
func (uoluo *UserOperationLogUpdateOne) SetUpdatedAt(t time.Time) *UserOperationLogUpdateOne {
	uoluo.mutation.SetUpdatedAt(t)
	return uoluo
}

// SetUsername sets the "username" field.
func (uoluo *UserOperationLogUpdateOne) SetUsername(s string) *UserOperationLogUpdateOne {
	uoluo.mutation.SetUsername(s)
	return uoluo
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (uoluo *UserOperationLogUpdateOne) SetNillableUsername(s *string) *UserOperationLogUpdateOne {
	if s != nil {
		uoluo.SetUsername(*s)
	}
	return uoluo
}

// SetMethod sets the "method" field.
func (uoluo *UserOperationLogUpdateOne) SetMethod(s string) *UserOperationLogUpdateOne {
	uoluo.mutation.SetMethod(s)
	return uoluo
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (uoluo *UserOperationLogUpdateOne) SetNillableMethod(s *string) *UserOperationLogUpdateOne {
	if s != nil {
		uoluo.SetMethod(*s)
	}
	return uoluo
}

// SetRequestID sets the "request_id" field.
func (uoluo *UserOperationLogUpdateOne) SetRequestID(s string) *UserOperationLogUpdateOne {
	uoluo.mutation.SetRequestID(s)
	return uoluo
}

// SetNillableRequestID sets the "request_id" field if the given value is not nil.
func (uoluo *UserOperationLogUpdateOne) SetNillableRequestID(s *string) *UserOperationLogUpdateOne {
	if s != nil {
		uoluo.SetRequestID(*s)
	}
	return uoluo
}

// SetURI sets the "uri" field.
func (uoluo *UserOperationLogUpdateOne) SetURI(s string) *UserOperationLogUpdateOne {
	uoluo.mutation.SetURI(s)
	return uoluo
}

// SetNillableURI sets the "uri" field if the given value is not nil.
func (uoluo *UserOperationLogUpdateOne) SetNillableURI(s *string) *UserOperationLogUpdateOne {
	if s != nil {
		uoluo.SetURI(*s)
	}
	return uoluo
}

// SetRequestBody sets the "request_body" field.
func (uoluo *UserOperationLogUpdateOne) SetRequestBody(s string) *UserOperationLogUpdateOne {
	uoluo.mutation.SetRequestBody(s)
	return uoluo
}

// SetNillableRequestBody sets the "request_body" field if the given value is not nil.
func (uoluo *UserOperationLogUpdateOne) SetNillableRequestBody(s *string) *UserOperationLogUpdateOne {
	if s != nil {
		uoluo.SetRequestBody(*s)
	}
	return uoluo
}

// SetProject sets the "project" field.
func (uoluo *UserOperationLogUpdateOne) SetProject(s string) *UserOperationLogUpdateOne {
	uoluo.mutation.SetProject(s)
	return uoluo
}

// SetNillableProject sets the "project" field if the given value is not nil.
func (uoluo *UserOperationLogUpdateOne) SetNillableProject(s *string) *UserOperationLogUpdateOne {
	if s != nil {
		uoluo.SetProject(*s)
	}
	return uoluo
}

// Mutation returns the UserOperationLogMutation object of the builder.
func (uoluo *UserOperationLogUpdateOne) Mutation() *UserOperationLogMutation {
	return uoluo.mutation
}

// Where appends a list predicates to the UserOperationLogUpdate builder.
func (uoluo *UserOperationLogUpdateOne) Where(ps ...predicate.UserOperationLog) *UserOperationLogUpdateOne {
	uoluo.mutation.Where(ps...)
	return uoluo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uoluo *UserOperationLogUpdateOne) Select(field string, fields ...string) *UserOperationLogUpdateOne {
	uoluo.fields = append([]string{field}, fields...)
	return uoluo
}

// Save executes the query and returns the updated UserOperationLog entity.
func (uoluo *UserOperationLogUpdateOne) Save(ctx context.Context) (*UserOperationLog, error) {
	uoluo.defaults()
	return withHooks(ctx, uoluo.sqlSave, uoluo.mutation, uoluo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uoluo *UserOperationLogUpdateOne) SaveX(ctx context.Context) *UserOperationLog {
	node, err := uoluo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uoluo *UserOperationLogUpdateOne) Exec(ctx context.Context) error {
	_, err := uoluo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uoluo *UserOperationLogUpdateOne) ExecX(ctx context.Context) {
	if err := uoluo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uoluo *UserOperationLogUpdateOne) defaults() {
	if _, ok := uoluo.mutation.UpdatedAt(); !ok {
		v := useroperationlog.UpdateDefaultUpdatedAt()
		uoluo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uoluo *UserOperationLogUpdateOne) check() error {
	if v, ok := uoluo.mutation.Remark(); ok {
		if err := useroperationlog.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "UserOperationLog.remark": %w`, err)}
		}
	}
	if v, ok := uoluo.mutation.URI(); ok {
		if err := useroperationlog.URIValidator(v); err != nil {
			return &ValidationError{Name: "uri", err: fmt.Errorf(`ent: validator failed for field "UserOperationLog.uri": %w`, err)}
		}
	}
	return nil
}

func (uoluo *UserOperationLogUpdateOne) sqlSave(ctx context.Context) (_node *UserOperationLog, err error) {
	if err := uoluo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(useroperationlog.Table, useroperationlog.Columns, sqlgraph.NewFieldSpec(useroperationlog.FieldID, field.TypeInt))
	id, ok := uoluo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "UserOperationLog.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uoluo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, useroperationlog.FieldID)
		for _, f := range fields {
			if !useroperationlog.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != useroperationlog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uoluo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uoluo.mutation.Remark(); ok {
		_spec.SetField(useroperationlog.FieldRemark, field.TypeString, value)
	}
	if uoluo.mutation.RemarkCleared() {
		_spec.ClearField(useroperationlog.FieldRemark, field.TypeString)
	}
	if value, ok := uoluo.mutation.UpdatedAt(); ok {
		_spec.SetField(useroperationlog.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uoluo.mutation.Username(); ok {
		_spec.SetField(useroperationlog.FieldUsername, field.TypeString, value)
	}
	if value, ok := uoluo.mutation.Method(); ok {
		_spec.SetField(useroperationlog.FieldMethod, field.TypeString, value)
	}
	if value, ok := uoluo.mutation.RequestID(); ok {
		_spec.SetField(useroperationlog.FieldRequestID, field.TypeString, value)
	}
	if value, ok := uoluo.mutation.URI(); ok {
		_spec.SetField(useroperationlog.FieldURI, field.TypeString, value)
	}
	if value, ok := uoluo.mutation.RequestBody(); ok {
		_spec.SetField(useroperationlog.FieldRequestBody, field.TypeString, value)
	}
	if value, ok := uoluo.mutation.Project(); ok {
		_spec.SetField(useroperationlog.FieldProject, field.TypeString, value)
	}
	_node = &UserOperationLog{config: uoluo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uoluo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{useroperationlog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uoluo.mutation.done = true
	return _node, nil
}
