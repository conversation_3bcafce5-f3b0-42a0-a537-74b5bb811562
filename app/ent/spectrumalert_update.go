// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cleandata"
	"meta/app/ent/predicate"
	"meta/app/ent/protectgroup"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"
	"meta/app/ent/wofang"
	"meta/app/entity/netease"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SpectrumAlertUpdate is the builder for updating SpectrumAlert entities.
type SpectrumAlertUpdate struct {
	config
	hooks    []Hook
	mutation *SpectrumAlertMutation
}

// Where appends a list predicates to the SpectrumAlertUpdate builder.
func (sau *SpectrumAlertUpdate) Where(ps ...predicate.SpectrumAlert) *SpectrumAlertUpdate {
	sau.mutation.Where(ps...)
	return sau
}

// SetTenantID sets the "tenant_id" field.
func (sau *SpectrumAlertUpdate) SetTenantID(i int) *SpectrumAlertUpdate {
	sau.mutation.SetTenantID(i)
	return sau
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableTenantID(i *int) *SpectrumAlertUpdate {
	if i != nil {
		sau.SetTenantID(*i)
	}
	return sau
}

// ClearTenantID clears the value of the "tenant_id" field.
func (sau *SpectrumAlertUpdate) ClearTenantID() *SpectrumAlertUpdate {
	sau.mutation.ClearTenantID()
	return sau
}

// SetUpdatedAt sets the "updated_at" field.
func (sau *SpectrumAlertUpdate) SetUpdatedAt(t time.Time) *SpectrumAlertUpdate {
	sau.mutation.SetUpdatedAt(t)
	return sau
}

// SetRemark sets the "remark" field.
func (sau *SpectrumAlertUpdate) SetRemark(s string) *SpectrumAlertUpdate {
	sau.mutation.SetRemark(s)
	return sau
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableRemark(s *string) *SpectrumAlertUpdate {
	if s != nil {
		sau.SetRemark(*s)
	}
	return sau
}

// ClearRemark clears the value of the "remark" field.
func (sau *SpectrumAlertUpdate) ClearRemark() *SpectrumAlertUpdate {
	sau.mutation.ClearRemark()
	return sau
}

// SetProtectGroupID sets the "protect_group_id" field.
func (sau *SpectrumAlertUpdate) SetProtectGroupID(i int) *SpectrumAlertUpdate {
	sau.mutation.SetProtectGroupID(i)
	return sau
}

// SetNillableProtectGroupID sets the "protect_group_id" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableProtectGroupID(i *int) *SpectrumAlertUpdate {
	if i != nil {
		sau.SetProtectGroupID(*i)
	}
	return sau
}

// ClearProtectGroupID clears the value of the "protect_group_id" field.
func (sau *SpectrumAlertUpdate) ClearProtectGroupID() *SpectrumAlertUpdate {
	sau.mutation.ClearProtectGroupID()
	return sau
}

// SetStrategyID sets the "strategy_id" field.
func (sau *SpectrumAlertUpdate) SetStrategyID(i int) *SpectrumAlertUpdate {
	sau.mutation.SetStrategyID(i)
	return sau
}

// SetNillableStrategyID sets the "strategy_id" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableStrategyID(i *int) *SpectrumAlertUpdate {
	if i != nil {
		sau.SetStrategyID(*i)
	}
	return sau
}

// ClearStrategyID clears the value of the "strategy_id" field.
func (sau *SpectrumAlertUpdate) ClearStrategyID() *SpectrumAlertUpdate {
	sau.mutation.ClearStrategyID()
	return sau
}

// SetWofangID sets the "wofang_id" field.
func (sau *SpectrumAlertUpdate) SetWofangID(i int) *SpectrumAlertUpdate {
	sau.mutation.SetWofangID(i)
	return sau
}

// SetNillableWofangID sets the "wofang_id" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableWofangID(i *int) *SpectrumAlertUpdate {
	if i != nil {
		sau.SetWofangID(*i)
	}
	return sau
}

// ClearWofangID clears the value of the "wofang_id" field.
func (sau *SpectrumAlertUpdate) ClearWofangID() *SpectrumAlertUpdate {
	sau.mutation.ClearWofangID()
	return sau
}

// SetProtectStatus sets the "protect_status" field.
func (sau *SpectrumAlertUpdate) SetProtectStatus(i *[]int) *SpectrumAlertUpdate {
	sau.mutation.SetProtectStatus(i)
	return sau
}

// ClearProtectStatus clears the value of the "protect_status" field.
func (sau *SpectrumAlertUpdate) ClearProtectStatus() *SpectrumAlertUpdate {
	sau.mutation.ClearProtectStatus()
	return sau
}

// SetIP sets the "ip" field.
func (sau *SpectrumAlertUpdate) SetIP(s string) *SpectrumAlertUpdate {
	sau.mutation.SetIP(s)
	return sau
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableIP(s *string) *SpectrumAlertUpdate {
	if s != nil {
		sau.SetIP(*s)
	}
	return sau
}

// SetStartTime sets the "start_time" field.
func (sau *SpectrumAlertUpdate) SetStartTime(t time.Time) *SpectrumAlertUpdate {
	sau.mutation.SetStartTime(t)
	return sau
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableStartTime(t *time.Time) *SpectrumAlertUpdate {
	if t != nil {
		sau.SetStartTime(*t)
	}
	return sau
}

// SetEndTime sets the "end_time" field.
func (sau *SpectrumAlertUpdate) SetEndTime(t time.Time) *SpectrumAlertUpdate {
	sau.mutation.SetEndTime(t)
	return sau
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableEndTime(t *time.Time) *SpectrumAlertUpdate {
	if t != nil {
		sau.SetEndTime(*t)
	}
	return sau
}

// ClearEndTime clears the value of the "end_time" field.
func (sau *SpectrumAlertUpdate) ClearEndTime() *SpectrumAlertUpdate {
	sau.mutation.ClearEndTime()
	return sau
}

// SetAttackType sets the "attack_type" field.
func (sau *SpectrumAlertUpdate) SetAttackType(s string) *SpectrumAlertUpdate {
	sau.mutation.SetAttackType(s)
	return sau
}

// SetNillableAttackType sets the "attack_type" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableAttackType(s *string) *SpectrumAlertUpdate {
	if s != nil {
		sau.SetAttackType(*s)
	}
	return sau
}

// SetMaxPps sets the "max_pps" field.
func (sau *SpectrumAlertUpdate) SetMaxPps(i int64) *SpectrumAlertUpdate {
	sau.mutation.ResetMaxPps()
	sau.mutation.SetMaxPps(i)
	return sau
}

// SetNillableMaxPps sets the "max_pps" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableMaxPps(i *int64) *SpectrumAlertUpdate {
	if i != nil {
		sau.SetMaxPps(*i)
	}
	return sau
}

// AddMaxPps adds i to the "max_pps" field.
func (sau *SpectrumAlertUpdate) AddMaxPps(i int64) *SpectrumAlertUpdate {
	sau.mutation.AddMaxPps(i)
	return sau
}

// SetMaxBps sets the "max_bps" field.
func (sau *SpectrumAlertUpdate) SetMaxBps(i int64) *SpectrumAlertUpdate {
	sau.mutation.ResetMaxBps()
	sau.mutation.SetMaxBps(i)
	return sau
}

// SetNillableMaxBps sets the "max_bps" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableMaxBps(i *int64) *SpectrumAlertUpdate {
	if i != nil {
		sau.SetMaxBps(*i)
	}
	return sau
}

// AddMaxBps adds i to the "max_bps" field.
func (sau *SpectrumAlertUpdate) AddMaxBps(i int64) *SpectrumAlertUpdate {
	sau.mutation.AddMaxBps(i)
	return sau
}

// SetAttackInfo sets the "attack_info" field.
func (sau *SpectrumAlertUpdate) SetAttackInfo(ni netease.AttackInfo) *SpectrumAlertUpdate {
	sau.mutation.SetAttackInfo(ni)
	return sau
}

// SetNillableAttackInfo sets the "attack_info" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableAttackInfo(ni *netease.AttackInfo) *SpectrumAlertUpdate {
	if ni != nil {
		sau.SetAttackInfo(*ni)
	}
	return sau
}

// ClearAttackInfo clears the value of the "attack_info" field.
func (sau *SpectrumAlertUpdate) ClearAttackInfo() *SpectrumAlertUpdate {
	sau.mutation.ClearAttackInfo()
	return sau
}

// SetIspCode sets the "isp_code" field.
func (sau *SpectrumAlertUpdate) SetIspCode(i int) *SpectrumAlertUpdate {
	sau.mutation.ResetIspCode()
	sau.mutation.SetIspCode(i)
	return sau
}

// SetNillableIspCode sets the "isp_code" field if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableIspCode(i *int) *SpectrumAlertUpdate {
	if i != nil {
		sau.SetIspCode(*i)
	}
	return sau
}

// AddIspCode adds i to the "isp_code" field.
func (sau *SpectrumAlertUpdate) AddIspCode(i int) *SpectrumAlertUpdate {
	sau.mutation.AddIspCode(i)
	return sau
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sau *SpectrumAlertUpdate) SetTenant(t *Tenant) *SpectrumAlertUpdate {
	return sau.SetTenantID(t.ID)
}

// AddSpectrumDataIDs adds the "spectrum_datas" edge to the SpectrumData entity by IDs.
func (sau *SpectrumAlertUpdate) AddSpectrumDataIDs(ids ...int) *SpectrumAlertUpdate {
	sau.mutation.AddSpectrumDataIDs(ids...)
	return sau
}

// AddSpectrumDatas adds the "spectrum_datas" edges to the SpectrumData entity.
func (sau *SpectrumAlertUpdate) AddSpectrumDatas(s ...*SpectrumData) *SpectrumAlertUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return sau.AddSpectrumDataIDs(ids...)
}

// AddCleanDataIDs adds the "clean_datas" edge to the CleanData entity by IDs.
func (sau *SpectrumAlertUpdate) AddCleanDataIDs(ids ...int) *SpectrumAlertUpdate {
	sau.mutation.AddCleanDataIDs(ids...)
	return sau
}

// AddCleanDatas adds the "clean_datas" edges to the CleanData entity.
func (sau *SpectrumAlertUpdate) AddCleanDatas(c ...*CleanData) *SpectrumAlertUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return sau.AddCleanDataIDs(ids...)
}

// SetProtectGroup sets the "protect_group" edge to the ProtectGroup entity.
func (sau *SpectrumAlertUpdate) SetProtectGroup(p *ProtectGroup) *SpectrumAlertUpdate {
	return sau.SetProtectGroupID(p.ID)
}

// SetStrategy sets the "strategy" edge to the Strategy entity.
func (sau *SpectrumAlertUpdate) SetStrategy(s *Strategy) *SpectrumAlertUpdate {
	return sau.SetStrategyID(s.ID)
}

// SetWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID.
func (sau *SpectrumAlertUpdate) SetWofangTicketID(id int) *SpectrumAlertUpdate {
	sau.mutation.SetWofangTicketID(id)
	return sau
}

// SetNillableWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID if the given value is not nil.
func (sau *SpectrumAlertUpdate) SetNillableWofangTicketID(id *int) *SpectrumAlertUpdate {
	if id != nil {
		sau = sau.SetWofangTicketID(*id)
	}
	return sau
}

// SetWofangTicket sets the "wofang_ticket" edge to the Wofang entity.
func (sau *SpectrumAlertUpdate) SetWofangTicket(w *Wofang) *SpectrumAlertUpdate {
	return sau.SetWofangTicketID(w.ID)
}

// Mutation returns the SpectrumAlertMutation object of the builder.
func (sau *SpectrumAlertUpdate) Mutation() *SpectrumAlertMutation {
	return sau.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (sau *SpectrumAlertUpdate) ClearTenant() *SpectrumAlertUpdate {
	sau.mutation.ClearTenant()
	return sau
}

// ClearSpectrumDatas clears all "spectrum_datas" edges to the SpectrumData entity.
func (sau *SpectrumAlertUpdate) ClearSpectrumDatas() *SpectrumAlertUpdate {
	sau.mutation.ClearSpectrumDatas()
	return sau
}

// RemoveSpectrumDataIDs removes the "spectrum_datas" edge to SpectrumData entities by IDs.
func (sau *SpectrumAlertUpdate) RemoveSpectrumDataIDs(ids ...int) *SpectrumAlertUpdate {
	sau.mutation.RemoveSpectrumDataIDs(ids...)
	return sau
}

// RemoveSpectrumDatas removes "spectrum_datas" edges to SpectrumData entities.
func (sau *SpectrumAlertUpdate) RemoveSpectrumDatas(s ...*SpectrumData) *SpectrumAlertUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return sau.RemoveSpectrumDataIDs(ids...)
}

// ClearCleanDatas clears all "clean_datas" edges to the CleanData entity.
func (sau *SpectrumAlertUpdate) ClearCleanDatas() *SpectrumAlertUpdate {
	sau.mutation.ClearCleanDatas()
	return sau
}

// RemoveCleanDataIDs removes the "clean_datas" edge to CleanData entities by IDs.
func (sau *SpectrumAlertUpdate) RemoveCleanDataIDs(ids ...int) *SpectrumAlertUpdate {
	sau.mutation.RemoveCleanDataIDs(ids...)
	return sau
}

// RemoveCleanDatas removes "clean_datas" edges to CleanData entities.
func (sau *SpectrumAlertUpdate) RemoveCleanDatas(c ...*CleanData) *SpectrumAlertUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return sau.RemoveCleanDataIDs(ids...)
}

// ClearProtectGroup clears the "protect_group" edge to the ProtectGroup entity.
func (sau *SpectrumAlertUpdate) ClearProtectGroup() *SpectrumAlertUpdate {
	sau.mutation.ClearProtectGroup()
	return sau
}

// ClearStrategy clears the "strategy" edge to the Strategy entity.
func (sau *SpectrumAlertUpdate) ClearStrategy() *SpectrumAlertUpdate {
	sau.mutation.ClearStrategy()
	return sau
}

// ClearWofangTicket clears the "wofang_ticket" edge to the Wofang entity.
func (sau *SpectrumAlertUpdate) ClearWofangTicket() *SpectrumAlertUpdate {
	sau.mutation.ClearWofangTicket()
	return sau
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (sau *SpectrumAlertUpdate) Save(ctx context.Context) (int, error) {
	if err := sau.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, sau.sqlSave, sau.mutation, sau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sau *SpectrumAlertUpdate) SaveX(ctx context.Context) int {
	affected, err := sau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (sau *SpectrumAlertUpdate) Exec(ctx context.Context) error {
	_, err := sau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sau *SpectrumAlertUpdate) ExecX(ctx context.Context) {
	if err := sau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sau *SpectrumAlertUpdate) defaults() error {
	if _, ok := sau.mutation.UpdatedAt(); !ok {
		if spectrumalert.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized spectrumalert.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := spectrumalert.UpdateDefaultUpdatedAt()
		sau.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sau *SpectrumAlertUpdate) check() error {
	if v, ok := sau.mutation.Remark(); ok {
		if err := spectrumalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SpectrumAlert.remark": %w`, err)}
		}
	}
	return nil
}

func (sau *SpectrumAlertUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := sau.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(spectrumalert.Table, spectrumalert.Columns, sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt))
	if ps := sau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sau.mutation.UpdatedAt(); ok {
		_spec.SetField(spectrumalert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := sau.mutation.Remark(); ok {
		_spec.SetField(spectrumalert.FieldRemark, field.TypeString, value)
	}
	if sau.mutation.RemarkCleared() {
		_spec.ClearField(spectrumalert.FieldRemark, field.TypeString)
	}
	if value, ok := sau.mutation.ProtectStatus(); ok {
		_spec.SetField(spectrumalert.FieldProtectStatus, field.TypeJSON, value)
	}
	if sau.mutation.ProtectStatusCleared() {
		_spec.ClearField(spectrumalert.FieldProtectStatus, field.TypeJSON)
	}
	if value, ok := sau.mutation.IP(); ok {
		_spec.SetField(spectrumalert.FieldIP, field.TypeString, value)
	}
	if value, ok := sau.mutation.StartTime(); ok {
		_spec.SetField(spectrumalert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := sau.mutation.EndTime(); ok {
		_spec.SetField(spectrumalert.FieldEndTime, field.TypeTime, value)
	}
	if sau.mutation.EndTimeCleared() {
		_spec.ClearField(spectrumalert.FieldEndTime, field.TypeTime)
	}
	if value, ok := sau.mutation.AttackType(); ok {
		_spec.SetField(spectrumalert.FieldAttackType, field.TypeString, value)
	}
	if value, ok := sau.mutation.MaxPps(); ok {
		_spec.SetField(spectrumalert.FieldMaxPps, field.TypeInt64, value)
	}
	if value, ok := sau.mutation.AddedMaxPps(); ok {
		_spec.AddField(spectrumalert.FieldMaxPps, field.TypeInt64, value)
	}
	if value, ok := sau.mutation.MaxBps(); ok {
		_spec.SetField(spectrumalert.FieldMaxBps, field.TypeInt64, value)
	}
	if value, ok := sau.mutation.AddedMaxBps(); ok {
		_spec.AddField(spectrumalert.FieldMaxBps, field.TypeInt64, value)
	}
	if value, ok := sau.mutation.AttackInfo(); ok {
		_spec.SetField(spectrumalert.FieldAttackInfo, field.TypeJSON, value)
	}
	if sau.mutation.AttackInfoCleared() {
		_spec.ClearField(spectrumalert.FieldAttackInfo, field.TypeJSON)
	}
	if value, ok := sau.mutation.IspCode(); ok {
		_spec.SetField(spectrumalert.FieldIspCode, field.TypeInt, value)
	}
	if value, ok := sau.mutation.AddedIspCode(); ok {
		_spec.AddField(spectrumalert.FieldIspCode, field.TypeInt, value)
	}
	if sau.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumalert.TenantTable,
			Columns: []string{spectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sau.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumalert.TenantTable,
			Columns: []string{spectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sau.mutation.SpectrumDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.SpectrumDatasTable,
			Columns: []string{spectrumalert.SpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sau.mutation.RemovedSpectrumDatasIDs(); len(nodes) > 0 && !sau.mutation.SpectrumDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.SpectrumDatasTable,
			Columns: []string{spectrumalert.SpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sau.mutation.SpectrumDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.SpectrumDatasTable,
			Columns: []string{spectrumalert.SpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sau.mutation.CleanDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.CleanDatasTable,
			Columns: []string{spectrumalert.CleanDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sau.mutation.RemovedCleanDatasIDs(); len(nodes) > 0 && !sau.mutation.CleanDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.CleanDatasTable,
			Columns: []string{spectrumalert.CleanDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sau.mutation.CleanDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.CleanDatasTable,
			Columns: []string{spectrumalert.CleanDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sau.mutation.ProtectGroupCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.ProtectGroupTable,
			Columns: []string{spectrumalert.ProtectGroupColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sau.mutation.ProtectGroupIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.ProtectGroupTable,
			Columns: []string{spectrumalert.ProtectGroupColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sau.mutation.StrategyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.StrategyTable,
			Columns: []string{spectrumalert.StrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sau.mutation.StrategyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.StrategyTable,
			Columns: []string{spectrumalert.StrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sau.mutation.WofangTicketCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.WofangTicketTable,
			Columns: []string{spectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sau.mutation.WofangTicketIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.WofangTicketTable,
			Columns: []string{spectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, sau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{spectrumalert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	sau.mutation.done = true
	return n, nil
}

// SpectrumAlertUpdateOne is the builder for updating a single SpectrumAlert entity.
type SpectrumAlertUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SpectrumAlertMutation
}

// SetTenantID sets the "tenant_id" field.
func (sauo *SpectrumAlertUpdateOne) SetTenantID(i int) *SpectrumAlertUpdateOne {
	sauo.mutation.SetTenantID(i)
	return sauo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableTenantID(i *int) *SpectrumAlertUpdateOne {
	if i != nil {
		sauo.SetTenantID(*i)
	}
	return sauo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (sauo *SpectrumAlertUpdateOne) ClearTenantID() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearTenantID()
	return sauo
}

// SetUpdatedAt sets the "updated_at" field.
func (sauo *SpectrumAlertUpdateOne) SetUpdatedAt(t time.Time) *SpectrumAlertUpdateOne {
	sauo.mutation.SetUpdatedAt(t)
	return sauo
}

// SetRemark sets the "remark" field.
func (sauo *SpectrumAlertUpdateOne) SetRemark(s string) *SpectrumAlertUpdateOne {
	sauo.mutation.SetRemark(s)
	return sauo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableRemark(s *string) *SpectrumAlertUpdateOne {
	if s != nil {
		sauo.SetRemark(*s)
	}
	return sauo
}

// ClearRemark clears the value of the "remark" field.
func (sauo *SpectrumAlertUpdateOne) ClearRemark() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearRemark()
	return sauo
}

// SetProtectGroupID sets the "protect_group_id" field.
func (sauo *SpectrumAlertUpdateOne) SetProtectGroupID(i int) *SpectrumAlertUpdateOne {
	sauo.mutation.SetProtectGroupID(i)
	return sauo
}

// SetNillableProtectGroupID sets the "protect_group_id" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableProtectGroupID(i *int) *SpectrumAlertUpdateOne {
	if i != nil {
		sauo.SetProtectGroupID(*i)
	}
	return sauo
}

// ClearProtectGroupID clears the value of the "protect_group_id" field.
func (sauo *SpectrumAlertUpdateOne) ClearProtectGroupID() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearProtectGroupID()
	return sauo
}

// SetStrategyID sets the "strategy_id" field.
func (sauo *SpectrumAlertUpdateOne) SetStrategyID(i int) *SpectrumAlertUpdateOne {
	sauo.mutation.SetStrategyID(i)
	return sauo
}

// SetNillableStrategyID sets the "strategy_id" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableStrategyID(i *int) *SpectrumAlertUpdateOne {
	if i != nil {
		sauo.SetStrategyID(*i)
	}
	return sauo
}

// ClearStrategyID clears the value of the "strategy_id" field.
func (sauo *SpectrumAlertUpdateOne) ClearStrategyID() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearStrategyID()
	return sauo
}

// SetWofangID sets the "wofang_id" field.
func (sauo *SpectrumAlertUpdateOne) SetWofangID(i int) *SpectrumAlertUpdateOne {
	sauo.mutation.SetWofangID(i)
	return sauo
}

// SetNillableWofangID sets the "wofang_id" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableWofangID(i *int) *SpectrumAlertUpdateOne {
	if i != nil {
		sauo.SetWofangID(*i)
	}
	return sauo
}

// ClearWofangID clears the value of the "wofang_id" field.
func (sauo *SpectrumAlertUpdateOne) ClearWofangID() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearWofangID()
	return sauo
}

// SetProtectStatus sets the "protect_status" field.
func (sauo *SpectrumAlertUpdateOne) SetProtectStatus(i *[]int) *SpectrumAlertUpdateOne {
	sauo.mutation.SetProtectStatus(i)
	return sauo
}

// ClearProtectStatus clears the value of the "protect_status" field.
func (sauo *SpectrumAlertUpdateOne) ClearProtectStatus() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearProtectStatus()
	return sauo
}

// SetIP sets the "ip" field.
func (sauo *SpectrumAlertUpdateOne) SetIP(s string) *SpectrumAlertUpdateOne {
	sauo.mutation.SetIP(s)
	return sauo
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableIP(s *string) *SpectrumAlertUpdateOne {
	if s != nil {
		sauo.SetIP(*s)
	}
	return sauo
}

// SetStartTime sets the "start_time" field.
func (sauo *SpectrumAlertUpdateOne) SetStartTime(t time.Time) *SpectrumAlertUpdateOne {
	sauo.mutation.SetStartTime(t)
	return sauo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableStartTime(t *time.Time) *SpectrumAlertUpdateOne {
	if t != nil {
		sauo.SetStartTime(*t)
	}
	return sauo
}

// SetEndTime sets the "end_time" field.
func (sauo *SpectrumAlertUpdateOne) SetEndTime(t time.Time) *SpectrumAlertUpdateOne {
	sauo.mutation.SetEndTime(t)
	return sauo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableEndTime(t *time.Time) *SpectrumAlertUpdateOne {
	if t != nil {
		sauo.SetEndTime(*t)
	}
	return sauo
}

// ClearEndTime clears the value of the "end_time" field.
func (sauo *SpectrumAlertUpdateOne) ClearEndTime() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearEndTime()
	return sauo
}

// SetAttackType sets the "attack_type" field.
func (sauo *SpectrumAlertUpdateOne) SetAttackType(s string) *SpectrumAlertUpdateOne {
	sauo.mutation.SetAttackType(s)
	return sauo
}

// SetNillableAttackType sets the "attack_type" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableAttackType(s *string) *SpectrumAlertUpdateOne {
	if s != nil {
		sauo.SetAttackType(*s)
	}
	return sauo
}

// SetMaxPps sets the "max_pps" field.
func (sauo *SpectrumAlertUpdateOne) SetMaxPps(i int64) *SpectrumAlertUpdateOne {
	sauo.mutation.ResetMaxPps()
	sauo.mutation.SetMaxPps(i)
	return sauo
}

// SetNillableMaxPps sets the "max_pps" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableMaxPps(i *int64) *SpectrumAlertUpdateOne {
	if i != nil {
		sauo.SetMaxPps(*i)
	}
	return sauo
}

// AddMaxPps adds i to the "max_pps" field.
func (sauo *SpectrumAlertUpdateOne) AddMaxPps(i int64) *SpectrumAlertUpdateOne {
	sauo.mutation.AddMaxPps(i)
	return sauo
}

// SetMaxBps sets the "max_bps" field.
func (sauo *SpectrumAlertUpdateOne) SetMaxBps(i int64) *SpectrumAlertUpdateOne {
	sauo.mutation.ResetMaxBps()
	sauo.mutation.SetMaxBps(i)
	return sauo
}

// SetNillableMaxBps sets the "max_bps" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableMaxBps(i *int64) *SpectrumAlertUpdateOne {
	if i != nil {
		sauo.SetMaxBps(*i)
	}
	return sauo
}

// AddMaxBps adds i to the "max_bps" field.
func (sauo *SpectrumAlertUpdateOne) AddMaxBps(i int64) *SpectrumAlertUpdateOne {
	sauo.mutation.AddMaxBps(i)
	return sauo
}

// SetAttackInfo sets the "attack_info" field.
func (sauo *SpectrumAlertUpdateOne) SetAttackInfo(ni netease.AttackInfo) *SpectrumAlertUpdateOne {
	sauo.mutation.SetAttackInfo(ni)
	return sauo
}

// SetNillableAttackInfo sets the "attack_info" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableAttackInfo(ni *netease.AttackInfo) *SpectrumAlertUpdateOne {
	if ni != nil {
		sauo.SetAttackInfo(*ni)
	}
	return sauo
}

// ClearAttackInfo clears the value of the "attack_info" field.
func (sauo *SpectrumAlertUpdateOne) ClearAttackInfo() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearAttackInfo()
	return sauo
}

// SetIspCode sets the "isp_code" field.
func (sauo *SpectrumAlertUpdateOne) SetIspCode(i int) *SpectrumAlertUpdateOne {
	sauo.mutation.ResetIspCode()
	sauo.mutation.SetIspCode(i)
	return sauo
}

// SetNillableIspCode sets the "isp_code" field if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableIspCode(i *int) *SpectrumAlertUpdateOne {
	if i != nil {
		sauo.SetIspCode(*i)
	}
	return sauo
}

// AddIspCode adds i to the "isp_code" field.
func (sauo *SpectrumAlertUpdateOne) AddIspCode(i int) *SpectrumAlertUpdateOne {
	sauo.mutation.AddIspCode(i)
	return sauo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sauo *SpectrumAlertUpdateOne) SetTenant(t *Tenant) *SpectrumAlertUpdateOne {
	return sauo.SetTenantID(t.ID)
}

// AddSpectrumDataIDs adds the "spectrum_datas" edge to the SpectrumData entity by IDs.
func (sauo *SpectrumAlertUpdateOne) AddSpectrumDataIDs(ids ...int) *SpectrumAlertUpdateOne {
	sauo.mutation.AddSpectrumDataIDs(ids...)
	return sauo
}

// AddSpectrumDatas adds the "spectrum_datas" edges to the SpectrumData entity.
func (sauo *SpectrumAlertUpdateOne) AddSpectrumDatas(s ...*SpectrumData) *SpectrumAlertUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return sauo.AddSpectrumDataIDs(ids...)
}

// AddCleanDataIDs adds the "clean_datas" edge to the CleanData entity by IDs.
func (sauo *SpectrumAlertUpdateOne) AddCleanDataIDs(ids ...int) *SpectrumAlertUpdateOne {
	sauo.mutation.AddCleanDataIDs(ids...)
	return sauo
}

// AddCleanDatas adds the "clean_datas" edges to the CleanData entity.
func (sauo *SpectrumAlertUpdateOne) AddCleanDatas(c ...*CleanData) *SpectrumAlertUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return sauo.AddCleanDataIDs(ids...)
}

// SetProtectGroup sets the "protect_group" edge to the ProtectGroup entity.
func (sauo *SpectrumAlertUpdateOne) SetProtectGroup(p *ProtectGroup) *SpectrumAlertUpdateOne {
	return sauo.SetProtectGroupID(p.ID)
}

// SetStrategy sets the "strategy" edge to the Strategy entity.
func (sauo *SpectrumAlertUpdateOne) SetStrategy(s *Strategy) *SpectrumAlertUpdateOne {
	return sauo.SetStrategyID(s.ID)
}

// SetWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID.
func (sauo *SpectrumAlertUpdateOne) SetWofangTicketID(id int) *SpectrumAlertUpdateOne {
	sauo.mutation.SetWofangTicketID(id)
	return sauo
}

// SetNillableWofangTicketID sets the "wofang_ticket" edge to the Wofang entity by ID if the given value is not nil.
func (sauo *SpectrumAlertUpdateOne) SetNillableWofangTicketID(id *int) *SpectrumAlertUpdateOne {
	if id != nil {
		sauo = sauo.SetWofangTicketID(*id)
	}
	return sauo
}

// SetWofangTicket sets the "wofang_ticket" edge to the Wofang entity.
func (sauo *SpectrumAlertUpdateOne) SetWofangTicket(w *Wofang) *SpectrumAlertUpdateOne {
	return sauo.SetWofangTicketID(w.ID)
}

// Mutation returns the SpectrumAlertMutation object of the builder.
func (sauo *SpectrumAlertUpdateOne) Mutation() *SpectrumAlertMutation {
	return sauo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (sauo *SpectrumAlertUpdateOne) ClearTenant() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearTenant()
	return sauo
}

// ClearSpectrumDatas clears all "spectrum_datas" edges to the SpectrumData entity.
func (sauo *SpectrumAlertUpdateOne) ClearSpectrumDatas() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearSpectrumDatas()
	return sauo
}

// RemoveSpectrumDataIDs removes the "spectrum_datas" edge to SpectrumData entities by IDs.
func (sauo *SpectrumAlertUpdateOne) RemoveSpectrumDataIDs(ids ...int) *SpectrumAlertUpdateOne {
	sauo.mutation.RemoveSpectrumDataIDs(ids...)
	return sauo
}

// RemoveSpectrumDatas removes "spectrum_datas" edges to SpectrumData entities.
func (sauo *SpectrumAlertUpdateOne) RemoveSpectrumDatas(s ...*SpectrumData) *SpectrumAlertUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return sauo.RemoveSpectrumDataIDs(ids...)
}

// ClearCleanDatas clears all "clean_datas" edges to the CleanData entity.
func (sauo *SpectrumAlertUpdateOne) ClearCleanDatas() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearCleanDatas()
	return sauo
}

// RemoveCleanDataIDs removes the "clean_datas" edge to CleanData entities by IDs.
func (sauo *SpectrumAlertUpdateOne) RemoveCleanDataIDs(ids ...int) *SpectrumAlertUpdateOne {
	sauo.mutation.RemoveCleanDataIDs(ids...)
	return sauo
}

// RemoveCleanDatas removes "clean_datas" edges to CleanData entities.
func (sauo *SpectrumAlertUpdateOne) RemoveCleanDatas(c ...*CleanData) *SpectrumAlertUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return sauo.RemoveCleanDataIDs(ids...)
}

// ClearProtectGroup clears the "protect_group" edge to the ProtectGroup entity.
func (sauo *SpectrumAlertUpdateOne) ClearProtectGroup() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearProtectGroup()
	return sauo
}

// ClearStrategy clears the "strategy" edge to the Strategy entity.
func (sauo *SpectrumAlertUpdateOne) ClearStrategy() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearStrategy()
	return sauo
}

// ClearWofangTicket clears the "wofang_ticket" edge to the Wofang entity.
func (sauo *SpectrumAlertUpdateOne) ClearWofangTicket() *SpectrumAlertUpdateOne {
	sauo.mutation.ClearWofangTicket()
	return sauo
}

// Where appends a list predicates to the SpectrumAlertUpdate builder.
func (sauo *SpectrumAlertUpdateOne) Where(ps ...predicate.SpectrumAlert) *SpectrumAlertUpdateOne {
	sauo.mutation.Where(ps...)
	return sauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (sauo *SpectrumAlertUpdateOne) Select(field string, fields ...string) *SpectrumAlertUpdateOne {
	sauo.fields = append([]string{field}, fields...)
	return sauo
}

// Save executes the query and returns the updated SpectrumAlert entity.
func (sauo *SpectrumAlertUpdateOne) Save(ctx context.Context) (*SpectrumAlert, error) {
	if err := sauo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sauo.sqlSave, sauo.mutation, sauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sauo *SpectrumAlertUpdateOne) SaveX(ctx context.Context) *SpectrumAlert {
	node, err := sauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (sauo *SpectrumAlertUpdateOne) Exec(ctx context.Context) error {
	_, err := sauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sauo *SpectrumAlertUpdateOne) ExecX(ctx context.Context) {
	if err := sauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sauo *SpectrumAlertUpdateOne) defaults() error {
	if _, ok := sauo.mutation.UpdatedAt(); !ok {
		if spectrumalert.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized spectrumalert.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := spectrumalert.UpdateDefaultUpdatedAt()
		sauo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sauo *SpectrumAlertUpdateOne) check() error {
	if v, ok := sauo.mutation.Remark(); ok {
		if err := spectrumalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SpectrumAlert.remark": %w`, err)}
		}
	}
	return nil
}

func (sauo *SpectrumAlertUpdateOne) sqlSave(ctx context.Context) (_node *SpectrumAlert, err error) {
	if err := sauo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(spectrumalert.Table, spectrumalert.Columns, sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt))
	id, ok := sauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SpectrumAlert.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := sauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, spectrumalert.FieldID)
		for _, f := range fields {
			if !spectrumalert.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != spectrumalert.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := sauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sauo.mutation.UpdatedAt(); ok {
		_spec.SetField(spectrumalert.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := sauo.mutation.Remark(); ok {
		_spec.SetField(spectrumalert.FieldRemark, field.TypeString, value)
	}
	if sauo.mutation.RemarkCleared() {
		_spec.ClearField(spectrumalert.FieldRemark, field.TypeString)
	}
	if value, ok := sauo.mutation.ProtectStatus(); ok {
		_spec.SetField(spectrumalert.FieldProtectStatus, field.TypeJSON, value)
	}
	if sauo.mutation.ProtectStatusCleared() {
		_spec.ClearField(spectrumalert.FieldProtectStatus, field.TypeJSON)
	}
	if value, ok := sauo.mutation.IP(); ok {
		_spec.SetField(spectrumalert.FieldIP, field.TypeString, value)
	}
	if value, ok := sauo.mutation.StartTime(); ok {
		_spec.SetField(spectrumalert.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := sauo.mutation.EndTime(); ok {
		_spec.SetField(spectrumalert.FieldEndTime, field.TypeTime, value)
	}
	if sauo.mutation.EndTimeCleared() {
		_spec.ClearField(spectrumalert.FieldEndTime, field.TypeTime)
	}
	if value, ok := sauo.mutation.AttackType(); ok {
		_spec.SetField(spectrumalert.FieldAttackType, field.TypeString, value)
	}
	if value, ok := sauo.mutation.MaxPps(); ok {
		_spec.SetField(spectrumalert.FieldMaxPps, field.TypeInt64, value)
	}
	if value, ok := sauo.mutation.AddedMaxPps(); ok {
		_spec.AddField(spectrumalert.FieldMaxPps, field.TypeInt64, value)
	}
	if value, ok := sauo.mutation.MaxBps(); ok {
		_spec.SetField(spectrumalert.FieldMaxBps, field.TypeInt64, value)
	}
	if value, ok := sauo.mutation.AddedMaxBps(); ok {
		_spec.AddField(spectrumalert.FieldMaxBps, field.TypeInt64, value)
	}
	if value, ok := sauo.mutation.AttackInfo(); ok {
		_spec.SetField(spectrumalert.FieldAttackInfo, field.TypeJSON, value)
	}
	if sauo.mutation.AttackInfoCleared() {
		_spec.ClearField(spectrumalert.FieldAttackInfo, field.TypeJSON)
	}
	if value, ok := sauo.mutation.IspCode(); ok {
		_spec.SetField(spectrumalert.FieldIspCode, field.TypeInt, value)
	}
	if value, ok := sauo.mutation.AddedIspCode(); ok {
		_spec.AddField(spectrumalert.FieldIspCode, field.TypeInt, value)
	}
	if sauo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumalert.TenantTable,
			Columns: []string{spectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sauo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumalert.TenantTable,
			Columns: []string{spectrumalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sauo.mutation.SpectrumDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.SpectrumDatasTable,
			Columns: []string{spectrumalert.SpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sauo.mutation.RemovedSpectrumDatasIDs(); len(nodes) > 0 && !sauo.mutation.SpectrumDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.SpectrumDatasTable,
			Columns: []string{spectrumalert.SpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sauo.mutation.SpectrumDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.SpectrumDatasTable,
			Columns: []string{spectrumalert.SpectrumDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sauo.mutation.CleanDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.CleanDatasTable,
			Columns: []string{spectrumalert.CleanDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sauo.mutation.RemovedCleanDatasIDs(); len(nodes) > 0 && !sauo.mutation.CleanDatasCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.CleanDatasTable,
			Columns: []string{spectrumalert.CleanDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sauo.mutation.CleanDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   spectrumalert.CleanDatasTable,
			Columns: []string{spectrumalert.CleanDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cleandata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sauo.mutation.ProtectGroupCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.ProtectGroupTable,
			Columns: []string{spectrumalert.ProtectGroupColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sauo.mutation.ProtectGroupIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.ProtectGroupTable,
			Columns: []string{spectrumalert.ProtectGroupColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sauo.mutation.StrategyCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.StrategyTable,
			Columns: []string{spectrumalert.StrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sauo.mutation.StrategyIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.StrategyTable,
			Columns: []string{spectrumalert.StrategyColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if sauo.mutation.WofangTicketCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.WofangTicketTable,
			Columns: []string{spectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sauo.mutation.WofangTicketIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumalert.WofangTicketTable,
			Columns: []string{spectrumalert.WofangTicketColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &SpectrumAlert{config: sauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, sauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{spectrumalert.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	sauo.mutation.done = true
	return _node, nil
}
