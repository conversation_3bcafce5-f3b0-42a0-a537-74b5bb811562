// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixspectrumdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumDataUpdate is the builder for updating MatrixSpectrumData entities.
type MatrixSpectrumDataUpdate struct {
	config
	hooks    []Hook
	mutation *MatrixSpectrumDataMutation
}

// Where appends a list predicates to the MatrixSpectrumDataUpdate builder.
func (msdu *MatrixSpectrumDataUpdate) Where(ps ...predicate.MatrixSpectrumData) *MatrixSpectrumDataUpdate {
	msdu.mutation.Where(ps...)
	return msdu
}

// SetTenantID sets the "tenant_id" field.
func (msdu *MatrixSpectrumDataUpdate) SetTenantID(i int) *MatrixSpectrumDataUpdate {
	msdu.mutation.SetTenantID(i)
	return msdu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (msdu *MatrixSpectrumDataUpdate) SetNillableTenantID(i *int) *MatrixSpectrumDataUpdate {
	if i != nil {
		msdu.SetTenantID(*i)
	}
	return msdu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (msdu *MatrixSpectrumDataUpdate) ClearTenantID() *MatrixSpectrumDataUpdate {
	msdu.mutation.ClearTenantID()
	return msdu
}

// SetUpdatedAt sets the "updated_at" field.
func (msdu *MatrixSpectrumDataUpdate) SetUpdatedAt(t time.Time) *MatrixSpectrumDataUpdate {
	msdu.mutation.SetUpdatedAt(t)
	return msdu
}

// SetMatrixSpectrumAlertID sets the "matrix_spectrum_alert_id" field.
func (msdu *MatrixSpectrumDataUpdate) SetMatrixSpectrumAlertID(i int) *MatrixSpectrumDataUpdate {
	msdu.mutation.SetMatrixSpectrumAlertID(i)
	return msdu
}

// SetNillableMatrixSpectrumAlertID sets the "matrix_spectrum_alert_id" field if the given value is not nil.
func (msdu *MatrixSpectrumDataUpdate) SetNillableMatrixSpectrumAlertID(i *int) *MatrixSpectrumDataUpdate {
	if i != nil {
		msdu.SetMatrixSpectrumAlertID(*i)
	}
	return msdu
}

// ClearMatrixSpectrumAlertID clears the value of the "matrix_spectrum_alert_id" field.
func (msdu *MatrixSpectrumDataUpdate) ClearMatrixSpectrumAlertID() *MatrixSpectrumDataUpdate {
	msdu.mutation.ClearMatrixSpectrumAlertID()
	return msdu
}

// SetRegion sets the "region" field.
func (msdu *MatrixSpectrumDataUpdate) SetRegion(s string) *MatrixSpectrumDataUpdate {
	msdu.mutation.SetRegion(s)
	return msdu
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (msdu *MatrixSpectrumDataUpdate) SetNillableRegion(s *string) *MatrixSpectrumDataUpdate {
	if s != nil {
		msdu.SetRegion(*s)
	}
	return msdu
}

// SetNetType sets the "net_type" field.
func (msdu *MatrixSpectrumDataUpdate) SetNetType(s string) *MatrixSpectrumDataUpdate {
	msdu.mutation.SetNetType(s)
	return msdu
}

// SetNillableNetType sets the "net_type" field if the given value is not nil.
func (msdu *MatrixSpectrumDataUpdate) SetNillableNetType(s *string) *MatrixSpectrumDataUpdate {
	if s != nil {
		msdu.SetNetType(*s)
	}
	return msdu
}

// SetIsp sets the "isp" field.
func (msdu *MatrixSpectrumDataUpdate) SetIsp(s string) *MatrixSpectrumDataUpdate {
	msdu.mutation.SetIsp(s)
	return msdu
}

// SetNillableIsp sets the "isp" field if the given value is not nil.
func (msdu *MatrixSpectrumDataUpdate) SetNillableIsp(s *string) *MatrixSpectrumDataUpdate {
	if s != nil {
		msdu.SetIsp(*s)
	}
	return msdu
}

// SetBps sets the "bps" field.
func (msdu *MatrixSpectrumDataUpdate) SetBps(i int64) *MatrixSpectrumDataUpdate {
	msdu.mutation.ResetBps()
	msdu.mutation.SetBps(i)
	return msdu
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (msdu *MatrixSpectrumDataUpdate) SetNillableBps(i *int64) *MatrixSpectrumDataUpdate {
	if i != nil {
		msdu.SetBps(*i)
	}
	return msdu
}

// AddBps adds i to the "bps" field.
func (msdu *MatrixSpectrumDataUpdate) AddBps(i int64) *MatrixSpectrumDataUpdate {
	msdu.mutation.AddBps(i)
	return msdu
}

// SetTime sets the "time" field.
func (msdu *MatrixSpectrumDataUpdate) SetTime(t time.Time) *MatrixSpectrumDataUpdate {
	msdu.mutation.SetTime(t)
	return msdu
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (msdu *MatrixSpectrumDataUpdate) SetNillableTime(t *time.Time) *MatrixSpectrumDataUpdate {
	if t != nil {
		msdu.SetTime(*t)
	}
	return msdu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (msdu *MatrixSpectrumDataUpdate) SetTenant(t *Tenant) *MatrixSpectrumDataUpdate {
	return msdu.SetTenantID(t.ID)
}

// SetMatrixSpectrumAlert sets the "matrix_spectrum_alert" edge to the MatrixSpectrumAlert entity.
func (msdu *MatrixSpectrumDataUpdate) SetMatrixSpectrumAlert(m *MatrixSpectrumAlert) *MatrixSpectrumDataUpdate {
	return msdu.SetMatrixSpectrumAlertID(m.ID)
}

// Mutation returns the MatrixSpectrumDataMutation object of the builder.
func (msdu *MatrixSpectrumDataUpdate) Mutation() *MatrixSpectrumDataMutation {
	return msdu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (msdu *MatrixSpectrumDataUpdate) ClearTenant() *MatrixSpectrumDataUpdate {
	msdu.mutation.ClearTenant()
	return msdu
}

// ClearMatrixSpectrumAlert clears the "matrix_spectrum_alert" edge to the MatrixSpectrumAlert entity.
func (msdu *MatrixSpectrumDataUpdate) ClearMatrixSpectrumAlert() *MatrixSpectrumDataUpdate {
	msdu.mutation.ClearMatrixSpectrumAlert()
	return msdu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (msdu *MatrixSpectrumDataUpdate) Save(ctx context.Context) (int, error) {
	if err := msdu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, msdu.sqlSave, msdu.mutation, msdu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (msdu *MatrixSpectrumDataUpdate) SaveX(ctx context.Context) int {
	affected, err := msdu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (msdu *MatrixSpectrumDataUpdate) Exec(ctx context.Context) error {
	_, err := msdu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msdu *MatrixSpectrumDataUpdate) ExecX(ctx context.Context) {
	if err := msdu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msdu *MatrixSpectrumDataUpdate) defaults() error {
	if _, ok := msdu.mutation.UpdatedAt(); !ok {
		if matrixspectrumdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixspectrumdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixspectrumdata.UpdateDefaultUpdatedAt()
		msdu.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (msdu *MatrixSpectrumDataUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(matrixspectrumdata.Table, matrixspectrumdata.Columns, sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt))
	if ps := msdu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := msdu.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixspectrumdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := msdu.mutation.Region(); ok {
		_spec.SetField(matrixspectrumdata.FieldRegion, field.TypeString, value)
	}
	if value, ok := msdu.mutation.NetType(); ok {
		_spec.SetField(matrixspectrumdata.FieldNetType, field.TypeString, value)
	}
	if value, ok := msdu.mutation.Isp(); ok {
		_spec.SetField(matrixspectrumdata.FieldIsp, field.TypeString, value)
	}
	if value, ok := msdu.mutation.Bps(); ok {
		_spec.SetField(matrixspectrumdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := msdu.mutation.AddedBps(); ok {
		_spec.AddField(matrixspectrumdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := msdu.mutation.Time(); ok {
		_spec.SetField(matrixspectrumdata.FieldTime, field.TypeTime, value)
	}
	if msdu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumdata.TenantTable,
			Columns: []string{matrixspectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msdu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumdata.TenantTable,
			Columns: []string{matrixspectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if msdu.mutation.MatrixSpectrumAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumdata.MatrixSpectrumAlertTable,
			Columns: []string{matrixspectrumdata.MatrixSpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msdu.mutation.MatrixSpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumdata.MatrixSpectrumAlertTable,
			Columns: []string{matrixspectrumdata.MatrixSpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, msdu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{matrixspectrumdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	msdu.mutation.done = true
	return n, nil
}

// MatrixSpectrumDataUpdateOne is the builder for updating a single MatrixSpectrumData entity.
type MatrixSpectrumDataUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *MatrixSpectrumDataMutation
}

// SetTenantID sets the "tenant_id" field.
func (msduo *MatrixSpectrumDataUpdateOne) SetTenantID(i int) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.SetTenantID(i)
	return msduo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (msduo *MatrixSpectrumDataUpdateOne) SetNillableTenantID(i *int) *MatrixSpectrumDataUpdateOne {
	if i != nil {
		msduo.SetTenantID(*i)
	}
	return msduo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (msduo *MatrixSpectrumDataUpdateOne) ClearTenantID() *MatrixSpectrumDataUpdateOne {
	msduo.mutation.ClearTenantID()
	return msduo
}

// SetUpdatedAt sets the "updated_at" field.
func (msduo *MatrixSpectrumDataUpdateOne) SetUpdatedAt(t time.Time) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.SetUpdatedAt(t)
	return msduo
}

// SetMatrixSpectrumAlertID sets the "matrix_spectrum_alert_id" field.
func (msduo *MatrixSpectrumDataUpdateOne) SetMatrixSpectrumAlertID(i int) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.SetMatrixSpectrumAlertID(i)
	return msduo
}

// SetNillableMatrixSpectrumAlertID sets the "matrix_spectrum_alert_id" field if the given value is not nil.
func (msduo *MatrixSpectrumDataUpdateOne) SetNillableMatrixSpectrumAlertID(i *int) *MatrixSpectrumDataUpdateOne {
	if i != nil {
		msduo.SetMatrixSpectrumAlertID(*i)
	}
	return msduo
}

// ClearMatrixSpectrumAlertID clears the value of the "matrix_spectrum_alert_id" field.
func (msduo *MatrixSpectrumDataUpdateOne) ClearMatrixSpectrumAlertID() *MatrixSpectrumDataUpdateOne {
	msduo.mutation.ClearMatrixSpectrumAlertID()
	return msduo
}

// SetRegion sets the "region" field.
func (msduo *MatrixSpectrumDataUpdateOne) SetRegion(s string) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.SetRegion(s)
	return msduo
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (msduo *MatrixSpectrumDataUpdateOne) SetNillableRegion(s *string) *MatrixSpectrumDataUpdateOne {
	if s != nil {
		msduo.SetRegion(*s)
	}
	return msduo
}

// SetNetType sets the "net_type" field.
func (msduo *MatrixSpectrumDataUpdateOne) SetNetType(s string) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.SetNetType(s)
	return msduo
}

// SetNillableNetType sets the "net_type" field if the given value is not nil.
func (msduo *MatrixSpectrumDataUpdateOne) SetNillableNetType(s *string) *MatrixSpectrumDataUpdateOne {
	if s != nil {
		msduo.SetNetType(*s)
	}
	return msduo
}

// SetIsp sets the "isp" field.
func (msduo *MatrixSpectrumDataUpdateOne) SetIsp(s string) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.SetIsp(s)
	return msduo
}

// SetNillableIsp sets the "isp" field if the given value is not nil.
func (msduo *MatrixSpectrumDataUpdateOne) SetNillableIsp(s *string) *MatrixSpectrumDataUpdateOne {
	if s != nil {
		msduo.SetIsp(*s)
	}
	return msduo
}

// SetBps sets the "bps" field.
func (msduo *MatrixSpectrumDataUpdateOne) SetBps(i int64) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.ResetBps()
	msduo.mutation.SetBps(i)
	return msduo
}

// SetNillableBps sets the "bps" field if the given value is not nil.
func (msduo *MatrixSpectrumDataUpdateOne) SetNillableBps(i *int64) *MatrixSpectrumDataUpdateOne {
	if i != nil {
		msduo.SetBps(*i)
	}
	return msduo
}

// AddBps adds i to the "bps" field.
func (msduo *MatrixSpectrumDataUpdateOne) AddBps(i int64) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.AddBps(i)
	return msduo
}

// SetTime sets the "time" field.
func (msduo *MatrixSpectrumDataUpdateOne) SetTime(t time.Time) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.SetTime(t)
	return msduo
}

// SetNillableTime sets the "time" field if the given value is not nil.
func (msduo *MatrixSpectrumDataUpdateOne) SetNillableTime(t *time.Time) *MatrixSpectrumDataUpdateOne {
	if t != nil {
		msduo.SetTime(*t)
	}
	return msduo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (msduo *MatrixSpectrumDataUpdateOne) SetTenant(t *Tenant) *MatrixSpectrumDataUpdateOne {
	return msduo.SetTenantID(t.ID)
}

// SetMatrixSpectrumAlert sets the "matrix_spectrum_alert" edge to the MatrixSpectrumAlert entity.
func (msduo *MatrixSpectrumDataUpdateOne) SetMatrixSpectrumAlert(m *MatrixSpectrumAlert) *MatrixSpectrumDataUpdateOne {
	return msduo.SetMatrixSpectrumAlertID(m.ID)
}

// Mutation returns the MatrixSpectrumDataMutation object of the builder.
func (msduo *MatrixSpectrumDataUpdateOne) Mutation() *MatrixSpectrumDataMutation {
	return msduo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (msduo *MatrixSpectrumDataUpdateOne) ClearTenant() *MatrixSpectrumDataUpdateOne {
	msduo.mutation.ClearTenant()
	return msduo
}

// ClearMatrixSpectrumAlert clears the "matrix_spectrum_alert" edge to the MatrixSpectrumAlert entity.
func (msduo *MatrixSpectrumDataUpdateOne) ClearMatrixSpectrumAlert() *MatrixSpectrumDataUpdateOne {
	msduo.mutation.ClearMatrixSpectrumAlert()
	return msduo
}

// Where appends a list predicates to the MatrixSpectrumDataUpdate builder.
func (msduo *MatrixSpectrumDataUpdateOne) Where(ps ...predicate.MatrixSpectrumData) *MatrixSpectrumDataUpdateOne {
	msduo.mutation.Where(ps...)
	return msduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (msduo *MatrixSpectrumDataUpdateOne) Select(field string, fields ...string) *MatrixSpectrumDataUpdateOne {
	msduo.fields = append([]string{field}, fields...)
	return msduo
}

// Save executes the query and returns the updated MatrixSpectrumData entity.
func (msduo *MatrixSpectrumDataUpdateOne) Save(ctx context.Context) (*MatrixSpectrumData, error) {
	if err := msduo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, msduo.sqlSave, msduo.mutation, msduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (msduo *MatrixSpectrumDataUpdateOne) SaveX(ctx context.Context) *MatrixSpectrumData {
	node, err := msduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (msduo *MatrixSpectrumDataUpdateOne) Exec(ctx context.Context) error {
	_, err := msduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msduo *MatrixSpectrumDataUpdateOne) ExecX(ctx context.Context) {
	if err := msduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msduo *MatrixSpectrumDataUpdateOne) defaults() error {
	if _, ok := msduo.mutation.UpdatedAt(); !ok {
		if matrixspectrumdata.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixspectrumdata.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixspectrumdata.UpdateDefaultUpdatedAt()
		msduo.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (msduo *MatrixSpectrumDataUpdateOne) sqlSave(ctx context.Context) (_node *MatrixSpectrumData, err error) {
	_spec := sqlgraph.NewUpdateSpec(matrixspectrumdata.Table, matrixspectrumdata.Columns, sqlgraph.NewFieldSpec(matrixspectrumdata.FieldID, field.TypeInt))
	id, ok := msduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "MatrixSpectrumData.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := msduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, matrixspectrumdata.FieldID)
		for _, f := range fields {
			if !matrixspectrumdata.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != matrixspectrumdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := msduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := msduo.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixspectrumdata.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := msduo.mutation.Region(); ok {
		_spec.SetField(matrixspectrumdata.FieldRegion, field.TypeString, value)
	}
	if value, ok := msduo.mutation.NetType(); ok {
		_spec.SetField(matrixspectrumdata.FieldNetType, field.TypeString, value)
	}
	if value, ok := msduo.mutation.Isp(); ok {
		_spec.SetField(matrixspectrumdata.FieldIsp, field.TypeString, value)
	}
	if value, ok := msduo.mutation.Bps(); ok {
		_spec.SetField(matrixspectrumdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := msduo.mutation.AddedBps(); ok {
		_spec.AddField(matrixspectrumdata.FieldBps, field.TypeInt64, value)
	}
	if value, ok := msduo.mutation.Time(); ok {
		_spec.SetField(matrixspectrumdata.FieldTime, field.TypeTime, value)
	}
	if msduo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumdata.TenantTable,
			Columns: []string{matrixspectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msduo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   matrixspectrumdata.TenantTable,
			Columns: []string{matrixspectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if msduo.mutation.MatrixSpectrumAlertCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumdata.MatrixSpectrumAlertTable,
			Columns: []string{matrixspectrumdata.MatrixSpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msduo.mutation.MatrixSpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   matrixspectrumdata.MatrixSpectrumAlertTable,
			Columns: []string{matrixspectrumdata.MatrixSpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &MatrixSpectrumData{config: msduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, msduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{matrixspectrumdata.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	msduo.mutation.done = true
	return _node, nil
}
