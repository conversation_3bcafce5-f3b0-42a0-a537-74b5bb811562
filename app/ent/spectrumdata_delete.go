// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumdata"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SpectrumDataDelete is the builder for deleting a SpectrumData entity.
type SpectrumDataDelete struct {
	config
	hooks    []Hook
	mutation *SpectrumDataMutation
}

// Where appends a list predicates to the SpectrumDataDelete builder.
func (sdd *SpectrumDataDelete) Where(ps ...predicate.SpectrumData) *SpectrumDataDelete {
	sdd.mutation.Where(ps...)
	return sdd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (sdd *SpectrumDataDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, sdd.sqlExec, sdd.mutation, sdd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (sdd *SpectrumDataDelete) ExecX(ctx context.Context) int {
	n, err := sdd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (sdd *SpectrumDataDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(spectrumdata.Table, sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt))
	if ps := sdd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, sdd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	sdd.mutation.done = true
	return affected, err
}

// SpectrumDataDeleteOne is the builder for deleting a single SpectrumData entity.
type SpectrumDataDeleteOne struct {
	sdd *SpectrumDataDelete
}

// Where appends a list predicates to the SpectrumDataDelete builder.
func (sddo *SpectrumDataDeleteOne) Where(ps ...predicate.SpectrumData) *SpectrumDataDeleteOne {
	sddo.sdd.mutation.Where(ps...)
	return sddo
}

// Exec executes the deletion query.
func (sddo *SpectrumDataDeleteOne) Exec(ctx context.Context) error {
	n, err := sddo.sdd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{spectrumdata.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (sddo *SpectrumDataDeleteOne) ExecX(ctx context.Context) {
	if err := sddo.Exec(ctx); err != nil {
		panic(err)
	}
}
