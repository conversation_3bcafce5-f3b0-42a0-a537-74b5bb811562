// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/socgroupticket"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/entity/netease/socgroup"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SocGroupTicketCreate is the builder for creating a SocGroupTicket entity.
type SocGroupTicketCreate struct {
	config
	mutation *SocGroupTicketMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (sgtc *SocGroupTicketCreate) SetCreatedAt(t time.Time) *SocGroupTicketCreate {
	sgtc.mutation.SetCreatedAt(t)
	return sgtc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableCreatedAt(t *time.Time) *SocGroupTicketCreate {
	if t != nil {
		sgtc.SetCreatedAt(*t)
	}
	return sgtc
}

// SetUpdatedAt sets the "updated_at" field.
func (sgtc *SocGroupTicketCreate) SetUpdatedAt(t time.Time) *SocGroupTicketCreate {
	sgtc.mutation.SetUpdatedAt(t)
	return sgtc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableUpdatedAt(t *time.Time) *SocGroupTicketCreate {
	if t != nil {
		sgtc.SetUpdatedAt(*t)
	}
	return sgtc
}

// SetTenantID sets the "tenant_id" field.
func (sgtc *SocGroupTicketCreate) SetTenantID(i int) *SocGroupTicketCreate {
	sgtc.mutation.SetTenantID(i)
	return sgtc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableTenantID(i *int) *SocGroupTicketCreate {
	if i != nil {
		sgtc.SetTenantID(*i)
	}
	return sgtc
}

// SetRemark sets the "remark" field.
func (sgtc *SocGroupTicketCreate) SetRemark(s string) *SocGroupTicketCreate {
	sgtc.mutation.SetRemark(s)
	return sgtc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableRemark(s *string) *SocGroupTicketCreate {
	if s != nil {
		sgtc.SetRemark(*s)
	}
	return sgtc
}

// SetName sets the "name" field.
func (sgtc *SocGroupTicketCreate) SetName(s string) *SocGroupTicketCreate {
	sgtc.mutation.SetName(s)
	return sgtc
}

// SetType sets the "type" field.
func (sgtc *SocGroupTicketCreate) SetType(s string) *SocGroupTicketCreate {
	sgtc.mutation.SetType(s)
	return sgtc
}

// SetDescription sets the "description" field.
func (sgtc *SocGroupTicketCreate) SetDescription(s string) *SocGroupTicketCreate {
	sgtc.mutation.SetDescription(s)
	return sgtc
}

// SetFollowList sets the "follow_list" field.
func (sgtc *SocGroupTicketCreate) SetFollowList(i *[]int) *SocGroupTicketCreate {
	sgtc.mutation.SetFollowList(i)
	return sgtc
}

// SetDepartmentID sets the "department_id" field.
func (sgtc *SocGroupTicketCreate) SetDepartmentID(i int) *SocGroupTicketCreate {
	sgtc.mutation.SetDepartmentID(i)
	return sgtc
}

// SetIPList sets the "ip_list" field.
func (sgtc *SocGroupTicketCreate) SetIPList(s *[]string) *SocGroupTicketCreate {
	sgtc.mutation.SetIPList(s)
	return sgtc
}

// SetMinBandwidth sets the "min_bandwidth" field.
func (sgtc *SocGroupTicketCreate) SetMinBandwidth(f float32) *SocGroupTicketCreate {
	sgtc.mutation.SetMinBandwidth(f)
	return sgtc
}

// SetDivertType sets the "divert_type" field.
func (sgtc *SocGroupTicketCreate) SetDivertType(i int) *SocGroupTicketCreate {
	sgtc.mutation.SetDivertType(i)
	return sgtc
}

// SetOpType sets the "op_type" field.
func (sgtc *SocGroupTicketCreate) SetOpType(i int) *SocGroupTicketCreate {
	sgtc.mutation.SetOpType(i)
	return sgtc
}

// SetOpTime sets the "op_time" field.
func (sgtc *SocGroupTicketCreate) SetOpTime(t time.Time) *SocGroupTicketCreate {
	sgtc.mutation.SetOpTime(t)
	return sgtc
}

// SetNillableOpTime sets the "op_time" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableOpTime(t *time.Time) *SocGroupTicketCreate {
	if t != nil {
		sgtc.SetOpTime(*t)
	}
	return sgtc
}

// SetConfigType sets the "config_type" field.
func (sgtc *SocGroupTicketCreate) SetConfigType(i int) *SocGroupTicketCreate {
	sgtc.mutation.SetConfigType(i)
	return sgtc
}

// SetConfigArgs sets the "config_args" field.
func (sgtc *SocGroupTicketCreate) SetConfigArgs(s string) *SocGroupTicketCreate {
	sgtc.mutation.SetConfigArgs(s)
	return sgtc
}

// SetProductName sets the "product_name" field.
func (sgtc *SocGroupTicketCreate) SetProductName(s string) *SocGroupTicketCreate {
	sgtc.mutation.SetProductName(s)
	return sgtc
}

// SetProductCode sets the "product_code" field.
func (sgtc *SocGroupTicketCreate) SetProductCode(s string) *SocGroupTicketCreate {
	sgtc.mutation.SetProductCode(s)
	return sgtc
}

// SetNillableProductCode sets the "product_code" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableProductCode(s *string) *SocGroupTicketCreate {
	if s != nil {
		sgtc.SetProductCode(*s)
	}
	return sgtc
}

// SetContactList sets the "contact_list" field.
func (sgtc *SocGroupTicketCreate) SetContactList(s *[]socgroup.User) *SocGroupTicketCreate {
	sgtc.mutation.SetContactList(s)
	return sgtc
}

// SetGroupTicketID sets the "group_ticket_id" field.
func (sgtc *SocGroupTicketCreate) SetGroupTicketID(i int) *SocGroupTicketCreate {
	sgtc.mutation.SetGroupTicketID(i)
	return sgtc
}

// SetNillableGroupTicketID sets the "group_ticket_id" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableGroupTicketID(i *int) *SocGroupTicketCreate {
	if i != nil {
		sgtc.SetGroupTicketID(*i)
	}
	return sgtc
}

// SetErrorInfo sets the "error_info" field.
func (sgtc *SocGroupTicketCreate) SetErrorInfo(s string) *SocGroupTicketCreate {
	sgtc.mutation.SetErrorInfo(s)
	return sgtc
}

// SetNillableErrorInfo sets the "error_info" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableErrorInfo(s *string) *SocGroupTicketCreate {
	if s != nil {
		sgtc.SetErrorInfo(*s)
	}
	return sgtc
}

// SetCreateUserID sets the "create_user_id" field.
func (sgtc *SocGroupTicketCreate) SetCreateUserID(i int) *SocGroupTicketCreate {
	sgtc.mutation.SetCreateUserID(i)
	return sgtc
}

// SetNillableCreateUserID sets the "create_user_id" field if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableCreateUserID(i *int) *SocGroupTicketCreate {
	if i != nil {
		sgtc.SetCreateUserID(*i)
	}
	return sgtc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sgtc *SocGroupTicketCreate) SetTenant(t *Tenant) *SocGroupTicketCreate {
	return sgtc.SetTenantID(t.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (sgtc *SocGroupTicketCreate) SetUserID(id int) *SocGroupTicketCreate {
	sgtc.mutation.SetUserID(id)
	return sgtc
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (sgtc *SocGroupTicketCreate) SetNillableUserID(id *int) *SocGroupTicketCreate {
	if id != nil {
		sgtc = sgtc.SetUserID(*id)
	}
	return sgtc
}

// SetUser sets the "user" edge to the User entity.
func (sgtc *SocGroupTicketCreate) SetUser(u *User) *SocGroupTicketCreate {
	return sgtc.SetUserID(u.ID)
}

// Mutation returns the SocGroupTicketMutation object of the builder.
func (sgtc *SocGroupTicketCreate) Mutation() *SocGroupTicketMutation {
	return sgtc.mutation
}

// Save creates the SocGroupTicket in the database.
func (sgtc *SocGroupTicketCreate) Save(ctx context.Context) (*SocGroupTicket, error) {
	if err := sgtc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sgtc.sqlSave, sgtc.mutation, sgtc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sgtc *SocGroupTicketCreate) SaveX(ctx context.Context) *SocGroupTicket {
	v, err := sgtc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sgtc *SocGroupTicketCreate) Exec(ctx context.Context) error {
	_, err := sgtc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sgtc *SocGroupTicketCreate) ExecX(ctx context.Context) {
	if err := sgtc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sgtc *SocGroupTicketCreate) defaults() error {
	if _, ok := sgtc.mutation.CreatedAt(); !ok {
		if socgroupticket.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized socgroupticket.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := socgroupticket.DefaultCreatedAt()
		sgtc.mutation.SetCreatedAt(v)
	}
	if _, ok := sgtc.mutation.UpdatedAt(); !ok {
		if socgroupticket.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized socgroupticket.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := socgroupticket.DefaultUpdatedAt()
		sgtc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sgtc *SocGroupTicketCreate) check() error {
	if _, ok := sgtc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "SocGroupTicket.created_at"`)}
	}
	if _, ok := sgtc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "SocGroupTicket.updated_at"`)}
	}
	if v, ok := sgtc.mutation.Remark(); ok {
		if err := socgroupticket.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.remark": %w`, err)}
		}
	}
	if _, ok := sgtc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "SocGroupTicket.name"`)}
	}
	if _, ok := sgtc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "SocGroupTicket.type"`)}
	}
	if _, ok := sgtc.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "SocGroupTicket.description"`)}
	}
	if v, ok := sgtc.mutation.Description(); ok {
		if err := socgroupticket.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.description": %w`, err)}
		}
	}
	if _, ok := sgtc.mutation.DepartmentID(); !ok {
		return &ValidationError{Name: "department_id", err: errors.New(`ent: missing required field "SocGroupTicket.department_id"`)}
	}
	if _, ok := sgtc.mutation.MinBandwidth(); !ok {
		return &ValidationError{Name: "min_bandwidth", err: errors.New(`ent: missing required field "SocGroupTicket.min_bandwidth"`)}
	}
	if _, ok := sgtc.mutation.DivertType(); !ok {
		return &ValidationError{Name: "divert_type", err: errors.New(`ent: missing required field "SocGroupTicket.divert_type"`)}
	}
	if _, ok := sgtc.mutation.OpType(); !ok {
		return &ValidationError{Name: "op_type", err: errors.New(`ent: missing required field "SocGroupTicket.op_type"`)}
	}
	if _, ok := sgtc.mutation.ConfigType(); !ok {
		return &ValidationError{Name: "config_type", err: errors.New(`ent: missing required field "SocGroupTicket.config_type"`)}
	}
	if _, ok := sgtc.mutation.ConfigArgs(); !ok {
		return &ValidationError{Name: "config_args", err: errors.New(`ent: missing required field "SocGroupTicket.config_args"`)}
	}
	if v, ok := sgtc.mutation.ConfigArgs(); ok {
		if err := socgroupticket.ConfigArgsValidator(v); err != nil {
			return &ValidationError{Name: "config_args", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.config_args": %w`, err)}
		}
	}
	if _, ok := sgtc.mutation.ProductName(); !ok {
		return &ValidationError{Name: "product_name", err: errors.New(`ent: missing required field "SocGroupTicket.product_name"`)}
	}
	if v, ok := sgtc.mutation.ErrorInfo(); ok {
		if err := socgroupticket.ErrorInfoValidator(v); err != nil {
			return &ValidationError{Name: "error_info", err: fmt.Errorf(`ent: validator failed for field "SocGroupTicket.error_info": %w`, err)}
		}
	}
	return nil
}

func (sgtc *SocGroupTicketCreate) sqlSave(ctx context.Context) (*SocGroupTicket, error) {
	if err := sgtc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sgtc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sgtc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sgtc.mutation.id = &_node.ID
	sgtc.mutation.done = true
	return _node, nil
}

func (sgtc *SocGroupTicketCreate) createSpec() (*SocGroupTicket, *sqlgraph.CreateSpec) {
	var (
		_node = &SocGroupTicket{config: sgtc.config}
		_spec = sqlgraph.NewCreateSpec(socgroupticket.Table, sqlgraph.NewFieldSpec(socgroupticket.FieldID, field.TypeInt))
	)
	if value, ok := sgtc.mutation.CreatedAt(); ok {
		_spec.SetField(socgroupticket.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sgtc.mutation.UpdatedAt(); ok {
		_spec.SetField(socgroupticket.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := sgtc.mutation.Remark(); ok {
		_spec.SetField(socgroupticket.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := sgtc.mutation.Name(); ok {
		_spec.SetField(socgroupticket.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := sgtc.mutation.GetType(); ok {
		_spec.SetField(socgroupticket.FieldType, field.TypeString, value)
		_node.Type = value
	}
	if value, ok := sgtc.mutation.Description(); ok {
		_spec.SetField(socgroupticket.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := sgtc.mutation.FollowList(); ok {
		_spec.SetField(socgroupticket.FieldFollowList, field.TypeJSON, value)
		_node.FollowList = value
	}
	if value, ok := sgtc.mutation.DepartmentID(); ok {
		_spec.SetField(socgroupticket.FieldDepartmentID, field.TypeInt, value)
		_node.DepartmentID = value
	}
	if value, ok := sgtc.mutation.IPList(); ok {
		_spec.SetField(socgroupticket.FieldIPList, field.TypeJSON, value)
		_node.IPList = value
	}
	if value, ok := sgtc.mutation.MinBandwidth(); ok {
		_spec.SetField(socgroupticket.FieldMinBandwidth, field.TypeFloat32, value)
		_node.MinBandwidth = value
	}
	if value, ok := sgtc.mutation.DivertType(); ok {
		_spec.SetField(socgroupticket.FieldDivertType, field.TypeInt, value)
		_node.DivertType = value
	}
	if value, ok := sgtc.mutation.OpType(); ok {
		_spec.SetField(socgroupticket.FieldOpType, field.TypeInt, value)
		_node.OpType = value
	}
	if value, ok := sgtc.mutation.OpTime(); ok {
		_spec.SetField(socgroupticket.FieldOpTime, field.TypeTime, value)
		_node.OpTime = value
	}
	if value, ok := sgtc.mutation.ConfigType(); ok {
		_spec.SetField(socgroupticket.FieldConfigType, field.TypeInt, value)
		_node.ConfigType = value
	}
	if value, ok := sgtc.mutation.ConfigArgs(); ok {
		_spec.SetField(socgroupticket.FieldConfigArgs, field.TypeString, value)
		_node.ConfigArgs = value
	}
	if value, ok := sgtc.mutation.ProductName(); ok {
		_spec.SetField(socgroupticket.FieldProductName, field.TypeString, value)
		_node.ProductName = value
	}
	if value, ok := sgtc.mutation.ProductCode(); ok {
		_spec.SetField(socgroupticket.FieldProductCode, field.TypeString, value)
		_node.ProductCode = value
	}
	if value, ok := sgtc.mutation.ContactList(); ok {
		_spec.SetField(socgroupticket.FieldContactList, field.TypeJSON, value)
		_node.ContactList = value
	}
	if value, ok := sgtc.mutation.GroupTicketID(); ok {
		_spec.SetField(socgroupticket.FieldGroupTicketID, field.TypeInt, value)
		_node.GroupTicketID = value
	}
	if value, ok := sgtc.mutation.ErrorInfo(); ok {
		_spec.SetField(socgroupticket.FieldErrorInfo, field.TypeString, value)
		_node.ErrorInfo = value
	}
	if nodes := sgtc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.TenantTable,
			Columns: []string{socgroupticket.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sgtc.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   socgroupticket.UserTable,
			Columns: []string{socgroupticket.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.CreateUserID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// SocGroupTicketCreateBulk is the builder for creating many SocGroupTicket entities in bulk.
type SocGroupTicketCreateBulk struct {
	config
	err      error
	builders []*SocGroupTicketCreate
}

// Save creates the SocGroupTicket entities in the database.
func (sgtcb *SocGroupTicketCreateBulk) Save(ctx context.Context) ([]*SocGroupTicket, error) {
	if sgtcb.err != nil {
		return nil, sgtcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(sgtcb.builders))
	nodes := make([]*SocGroupTicket, len(sgtcb.builders))
	mutators := make([]Mutator, len(sgtcb.builders))
	for i := range sgtcb.builders {
		func(i int, root context.Context) {
			builder := sgtcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SocGroupTicketMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, sgtcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, sgtcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, sgtcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (sgtcb *SocGroupTicketCreateBulk) SaveX(ctx context.Context) []*SocGroupTicket {
	v, err := sgtcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sgtcb *SocGroupTicketCreateBulk) Exec(ctx context.Context) error {
	_, err := sgtcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sgtcb *SocGroupTicketCreateBulk) ExecX(ctx context.Context) {
	if err := sgtcb.Exec(ctx); err != nil {
		panic(err)
	}
}
