// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/strategy"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// StrategyCreate is the builder for creating a Strategy entity.
type StrategyCreate struct {
	config
	mutation *StrategyMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (sc *StrategyCreate) SetTenantID(i int) *StrategyCreate {
	sc.mutation.SetTenantID(i)
	return sc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sc *StrategyCreate) SetNillableTenantID(i *int) *StrategyCreate {
	if i != nil {
		sc.SetTenantID(*i)
	}
	return sc
}

// SetCreatedAt sets the "created_at" field.
func (sc *StrategyCreate) SetCreatedAt(t time.Time) *StrategyCreate {
	sc.mutation.SetCreatedAt(t)
	return sc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sc *StrategyCreate) SetNillableCreatedAt(t *time.Time) *StrategyCreate {
	if t != nil {
		sc.SetCreatedAt(*t)
	}
	return sc
}

// SetUpdatedAt sets the "updated_at" field.
func (sc *StrategyCreate) SetUpdatedAt(t time.Time) *StrategyCreate {
	sc.mutation.SetUpdatedAt(t)
	return sc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sc *StrategyCreate) SetNillableUpdatedAt(t *time.Time) *StrategyCreate {
	if t != nil {
		sc.SetUpdatedAt(*t)
	}
	return sc
}

// SetRemark sets the "remark" field.
func (sc *StrategyCreate) SetRemark(s string) *StrategyCreate {
	sc.mutation.SetRemark(s)
	return sc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sc *StrategyCreate) SetNillableRemark(s *string) *StrategyCreate {
	if s != nil {
		sc.SetRemark(*s)
	}
	return sc
}

// SetName sets the "name" field.
func (sc *StrategyCreate) SetName(s string) *StrategyCreate {
	sc.mutation.SetName(s)
	return sc
}

// SetType sets the "type" field.
func (sc *StrategyCreate) SetType(i int) *StrategyCreate {
	sc.mutation.SetType(i)
	return sc
}

// SetEnabled sets the "enabled" field.
func (sc *StrategyCreate) SetEnabled(b bool) *StrategyCreate {
	sc.mutation.SetEnabled(b)
	return sc
}

// SetSystem sets the "system" field.
func (sc *StrategyCreate) SetSystem(b bool) *StrategyCreate {
	sc.mutation.SetSystem(b)
	return sc
}

// SetBps sets the "bps" field.
func (sc *StrategyCreate) SetBps(i int64) *StrategyCreate {
	sc.mutation.SetBps(i)
	return sc
}

// SetPps sets the "pps" field.
func (sc *StrategyCreate) SetPps(i int64) *StrategyCreate {
	sc.mutation.SetPps(i)
	return sc
}

// SetBpsCount sets the "bps_count" field.
func (sc *StrategyCreate) SetBpsCount(i int) *StrategyCreate {
	sc.mutation.SetBpsCount(i)
	return sc
}

// SetPpsCount sets the "pps_count" field.
func (sc *StrategyCreate) SetPpsCount(i int) *StrategyCreate {
	sc.mutation.SetPpsCount(i)
	return sc
}

// SetIspCode sets the "isp_code" field.
func (sc *StrategyCreate) SetIspCode(i int) *StrategyCreate {
	sc.mutation.SetIspCode(i)
	return sc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sc *StrategyCreate) SetTenant(t *Tenant) *StrategyCreate {
	return sc.SetTenantID(t.ID)
}

// AddStrategyAlertIDs adds the "strategy_alerts" edge to the SpectrumAlert entity by IDs.
func (sc *StrategyCreate) AddStrategyAlertIDs(ids ...int) *StrategyCreate {
	sc.mutation.AddStrategyAlertIDs(ids...)
	return sc
}

// AddStrategyAlerts adds the "strategy_alerts" edges to the SpectrumAlert entity.
func (sc *StrategyCreate) AddStrategyAlerts(s ...*SpectrumAlert) *StrategyCreate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return sc.AddStrategyAlertIDs(ids...)
}

// Mutation returns the StrategyMutation object of the builder.
func (sc *StrategyCreate) Mutation() *StrategyMutation {
	return sc.mutation
}

// Save creates the Strategy in the database.
func (sc *StrategyCreate) Save(ctx context.Context) (*Strategy, error) {
	if err := sc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sc.sqlSave, sc.mutation, sc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sc *StrategyCreate) SaveX(ctx context.Context) *Strategy {
	v, err := sc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sc *StrategyCreate) Exec(ctx context.Context) error {
	_, err := sc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sc *StrategyCreate) ExecX(ctx context.Context) {
	if err := sc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sc *StrategyCreate) defaults() error {
	if _, ok := sc.mutation.CreatedAt(); !ok {
		if strategy.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized strategy.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := strategy.DefaultCreatedAt()
		sc.mutation.SetCreatedAt(v)
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		if strategy.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized strategy.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := strategy.DefaultUpdatedAt()
		sc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sc *StrategyCreate) check() error {
	if _, ok := sc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Strategy.created_at"`)}
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Strategy.updated_at"`)}
	}
	if v, ok := sc.mutation.Remark(); ok {
		if err := strategy.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Strategy.remark": %w`, err)}
		}
	}
	if _, ok := sc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Strategy.name"`)}
	}
	if _, ok := sc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Strategy.type"`)}
	}
	if _, ok := sc.mutation.Enabled(); !ok {
		return &ValidationError{Name: "enabled", err: errors.New(`ent: missing required field "Strategy.enabled"`)}
	}
	if _, ok := sc.mutation.System(); !ok {
		return &ValidationError{Name: "system", err: errors.New(`ent: missing required field "Strategy.system"`)}
	}
	if _, ok := sc.mutation.Bps(); !ok {
		return &ValidationError{Name: "bps", err: errors.New(`ent: missing required field "Strategy.bps"`)}
	}
	if _, ok := sc.mutation.Pps(); !ok {
		return &ValidationError{Name: "pps", err: errors.New(`ent: missing required field "Strategy.pps"`)}
	}
	if _, ok := sc.mutation.BpsCount(); !ok {
		return &ValidationError{Name: "bps_count", err: errors.New(`ent: missing required field "Strategy.bps_count"`)}
	}
	if _, ok := sc.mutation.PpsCount(); !ok {
		return &ValidationError{Name: "pps_count", err: errors.New(`ent: missing required field "Strategy.pps_count"`)}
	}
	if _, ok := sc.mutation.IspCode(); !ok {
		return &ValidationError{Name: "isp_code", err: errors.New(`ent: missing required field "Strategy.isp_code"`)}
	}
	return nil
}

func (sc *StrategyCreate) sqlSave(ctx context.Context) (*Strategy, error) {
	if err := sc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sc.mutation.id = &_node.ID
	sc.mutation.done = true
	return _node, nil
}

func (sc *StrategyCreate) createSpec() (*Strategy, *sqlgraph.CreateSpec) {
	var (
		_node = &Strategy{config: sc.config}
		_spec = sqlgraph.NewCreateSpec(strategy.Table, sqlgraph.NewFieldSpec(strategy.FieldID, field.TypeInt))
	)
	if value, ok := sc.mutation.CreatedAt(); ok {
		_spec.SetField(strategy.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sc.mutation.UpdatedAt(); ok {
		_spec.SetField(strategy.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := sc.mutation.Remark(); ok {
		_spec.SetField(strategy.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := sc.mutation.Name(); ok {
		_spec.SetField(strategy.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := sc.mutation.GetType(); ok {
		_spec.SetField(strategy.FieldType, field.TypeInt, value)
		_node.Type = value
	}
	if value, ok := sc.mutation.Enabled(); ok {
		_spec.SetField(strategy.FieldEnabled, field.TypeBool, value)
		_node.Enabled = value
	}
	if value, ok := sc.mutation.System(); ok {
		_spec.SetField(strategy.FieldSystem, field.TypeBool, value)
		_node.System = value
	}
	if value, ok := sc.mutation.Bps(); ok {
		_spec.SetField(strategy.FieldBps, field.TypeInt64, value)
		_node.Bps = value
	}
	if value, ok := sc.mutation.Pps(); ok {
		_spec.SetField(strategy.FieldPps, field.TypeInt64, value)
		_node.Pps = value
	}
	if value, ok := sc.mutation.BpsCount(); ok {
		_spec.SetField(strategy.FieldBpsCount, field.TypeInt, value)
		_node.BpsCount = value
	}
	if value, ok := sc.mutation.PpsCount(); ok {
		_spec.SetField(strategy.FieldPpsCount, field.TypeInt, value)
		_node.PpsCount = value
	}
	if value, ok := sc.mutation.IspCode(); ok {
		_spec.SetField(strategy.FieldIspCode, field.TypeInt, value)
		_node.IspCode = value
	}
	if nodes := sc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   strategy.TenantTable,
			Columns: []string{strategy.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sc.mutation.StrategyAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   strategy.StrategyAlertsTable,
			Columns: []string{strategy.StrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// StrategyCreateBulk is the builder for creating many Strategy entities in bulk.
type StrategyCreateBulk struct {
	config
	err      error
	builders []*StrategyCreate
}

// Save creates the Strategy entities in the database.
func (scb *StrategyCreateBulk) Save(ctx context.Context) ([]*Strategy, error) {
	if scb.err != nil {
		return nil, scb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(scb.builders))
	nodes := make([]*Strategy, len(scb.builders))
	mutators := make([]Mutator, len(scb.builders))
	for i := range scb.builders {
		func(i int, root context.Context) {
			builder := scb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*StrategyMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, scb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, scb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, scb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (scb *StrategyCreateBulk) SaveX(ctx context.Context) []*Strategy {
	v, err := scb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (scb *StrategyCreateBulk) Exec(ctx context.Context) error {
	_, err := scb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scb *StrategyCreateBulk) ExecX(ctx context.Context) {
	if err := scb.Exec(ctx); err != nil {
		panic(err)
	}
}
