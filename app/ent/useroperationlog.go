// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"meta/app/ent/useroperationlog"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// UserOperationLog is the model entity for the UserOperationLog schema.
type UserOperationLog struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Username holds the value of the "username" field.
	Username string `json:"username,omitempty"`
	// Method holds the value of the "method" field.
	Method string `json:"method,omitempty"`
	// RequestID holds the value of the "request_id" field.
	RequestID string `json:"request_id,omitempty" query:"request_id,omitempty"`
	// URI holds the value of the "uri" field.
	URI string `json:"uri,omitempty"`
	// RequestBody holds the value of the "request_body" field.
	RequestBody string `json:"request_body,omitempty" query:"request_body,omitempty"`
	// Project holds the value of the "project" field.
	Project      string `json:"project,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*UserOperationLog) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case useroperationlog.FieldID:
			values[i] = new(sql.NullInt64)
		case useroperationlog.FieldRemark, useroperationlog.FieldUsername, useroperationlog.FieldMethod, useroperationlog.FieldRequestID, useroperationlog.FieldURI, useroperationlog.FieldRequestBody, useroperationlog.FieldProject:
			values[i] = new(sql.NullString)
		case useroperationlog.FieldCreatedAt, useroperationlog.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the UserOperationLog fields.
func (uol *UserOperationLog) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case useroperationlog.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			uol.ID = int(value.Int64)
		case useroperationlog.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				uol.Remark = new(string)
				*uol.Remark = value.String
			}
		case useroperationlog.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				uol.CreatedAt = value.Time
			}
		case useroperationlog.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				uol.UpdatedAt = value.Time
			}
		case useroperationlog.FieldUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field username", values[i])
			} else if value.Valid {
				uol.Username = value.String
			}
		case useroperationlog.FieldMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field method", values[i])
			} else if value.Valid {
				uol.Method = value.String
			}
		case useroperationlog.FieldRequestID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field request_id", values[i])
			} else if value.Valid {
				uol.RequestID = value.String
			}
		case useroperationlog.FieldURI:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field uri", values[i])
			} else if value.Valid {
				uol.URI = value.String
			}
		case useroperationlog.FieldRequestBody:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field request_body", values[i])
			} else if value.Valid {
				uol.RequestBody = value.String
			}
		case useroperationlog.FieldProject:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field project", values[i])
			} else if value.Valid {
				uol.Project = value.String
			}
		default:
			uol.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the UserOperationLog.
// This includes values selected through modifiers, order, etc.
func (uol *UserOperationLog) Value(name string) (ent.Value, error) {
	return uol.selectValues.Get(name)
}

// Update returns a builder for updating this UserOperationLog.
// Note that you need to call UserOperationLog.Unwrap() before calling this method if this UserOperationLog
// was returned from a transaction, and the transaction was committed or rolled back.
func (uol *UserOperationLog) Update() *UserOperationLogUpdateOne {
	return NewUserOperationLogClient(uol.config).UpdateOne(uol)
}

// Unwrap unwraps the UserOperationLog entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (uol *UserOperationLog) Unwrap() *UserOperationLog {
	_tx, ok := uol.config.driver.(*txDriver)
	if !ok {
		panic("ent: UserOperationLog is not a transactional entity")
	}
	uol.config.driver = _tx.drv
	return uol
}

// String implements the fmt.Stringer.
func (uol *UserOperationLog) String() string {
	var builder strings.Builder
	builder.WriteString("UserOperationLog(")
	builder.WriteString(fmt.Sprintf("id=%v, ", uol.ID))
	if v := uol.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(uol.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(uol.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("username=")
	builder.WriteString(uol.Username)
	builder.WriteString(", ")
	builder.WriteString("method=")
	builder.WriteString(uol.Method)
	builder.WriteString(", ")
	builder.WriteString("request_id=")
	builder.WriteString(uol.RequestID)
	builder.WriteString(", ")
	builder.WriteString("uri=")
	builder.WriteString(uol.URI)
	builder.WriteString(", ")
	builder.WriteString("request_body=")
	builder.WriteString(uol.RequestBody)
	builder.WriteString(", ")
	builder.WriteString("project=")
	builder.WriteString(uol.Project)
	builder.WriteByte(')')
	return builder.String()
}

// UserOperationLogs is a parsable slice of UserOperationLog.
type UserOperationLogs []*UserOperationLog
