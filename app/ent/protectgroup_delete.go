// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/protectgroup"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ProtectGroupDelete is the builder for deleting a ProtectGroup entity.
type ProtectGroupDelete struct {
	config
	hooks    []Hook
	mutation *ProtectGroupMutation
}

// Where appends a list predicates to the ProtectGroupDelete builder.
func (pgd *ProtectGroupDelete) Where(ps ...predicate.ProtectGroup) *ProtectGroupDelete {
	pgd.mutation.Where(ps...)
	return pgd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (pgd *ProtectGroupDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, pgd.sqlExec, pgd.mutation, pgd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (pgd *ProtectGroupDelete) ExecX(ctx context.Context) int {
	n, err := pgd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (pgd *ProtectGroupDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(protectgroup.Table, sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt))
	if ps := pgd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, pgd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	pgd.mutation.done = true
	return affected, err
}

// ProtectGroupDeleteOne is the builder for deleting a single ProtectGroup entity.
type ProtectGroupDeleteOne struct {
	pgd *ProtectGroupDelete
}

// Where appends a list predicates to the ProtectGroupDelete builder.
func (pgdo *ProtectGroupDeleteOne) Where(ps ...predicate.ProtectGroup) *ProtectGroupDeleteOne {
	pgdo.pgd.mutation.Where(ps...)
	return pgdo
}

// Exec executes the deletion query.
func (pgdo *ProtectGroupDeleteOne) Exec(ctx context.Context) error {
	n, err := pgdo.pgd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{protectgroup.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (pgdo *ProtectGroupDeleteOne) ExecX(ctx context.Context) {
	if err := pgdo.Exec(ctx); err != nil {
		panic(err)
	}
}
