// Code generated by ent, DO NOT EDIT.

package matrixstrategy

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldUpdatedAt, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldRemark, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldName, v))
}

// Region applies equality check predicate on the "region" field. It's identical to RegionEQ.
func Region(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldRegion, v))
}

// NetType applies equality check predicate on the "net_type" field. It's identical to NetTypeEQ.
func NetType(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldNetType, v))
}

// Isp applies equality check predicate on the "isp" field. It's identical to IspEQ.
func Isp(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldIsp, v))
}

// MonitorBps applies equality check predicate on the "monitor_bps" field. It's identical to MonitorBpsEQ.
func MonitorBps(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldMonitorBps, v))
}

// DragBps applies equality check predicate on the "drag_bps" field. It's identical to DragBpsEQ.
func DragBps(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldDragBps, v))
}

// DragType applies equality check predicate on the "drag_type" field. It's identical to DragTypeEQ.
func DragType(v int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldDragType, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldUpdatedAt, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContainsFold(FieldRemark, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContainsFold(FieldName, v))
}

// RegionEQ applies the EQ predicate on the "region" field.
func RegionEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldRegion, v))
}

// RegionNEQ applies the NEQ predicate on the "region" field.
func RegionNEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldRegion, v))
}

// RegionIn applies the In predicate on the "region" field.
func RegionIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldRegion, vs...))
}

// RegionNotIn applies the NotIn predicate on the "region" field.
func RegionNotIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldRegion, vs...))
}

// RegionGT applies the GT predicate on the "region" field.
func RegionGT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldRegion, v))
}

// RegionGTE applies the GTE predicate on the "region" field.
func RegionGTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldRegion, v))
}

// RegionLT applies the LT predicate on the "region" field.
func RegionLT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldRegion, v))
}

// RegionLTE applies the LTE predicate on the "region" field.
func RegionLTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldRegion, v))
}

// RegionContains applies the Contains predicate on the "region" field.
func RegionContains(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContains(FieldRegion, v))
}

// RegionHasPrefix applies the HasPrefix predicate on the "region" field.
func RegionHasPrefix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasPrefix(FieldRegion, v))
}

// RegionHasSuffix applies the HasSuffix predicate on the "region" field.
func RegionHasSuffix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasSuffix(FieldRegion, v))
}

// RegionEqualFold applies the EqualFold predicate on the "region" field.
func RegionEqualFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEqualFold(FieldRegion, v))
}

// RegionContainsFold applies the ContainsFold predicate on the "region" field.
func RegionContainsFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContainsFold(FieldRegion, v))
}

// NetTypeEQ applies the EQ predicate on the "net_type" field.
func NetTypeEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldNetType, v))
}

// NetTypeNEQ applies the NEQ predicate on the "net_type" field.
func NetTypeNEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldNetType, v))
}

// NetTypeIn applies the In predicate on the "net_type" field.
func NetTypeIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldNetType, vs...))
}

// NetTypeNotIn applies the NotIn predicate on the "net_type" field.
func NetTypeNotIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldNetType, vs...))
}

// NetTypeGT applies the GT predicate on the "net_type" field.
func NetTypeGT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldNetType, v))
}

// NetTypeGTE applies the GTE predicate on the "net_type" field.
func NetTypeGTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldNetType, v))
}

// NetTypeLT applies the LT predicate on the "net_type" field.
func NetTypeLT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldNetType, v))
}

// NetTypeLTE applies the LTE predicate on the "net_type" field.
func NetTypeLTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldNetType, v))
}

// NetTypeContains applies the Contains predicate on the "net_type" field.
func NetTypeContains(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContains(FieldNetType, v))
}

// NetTypeHasPrefix applies the HasPrefix predicate on the "net_type" field.
func NetTypeHasPrefix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasPrefix(FieldNetType, v))
}

// NetTypeHasSuffix applies the HasSuffix predicate on the "net_type" field.
func NetTypeHasSuffix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasSuffix(FieldNetType, v))
}

// NetTypeEqualFold applies the EqualFold predicate on the "net_type" field.
func NetTypeEqualFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEqualFold(FieldNetType, v))
}

// NetTypeContainsFold applies the ContainsFold predicate on the "net_type" field.
func NetTypeContainsFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContainsFold(FieldNetType, v))
}

// IspEQ applies the EQ predicate on the "isp" field.
func IspEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldIsp, v))
}

// IspNEQ applies the NEQ predicate on the "isp" field.
func IspNEQ(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldIsp, v))
}

// IspIn applies the In predicate on the "isp" field.
func IspIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldIsp, vs...))
}

// IspNotIn applies the NotIn predicate on the "isp" field.
func IspNotIn(vs ...string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldIsp, vs...))
}

// IspGT applies the GT predicate on the "isp" field.
func IspGT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldIsp, v))
}

// IspGTE applies the GTE predicate on the "isp" field.
func IspGTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldIsp, v))
}

// IspLT applies the LT predicate on the "isp" field.
func IspLT(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldIsp, v))
}

// IspLTE applies the LTE predicate on the "isp" field.
func IspLTE(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldIsp, v))
}

// IspContains applies the Contains predicate on the "isp" field.
func IspContains(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContains(FieldIsp, v))
}

// IspHasPrefix applies the HasPrefix predicate on the "isp" field.
func IspHasPrefix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasPrefix(FieldIsp, v))
}

// IspHasSuffix applies the HasSuffix predicate on the "isp" field.
func IspHasSuffix(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldHasSuffix(FieldIsp, v))
}

// IspEqualFold applies the EqualFold predicate on the "isp" field.
func IspEqualFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEqualFold(FieldIsp, v))
}

// IspContainsFold applies the ContainsFold predicate on the "isp" field.
func IspContainsFold(v string) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldContainsFold(FieldIsp, v))
}

// MonitorBpsEQ applies the EQ predicate on the "monitor_bps" field.
func MonitorBpsEQ(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldMonitorBps, v))
}

// MonitorBpsNEQ applies the NEQ predicate on the "monitor_bps" field.
func MonitorBpsNEQ(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldMonitorBps, v))
}

// MonitorBpsIn applies the In predicate on the "monitor_bps" field.
func MonitorBpsIn(vs ...int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldMonitorBps, vs...))
}

// MonitorBpsNotIn applies the NotIn predicate on the "monitor_bps" field.
func MonitorBpsNotIn(vs ...int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldMonitorBps, vs...))
}

// MonitorBpsGT applies the GT predicate on the "monitor_bps" field.
func MonitorBpsGT(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldMonitorBps, v))
}

// MonitorBpsGTE applies the GTE predicate on the "monitor_bps" field.
func MonitorBpsGTE(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldMonitorBps, v))
}

// MonitorBpsLT applies the LT predicate on the "monitor_bps" field.
func MonitorBpsLT(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldMonitorBps, v))
}

// MonitorBpsLTE applies the LTE predicate on the "monitor_bps" field.
func MonitorBpsLTE(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldMonitorBps, v))
}

// DragBpsEQ applies the EQ predicate on the "drag_bps" field.
func DragBpsEQ(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldDragBps, v))
}

// DragBpsNEQ applies the NEQ predicate on the "drag_bps" field.
func DragBpsNEQ(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldDragBps, v))
}

// DragBpsIn applies the In predicate on the "drag_bps" field.
func DragBpsIn(vs ...int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldDragBps, vs...))
}

// DragBpsNotIn applies the NotIn predicate on the "drag_bps" field.
func DragBpsNotIn(vs ...int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldDragBps, vs...))
}

// DragBpsGT applies the GT predicate on the "drag_bps" field.
func DragBpsGT(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldDragBps, v))
}

// DragBpsGTE applies the GTE predicate on the "drag_bps" field.
func DragBpsGTE(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldDragBps, v))
}

// DragBpsLT applies the LT predicate on the "drag_bps" field.
func DragBpsLT(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldDragBps, v))
}

// DragBpsLTE applies the LTE predicate on the "drag_bps" field.
func DragBpsLTE(v int64) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldDragBps, v))
}

// DragTypeEQ applies the EQ predicate on the "drag_type" field.
func DragTypeEQ(v int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldEQ(FieldDragType, v))
}

// DragTypeNEQ applies the NEQ predicate on the "drag_type" field.
func DragTypeNEQ(v int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNEQ(FieldDragType, v))
}

// DragTypeIn applies the In predicate on the "drag_type" field.
func DragTypeIn(vs ...int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldIn(FieldDragType, vs...))
}

// DragTypeNotIn applies the NotIn predicate on the "drag_type" field.
func DragTypeNotIn(vs ...int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldNotIn(FieldDragType, vs...))
}

// DragTypeGT applies the GT predicate on the "drag_type" field.
func DragTypeGT(v int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGT(FieldDragType, v))
}

// DragTypeGTE applies the GTE predicate on the "drag_type" field.
func DragTypeGTE(v int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldGTE(FieldDragType, v))
}

// DragTypeLT applies the LT predicate on the "drag_type" field.
func DragTypeLT(v int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLT(FieldDragType, v))
}

// DragTypeLTE applies the LTE predicate on the "drag_type" field.
func DragTypeLTE(v int) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.FieldLTE(FieldDragType, v))
}

// HasMatrixStrategyAlerts applies the HasEdge predicate on the "matrix_strategy_alerts" edge.
func HasMatrixStrategyAlerts() predicate.MatrixStrategy {
	return predicate.MatrixStrategy(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, MatrixStrategyAlertsTable, MatrixStrategyAlertsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMatrixStrategyAlertsWith applies the HasEdge predicate on the "matrix_strategy_alerts" edge with a given conditions (other predicates).
func HasMatrixStrategyAlertsWith(preds ...predicate.MatrixSpectrumAlert) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(func(s *sql.Selector) {
		step := newMatrixStrategyAlertsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.MatrixStrategy) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.MatrixStrategy) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.MatrixStrategy) predicate.MatrixStrategy {
	return predicate.MatrixStrategy(sql.NotPredicates(p))
}
