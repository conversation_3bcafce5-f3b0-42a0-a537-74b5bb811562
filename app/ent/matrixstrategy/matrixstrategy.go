// Code generated by ent, DO NOT EDIT.

package matrixstrategy

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the matrixstrategy type in the database.
	Label = "matrix_strategy"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldRegion holds the string denoting the region field in the database.
	FieldRegion = "region"
	// FieldNetType holds the string denoting the net_type field in the database.
	FieldNetType = "net_type"
	// FieldIsp holds the string denoting the isp field in the database.
	FieldIsp = "isp"
	// FieldMonitorBps holds the string denoting the monitor_bps field in the database.
	FieldMonitorBps = "monitor_bps"
	// FieldDragBps holds the string denoting the drag_bps field in the database.
	FieldDragBps = "drag_bps"
	// FieldDragType holds the string denoting the drag_type field in the database.
	FieldDragType = "drag_type"
	// EdgeMatrixStrategyAlerts holds the string denoting the matrix_strategy_alerts edge name in mutations.
	EdgeMatrixStrategyAlerts = "matrix_strategy_alerts"
	// Table holds the table name of the matrixstrategy in the database.
	Table = "matrix_strategies"
	// MatrixStrategyAlertsTable is the table that holds the matrix_strategy_alerts relation/edge.
	MatrixStrategyAlertsTable = "matrix_spectrum_alerts"
	// MatrixStrategyAlertsInverseTable is the table name for the MatrixSpectrumAlert entity.
	// It exists in this package in order to avoid circular dependency with the "matrixspectrumalert" package.
	MatrixStrategyAlertsInverseTable = "matrix_spectrum_alerts"
	// MatrixStrategyAlertsColumn is the table column denoting the matrix_strategy_alerts relation/edge.
	MatrixStrategyAlertsColumn = "matrix_strategy_id"
)

// Columns holds all SQL columns for matrixstrategy fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRemark,
	FieldName,
	FieldRegion,
	FieldNetType,
	FieldIsp,
	FieldMonitorBps,
	FieldDragBps,
	FieldDragType,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
	// NameValidator is a validator for the "name" field. It is called by the builders before save.
	NameValidator func(string) error
)

// OrderOption defines the ordering options for the MatrixStrategy queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByRegion orders the results by the region field.
func ByRegion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRegion, opts...).ToFunc()
}

// ByNetType orders the results by the net_type field.
func ByNetType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNetType, opts...).ToFunc()
}

// ByIsp orders the results by the isp field.
func ByIsp(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsp, opts...).ToFunc()
}

// ByMonitorBps orders the results by the monitor_bps field.
func ByMonitorBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMonitorBps, opts...).ToFunc()
}

// ByDragBps orders the results by the drag_bps field.
func ByDragBps(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDragBps, opts...).ToFunc()
}

// ByDragType orders the results by the drag_type field.
func ByDragType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDragType, opts...).ToFunc()
}

// ByMatrixStrategyAlertsCount orders the results by matrix_strategy_alerts count.
func ByMatrixStrategyAlertsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMatrixStrategyAlertsStep(), opts...)
	}
}

// ByMatrixStrategyAlerts orders the results by matrix_strategy_alerts terms.
func ByMatrixStrategyAlerts(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMatrixStrategyAlertsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newMatrixStrategyAlertsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MatrixStrategyAlertsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, MatrixStrategyAlertsTable, MatrixStrategyAlertsColumn),
	)
}
