// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/notify"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotifyCreate is the builder for creating a Notify entity.
type NotifyCreate struct {
	config
	mutation *NotifyMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (nc *NotifyCreate) SetCreatedAt(t time.Time) *NotifyCreate {
	nc.mutation.SetCreatedAt(t)
	return nc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (nc *NotifyCreate) SetNillableCreatedAt(t *time.Time) *NotifyCreate {
	if t != nil {
		nc.SetCreatedAt(*t)
	}
	return nc
}

// SetUpdatedAt sets the "updated_at" field.
func (nc *NotifyCreate) SetUpdatedAt(t time.Time) *NotifyCreate {
	nc.mutation.SetUpdatedAt(t)
	return nc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (nc *NotifyCreate) SetNillableUpdatedAt(t *time.Time) *NotifyCreate {
	if t != nil {
		nc.SetUpdatedAt(*t)
	}
	return nc
}

// SetTenantID sets the "tenant_id" field.
func (nc *NotifyCreate) SetTenantID(i int) *NotifyCreate {
	nc.mutation.SetTenantID(i)
	return nc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (nc *NotifyCreate) SetNillableTenantID(i *int) *NotifyCreate {
	if i != nil {
		nc.SetTenantID(*i)
	}
	return nc
}

// SetRemark sets the "remark" field.
func (nc *NotifyCreate) SetRemark(s string) *NotifyCreate {
	nc.mutation.SetRemark(s)
	return nc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (nc *NotifyCreate) SetNillableRemark(s *string) *NotifyCreate {
	if s != nil {
		nc.SetRemark(*s)
	}
	return nc
}

// SetName sets the "name" field.
func (nc *NotifyCreate) SetName(s string) *NotifyCreate {
	nc.mutation.SetName(s)
	return nc
}

// SetPopo sets the "popo" field.
func (nc *NotifyCreate) SetPopo(b bool) *NotifyCreate {
	nc.mutation.SetPopo(b)
	return nc
}

// SetEmail sets the "email" field.
func (nc *NotifyCreate) SetEmail(b bool) *NotifyCreate {
	nc.mutation.SetEmail(b)
	return nc
}

// SetSms sets the "sms" field.
func (nc *NotifyCreate) SetSms(b bool) *NotifyCreate {
	nc.mutation.SetSms(b)
	return nc
}

// SetPhone sets the "phone" field.
func (nc *NotifyCreate) SetPhone(b bool) *NotifyCreate {
	nc.mutation.SetPhone(b)
	return nc
}

// SetPopoGroups sets the "popo_groups" field.
func (nc *NotifyCreate) SetPopoGroups(s *[]string) *NotifyCreate {
	nc.mutation.SetPopoGroups(s)
	return nc
}

// SetEmails sets the "emails" field.
func (nc *NotifyCreate) SetEmails(s *[]string) *NotifyCreate {
	nc.mutation.SetEmails(s)
	return nc
}

// SetPhones sets the "phones" field.
func (nc *NotifyCreate) SetPhones(s *[]string) *NotifyCreate {
	nc.mutation.SetPhones(s)
	return nc
}

// SetIPWhitelists sets the "ip_whitelists" field.
func (nc *NotifyCreate) SetIPWhitelists(s *[]string) *NotifyCreate {
	nc.mutation.SetIPWhitelists(s)
	return nc
}

// SetSystem sets the "system" field.
func (nc *NotifyCreate) SetSystem(b bool) *NotifyCreate {
	nc.mutation.SetSystem(b)
	return nc
}

// SetEnabled sets the "enabled" field.
func (nc *NotifyCreate) SetEnabled(b bool) *NotifyCreate {
	nc.mutation.SetEnabled(b)
	return nc
}

// SetSaNotifyPopo sets the "sa_notify_popo" field.
func (nc *NotifyCreate) SetSaNotifyPopo(b bool) *NotifyCreate {
	nc.mutation.SetSaNotifyPopo(b)
	return nc
}

// SetSaNotifyEmail sets the "sa_notify_email" field.
func (nc *NotifyCreate) SetSaNotifyEmail(b bool) *NotifyCreate {
	nc.mutation.SetSaNotifyEmail(b)
	return nc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (nc *NotifyCreate) SetTenant(t *Tenant) *NotifyCreate {
	return nc.SetTenantID(t.ID)
}

// Mutation returns the NotifyMutation object of the builder.
func (nc *NotifyCreate) Mutation() *NotifyMutation {
	return nc.mutation
}

// Save creates the Notify in the database.
func (nc *NotifyCreate) Save(ctx context.Context) (*Notify, error) {
	if err := nc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, nc.sqlSave, nc.mutation, nc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (nc *NotifyCreate) SaveX(ctx context.Context) *Notify {
	v, err := nc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (nc *NotifyCreate) Exec(ctx context.Context) error {
	_, err := nc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (nc *NotifyCreate) ExecX(ctx context.Context) {
	if err := nc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (nc *NotifyCreate) defaults() error {
	if _, ok := nc.mutation.CreatedAt(); !ok {
		if notify.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized notify.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := notify.DefaultCreatedAt()
		nc.mutation.SetCreatedAt(v)
	}
	if _, ok := nc.mutation.UpdatedAt(); !ok {
		if notify.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized notify.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := notify.DefaultUpdatedAt()
		nc.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (nc *NotifyCreate) check() error {
	if _, ok := nc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Notify.created_at"`)}
	}
	if _, ok := nc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Notify.updated_at"`)}
	}
	if v, ok := nc.mutation.Remark(); ok {
		if err := notify.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Notify.remark": %w`, err)}
		}
	}
	if _, ok := nc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Notify.name"`)}
	}
	if _, ok := nc.mutation.Popo(); !ok {
		return &ValidationError{Name: "popo", err: errors.New(`ent: missing required field "Notify.popo"`)}
	}
	if _, ok := nc.mutation.Email(); !ok {
		return &ValidationError{Name: "email", err: errors.New(`ent: missing required field "Notify.email"`)}
	}
	if _, ok := nc.mutation.Sms(); !ok {
		return &ValidationError{Name: "sms", err: errors.New(`ent: missing required field "Notify.sms"`)}
	}
	if _, ok := nc.mutation.Phone(); !ok {
		return &ValidationError{Name: "phone", err: errors.New(`ent: missing required field "Notify.phone"`)}
	}
	if _, ok := nc.mutation.PopoGroups(); !ok {
		return &ValidationError{Name: "popo_groups", err: errors.New(`ent: missing required field "Notify.popo_groups"`)}
	}
	if _, ok := nc.mutation.Emails(); !ok {
		return &ValidationError{Name: "emails", err: errors.New(`ent: missing required field "Notify.emails"`)}
	}
	if _, ok := nc.mutation.Phones(); !ok {
		return &ValidationError{Name: "phones", err: errors.New(`ent: missing required field "Notify.phones"`)}
	}
	if _, ok := nc.mutation.IPWhitelists(); !ok {
		return &ValidationError{Name: "ip_whitelists", err: errors.New(`ent: missing required field "Notify.ip_whitelists"`)}
	}
	if _, ok := nc.mutation.System(); !ok {
		return &ValidationError{Name: "system", err: errors.New(`ent: missing required field "Notify.system"`)}
	}
	if _, ok := nc.mutation.Enabled(); !ok {
		return &ValidationError{Name: "enabled", err: errors.New(`ent: missing required field "Notify.enabled"`)}
	}
	if _, ok := nc.mutation.SaNotifyPopo(); !ok {
		return &ValidationError{Name: "sa_notify_popo", err: errors.New(`ent: missing required field "Notify.sa_notify_popo"`)}
	}
	if _, ok := nc.mutation.SaNotifyEmail(); !ok {
		return &ValidationError{Name: "sa_notify_email", err: errors.New(`ent: missing required field "Notify.sa_notify_email"`)}
	}
	return nil
}

func (nc *NotifyCreate) sqlSave(ctx context.Context) (*Notify, error) {
	if err := nc.check(); err != nil {
		return nil, err
	}
	_node, _spec := nc.createSpec()
	if err := sqlgraph.CreateNode(ctx, nc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	nc.mutation.id = &_node.ID
	nc.mutation.done = true
	return _node, nil
}

func (nc *NotifyCreate) createSpec() (*Notify, *sqlgraph.CreateSpec) {
	var (
		_node = &Notify{config: nc.config}
		_spec = sqlgraph.NewCreateSpec(notify.Table, sqlgraph.NewFieldSpec(notify.FieldID, field.TypeInt))
	)
	if value, ok := nc.mutation.CreatedAt(); ok {
		_spec.SetField(notify.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := nc.mutation.UpdatedAt(); ok {
		_spec.SetField(notify.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := nc.mutation.Remark(); ok {
		_spec.SetField(notify.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := nc.mutation.Name(); ok {
		_spec.SetField(notify.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := nc.mutation.Popo(); ok {
		_spec.SetField(notify.FieldPopo, field.TypeBool, value)
		_node.Popo = value
	}
	if value, ok := nc.mutation.Email(); ok {
		_spec.SetField(notify.FieldEmail, field.TypeBool, value)
		_node.Email = value
	}
	if value, ok := nc.mutation.Sms(); ok {
		_spec.SetField(notify.FieldSms, field.TypeBool, value)
		_node.Sms = value
	}
	if value, ok := nc.mutation.Phone(); ok {
		_spec.SetField(notify.FieldPhone, field.TypeBool, value)
		_node.Phone = value
	}
	if value, ok := nc.mutation.PopoGroups(); ok {
		_spec.SetField(notify.FieldPopoGroups, field.TypeJSON, value)
		_node.PopoGroups = value
	}
	if value, ok := nc.mutation.Emails(); ok {
		_spec.SetField(notify.FieldEmails, field.TypeJSON, value)
		_node.Emails = value
	}
	if value, ok := nc.mutation.Phones(); ok {
		_spec.SetField(notify.FieldPhones, field.TypeJSON, value)
		_node.Phones = value
	}
	if value, ok := nc.mutation.IPWhitelists(); ok {
		_spec.SetField(notify.FieldIPWhitelists, field.TypeJSON, value)
		_node.IPWhitelists = value
	}
	if value, ok := nc.mutation.System(); ok {
		_spec.SetField(notify.FieldSystem, field.TypeBool, value)
		_node.System = value
	}
	if value, ok := nc.mutation.Enabled(); ok {
		_spec.SetField(notify.FieldEnabled, field.TypeBool, value)
		_node.Enabled = value
	}
	if value, ok := nc.mutation.SaNotifyPopo(); ok {
		_spec.SetField(notify.FieldSaNotifyPopo, field.TypeBool, value)
		_node.SaNotifyPopo = value
	}
	if value, ok := nc.mutation.SaNotifyEmail(); ok {
		_spec.SetField(notify.FieldSaNotifyEmail, field.TypeBool, value)
		_node.SaNotifyEmail = value
	}
	if nodes := nc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   notify.TenantTable,
			Columns: []string{notify.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// NotifyCreateBulk is the builder for creating many Notify entities in bulk.
type NotifyCreateBulk struct {
	config
	err      error
	builders []*NotifyCreate
}

// Save creates the Notify entities in the database.
func (ncb *NotifyCreateBulk) Save(ctx context.Context) ([]*Notify, error) {
	if ncb.err != nil {
		return nil, ncb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ncb.builders))
	nodes := make([]*Notify, len(ncb.builders))
	mutators := make([]Mutator, len(ncb.builders))
	for i := range ncb.builders {
		func(i int, root context.Context) {
			builder := ncb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*NotifyMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ncb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ncb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ncb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ncb *NotifyCreateBulk) SaveX(ctx context.Context) []*Notify {
	v, err := ncb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ncb *NotifyCreateBulk) Exec(ctx context.Context) error {
	_, err := ncb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ncb *NotifyCreateBulk) ExecX(ctx context.Context) {
	if err := ncb.Exec(ctx); err != nil {
		panic(err)
	}
}
