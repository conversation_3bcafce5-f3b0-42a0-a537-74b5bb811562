// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/systemconfig"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SystemConfigUpdate is the builder for updating SystemConfig entities.
type SystemConfigUpdate struct {
	config
	hooks    []Hook
	mutation *SystemConfigMutation
}

// Where appends a list predicates to the SystemConfigUpdate builder.
func (scu *SystemConfigUpdate) Where(ps ...predicate.SystemConfig) *SystemConfigUpdate {
	scu.mutation.Where(ps...)
	return scu
}

// SetUpdatedAt sets the "updated_at" field.
func (scu *SystemConfigUpdate) SetUpdatedAt(t time.Time) *SystemConfigUpdate {
	scu.mutation.SetUpdatedAt(t)
	return scu
}

// SetRemark sets the "remark" field.
func (scu *SystemConfigUpdate) SetRemark(s string) *SystemConfigUpdate {
	scu.mutation.SetRemark(s)
	return scu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (scu *SystemConfigUpdate) SetNillableRemark(s *string) *SystemConfigUpdate {
	if s != nil {
		scu.SetRemark(*s)
	}
	return scu
}

// ClearRemark clears the value of the "remark" field.
func (scu *SystemConfigUpdate) ClearRemark() *SystemConfigUpdate {
	scu.mutation.ClearRemark()
	return scu
}

// SetWofangTestIP sets the "wofang_test_ip" field.
func (scu *SystemConfigUpdate) SetWofangTestIP(s string) *SystemConfigUpdate {
	scu.mutation.SetWofangTestIP(s)
	return scu
}

// SetNillableWofangTestIP sets the "wofang_test_ip" field if the given value is not nil.
func (scu *SystemConfigUpdate) SetNillableWofangTestIP(s *string) *SystemConfigUpdate {
	if s != nil {
		scu.SetWofangTestIP(*s)
	}
	return scu
}

// SetNotifyPhones sets the "notify_phones" field.
func (scu *SystemConfigUpdate) SetNotifyPhones(s *[]string) *SystemConfigUpdate {
	scu.mutation.SetNotifyPhones(s)
	return scu
}

// SetNotifyEmails sets the "notify_emails" field.
func (scu *SystemConfigUpdate) SetNotifyEmails(s *[]string) *SystemConfigUpdate {
	scu.mutation.SetNotifyEmails(s)
	return scu
}

// SetNotifyScenes sets the "notify_scenes" field.
func (scu *SystemConfigUpdate) SetNotifyScenes(s *[]string) *SystemConfigUpdate {
	scu.mutation.SetNotifyScenes(s)
	return scu
}

// SetIPWhitelists sets the "ip_whitelists" field.
func (scu *SystemConfigUpdate) SetIPWhitelists(s *[]string) *SystemConfigUpdate {
	scu.mutation.SetIPWhitelists(s)
	return scu
}

// Mutation returns the SystemConfigMutation object of the builder.
func (scu *SystemConfigUpdate) Mutation() *SystemConfigMutation {
	return scu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (scu *SystemConfigUpdate) Save(ctx context.Context) (int, error) {
	if err := scu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, scu.sqlSave, scu.mutation, scu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (scu *SystemConfigUpdate) SaveX(ctx context.Context) int {
	affected, err := scu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (scu *SystemConfigUpdate) Exec(ctx context.Context) error {
	_, err := scu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scu *SystemConfigUpdate) ExecX(ctx context.Context) {
	if err := scu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (scu *SystemConfigUpdate) defaults() error {
	if _, ok := scu.mutation.UpdatedAt(); !ok {
		if systemconfig.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized systemconfig.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := systemconfig.UpdateDefaultUpdatedAt()
		scu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (scu *SystemConfigUpdate) check() error {
	if v, ok := scu.mutation.Remark(); ok {
		if err := systemconfig.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SystemConfig.remark": %w`, err)}
		}
	}
	return nil
}

func (scu *SystemConfigUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := scu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(systemconfig.Table, systemconfig.Columns, sqlgraph.NewFieldSpec(systemconfig.FieldID, field.TypeInt))
	if ps := scu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := scu.mutation.UpdatedAt(); ok {
		_spec.SetField(systemconfig.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := scu.mutation.Remark(); ok {
		_spec.SetField(systemconfig.FieldRemark, field.TypeString, value)
	}
	if scu.mutation.RemarkCleared() {
		_spec.ClearField(systemconfig.FieldRemark, field.TypeString)
	}
	if value, ok := scu.mutation.WofangTestIP(); ok {
		_spec.SetField(systemconfig.FieldWofangTestIP, field.TypeString, value)
	}
	if value, ok := scu.mutation.NotifyPhones(); ok {
		_spec.SetField(systemconfig.FieldNotifyPhones, field.TypeJSON, value)
	}
	if value, ok := scu.mutation.NotifyEmails(); ok {
		_spec.SetField(systemconfig.FieldNotifyEmails, field.TypeJSON, value)
	}
	if value, ok := scu.mutation.NotifyScenes(); ok {
		_spec.SetField(systemconfig.FieldNotifyScenes, field.TypeJSON, value)
	}
	if value, ok := scu.mutation.IPWhitelists(); ok {
		_spec.SetField(systemconfig.FieldIPWhitelists, field.TypeJSON, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, scu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{systemconfig.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	scu.mutation.done = true
	return n, nil
}

// SystemConfigUpdateOne is the builder for updating a single SystemConfig entity.
type SystemConfigUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SystemConfigMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (scuo *SystemConfigUpdateOne) SetUpdatedAt(t time.Time) *SystemConfigUpdateOne {
	scuo.mutation.SetUpdatedAt(t)
	return scuo
}

// SetRemark sets the "remark" field.
func (scuo *SystemConfigUpdateOne) SetRemark(s string) *SystemConfigUpdateOne {
	scuo.mutation.SetRemark(s)
	return scuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (scuo *SystemConfigUpdateOne) SetNillableRemark(s *string) *SystemConfigUpdateOne {
	if s != nil {
		scuo.SetRemark(*s)
	}
	return scuo
}

// ClearRemark clears the value of the "remark" field.
func (scuo *SystemConfigUpdateOne) ClearRemark() *SystemConfigUpdateOne {
	scuo.mutation.ClearRemark()
	return scuo
}

// SetWofangTestIP sets the "wofang_test_ip" field.
func (scuo *SystemConfigUpdateOne) SetWofangTestIP(s string) *SystemConfigUpdateOne {
	scuo.mutation.SetWofangTestIP(s)
	return scuo
}

// SetNillableWofangTestIP sets the "wofang_test_ip" field if the given value is not nil.
func (scuo *SystemConfigUpdateOne) SetNillableWofangTestIP(s *string) *SystemConfigUpdateOne {
	if s != nil {
		scuo.SetWofangTestIP(*s)
	}
	return scuo
}

// SetNotifyPhones sets the "notify_phones" field.
func (scuo *SystemConfigUpdateOne) SetNotifyPhones(s *[]string) *SystemConfigUpdateOne {
	scuo.mutation.SetNotifyPhones(s)
	return scuo
}

// SetNotifyEmails sets the "notify_emails" field.
func (scuo *SystemConfigUpdateOne) SetNotifyEmails(s *[]string) *SystemConfigUpdateOne {
	scuo.mutation.SetNotifyEmails(s)
	return scuo
}

// SetNotifyScenes sets the "notify_scenes" field.
func (scuo *SystemConfigUpdateOne) SetNotifyScenes(s *[]string) *SystemConfigUpdateOne {
	scuo.mutation.SetNotifyScenes(s)
	return scuo
}

// SetIPWhitelists sets the "ip_whitelists" field.
func (scuo *SystemConfigUpdateOne) SetIPWhitelists(s *[]string) *SystemConfigUpdateOne {
	scuo.mutation.SetIPWhitelists(s)
	return scuo
}

// Mutation returns the SystemConfigMutation object of the builder.
func (scuo *SystemConfigUpdateOne) Mutation() *SystemConfigMutation {
	return scuo.mutation
}

// Where appends a list predicates to the SystemConfigUpdate builder.
func (scuo *SystemConfigUpdateOne) Where(ps ...predicate.SystemConfig) *SystemConfigUpdateOne {
	scuo.mutation.Where(ps...)
	return scuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (scuo *SystemConfigUpdateOne) Select(field string, fields ...string) *SystemConfigUpdateOne {
	scuo.fields = append([]string{field}, fields...)
	return scuo
}

// Save executes the query and returns the updated SystemConfig entity.
func (scuo *SystemConfigUpdateOne) Save(ctx context.Context) (*SystemConfig, error) {
	if err := scuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, scuo.sqlSave, scuo.mutation, scuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (scuo *SystemConfigUpdateOne) SaveX(ctx context.Context) *SystemConfig {
	node, err := scuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (scuo *SystemConfigUpdateOne) Exec(ctx context.Context) error {
	_, err := scuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scuo *SystemConfigUpdateOne) ExecX(ctx context.Context) {
	if err := scuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (scuo *SystemConfigUpdateOne) defaults() error {
	if _, ok := scuo.mutation.UpdatedAt(); !ok {
		if systemconfig.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized systemconfig.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := systemconfig.UpdateDefaultUpdatedAt()
		scuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (scuo *SystemConfigUpdateOne) check() error {
	if v, ok := scuo.mutation.Remark(); ok {
		if err := systemconfig.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SystemConfig.remark": %w`, err)}
		}
	}
	return nil
}

func (scuo *SystemConfigUpdateOne) sqlSave(ctx context.Context) (_node *SystemConfig, err error) {
	if err := scuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(systemconfig.Table, systemconfig.Columns, sqlgraph.NewFieldSpec(systemconfig.FieldID, field.TypeInt))
	id, ok := scuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SystemConfig.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := scuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, systemconfig.FieldID)
		for _, f := range fields {
			if !systemconfig.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != systemconfig.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := scuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := scuo.mutation.UpdatedAt(); ok {
		_spec.SetField(systemconfig.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := scuo.mutation.Remark(); ok {
		_spec.SetField(systemconfig.FieldRemark, field.TypeString, value)
	}
	if scuo.mutation.RemarkCleared() {
		_spec.ClearField(systemconfig.FieldRemark, field.TypeString)
	}
	if value, ok := scuo.mutation.WofangTestIP(); ok {
		_spec.SetField(systemconfig.FieldWofangTestIP, field.TypeString, value)
	}
	if value, ok := scuo.mutation.NotifyPhones(); ok {
		_spec.SetField(systemconfig.FieldNotifyPhones, field.TypeJSON, value)
	}
	if value, ok := scuo.mutation.NotifyEmails(); ok {
		_spec.SetField(systemconfig.FieldNotifyEmails, field.TypeJSON, value)
	}
	if value, ok := scuo.mutation.NotifyScenes(); ok {
		_spec.SetField(systemconfig.FieldNotifyScenes, field.TypeJSON, value)
	}
	if value, ok := scuo.mutation.IPWhitelists(); ok {
		_spec.SetField(systemconfig.FieldIPWhitelists, field.TypeJSON, value)
	}
	_node = &SystemConfig{config: scuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, scuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{systemconfig.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	scuo.mutation.done = true
	return _node, nil
}
