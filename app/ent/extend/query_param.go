// Code generated by meta-generator, DO NOT EDIT.
package extend

import "time"

// 时间参数结构体
type TimeParam struct {
	StartTimeGte time.Time `query:"start_time_gte"`
	StartTimeLte time.Time `query:"start_time_lte"`

	EndTimeGte time.Time `query:"end_time_gte"`
	EndTimeLte time.Time `query:"end_time_lte"`

	OpTimeGte time.Time `query:"op_time_gte"`
	OpTimeLte time.Time `query:"op_time_lte"`

	CreatedAtGte time.Time `query:"created_at_gte"`
	CreatedAtLte time.Time `query:"created_at_lte"`

	TimeGte time.Time `query:"time_gte"`
	TimeLte time.Time `query:"time_lte"`

	UpdatedAtGte time.Time `query:"updated_at_gte"`
	UpdatedAtLte time.Time `query:"updated_at_lte"`
}
