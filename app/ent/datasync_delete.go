// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/datasync"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// DataSyncDelete is the builder for deleting a DataSync entity.
type DataSyncDelete struct {
	config
	hooks    []Hook
	mutation *DataSyncMutation
}

// Where appends a list predicates to the DataSyncDelete builder.
func (dsd *DataSyncDelete) Where(ps ...predicate.DataSync) *DataSyncDelete {
	dsd.mutation.Where(ps...)
	return dsd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (dsd *DataSyncDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, dsd.sqlExec, dsd.mutation, dsd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (dsd *DataSyncDelete) ExecX(ctx context.Context) int {
	n, err := dsd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (dsd *DataSyncDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(datasync.Table, sqlgraph.NewFieldSpec(datasync.FieldID, field.TypeInt))
	if ps := dsd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, dsd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	dsd.mutation.done = true
	return affected, err
}

// DataSyncDeleteOne is the builder for deleting a single DataSync entity.
type DataSyncDeleteOne struct {
	dsd *DataSyncDelete
}

// Where appends a list predicates to the DataSyncDelete builder.
func (dsdo *DataSyncDeleteOne) Where(ps ...predicate.DataSync) *DataSyncDeleteOne {
	dsdo.dsd.mutation.Where(ps...)
	return dsdo
}

// Exec executes the deletion query.
func (dsdo *DataSyncDeleteOne) Exec(ctx context.Context) error {
	n, err := dsdo.dsd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{datasync.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (dsdo *DataSyncDeleteOne) ExecX(ctx context.Context) {
	if err := dsdo.Exec(ctx); err != nil {
		panic(err)
	}
}
