// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SpectrumAlertDelete is the builder for deleting a SpectrumAlert entity.
type SpectrumAlertDelete struct {
	config
	hooks    []Hook
	mutation *SpectrumAlertMutation
}

// Where appends a list predicates to the SpectrumAlertDelete builder.
func (sad *SpectrumAlertDelete) Where(ps ...predicate.SpectrumAlert) *SpectrumAlertDelete {
	sad.mutation.Where(ps...)
	return sad
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (sad *SpectrumAlertDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, sad.sqlExec, sad.mutation, sad.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (sad *SpectrumAlertDelete) ExecX(ctx context.Context) int {
	n, err := sad.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (sad *SpectrumAlertDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(spectrumalert.Table, sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt))
	if ps := sad.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, sad.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	sad.mutation.done = true
	return affected, err
}

// SpectrumAlertDeleteOne is the builder for deleting a single SpectrumAlert entity.
type SpectrumAlertDeleteOne struct {
	sad *SpectrumAlertDelete
}

// Where appends a list predicates to the SpectrumAlertDelete builder.
func (sado *SpectrumAlertDeleteOne) Where(ps ...predicate.SpectrumAlert) *SpectrumAlertDeleteOne {
	sado.sad.mutation.Where(ps...)
	return sado
}

// Exec executes the deletion query.
func (sado *SpectrumAlertDeleteOne) Exec(ctx context.Context) error {
	n, err := sado.sad.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{spectrumalert.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (sado *SpectrumAlertDeleteOne) ExecX(ctx context.Context) {
	if err := sado.Exec(ctx); err != nil {
		panic(err)
	}
}
