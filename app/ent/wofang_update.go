// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/ent/wofang"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WofangUpdate is the builder for updating Wofang entities.
type WofangUpdate struct {
	config
	hooks    []Hook
	mutation *WofangMutation
}

// Where appends a list predicates to the WofangUpdate builder.
func (wu *WofangUpdate) Where(ps ...predicate.Wofang) *WofangUpdate {
	wu.mutation.Where(ps...)
	return wu
}

// SetUpdatedAt sets the "updated_at" field.
func (wu *WofangUpdate) SetUpdatedAt(t time.Time) *WofangUpdate {
	wu.mutation.SetUpdatedAt(t)
	return wu
}

// SetTenantID sets the "tenant_id" field.
func (wu *WofangUpdate) SetTenantID(i int) *WofangUpdate {
	wu.mutation.SetTenantID(i)
	return wu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableTenantID(i *int) *WofangUpdate {
	if i != nil {
		wu.SetTenantID(*i)
	}
	return wu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (wu *WofangUpdate) ClearTenantID() *WofangUpdate {
	wu.mutation.ClearTenantID()
	return wu
}

// SetRemark sets the "remark" field.
func (wu *WofangUpdate) SetRemark(s string) *WofangUpdate {
	wu.mutation.SetRemark(s)
	return wu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableRemark(s *string) *WofangUpdate {
	if s != nil {
		wu.SetRemark(*s)
	}
	return wu
}

// ClearRemark clears the value of the "remark" field.
func (wu *WofangUpdate) ClearRemark() *WofangUpdate {
	wu.mutation.ClearRemark()
	return wu
}

// SetName sets the "name" field.
func (wu *WofangUpdate) SetName(s string) *WofangUpdate {
	wu.mutation.SetName(s)
	return wu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableName(s *string) *WofangUpdate {
	if s != nil {
		wu.SetName(*s)
	}
	return wu
}

// SetIP sets the "ip" field.
func (wu *WofangUpdate) SetIP(s string) *WofangUpdate {
	wu.mutation.SetIP(s)
	return wu
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableIP(s *string) *WofangUpdate {
	if s != nil {
		wu.SetIP(*s)
	}
	return wu
}

// SetType sets the "type" field.
func (wu *WofangUpdate) SetType(s string) *WofangUpdate {
	wu.mutation.SetType(s)
	return wu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableType(s *string) *WofangUpdate {
	if s != nil {
		wu.SetType(*s)
	}
	return wu
}

// SetUnDragSecond sets the "un_drag_second" field.
func (wu *WofangUpdate) SetUnDragSecond(i int) *WofangUpdate {
	wu.mutation.ResetUnDragSecond()
	wu.mutation.SetUnDragSecond(i)
	return wu
}

// SetNillableUnDragSecond sets the "un_drag_second" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableUnDragSecond(i *int) *WofangUpdate {
	if i != nil {
		wu.SetUnDragSecond(*i)
	}
	return wu
}

// AddUnDragSecond adds i to the "un_drag_second" field.
func (wu *WofangUpdate) AddUnDragSecond(i int) *WofangUpdate {
	wu.mutation.AddUnDragSecond(i)
	return wu
}

// SetStartTime sets the "start_time" field.
func (wu *WofangUpdate) SetStartTime(t time.Time) *WofangUpdate {
	wu.mutation.SetStartTime(t)
	return wu
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableStartTime(t *time.Time) *WofangUpdate {
	if t != nil {
		wu.SetStartTime(*t)
	}
	return wu
}

// SetErrorInfo sets the "error_info" field.
func (wu *WofangUpdate) SetErrorInfo(s string) *WofangUpdate {
	wu.mutation.SetErrorInfo(s)
	return wu
}

// SetNillableErrorInfo sets the "error_info" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableErrorInfo(s *string) *WofangUpdate {
	if s != nil {
		wu.SetErrorInfo(*s)
	}
	return wu
}

// SetStatus sets the "status" field.
func (wu *WofangUpdate) SetStatus(s string) *WofangUpdate {
	wu.mutation.SetStatus(s)
	return wu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableStatus(s *string) *WofangUpdate {
	if s != nil {
		wu.SetStatus(*s)
	}
	return wu
}

// SetCreateUserID sets the "create_user_id" field.
func (wu *WofangUpdate) SetCreateUserID(i int) *WofangUpdate {
	wu.mutation.SetCreateUserID(i)
	return wu
}

// SetNillableCreateUserID sets the "create_user_id" field if the given value is not nil.
func (wu *WofangUpdate) SetNillableCreateUserID(i *int) *WofangUpdate {
	if i != nil {
		wu.SetCreateUserID(*i)
	}
	return wu
}

// ClearCreateUserID clears the value of the "create_user_id" field.
func (wu *WofangUpdate) ClearCreateUserID() *WofangUpdate {
	wu.mutation.ClearCreateUserID()
	return wu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (wu *WofangUpdate) SetTenant(t *Tenant) *WofangUpdate {
	return wu.SetTenantID(t.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (wu *WofangUpdate) SetUserID(id int) *WofangUpdate {
	wu.mutation.SetUserID(id)
	return wu
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (wu *WofangUpdate) SetNillableUserID(id *int) *WofangUpdate {
	if id != nil {
		wu = wu.SetUserID(*id)
	}
	return wu
}

// SetUser sets the "user" edge to the User entity.
func (wu *WofangUpdate) SetUser(u *User) *WofangUpdate {
	return wu.SetUserID(u.ID)
}

// AddSpectrumAlertIDs adds the "spectrum_alerts" edge to the SpectrumAlert entity by IDs.
func (wu *WofangUpdate) AddSpectrumAlertIDs(ids ...int) *WofangUpdate {
	wu.mutation.AddSpectrumAlertIDs(ids...)
	return wu
}

// AddSpectrumAlerts adds the "spectrum_alerts" edges to the SpectrumAlert entity.
func (wu *WofangUpdate) AddSpectrumAlerts(s ...*SpectrumAlert) *WofangUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return wu.AddSpectrumAlertIDs(ids...)
}

// AddMatrixSpectrumAlertIDs adds the "matrix_spectrum_alerts" edge to the MatrixSpectrumAlert entity by IDs.
func (wu *WofangUpdate) AddMatrixSpectrumAlertIDs(ids ...int) *WofangUpdate {
	wu.mutation.AddMatrixSpectrumAlertIDs(ids...)
	return wu
}

// AddMatrixSpectrumAlerts adds the "matrix_spectrum_alerts" edges to the MatrixSpectrumAlert entity.
func (wu *WofangUpdate) AddMatrixSpectrumAlerts(m ...*MatrixSpectrumAlert) *WofangUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return wu.AddMatrixSpectrumAlertIDs(ids...)
}

// Mutation returns the WofangMutation object of the builder.
func (wu *WofangUpdate) Mutation() *WofangMutation {
	return wu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (wu *WofangUpdate) ClearTenant() *WofangUpdate {
	wu.mutation.ClearTenant()
	return wu
}

// ClearUser clears the "user" edge to the User entity.
func (wu *WofangUpdate) ClearUser() *WofangUpdate {
	wu.mutation.ClearUser()
	return wu
}

// ClearSpectrumAlerts clears all "spectrum_alerts" edges to the SpectrumAlert entity.
func (wu *WofangUpdate) ClearSpectrumAlerts() *WofangUpdate {
	wu.mutation.ClearSpectrumAlerts()
	return wu
}

// RemoveSpectrumAlertIDs removes the "spectrum_alerts" edge to SpectrumAlert entities by IDs.
func (wu *WofangUpdate) RemoveSpectrumAlertIDs(ids ...int) *WofangUpdate {
	wu.mutation.RemoveSpectrumAlertIDs(ids...)
	return wu
}

// RemoveSpectrumAlerts removes "spectrum_alerts" edges to SpectrumAlert entities.
func (wu *WofangUpdate) RemoveSpectrumAlerts(s ...*SpectrumAlert) *WofangUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return wu.RemoveSpectrumAlertIDs(ids...)
}

// ClearMatrixSpectrumAlerts clears all "matrix_spectrum_alerts" edges to the MatrixSpectrumAlert entity.
func (wu *WofangUpdate) ClearMatrixSpectrumAlerts() *WofangUpdate {
	wu.mutation.ClearMatrixSpectrumAlerts()
	return wu
}

// RemoveMatrixSpectrumAlertIDs removes the "matrix_spectrum_alerts" edge to MatrixSpectrumAlert entities by IDs.
func (wu *WofangUpdate) RemoveMatrixSpectrumAlertIDs(ids ...int) *WofangUpdate {
	wu.mutation.RemoveMatrixSpectrumAlertIDs(ids...)
	return wu
}

// RemoveMatrixSpectrumAlerts removes "matrix_spectrum_alerts" edges to MatrixSpectrumAlert entities.
func (wu *WofangUpdate) RemoveMatrixSpectrumAlerts(m ...*MatrixSpectrumAlert) *WofangUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return wu.RemoveMatrixSpectrumAlertIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (wu *WofangUpdate) Save(ctx context.Context) (int, error) {
	if err := wu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, wu.sqlSave, wu.mutation, wu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wu *WofangUpdate) SaveX(ctx context.Context) int {
	affected, err := wu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (wu *WofangUpdate) Exec(ctx context.Context) error {
	_, err := wu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wu *WofangUpdate) ExecX(ctx context.Context) {
	if err := wu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wu *WofangUpdate) defaults() error {
	if _, ok := wu.mutation.UpdatedAt(); !ok {
		if wofang.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized wofang.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := wofang.UpdateDefaultUpdatedAt()
		wu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (wu *WofangUpdate) check() error {
	if v, ok := wu.mutation.Remark(); ok {
		if err := wofang.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Wofang.remark": %w`, err)}
		}
	}
	if v, ok := wu.mutation.ErrorInfo(); ok {
		if err := wofang.ErrorInfoValidator(v); err != nil {
			return &ValidationError{Name: "error_info", err: fmt.Errorf(`ent: validator failed for field "Wofang.error_info": %w`, err)}
		}
	}
	return nil
}

func (wu *WofangUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := wu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(wofang.Table, wofang.Columns, sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt))
	if ps := wu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wu.mutation.UpdatedAt(); ok {
		_spec.SetField(wofang.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wu.mutation.Remark(); ok {
		_spec.SetField(wofang.FieldRemark, field.TypeString, value)
	}
	if wu.mutation.RemarkCleared() {
		_spec.ClearField(wofang.FieldRemark, field.TypeString)
	}
	if value, ok := wu.mutation.Name(); ok {
		_spec.SetField(wofang.FieldName, field.TypeString, value)
	}
	if value, ok := wu.mutation.IP(); ok {
		_spec.SetField(wofang.FieldIP, field.TypeString, value)
	}
	if value, ok := wu.mutation.GetType(); ok {
		_spec.SetField(wofang.FieldType, field.TypeString, value)
	}
	if value, ok := wu.mutation.UnDragSecond(); ok {
		_spec.SetField(wofang.FieldUnDragSecond, field.TypeInt, value)
	}
	if value, ok := wu.mutation.AddedUnDragSecond(); ok {
		_spec.AddField(wofang.FieldUnDragSecond, field.TypeInt, value)
	}
	if value, ok := wu.mutation.StartTime(); ok {
		_spec.SetField(wofang.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := wu.mutation.ErrorInfo(); ok {
		_spec.SetField(wofang.FieldErrorInfo, field.TypeString, value)
	}
	if value, ok := wu.mutation.Status(); ok {
		_spec.SetField(wofang.FieldStatus, field.TypeString, value)
	}
	if wu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.TenantTable,
			Columns: []string{wofang.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.TenantTable,
			Columns: []string{wofang.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.UserTable,
			Columns: []string{wofang.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.UserTable,
			Columns: []string{wofang.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wu.mutation.SpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.SpectrumAlertsTable,
			Columns: []string{wofang.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.RemovedSpectrumAlertsIDs(); len(nodes) > 0 && !wu.mutation.SpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.SpectrumAlertsTable,
			Columns: []string{wofang.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.SpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.SpectrumAlertsTable,
			Columns: []string{wofang.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wu.mutation.MatrixSpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.MatrixSpectrumAlertsTable,
			Columns: []string{wofang.MatrixSpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.RemovedMatrixSpectrumAlertsIDs(); len(nodes) > 0 && !wu.mutation.MatrixSpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.MatrixSpectrumAlertsTable,
			Columns: []string{wofang.MatrixSpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wu.mutation.MatrixSpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.MatrixSpectrumAlertsTable,
			Columns: []string{wofang.MatrixSpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, wu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wofang.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	wu.mutation.done = true
	return n, nil
}

// WofangUpdateOne is the builder for updating a single Wofang entity.
type WofangUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *WofangMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (wuo *WofangUpdateOne) SetUpdatedAt(t time.Time) *WofangUpdateOne {
	wuo.mutation.SetUpdatedAt(t)
	return wuo
}

// SetTenantID sets the "tenant_id" field.
func (wuo *WofangUpdateOne) SetTenantID(i int) *WofangUpdateOne {
	wuo.mutation.SetTenantID(i)
	return wuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableTenantID(i *int) *WofangUpdateOne {
	if i != nil {
		wuo.SetTenantID(*i)
	}
	return wuo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (wuo *WofangUpdateOne) ClearTenantID() *WofangUpdateOne {
	wuo.mutation.ClearTenantID()
	return wuo
}

// SetRemark sets the "remark" field.
func (wuo *WofangUpdateOne) SetRemark(s string) *WofangUpdateOne {
	wuo.mutation.SetRemark(s)
	return wuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableRemark(s *string) *WofangUpdateOne {
	if s != nil {
		wuo.SetRemark(*s)
	}
	return wuo
}

// ClearRemark clears the value of the "remark" field.
func (wuo *WofangUpdateOne) ClearRemark() *WofangUpdateOne {
	wuo.mutation.ClearRemark()
	return wuo
}

// SetName sets the "name" field.
func (wuo *WofangUpdateOne) SetName(s string) *WofangUpdateOne {
	wuo.mutation.SetName(s)
	return wuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableName(s *string) *WofangUpdateOne {
	if s != nil {
		wuo.SetName(*s)
	}
	return wuo
}

// SetIP sets the "ip" field.
func (wuo *WofangUpdateOne) SetIP(s string) *WofangUpdateOne {
	wuo.mutation.SetIP(s)
	return wuo
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableIP(s *string) *WofangUpdateOne {
	if s != nil {
		wuo.SetIP(*s)
	}
	return wuo
}

// SetType sets the "type" field.
func (wuo *WofangUpdateOne) SetType(s string) *WofangUpdateOne {
	wuo.mutation.SetType(s)
	return wuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableType(s *string) *WofangUpdateOne {
	if s != nil {
		wuo.SetType(*s)
	}
	return wuo
}

// SetUnDragSecond sets the "un_drag_second" field.
func (wuo *WofangUpdateOne) SetUnDragSecond(i int) *WofangUpdateOne {
	wuo.mutation.ResetUnDragSecond()
	wuo.mutation.SetUnDragSecond(i)
	return wuo
}

// SetNillableUnDragSecond sets the "un_drag_second" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableUnDragSecond(i *int) *WofangUpdateOne {
	if i != nil {
		wuo.SetUnDragSecond(*i)
	}
	return wuo
}

// AddUnDragSecond adds i to the "un_drag_second" field.
func (wuo *WofangUpdateOne) AddUnDragSecond(i int) *WofangUpdateOne {
	wuo.mutation.AddUnDragSecond(i)
	return wuo
}

// SetStartTime sets the "start_time" field.
func (wuo *WofangUpdateOne) SetStartTime(t time.Time) *WofangUpdateOne {
	wuo.mutation.SetStartTime(t)
	return wuo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableStartTime(t *time.Time) *WofangUpdateOne {
	if t != nil {
		wuo.SetStartTime(*t)
	}
	return wuo
}

// SetErrorInfo sets the "error_info" field.
func (wuo *WofangUpdateOne) SetErrorInfo(s string) *WofangUpdateOne {
	wuo.mutation.SetErrorInfo(s)
	return wuo
}

// SetNillableErrorInfo sets the "error_info" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableErrorInfo(s *string) *WofangUpdateOne {
	if s != nil {
		wuo.SetErrorInfo(*s)
	}
	return wuo
}

// SetStatus sets the "status" field.
func (wuo *WofangUpdateOne) SetStatus(s string) *WofangUpdateOne {
	wuo.mutation.SetStatus(s)
	return wuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableStatus(s *string) *WofangUpdateOne {
	if s != nil {
		wuo.SetStatus(*s)
	}
	return wuo
}

// SetCreateUserID sets the "create_user_id" field.
func (wuo *WofangUpdateOne) SetCreateUserID(i int) *WofangUpdateOne {
	wuo.mutation.SetCreateUserID(i)
	return wuo
}

// SetNillableCreateUserID sets the "create_user_id" field if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableCreateUserID(i *int) *WofangUpdateOne {
	if i != nil {
		wuo.SetCreateUserID(*i)
	}
	return wuo
}

// ClearCreateUserID clears the value of the "create_user_id" field.
func (wuo *WofangUpdateOne) ClearCreateUserID() *WofangUpdateOne {
	wuo.mutation.ClearCreateUserID()
	return wuo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (wuo *WofangUpdateOne) SetTenant(t *Tenant) *WofangUpdateOne {
	return wuo.SetTenantID(t.ID)
}

// SetUserID sets the "user" edge to the User entity by ID.
func (wuo *WofangUpdateOne) SetUserID(id int) *WofangUpdateOne {
	wuo.mutation.SetUserID(id)
	return wuo
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (wuo *WofangUpdateOne) SetNillableUserID(id *int) *WofangUpdateOne {
	if id != nil {
		wuo = wuo.SetUserID(*id)
	}
	return wuo
}

// SetUser sets the "user" edge to the User entity.
func (wuo *WofangUpdateOne) SetUser(u *User) *WofangUpdateOne {
	return wuo.SetUserID(u.ID)
}

// AddSpectrumAlertIDs adds the "spectrum_alerts" edge to the SpectrumAlert entity by IDs.
func (wuo *WofangUpdateOne) AddSpectrumAlertIDs(ids ...int) *WofangUpdateOne {
	wuo.mutation.AddSpectrumAlertIDs(ids...)
	return wuo
}

// AddSpectrumAlerts adds the "spectrum_alerts" edges to the SpectrumAlert entity.
func (wuo *WofangUpdateOne) AddSpectrumAlerts(s ...*SpectrumAlert) *WofangUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return wuo.AddSpectrumAlertIDs(ids...)
}

// AddMatrixSpectrumAlertIDs adds the "matrix_spectrum_alerts" edge to the MatrixSpectrumAlert entity by IDs.
func (wuo *WofangUpdateOne) AddMatrixSpectrumAlertIDs(ids ...int) *WofangUpdateOne {
	wuo.mutation.AddMatrixSpectrumAlertIDs(ids...)
	return wuo
}

// AddMatrixSpectrumAlerts adds the "matrix_spectrum_alerts" edges to the MatrixSpectrumAlert entity.
func (wuo *WofangUpdateOne) AddMatrixSpectrumAlerts(m ...*MatrixSpectrumAlert) *WofangUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return wuo.AddMatrixSpectrumAlertIDs(ids...)
}

// Mutation returns the WofangMutation object of the builder.
func (wuo *WofangUpdateOne) Mutation() *WofangMutation {
	return wuo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (wuo *WofangUpdateOne) ClearTenant() *WofangUpdateOne {
	wuo.mutation.ClearTenant()
	return wuo
}

// ClearUser clears the "user" edge to the User entity.
func (wuo *WofangUpdateOne) ClearUser() *WofangUpdateOne {
	wuo.mutation.ClearUser()
	return wuo
}

// ClearSpectrumAlerts clears all "spectrum_alerts" edges to the SpectrumAlert entity.
func (wuo *WofangUpdateOne) ClearSpectrumAlerts() *WofangUpdateOne {
	wuo.mutation.ClearSpectrumAlerts()
	return wuo
}

// RemoveSpectrumAlertIDs removes the "spectrum_alerts" edge to SpectrumAlert entities by IDs.
func (wuo *WofangUpdateOne) RemoveSpectrumAlertIDs(ids ...int) *WofangUpdateOne {
	wuo.mutation.RemoveSpectrumAlertIDs(ids...)
	return wuo
}

// RemoveSpectrumAlerts removes "spectrum_alerts" edges to SpectrumAlert entities.
func (wuo *WofangUpdateOne) RemoveSpectrumAlerts(s ...*SpectrumAlert) *WofangUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return wuo.RemoveSpectrumAlertIDs(ids...)
}

// ClearMatrixSpectrumAlerts clears all "matrix_spectrum_alerts" edges to the MatrixSpectrumAlert entity.
func (wuo *WofangUpdateOne) ClearMatrixSpectrumAlerts() *WofangUpdateOne {
	wuo.mutation.ClearMatrixSpectrumAlerts()
	return wuo
}

// RemoveMatrixSpectrumAlertIDs removes the "matrix_spectrum_alerts" edge to MatrixSpectrumAlert entities by IDs.
func (wuo *WofangUpdateOne) RemoveMatrixSpectrumAlertIDs(ids ...int) *WofangUpdateOne {
	wuo.mutation.RemoveMatrixSpectrumAlertIDs(ids...)
	return wuo
}

// RemoveMatrixSpectrumAlerts removes "matrix_spectrum_alerts" edges to MatrixSpectrumAlert entities.
func (wuo *WofangUpdateOne) RemoveMatrixSpectrumAlerts(m ...*MatrixSpectrumAlert) *WofangUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return wuo.RemoveMatrixSpectrumAlertIDs(ids...)
}

// Where appends a list predicates to the WofangUpdate builder.
func (wuo *WofangUpdateOne) Where(ps ...predicate.Wofang) *WofangUpdateOne {
	wuo.mutation.Where(ps...)
	return wuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (wuo *WofangUpdateOne) Select(field string, fields ...string) *WofangUpdateOne {
	wuo.fields = append([]string{field}, fields...)
	return wuo
}

// Save executes the query and returns the updated Wofang entity.
func (wuo *WofangUpdateOne) Save(ctx context.Context) (*Wofang, error) {
	if err := wuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, wuo.sqlSave, wuo.mutation, wuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (wuo *WofangUpdateOne) SaveX(ctx context.Context) *Wofang {
	node, err := wuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (wuo *WofangUpdateOne) Exec(ctx context.Context) error {
	_, err := wuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (wuo *WofangUpdateOne) ExecX(ctx context.Context) {
	if err := wuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (wuo *WofangUpdateOne) defaults() error {
	if _, ok := wuo.mutation.UpdatedAt(); !ok {
		if wofang.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized wofang.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := wofang.UpdateDefaultUpdatedAt()
		wuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (wuo *WofangUpdateOne) check() error {
	if v, ok := wuo.mutation.Remark(); ok {
		if err := wofang.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "Wofang.remark": %w`, err)}
		}
	}
	if v, ok := wuo.mutation.ErrorInfo(); ok {
		if err := wofang.ErrorInfoValidator(v); err != nil {
			return &ValidationError{Name: "error_info", err: fmt.Errorf(`ent: validator failed for field "Wofang.error_info": %w`, err)}
		}
	}
	return nil
}

func (wuo *WofangUpdateOne) sqlSave(ctx context.Context) (_node *Wofang, err error) {
	if err := wuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(wofang.Table, wofang.Columns, sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt))
	id, ok := wuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Wofang.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := wuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wofang.FieldID)
		for _, f := range fields {
			if !wofang.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != wofang.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := wuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := wuo.mutation.UpdatedAt(); ok {
		_spec.SetField(wofang.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := wuo.mutation.Remark(); ok {
		_spec.SetField(wofang.FieldRemark, field.TypeString, value)
	}
	if wuo.mutation.RemarkCleared() {
		_spec.ClearField(wofang.FieldRemark, field.TypeString)
	}
	if value, ok := wuo.mutation.Name(); ok {
		_spec.SetField(wofang.FieldName, field.TypeString, value)
	}
	if value, ok := wuo.mutation.IP(); ok {
		_spec.SetField(wofang.FieldIP, field.TypeString, value)
	}
	if value, ok := wuo.mutation.GetType(); ok {
		_spec.SetField(wofang.FieldType, field.TypeString, value)
	}
	if value, ok := wuo.mutation.UnDragSecond(); ok {
		_spec.SetField(wofang.FieldUnDragSecond, field.TypeInt, value)
	}
	if value, ok := wuo.mutation.AddedUnDragSecond(); ok {
		_spec.AddField(wofang.FieldUnDragSecond, field.TypeInt, value)
	}
	if value, ok := wuo.mutation.StartTime(); ok {
		_spec.SetField(wofang.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := wuo.mutation.ErrorInfo(); ok {
		_spec.SetField(wofang.FieldErrorInfo, field.TypeString, value)
	}
	if value, ok := wuo.mutation.Status(); ok {
		_spec.SetField(wofang.FieldStatus, field.TypeString, value)
	}
	if wuo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.TenantTable,
			Columns: []string{wofang.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.TenantTable,
			Columns: []string{wofang.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.UserTable,
			Columns: []string{wofang.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   wofang.UserTable,
			Columns: []string{wofang.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wuo.mutation.SpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.SpectrumAlertsTable,
			Columns: []string{wofang.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.RemovedSpectrumAlertsIDs(); len(nodes) > 0 && !wuo.mutation.SpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.SpectrumAlertsTable,
			Columns: []string{wofang.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.SpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.SpectrumAlertsTable,
			Columns: []string{wofang.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if wuo.mutation.MatrixSpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.MatrixSpectrumAlertsTable,
			Columns: []string{wofang.MatrixSpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.RemovedMatrixSpectrumAlertsIDs(); len(nodes) > 0 && !wuo.mutation.MatrixSpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.MatrixSpectrumAlertsTable,
			Columns: []string{wofang.MatrixSpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := wuo.mutation.MatrixSpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   wofang.MatrixSpectrumAlertsTable,
			Columns: []string{wofang.MatrixSpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Wofang{config: wuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, wuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{wofang.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	wuo.mutation.done = true
	return _node, nil
}
