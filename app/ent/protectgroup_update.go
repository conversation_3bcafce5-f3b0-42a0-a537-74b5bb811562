// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/protectgroup"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"
	"meta/app/entity/netease"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ProtectGroupUpdate is the builder for updating ProtectGroup entities.
type ProtectGroupUpdate struct {
	config
	hooks    []Hook
	mutation *ProtectGroupMutation
}

// Where appends a list predicates to the ProtectGroupUpdate builder.
func (pgu *ProtectGroupUpdate) Where(ps ...predicate.ProtectGroup) *ProtectGroupUpdate {
	pgu.mutation.Where(ps...)
	return pgu
}

// SetTenantID sets the "tenant_id" field.
func (pgu *ProtectGroupUpdate) SetTenantID(i int) *ProtectGroupUpdate {
	pgu.mutation.SetTenantID(i)
	return pgu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (pgu *ProtectGroupUpdate) SetNillableTenantID(i *int) *ProtectGroupUpdate {
	if i != nil {
		pgu.SetTenantID(*i)
	}
	return pgu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (pgu *ProtectGroupUpdate) ClearTenantID() *ProtectGroupUpdate {
	pgu.mutation.ClearTenantID()
	return pgu
}

// SetUpdatedAt sets the "updated_at" field.
func (pgu *ProtectGroupUpdate) SetUpdatedAt(t time.Time) *ProtectGroupUpdate {
	pgu.mutation.SetUpdatedAt(t)
	return pgu
}

// SetRemark sets the "remark" field.
func (pgu *ProtectGroupUpdate) SetRemark(s string) *ProtectGroupUpdate {
	pgu.mutation.SetRemark(s)
	return pgu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (pgu *ProtectGroupUpdate) SetNillableRemark(s *string) *ProtectGroupUpdate {
	if s != nil {
		pgu.SetRemark(*s)
	}
	return pgu
}

// ClearRemark clears the value of the "remark" field.
func (pgu *ProtectGroupUpdate) ClearRemark() *ProtectGroupUpdate {
	pgu.mutation.ClearRemark()
	return pgu
}

// SetGroupName sets the "group_name" field.
func (pgu *ProtectGroupUpdate) SetGroupName(s string) *ProtectGroupUpdate {
	pgu.mutation.SetGroupName(s)
	return pgu
}

// SetNillableGroupName sets the "group_name" field if the given value is not nil.
func (pgu *ProtectGroupUpdate) SetNillableGroupName(s *string) *ProtectGroupUpdate {
	if s != nil {
		pgu.SetGroupName(*s)
	}
	return pgu
}

// SetGroupID sets the "group_id" field.
func (pgu *ProtectGroupUpdate) SetGroupID(i int64) *ProtectGroupUpdate {
	pgu.mutation.ResetGroupID()
	pgu.mutation.SetGroupID(i)
	return pgu
}

// SetNillableGroupID sets the "group_id" field if the given value is not nil.
func (pgu *ProtectGroupUpdate) SetNillableGroupID(i *int64) *ProtectGroupUpdate {
	if i != nil {
		pgu.SetGroupID(*i)
	}
	return pgu
}

// AddGroupID adds i to the "group_id" field.
func (pgu *ProtectGroupUpdate) AddGroupID(i int64) *ProtectGroupUpdate {
	pgu.mutation.AddGroupID(i)
	return pgu
}

// SetType sets the "type" field.
func (pgu *ProtectGroupUpdate) SetType(i int) *ProtectGroupUpdate {
	pgu.mutation.ResetType()
	pgu.mutation.SetType(i)
	return pgu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (pgu *ProtectGroupUpdate) SetNillableType(i *int) *ProtectGroupUpdate {
	if i != nil {
		pgu.SetType(*i)
	}
	return pgu
}

// AddType adds i to the "type" field.
func (pgu *ProtectGroupUpdate) AddType(i int) *ProtectGroupUpdate {
	pgu.mutation.AddType(i)
	return pgu
}

// SetIPList sets the "ip_list" field.
func (pgu *ProtectGroupUpdate) SetIPList(s *[]string) *ProtectGroupUpdate {
	pgu.mutation.SetIPList(s)
	return pgu
}

// ClearIPList clears the value of the "ip_list" field.
func (pgu *ProtectGroupUpdate) ClearIPList() *ProtectGroupUpdate {
	pgu.mutation.ClearIPList()
	return pgu
}

// SetExpandIP sets the "expand_ip" field.
func (pgu *ProtectGroupUpdate) SetExpandIP(s string) *ProtectGroupUpdate {
	pgu.mutation.SetExpandIP(s)
	return pgu
}

// SetNillableExpandIP sets the "expand_ip" field if the given value is not nil.
func (pgu *ProtectGroupUpdate) SetNillableExpandIP(s *string) *ProtectGroupUpdate {
	if s != nil {
		pgu.SetExpandIP(*s)
	}
	return pgu
}

// ClearExpandIP clears the value of the "expand_ip" field.
func (pgu *ProtectGroupUpdate) ClearExpandIP() *ProtectGroupUpdate {
	pgu.mutation.ClearExpandIP()
	return pgu
}

// SetMonitorInfo sets the "monitor_info" field.
func (pgu *ProtectGroupUpdate) SetMonitorInfo(ni *netease.MonitorInfo) *ProtectGroupUpdate {
	pgu.mutation.SetMonitorInfo(ni)
	return pgu
}

// ClearMonitorInfo clears the value of the "monitor_info" field.
func (pgu *ProtectGroupUpdate) ClearMonitorInfo() *ProtectGroupUpdate {
	pgu.mutation.ClearMonitorInfo()
	return pgu
}

// SetDragInfo sets the "drag_info" field.
func (pgu *ProtectGroupUpdate) SetDragInfo(ni *netease.DragInfo) *ProtectGroupUpdate {
	pgu.mutation.SetDragInfo(ni)
	return pgu
}

// ClearDragInfo clears the value of the "drag_info" field.
func (pgu *ProtectGroupUpdate) ClearDragInfo() *ProtectGroupUpdate {
	pgu.mutation.ClearDragInfo()
	return pgu
}

// SetNds4Config sets the "nds4_config" field.
func (pgu *ProtectGroupUpdate) SetNds4Config(n *netease.Nds4Config) *ProtectGroupUpdate {
	pgu.mutation.SetNds4Config(n)
	return pgu
}

// ClearNds4Config clears the value of the "nds4_config" field.
func (pgu *ProtectGroupUpdate) ClearNds4Config() *ProtectGroupUpdate {
	pgu.mutation.ClearNds4Config()
	return pgu
}

// SetNds6Config sets the "nds6_config" field.
func (pgu *ProtectGroupUpdate) SetNds6Config(n *netease.Nds6Config) *ProtectGroupUpdate {
	pgu.mutation.SetNds6Config(n)
	return pgu
}

// ClearNds6Config clears the value of the "nds6_config" field.
func (pgu *ProtectGroupUpdate) ClearNds6Config() *ProtectGroupUpdate {
	pgu.mutation.ClearNds6Config()
	return pgu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (pgu *ProtectGroupUpdate) SetTenant(t *Tenant) *ProtectGroupUpdate {
	return pgu.SetTenantID(t.ID)
}

// AddSpectrumAlertIDs adds the "spectrum_alerts" edge to the SpectrumAlert entity by IDs.
func (pgu *ProtectGroupUpdate) AddSpectrumAlertIDs(ids ...int) *ProtectGroupUpdate {
	pgu.mutation.AddSpectrumAlertIDs(ids...)
	return pgu
}

// AddSpectrumAlerts adds the "spectrum_alerts" edges to the SpectrumAlert entity.
func (pgu *ProtectGroupUpdate) AddSpectrumAlerts(s ...*SpectrumAlert) *ProtectGroupUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return pgu.AddSpectrumAlertIDs(ids...)
}

// Mutation returns the ProtectGroupMutation object of the builder.
func (pgu *ProtectGroupUpdate) Mutation() *ProtectGroupMutation {
	return pgu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (pgu *ProtectGroupUpdate) ClearTenant() *ProtectGroupUpdate {
	pgu.mutation.ClearTenant()
	return pgu
}

// ClearSpectrumAlerts clears all "spectrum_alerts" edges to the SpectrumAlert entity.
func (pgu *ProtectGroupUpdate) ClearSpectrumAlerts() *ProtectGroupUpdate {
	pgu.mutation.ClearSpectrumAlerts()
	return pgu
}

// RemoveSpectrumAlertIDs removes the "spectrum_alerts" edge to SpectrumAlert entities by IDs.
func (pgu *ProtectGroupUpdate) RemoveSpectrumAlertIDs(ids ...int) *ProtectGroupUpdate {
	pgu.mutation.RemoveSpectrumAlertIDs(ids...)
	return pgu
}

// RemoveSpectrumAlerts removes "spectrum_alerts" edges to SpectrumAlert entities.
func (pgu *ProtectGroupUpdate) RemoveSpectrumAlerts(s ...*SpectrumAlert) *ProtectGroupUpdate {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return pgu.RemoveSpectrumAlertIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (pgu *ProtectGroupUpdate) Save(ctx context.Context) (int, error) {
	if err := pgu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, pgu.sqlSave, pgu.mutation, pgu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pgu *ProtectGroupUpdate) SaveX(ctx context.Context) int {
	affected, err := pgu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (pgu *ProtectGroupUpdate) Exec(ctx context.Context) error {
	_, err := pgu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pgu *ProtectGroupUpdate) ExecX(ctx context.Context) {
	if err := pgu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pgu *ProtectGroupUpdate) defaults() error {
	if _, ok := pgu.mutation.UpdatedAt(); !ok {
		if protectgroup.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized protectgroup.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := protectgroup.UpdateDefaultUpdatedAt()
		pgu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (pgu *ProtectGroupUpdate) check() error {
	if v, ok := pgu.mutation.Remark(); ok {
		if err := protectgroup.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "ProtectGroup.remark": %w`, err)}
		}
	}
	if v, ok := pgu.mutation.ExpandIP(); ok {
		if err := protectgroup.ExpandIPValidator(v); err != nil {
			return &ValidationError{Name: "expand_ip", err: fmt.Errorf(`ent: validator failed for field "ProtectGroup.expand_ip": %w`, err)}
		}
	}
	return nil
}

func (pgu *ProtectGroupUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := pgu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(protectgroup.Table, protectgroup.Columns, sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt))
	if ps := pgu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pgu.mutation.UpdatedAt(); ok {
		_spec.SetField(protectgroup.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := pgu.mutation.Remark(); ok {
		_spec.SetField(protectgroup.FieldRemark, field.TypeString, value)
	}
	if pgu.mutation.RemarkCleared() {
		_spec.ClearField(protectgroup.FieldRemark, field.TypeString)
	}
	if value, ok := pgu.mutation.GroupName(); ok {
		_spec.SetField(protectgroup.FieldGroupName, field.TypeString, value)
	}
	if value, ok := pgu.mutation.GroupID(); ok {
		_spec.SetField(protectgroup.FieldGroupID, field.TypeInt64, value)
	}
	if value, ok := pgu.mutation.AddedGroupID(); ok {
		_spec.AddField(protectgroup.FieldGroupID, field.TypeInt64, value)
	}
	if value, ok := pgu.mutation.GetType(); ok {
		_spec.SetField(protectgroup.FieldType, field.TypeInt, value)
	}
	if value, ok := pgu.mutation.AddedType(); ok {
		_spec.AddField(protectgroup.FieldType, field.TypeInt, value)
	}
	if value, ok := pgu.mutation.IPList(); ok {
		_spec.SetField(protectgroup.FieldIPList, field.TypeJSON, value)
	}
	if pgu.mutation.IPListCleared() {
		_spec.ClearField(protectgroup.FieldIPList, field.TypeJSON)
	}
	if value, ok := pgu.mutation.ExpandIP(); ok {
		_spec.SetField(protectgroup.FieldExpandIP, field.TypeString, value)
	}
	if pgu.mutation.ExpandIPCleared() {
		_spec.ClearField(protectgroup.FieldExpandIP, field.TypeString)
	}
	if value, ok := pgu.mutation.MonitorInfo(); ok {
		_spec.SetField(protectgroup.FieldMonitorInfo, field.TypeJSON, value)
	}
	if pgu.mutation.MonitorInfoCleared() {
		_spec.ClearField(protectgroup.FieldMonitorInfo, field.TypeJSON)
	}
	if value, ok := pgu.mutation.DragInfo(); ok {
		_spec.SetField(protectgroup.FieldDragInfo, field.TypeJSON, value)
	}
	if pgu.mutation.DragInfoCleared() {
		_spec.ClearField(protectgroup.FieldDragInfo, field.TypeJSON)
	}
	if value, ok := pgu.mutation.Nds4Config(); ok {
		_spec.SetField(protectgroup.FieldNds4Config, field.TypeJSON, value)
	}
	if pgu.mutation.Nds4ConfigCleared() {
		_spec.ClearField(protectgroup.FieldNds4Config, field.TypeJSON)
	}
	if value, ok := pgu.mutation.Nds6Config(); ok {
		_spec.SetField(protectgroup.FieldNds6Config, field.TypeJSON, value)
	}
	if pgu.mutation.Nds6ConfigCleared() {
		_spec.ClearField(protectgroup.FieldNds6Config, field.TypeJSON)
	}
	if pgu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   protectgroup.TenantTable,
			Columns: []string{protectgroup.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pgu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   protectgroup.TenantTable,
			Columns: []string{protectgroup.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pgu.mutation.SpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   protectgroup.SpectrumAlertsTable,
			Columns: []string{protectgroup.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pgu.mutation.RemovedSpectrumAlertsIDs(); len(nodes) > 0 && !pgu.mutation.SpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   protectgroup.SpectrumAlertsTable,
			Columns: []string{protectgroup.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pgu.mutation.SpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   protectgroup.SpectrumAlertsTable,
			Columns: []string{protectgroup.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, pgu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{protectgroup.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	pgu.mutation.done = true
	return n, nil
}

// ProtectGroupUpdateOne is the builder for updating a single ProtectGroup entity.
type ProtectGroupUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ProtectGroupMutation
}

// SetTenantID sets the "tenant_id" field.
func (pguo *ProtectGroupUpdateOne) SetTenantID(i int) *ProtectGroupUpdateOne {
	pguo.mutation.SetTenantID(i)
	return pguo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (pguo *ProtectGroupUpdateOne) SetNillableTenantID(i *int) *ProtectGroupUpdateOne {
	if i != nil {
		pguo.SetTenantID(*i)
	}
	return pguo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (pguo *ProtectGroupUpdateOne) ClearTenantID() *ProtectGroupUpdateOne {
	pguo.mutation.ClearTenantID()
	return pguo
}

// SetUpdatedAt sets the "updated_at" field.
func (pguo *ProtectGroupUpdateOne) SetUpdatedAt(t time.Time) *ProtectGroupUpdateOne {
	pguo.mutation.SetUpdatedAt(t)
	return pguo
}

// SetRemark sets the "remark" field.
func (pguo *ProtectGroupUpdateOne) SetRemark(s string) *ProtectGroupUpdateOne {
	pguo.mutation.SetRemark(s)
	return pguo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (pguo *ProtectGroupUpdateOne) SetNillableRemark(s *string) *ProtectGroupUpdateOne {
	if s != nil {
		pguo.SetRemark(*s)
	}
	return pguo
}

// ClearRemark clears the value of the "remark" field.
func (pguo *ProtectGroupUpdateOne) ClearRemark() *ProtectGroupUpdateOne {
	pguo.mutation.ClearRemark()
	return pguo
}

// SetGroupName sets the "group_name" field.
func (pguo *ProtectGroupUpdateOne) SetGroupName(s string) *ProtectGroupUpdateOne {
	pguo.mutation.SetGroupName(s)
	return pguo
}

// SetNillableGroupName sets the "group_name" field if the given value is not nil.
func (pguo *ProtectGroupUpdateOne) SetNillableGroupName(s *string) *ProtectGroupUpdateOne {
	if s != nil {
		pguo.SetGroupName(*s)
	}
	return pguo
}

// SetGroupID sets the "group_id" field.
func (pguo *ProtectGroupUpdateOne) SetGroupID(i int64) *ProtectGroupUpdateOne {
	pguo.mutation.ResetGroupID()
	pguo.mutation.SetGroupID(i)
	return pguo
}

// SetNillableGroupID sets the "group_id" field if the given value is not nil.
func (pguo *ProtectGroupUpdateOne) SetNillableGroupID(i *int64) *ProtectGroupUpdateOne {
	if i != nil {
		pguo.SetGroupID(*i)
	}
	return pguo
}

// AddGroupID adds i to the "group_id" field.
func (pguo *ProtectGroupUpdateOne) AddGroupID(i int64) *ProtectGroupUpdateOne {
	pguo.mutation.AddGroupID(i)
	return pguo
}

// SetType sets the "type" field.
func (pguo *ProtectGroupUpdateOne) SetType(i int) *ProtectGroupUpdateOne {
	pguo.mutation.ResetType()
	pguo.mutation.SetType(i)
	return pguo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (pguo *ProtectGroupUpdateOne) SetNillableType(i *int) *ProtectGroupUpdateOne {
	if i != nil {
		pguo.SetType(*i)
	}
	return pguo
}

// AddType adds i to the "type" field.
func (pguo *ProtectGroupUpdateOne) AddType(i int) *ProtectGroupUpdateOne {
	pguo.mutation.AddType(i)
	return pguo
}

// SetIPList sets the "ip_list" field.
func (pguo *ProtectGroupUpdateOne) SetIPList(s *[]string) *ProtectGroupUpdateOne {
	pguo.mutation.SetIPList(s)
	return pguo
}

// ClearIPList clears the value of the "ip_list" field.
func (pguo *ProtectGroupUpdateOne) ClearIPList() *ProtectGroupUpdateOne {
	pguo.mutation.ClearIPList()
	return pguo
}

// SetExpandIP sets the "expand_ip" field.
func (pguo *ProtectGroupUpdateOne) SetExpandIP(s string) *ProtectGroupUpdateOne {
	pguo.mutation.SetExpandIP(s)
	return pguo
}

// SetNillableExpandIP sets the "expand_ip" field if the given value is not nil.
func (pguo *ProtectGroupUpdateOne) SetNillableExpandIP(s *string) *ProtectGroupUpdateOne {
	if s != nil {
		pguo.SetExpandIP(*s)
	}
	return pguo
}

// ClearExpandIP clears the value of the "expand_ip" field.
func (pguo *ProtectGroupUpdateOne) ClearExpandIP() *ProtectGroupUpdateOne {
	pguo.mutation.ClearExpandIP()
	return pguo
}

// SetMonitorInfo sets the "monitor_info" field.
func (pguo *ProtectGroupUpdateOne) SetMonitorInfo(ni *netease.MonitorInfo) *ProtectGroupUpdateOne {
	pguo.mutation.SetMonitorInfo(ni)
	return pguo
}

// ClearMonitorInfo clears the value of the "monitor_info" field.
func (pguo *ProtectGroupUpdateOne) ClearMonitorInfo() *ProtectGroupUpdateOne {
	pguo.mutation.ClearMonitorInfo()
	return pguo
}

// SetDragInfo sets the "drag_info" field.
func (pguo *ProtectGroupUpdateOne) SetDragInfo(ni *netease.DragInfo) *ProtectGroupUpdateOne {
	pguo.mutation.SetDragInfo(ni)
	return pguo
}

// ClearDragInfo clears the value of the "drag_info" field.
func (pguo *ProtectGroupUpdateOne) ClearDragInfo() *ProtectGroupUpdateOne {
	pguo.mutation.ClearDragInfo()
	return pguo
}

// SetNds4Config sets the "nds4_config" field.
func (pguo *ProtectGroupUpdateOne) SetNds4Config(n *netease.Nds4Config) *ProtectGroupUpdateOne {
	pguo.mutation.SetNds4Config(n)
	return pguo
}

// ClearNds4Config clears the value of the "nds4_config" field.
func (pguo *ProtectGroupUpdateOne) ClearNds4Config() *ProtectGroupUpdateOne {
	pguo.mutation.ClearNds4Config()
	return pguo
}

// SetNds6Config sets the "nds6_config" field.
func (pguo *ProtectGroupUpdateOne) SetNds6Config(n *netease.Nds6Config) *ProtectGroupUpdateOne {
	pguo.mutation.SetNds6Config(n)
	return pguo
}

// ClearNds6Config clears the value of the "nds6_config" field.
func (pguo *ProtectGroupUpdateOne) ClearNds6Config() *ProtectGroupUpdateOne {
	pguo.mutation.ClearNds6Config()
	return pguo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (pguo *ProtectGroupUpdateOne) SetTenant(t *Tenant) *ProtectGroupUpdateOne {
	return pguo.SetTenantID(t.ID)
}

// AddSpectrumAlertIDs adds the "spectrum_alerts" edge to the SpectrumAlert entity by IDs.
func (pguo *ProtectGroupUpdateOne) AddSpectrumAlertIDs(ids ...int) *ProtectGroupUpdateOne {
	pguo.mutation.AddSpectrumAlertIDs(ids...)
	return pguo
}

// AddSpectrumAlerts adds the "spectrum_alerts" edges to the SpectrumAlert entity.
func (pguo *ProtectGroupUpdateOne) AddSpectrumAlerts(s ...*SpectrumAlert) *ProtectGroupUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return pguo.AddSpectrumAlertIDs(ids...)
}

// Mutation returns the ProtectGroupMutation object of the builder.
func (pguo *ProtectGroupUpdateOne) Mutation() *ProtectGroupMutation {
	return pguo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (pguo *ProtectGroupUpdateOne) ClearTenant() *ProtectGroupUpdateOne {
	pguo.mutation.ClearTenant()
	return pguo
}

// ClearSpectrumAlerts clears all "spectrum_alerts" edges to the SpectrumAlert entity.
func (pguo *ProtectGroupUpdateOne) ClearSpectrumAlerts() *ProtectGroupUpdateOne {
	pguo.mutation.ClearSpectrumAlerts()
	return pguo
}

// RemoveSpectrumAlertIDs removes the "spectrum_alerts" edge to SpectrumAlert entities by IDs.
func (pguo *ProtectGroupUpdateOne) RemoveSpectrumAlertIDs(ids ...int) *ProtectGroupUpdateOne {
	pguo.mutation.RemoveSpectrumAlertIDs(ids...)
	return pguo
}

// RemoveSpectrumAlerts removes "spectrum_alerts" edges to SpectrumAlert entities.
func (pguo *ProtectGroupUpdateOne) RemoveSpectrumAlerts(s ...*SpectrumAlert) *ProtectGroupUpdateOne {
	ids := make([]int, len(s))
	for i := range s {
		ids[i] = s[i].ID
	}
	return pguo.RemoveSpectrumAlertIDs(ids...)
}

// Where appends a list predicates to the ProtectGroupUpdate builder.
func (pguo *ProtectGroupUpdateOne) Where(ps ...predicate.ProtectGroup) *ProtectGroupUpdateOne {
	pguo.mutation.Where(ps...)
	return pguo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (pguo *ProtectGroupUpdateOne) Select(field string, fields ...string) *ProtectGroupUpdateOne {
	pguo.fields = append([]string{field}, fields...)
	return pguo
}

// Save executes the query and returns the updated ProtectGroup entity.
func (pguo *ProtectGroupUpdateOne) Save(ctx context.Context) (*ProtectGroup, error) {
	if err := pguo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, pguo.sqlSave, pguo.mutation, pguo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (pguo *ProtectGroupUpdateOne) SaveX(ctx context.Context) *ProtectGroup {
	node, err := pguo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (pguo *ProtectGroupUpdateOne) Exec(ctx context.Context) error {
	_, err := pguo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pguo *ProtectGroupUpdateOne) ExecX(ctx context.Context) {
	if err := pguo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pguo *ProtectGroupUpdateOne) defaults() error {
	if _, ok := pguo.mutation.UpdatedAt(); !ok {
		if protectgroup.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized protectgroup.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := protectgroup.UpdateDefaultUpdatedAt()
		pguo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (pguo *ProtectGroupUpdateOne) check() error {
	if v, ok := pguo.mutation.Remark(); ok {
		if err := protectgroup.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "ProtectGroup.remark": %w`, err)}
		}
	}
	if v, ok := pguo.mutation.ExpandIP(); ok {
		if err := protectgroup.ExpandIPValidator(v); err != nil {
			return &ValidationError{Name: "expand_ip", err: fmt.Errorf(`ent: validator failed for field "ProtectGroup.expand_ip": %w`, err)}
		}
	}
	return nil
}

func (pguo *ProtectGroupUpdateOne) sqlSave(ctx context.Context) (_node *ProtectGroup, err error) {
	if err := pguo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(protectgroup.Table, protectgroup.Columns, sqlgraph.NewFieldSpec(protectgroup.FieldID, field.TypeInt))
	id, ok := pguo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "ProtectGroup.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := pguo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, protectgroup.FieldID)
		for _, f := range fields {
			if !protectgroup.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != protectgroup.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := pguo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := pguo.mutation.UpdatedAt(); ok {
		_spec.SetField(protectgroup.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := pguo.mutation.Remark(); ok {
		_spec.SetField(protectgroup.FieldRemark, field.TypeString, value)
	}
	if pguo.mutation.RemarkCleared() {
		_spec.ClearField(protectgroup.FieldRemark, field.TypeString)
	}
	if value, ok := pguo.mutation.GroupName(); ok {
		_spec.SetField(protectgroup.FieldGroupName, field.TypeString, value)
	}
	if value, ok := pguo.mutation.GroupID(); ok {
		_spec.SetField(protectgroup.FieldGroupID, field.TypeInt64, value)
	}
	if value, ok := pguo.mutation.AddedGroupID(); ok {
		_spec.AddField(protectgroup.FieldGroupID, field.TypeInt64, value)
	}
	if value, ok := pguo.mutation.GetType(); ok {
		_spec.SetField(protectgroup.FieldType, field.TypeInt, value)
	}
	if value, ok := pguo.mutation.AddedType(); ok {
		_spec.AddField(protectgroup.FieldType, field.TypeInt, value)
	}
	if value, ok := pguo.mutation.IPList(); ok {
		_spec.SetField(protectgroup.FieldIPList, field.TypeJSON, value)
	}
	if pguo.mutation.IPListCleared() {
		_spec.ClearField(protectgroup.FieldIPList, field.TypeJSON)
	}
	if value, ok := pguo.mutation.ExpandIP(); ok {
		_spec.SetField(protectgroup.FieldExpandIP, field.TypeString, value)
	}
	if pguo.mutation.ExpandIPCleared() {
		_spec.ClearField(protectgroup.FieldExpandIP, field.TypeString)
	}
	if value, ok := pguo.mutation.MonitorInfo(); ok {
		_spec.SetField(protectgroup.FieldMonitorInfo, field.TypeJSON, value)
	}
	if pguo.mutation.MonitorInfoCleared() {
		_spec.ClearField(protectgroup.FieldMonitorInfo, field.TypeJSON)
	}
	if value, ok := pguo.mutation.DragInfo(); ok {
		_spec.SetField(protectgroup.FieldDragInfo, field.TypeJSON, value)
	}
	if pguo.mutation.DragInfoCleared() {
		_spec.ClearField(protectgroup.FieldDragInfo, field.TypeJSON)
	}
	if value, ok := pguo.mutation.Nds4Config(); ok {
		_spec.SetField(protectgroup.FieldNds4Config, field.TypeJSON, value)
	}
	if pguo.mutation.Nds4ConfigCleared() {
		_spec.ClearField(protectgroup.FieldNds4Config, field.TypeJSON)
	}
	if value, ok := pguo.mutation.Nds6Config(); ok {
		_spec.SetField(protectgroup.FieldNds6Config, field.TypeJSON, value)
	}
	if pguo.mutation.Nds6ConfigCleared() {
		_spec.ClearField(protectgroup.FieldNds6Config, field.TypeJSON)
	}
	if pguo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   protectgroup.TenantTable,
			Columns: []string{protectgroup.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pguo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   protectgroup.TenantTable,
			Columns: []string{protectgroup.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if pguo.mutation.SpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   protectgroup.SpectrumAlertsTable,
			Columns: []string{protectgroup.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pguo.mutation.RemovedSpectrumAlertsIDs(); len(nodes) > 0 && !pguo.mutation.SpectrumAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   protectgroup.SpectrumAlertsTable,
			Columns: []string{protectgroup.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := pguo.mutation.SpectrumAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   protectgroup.SpectrumAlertsTable,
			Columns: []string{protectgroup.SpectrumAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &ProtectGroup{config: pguo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, pguo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{protectgroup.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	pguo.mutation.done = true
	return _node, nil
}
