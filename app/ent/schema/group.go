package schema

import (
	"entgo.io/ent"
	//"entgo.io/ent/examples/privacytenant/rule"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"meta/app/ent/privacy"
)

// Group holds the schema definition for the Group entity.
type Group struct {
	ent.Schema
}

// Mixin of the User schema.
func (Group) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
	}
}

// Fields of the User.
func (Group) Fields() []ent.Field {
	return []ent.Field{
		field.String("name"),
	}
}

// Edges of the Group.
func (Group) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("users", User.Type).
			Ref("groups"),
	}
}

// Policy defines the privacy policy of the Group.
func (Group) Policy() ent.Policy {
	return privacy.Policy{
		Mutation: privacy.MutationPolicy{
			// pageSize DenyMismatchedTenants only for
			// Create operations
			// rule.AllowIfAdmin(),
			//privacy.OnMutationOperation(
			//	rule.DenyMismatchedTenants(),
			//	ent.OpCreate,
			//),

			// rule.FilterTenantRule(),
			// rule.DenyMismatchedTenants(),
		},
	}
}
