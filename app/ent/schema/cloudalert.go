package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// CloudAlert holds the schema definition for the CloudAlert entity.
type CloudAlert struct {
	ent.Schema
}

// Mixin of the CloudAlert.
func (CloudAlert) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the CloudAlert.
func (CloudAlert) Fields() []ent.Field {
	return []ent.Field{
		field.String("src_ip").Comment("来源IP").StructTag(`query:"src_ip,omitempty"`),
		field.Int("src_port").Comment("来源端口").StructTag(`query:"src_port,omitempty"`),
		field.String("dst_ip").Comment("被攻击IP").StructTag(`query:"dst_ip,omitempty"`),
		field.Int("dst_port").Comment("被攻击端口").StructTag(`query:"dst_port,omitempty"`),
		field.Int("defence_mode").Comment("清洗模式"),
		field.Int("flow_mode").Comment("流匹配模式"),
		field.String("tcp_ack_num").Comment("TCP报文中的ACK值"),
		field.String("tcp_seq_num").Comment("TCP报文中的SEQ值"),
		field.Int("protocol").Comment("4层协议：6tcp，17udp"),
		field.Int("defence_level").Comment("防护水平"),
		field.Int64("max_pps").Comment("最大攻击PPS").StructTag(`query:"max_pps,omitempty"`),
		field.Int64("max_attack_pps").Comment("从攻击开始到攻击结束，最大的pps").StructTag(`query:"max_attack_pps,omitempty"`),
		field.Int("overlimit_pkt_count").Comment("超出阈值的报文总数").StructTag(`query:"overlimit_pkt_count,omitempty"`),
		field.Time("start_time").Comment("攻击开始时间"),
		field.Time("end_time").Comment("攻击结束时间").Optional(),
	}
}

// Edges of the CloudAlert.
func (CloudAlert) Edges() []ent.Edge {
	return []ent.Edge{
		// 一个告警有多个流超限数据
		edge.To("cloudflow_datas", CloudFlowData.Type),
	}
}
