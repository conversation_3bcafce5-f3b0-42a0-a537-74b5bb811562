package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// CleanData holds the schema definition for the CleanData entity.
type CleanData struct {
	ent.Schema
}

// Mixin of the CleanData.
func (CleanData) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		CreateTimeMixin{},
	}
}

// Fields of the CleanData.
func (CleanData) Fields() []ent.Field {
	return []ent.Field{
		field.Int("spectrum_alert_id").Optional().Nillable(),

		field.String("ip"),
		field.Time("time"),
		field.Int64("in_bps"),
		field.Int64("out_bps"),
		field.Int64("in_pps"),
		field.Int64("out_pps"),
		field.Int64("in_ack_pps"),
		field.Int64("out_ack_pps"),
		field.Int64("in_ack_bps"),
		field.Int64("out_ack_bps"),
		field.Int64("in_syn_pps"),
		field.Int64("out_syn_pps"),
		field.Int64("in_udp_pps"),
		field.Int64("out_udp_pps"),
		field.Int64("in_udp_bps"),
		field.Int64("out_udp_bps"),
		field.Int64("in_icmp_pps"),
		field.Int64("in_icmp_bps"),
		field.Int64("out_icmp_bps"),
		field.Int64("out_icmp_pps"),
		field.Int64("in_dns_pps"),
		field.Int64("out_dns_pps"),
		field.Int64("in_dns_bps"),
		field.Int64("out_dns_bps"),
		field.Int("c_filter_id"),
		field.Int("attack_flags"),
		field.Int("count"),
		field.Int("ip_type"),
		field.String("c_filter").Optional().Nillable(),
		field.String("host").Optional().Nillable(),
	}
}

// Edges of the CleanData.
func (CleanData) Edges() []ent.Edge {
	return []ent.Edge{
		//一个清洗数据属于一个分光告警
		edge.From("spectrum_alert", SpectrumAlert.Type).Ref("clean_datas").Field("spectrum_alert_id").Unique(),
	}
}
