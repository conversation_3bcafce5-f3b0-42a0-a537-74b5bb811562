package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// WofangAlert holds the schema definition for the WofangAlert entity.
type WofangAlert struct {
	ent.Schema
}

// Mixin of the WofangAlert.
func (WofangAlert) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the WofangAlert.
func (WofangAlert) Fields() []ent.Field {
	return []ent.Field{
		field.Int("attack_status").Comment("攻击状态，1：攻击结束，2：攻击中").StructTag(`query:"attack_status,omitempty"`),
		field.JSON("attack_type", &[]string{}).Optional().StructTag(`query:"attack_type,omitempty"`).Comment("攻击类型"),
		field.String("device_ip").StructTag(`query:"device_ip,omitempty"`),
		field.String("zone_ip").Comment("防护ip").StructTag(`query:"zone_ip,omitempty"`),
		field.Int("attack_id").StructTag(`query:"attack_id,omitempty"`),

		field.Time("start_time").Comment("攻击开始时间"),
		field.Time("end_time").Comment("攻击结束时间").Optional(),
		field.Int64("max_drop_bps").StructTag(`query:"max_drop_bps,omitempty"`),

		field.Int64("max_in_bps").StructTag(`query:"max_in_bps,omitempty"`),
	}
}

// Edges of the WofangAlert.
func (WofangAlert) Edges() []ent.Edge {
	return nil
}
