package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"math"
	"meta/app/entity/netease"
)

// ProtectGroup holds the schema definition for the ProtectGroup entity.
type ProtectGroup struct {
	ent.Schema
}

// Mixin of the ProtectGroup.
func (ProtectGroup) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the ProtectGroup.
func (ProtectGroup) Fields() []ent.Field {
	return []ent.Field{
		field.String("group_name").StructTag(`query:"group_name,omitempty"`),
		field.Int64("group_id"),
		field.Int("type"),
		field.JSON("ip_list", &[]string{}).Optional().StructTag(`query:"ip_list,omitempty"`),
		field.String("expand_ip").Optional().StructTag(`query:"expand_ip,omitempty"`).MaxLen(int(math.Pow(2, 32) - 1)),
		field.JSON("monitor_info", &netease.MonitorInfo{}).Optional(),
		field.JSON("drag_info", &netease.DragInfo{}).Optional(),
		field.JSON("nds4_config", &netease.Nds4Config{}).Optional(),
		field.JSON("nds6_config", &netease.Nds6Config{}).Optional(),
	}
}

// Edges of the ProtectGroup.
func (ProtectGroup) Edges() []ent.Edge {
	return []ent.Edge{
		// 一个防护群组有多个分光告警
		edge.To("spectrum_alerts", SpectrumAlert.Type),
	}
}
