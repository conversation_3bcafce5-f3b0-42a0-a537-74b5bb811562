package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// MatrixStrategy holds the schema definition for the MatrixStrategy entity.
type MatrixStrategy struct {
	ent.Schema
}

// Mixin of the MatrixStrategy schema.
func (MatrixStrategy) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the MatrixStrategy.
func (MatrixStrategy) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").NotEmpty(),
		field.String("region"),
		field.String("net_type"),
		field.String("isp"),
		field.Int64("monitor_bps"),
		field.Int64("drag_bps"),
		field.Int("drag_type"),
	}
}

// Edges of the MatrixStrategy.
func (MatrixStrategy) Edges() []ent.Edge {
	return []ent.Edge{
		// 一个策略有多个matrix告警
		edge.To("matrix_strategy_alerts", MatrixSpectrumAlert.Type),
	}
}
