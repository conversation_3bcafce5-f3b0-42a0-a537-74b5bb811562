package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// SystemConfig holds the schema definition for the SystemConfig entity.
type SystemConfig struct {
	ent.Schema
}

// Mixin of the SystemConfig.
func (SystemConfig) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the SystemConfig.
func (SystemConfig) Fields() []ent.Field {
	return []ent.Field{
		field.String("wofang_test_ip"),
		field.JSON("notify_phones", &[]string{}),
		field.JSON("notify_emails", &[]string{}),
		field.JSON("notify_scenes", &[]string{}),
		field.JSON("ip_whitelists", &[]string{}).Comment("通知IP白名单，IP在白名单中将不再通知"),
	}
}

// Edges of the SystemConfig.
func (SystemConfig) Edges() []ent.Edge {
	return nil
}
