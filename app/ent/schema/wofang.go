package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Wofang holds the schema definition for the Wofang entity.
type Wofang struct {
	ent.Schema
}

// Mixin of the Wofang.
func (Wofang) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TimeMixin{},
		TenantMixin{},
		RemarkMixin{},
	}
}

// Fields of the Wofang.
func (Wofang) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Comment("名称"),
		field.String("ip").Comment("IP"),
		field.String("type").Comment("类型"),
		field.Int("un_drag_second").Comment("自动解封时间，单位：秒"),
		field.Time("start_time").Default(time.Now).Comment("牵引清洗开始时间"),
		field.String("error_info").MaxLen(500).Comment("错误信息"),
		field.String("status").Comment("状态"),
		field.Int("create_user_id").Optional().Comment("创建用户Id，可选").Nillable(),
	}
}

// Edges of the Wofang.
func (Wofang) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("user", User.Type).
			Field("create_user_id").
			Unique(),
		// 一个工单有多个分光告警
		edge.To("spectrum_alerts", SpectrumAlert.Type),

		// 一个工单有多个matrix分光告警
		edge.To("matrix_spectrum_alerts", MatrixSpectrumAlert.Type),
	}
}
