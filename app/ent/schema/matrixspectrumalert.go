package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"meta/app/entity/netease"
)

// MatrixSpectrumAlert holds the schema definition for the MatrixSpectrumAlert entity.
type MatrixSpectrumAlert struct {
	ent.Schema
}

// Mixin of the MatrixSpectrumAlert.
func (MatrixSpectrumAlert) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the MatrixSpectrumAlert.
func (MatrixSpectrumAlert) Fields() []ent.Field {
	return []ent.Field{
		field.Int("wofang_id").Optional().Nillable(),
		field.Int("matrix_strategy_id").Optional().Nillable(),
		field.JSON("ip_list", &[]string{}).Optional().StructTag(`query:"ip_list,omitempty"`),
		field.String("region"),
		field.String("net_type").StructTag(`query:"net_type"`),
		field.String("isp"),
		//field.JSON("protect_status", &[]int{}).Optional().StructTag(`query:"protect_status,omitempty"`),
		field.Time("start_time").Comment("攻击开始时间"),
		field.Time("end_time").Comment("攻击结束时间").Optional(),
		field.String("attack_type").Comment("攻击类型"),
		field.Int64("bps").Comment("告警BPS"),
		field.JSON("attack_info", netease.MatrixAttackInfo{}).Optional(),
	}
}

// Edges of the MatrixSpectrumAlert.
func (MatrixSpectrumAlert) Edges() []ent.Edge {
	return []ent.Edge{
		// 一个告警有多个分光数据
		edge.To("matrix_spectrum_datas", MatrixSpectrumData.Type),
		// 一个告警属于一个防护策略
		edge.From("matrix_strategy", MatrixStrategy.Type).Ref("matrix_strategy_alerts").Field("matrix_strategy_id").Unique(),
		// 一个告警属于一个沃防工单
		edge.From("wofang_ticket", Wofang.Type).Ref("matrix_spectrum_alerts").Field("wofang_id").Unique(),
	}
}
