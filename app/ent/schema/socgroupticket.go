package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"meta/app/entity/netease/socgroup"
)

// SocGroupTicket holds the schema definition for the SocGroupTicket entity.
type SocGroupTicket struct {
	ent.Schema
}

// Mixin of the SocGroupTicket.
func (SocGroupTicket) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TimeMixin{},
		TenantMixin{},
		RemarkMixin{},
	}
}

// Fields of the SocGroupTicket.
func (SocGroupTicket) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Comment("名称"),
		field.String("type").Comment("类型"),
		field.String("description").MaxLen(1000).Comment("描述"),
		field.JSON("follow_list", &[]int{}).Optional().Comment("工单跟踪者id列表"),
		field.Int("department_id").Comment("部门id"),
		field.JSON("ip_list", &[]string{}).Optional().Comment("牵引IP列表"),
		field.Float32("min_bandwidth").Comment("牵引IP列表中最小的物理带宽或压测带宽(非业务流量)，保留两位小数，单位Gbps"),
		field.Int("divert_type").Comment("清洗方式, 1表示清洗上线，2表示清洗下线 3调优"),
		field.Int("op_type").Comment("操作方式，1表示自动，2表示手动"),
		field.Time("op_time").Comment("操作时间").Optional(),
		field.Int("config_type").Comment("参数配置类型，1表示默认，2表示自定义  仅[清洗上线] 有效"),

		field.String("config_args").MaxLen(1000).Comment("参数配置  仅[清洗上线和清洗调优且configType=2(自定义防护参数)] 有效"),
		field.String("product_name").Comment("产品中文名"),
		field.String("product_code").Comment("产品代号").Optional(),
		field.JSON("contact_list", &[]socgroup.User{}).Optional().Comment("紧急联系人列表"),

		field.Int("group_ticket_id").Comment("集团工单编号").Optional(),
		field.String("error_info").MaxLen(1000).Comment("错误信息").Optional(),
		field.Int("create_user_id").Optional().Comment("创建用户Id，可选").Nillable(),
	}
}

// Edges of the SocGroupTicket.
func (SocGroupTicket) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("user", User.Type).
			Field("create_user_id").
			Unique(),
	}
}
