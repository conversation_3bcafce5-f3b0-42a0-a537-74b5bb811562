package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// CloudAttackData holds the schema definition for the CloudAttackData entity.
type CloudAttackData struct {
	ent.Schema
}

// Mixin of the CloudAttackData.
func (CloudAttackData) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the CloudAttackData.
func (CloudAttackData) Fields() []ent.Field {
	return []ent.Field{
		field.String("src_ip").Comment("来源IP").StructTag(`query:"src_ip,omitempty"`),
		field.Int("src_port").Comment("来源端口").StructTag(`query:"src_port,omitempty"`),
		field.String("dst_ip").Comment("被攻击IP").StructTag(`query:"dst_ip,omitempty"`),
		field.Int("dst_port").Comment("被攻击端口").StructTag(`query:"dst_port,omitempty"`),
		field.Int("protocol").Comment("4层协议：6tcp，17udp"),
		field.Int64("current_attack_pps").Comment("这一秒pps").StructTag(`query:"current_attack_pps,omitempty"`),
		field.Time("start_time").Comment("攻击开始时间"),
		field.Time("end_time").Comment("结束时间").Optional(),
	}
}

// Edges of the CloudAttackData.
func (CloudAttackData) Edges() []ent.Edge {
	return nil
}
