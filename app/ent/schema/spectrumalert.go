package schema

import (
	"meta/app/entity/netease"

	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// SpectrumAlert holds the schema definition for the SpectrumAlert entity.
type SpectrumAlert struct {
	ent.Schema
}

// Mixin of the SpectrumAlert.
func (SpectrumAlert) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the SpectrumAlert.
func (SpectrumAlert) Fields() []ent.Field {
	return []ent.Field{
		field.Int("protect_group_id").Optional().Nillable(),
		field.Int("strategy_id").Optional().Nillable(),
		field.Int("wofang_id").Optional().Nillable(),
		field.JSON("protect_status", &[]int{}).Optional().StructTag(`query:"protect_status,omitempty"`),
		field.String("ip").Comment("被攻击IP"),
		field.Time("start_time").Comment("攻击开始时间"),
		field.Time("end_time").Comment("攻击结束时间").Optional(),
		field.String("attack_type").Comment("攻击类型").StructTag(`query:"attack_type"`),
		field.Int64("max_pps").Comment("告警PPS"),
		field.Int64("max_bps").Comment("告警BPS"),
		field.JSON("attack_info", netease.AttackInfo{}).Optional(),
		field.Int("isp_code"),
	}
}

// Edges of the SpectrumAlert.
func (SpectrumAlert) Edges() []ent.Edge {
	return []ent.Edge{
		// 一个告警有多个分光数据
		edge.To("spectrum_datas", SpectrumData.Type),
		// 一个告警有多个清洗数据
		edge.To("clean_datas", CleanData.Type),

		// 一个告警属于一个防护群组
		edge.From("protect_group", ProtectGroup.Type).Ref("spectrum_alerts").Field("protect_group_id").Unique(),
		// 一个告警属于一个防护策略
		edge.From("strategy", Strategy.Type).Ref("strategy_alerts").Field("strategy_id").Unique(),
		// 一个告警属于一个沃防工单
		edge.From("wofang_ticket", Wofang.Type).Ref("spectrum_alerts").Field("wofang_id").Unique(),
	}
}
