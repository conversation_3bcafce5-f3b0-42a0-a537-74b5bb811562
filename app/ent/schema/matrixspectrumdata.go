package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// MatrixSpectrumData holds the schema definition for the MatrixSpectrumData entity.
type MatrixSpectrumData struct {
	ent.Schema
}

// Mixin of the MatrixSpectrumData.
func (MatrixSpectrumData) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
	}
}

// Fields of the MatrixSpectrumData.
func (MatrixSpectrumData) Fields() []ent.Field {
	return []ent.Field{
		field.Int("matrix_spectrum_alert_id").Optional().Nillable(),
		field.String("region"),
		field.String("net_type").StructTag(`query:"net_type"`),
		field.String("isp"),
		field.Int64("bps"),
		//field.Int64("pps"),
		//field.Float32("ratio"),
		field.Time("time"),
	}
}

// Edges of the MatrixSpectrumData.
func (MatrixSpectrumData) Edges() []ent.Edge {
	return []ent.Edge{
		// 一个matrix分光数据属于一个分光告警
		edge.From("matrix_spectrum_alert", MatrixSpectrumAlert.Type).Ref("matrix_spectrum_datas").Field("matrix_spectrum_alert_id").Unique(),
	}
}
