package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"meta/app/entity/netease/gamecloud"
)

// SkylineDos holds the schema definition for the SkylineDos entity.
type SkylineDos struct {
	ent.Schema
}

// Mixin of the SkylineDos.
func (SkylineDos) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the SkylineDos.
func (SkylineDos) Fields() []ent.Field {
	return []ent.Field{
		field.Time("start_time").Comment("攻击开始时间"),
		field.Time("end_time").Comment("攻击结束时间").Optional(),
		field.String("region"),
		field.String("resource"),
		field.String("resource_type").StructTag(`query:"resource_type,omitempty"`),
		field.JSON("vector_types", &[]string{}).Optional().StructTag(`query:"vector_types,omitempty"`).Comment("攻击类型"),
		field.String("status"),
		field.String("attack_id"),
		field.JSON("attack_counters", &[]gamecloud.AttackCounter{}).Optional().StructTag(`query:"attack_counters,omitempty"`),
		field.String("project"),
		field.Int64("duration_time"),
	}
}

// Edges of the SkylineDos.
func (SkylineDos) Edges() []ent.Edge {
	return nil
}
