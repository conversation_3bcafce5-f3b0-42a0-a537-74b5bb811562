package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// DataSync holds the schema definition for the DataSync entity.
type DataSync struct {
	ent.Schema
}

// Mixin of the DataSync.
func (DataSync) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the DataSync.
func (DataSync) Fields() []ent.Field {
	return []ent.Field{
		field.JSON("pre_data_list", &[]string{}).Optional().StructTag(`query:"pre_data_list,omitempty"`),
		field.JSON("data_list", &[]string{}).Optional().StructTag(`query:"data_list,omitempty"`),
		field.String("data_type").StructTag(`query:"data_type,omitempty"`),
		field.String("type"),
	}
}

// Edges of the DataSync.
func (DataSync) Edges() []ent.Edge {
	return nil
}
