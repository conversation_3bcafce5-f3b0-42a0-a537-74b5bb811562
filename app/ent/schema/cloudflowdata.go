package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// CloudFlowData holds the schema definition for the CloudFlowData entity.
type CloudFlowData struct {
	ent.Schema
}

// Mixin of the CloudFlowData.
func (CloudFlowData) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the CloudFlowData.
func (CloudFlowData) Fields() []ent.Field {
	return []ent.Field{
		field.Int("cloud_alert_id").Optional().Nillable(),
		field.String("src_ip").Comment("来源IP").StructTag(`query:"src_ip,omitempty"`),
		field.Int("src_port").Comment("来源端口").StructTag(`query:"src_port,omitempty"`),
		field.String("dst_ip").Comment("被攻击IP").StructTag(`query:"dst_ip,omitempty"`),
		field.Int("dst_port").Comment("被攻击端口").StructTag(`query:"dst_port,omitempty"`),
		field.Int("protocol").Comment("4层协议：6tcp，17udp"),
		field.Int64("max_attack_pps").Comment("从攻击开始到攻击结束，最大的pps").StructTag(`query:"max_attack_pps,omitempty"`),
		field.Int("flow_over_max_pps_count").Comment("总pps>1000的次数").StructTag(`query:"flow_over_max_pps_count,omitempty"`),
		field.Time("start_time").Comment("攻击开始时间"),
		field.Time("end_time").Comment("上一个报文到达的时间").Optional(),
	}
}

// Edges of the CloudFlowData.
func (CloudFlowData) Edges() []ent.Edge {
	return []ent.Edge{
		// 一个流超限数据属于一个云网络告警
		edge.From("cloud_alert", CloudAlert.Type).Ref("cloudflow_datas").Field("cloud_alert_id").Unique(),
	}
}
