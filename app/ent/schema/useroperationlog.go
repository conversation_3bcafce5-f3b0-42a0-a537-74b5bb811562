package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// UserOperationLog holds the schema definition for the UserOperationLog entity.
type UserOperationLog struct {
	ent.Schema
}

// Mixin of the UserOperationLog schema.
func (UserOperationLog) Mixin() []ent.Mixin {
	return []ent.Mixin{
		RemarkMixin{},
		TimeMixin{},
	}
}

// Fields of the UserOperationLog.
func (UserOperationLog) Fields() []ent.Field {
	return []ent.Field{
		field.String("username"),
		field.String("method"),
		field.String("request_id").StructTag(`query:"request_id,omitempty"`),
		field.String("uri").MaxLen(500),
		field.Text("request_body").StructTag(`query:"request_body,omitempty"`),
		field.String("project"),
	}
}

// Edges of the UserOperationLog.
func (UserOperationLog) Edges() []ent.Edge {
	return nil
}
