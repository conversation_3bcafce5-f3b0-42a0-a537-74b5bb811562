package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// SpectrumData holds the schema definition for the SpectrumData entity.
type SpectrumData struct {
	ent.Schema
}

// Mixin of the SpectrumData.
func (SpectrumData) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		CreateTimeMixin{},
	}
}

// Fields of the SpectrumData.
func (SpectrumData) Fields() []ent.Field {
	return []ent.Field{
		field.Int("spectrum_alert_id").Optional().Nillable(),

		field.String("ip"),
		field.Time("time"),
		field.Int("monitor_id"),
		field.Int("data_type"),
		field.Int64("bps"),
		field.Int64("pps"),
		field.Int64("syn_bps"),
		field.Int64("syn_pps"),
		field.Int64("ack_bps"),
		field.Int64("ack_pps"),
		field.Int64("syn_ack_bps"),
		field.Int64("syn_ack_pps"),
		field.Int64("icmp_bps"),
		field.Int64("icmp_pps"),
		field.Int64("small_pps"),
		field.Int64("ntp_pps"),
		field.Int64("ntp_bps"),
		field.Int64("dns_query_pps"),
		field.Int64("dns_query_bps"),
		field.Int64("dns_answer_pps"),
		field.Int64("dns_answer_bps"),
		field.Int64("ssdp_bps"),
		field.Int64("ssdp_pps"),
		field.Int64("udp_pps"),
		field.Int64("udp_bps"),
		field.Int64("qps"),
		field.Int("receive_count"),
		field.Int("ip_type"),
		field.String("monitor").Optional().Nillable(),
		field.String("product").Optional().Nillable(),
		field.String("host").Optional().Nillable(),
	}
}

// Edges of the SpectrumData.
func (SpectrumData) Edges() []ent.Edge {
	return []ent.Edge{
		//一个分光数据属于一个分光告警
		edge.From("spectrum_alert", SpectrumAlert.Type).Ref("spectrum_datas").Field("spectrum_alert_id").Unique(),
	}
}
