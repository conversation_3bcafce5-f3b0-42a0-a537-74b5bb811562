package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Notify holds the schema definition for the Notify entity.
type Notify struct {
	ent.Schema
}

// Mixin of the Notify.
func (Notify) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TimeMixin{},
		TenantMixin{},
		RemarkMixin{},
	}
}

// Fields of the Notify.
func (Notify) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Comment("名称"),
		field.Bool("popo").Comment("true，通过popo对emails进行通知"),
		field.Bool("email").Comment("true，通过邮件对emails进行通知"),
		field.Bool("sms").Comment("true，通过短信对phones进行通知"),
		field.Bool("phone").Comment("true，通过电话对phones进行通知"),
		field.JSON("popo_groups", &[]string{}).Comment("通过popo群进行通知"),
		field.JSON("emails", &[]string{}).Comment("邮件列表"),
		field.JSON("phones", &[]string{}).Comment("电话列表"),
		field.JSON("ip_whitelists", &[]string{}).Comment("通知IP白名单，告警IP在白名单中将不再通知；项目白名单 > 系统设置白名单"),
		field.Bool("system").Comment("true，系统配置"),
		field.Bool("enabled").Comment("是否启用"),
		field.Bool("sa_notify_popo").Comment("true，通过auth获取sa列表，并通过popo进行通知；仅非系统配置生效"),
		field.Bool("sa_notify_email").Comment("true，通过auth获取sa列表，并通过邮件进行通知；仅非系统配置生效"),
	}
}

// Edges of the Notify.
func (Notify) Edges() []ent.Edge {
	return nil
}
