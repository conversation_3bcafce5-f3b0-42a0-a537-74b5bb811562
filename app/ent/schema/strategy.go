package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Strategy holds the schema definition for the Strategy entity.
type Strategy struct {
	ent.Schema
}

// Mixin of the Strategy.
func (Strategy) Mixin() []ent.Mixin {
	return []ent.Mixin{
		BaseMixin{},
		TenantMixin{},
		TimeMixin{},
		RemarkMixin{},
	}
}

// Fields of the Strategy.
func (Strategy) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Comment("名称"),
		field.Int("type").Comment("牵引防护类型"),
		field.Bool("enabled").Comment("是否启用"),
		field.Bool("system").Comment("系统策略，项目未配置自定义策略情况下，使用系统策略"),
		field.Int64("bps").Comment("总bps大小阈值"),
		field.Int64("pps").Comment("总pps大小阈值"),
		field.Int("bps_count").Comment("bps次数阈值"),
		field.Int("pps_count").Comment("pps次数阈值"),
		field.Int("isp_code").Comment("ip运营商类型"),
	}
}

// Edges of the Strategy.
func (Strategy) Edges() []ent.Edge {
	return []ent.Edge{
		// 一个策略有多个分光告警
		edge.To("strategy_alerts", SpectrumAlert.Type),
	}
}
