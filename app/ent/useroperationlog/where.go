// Code generated by ent, DO NOT EDIT.

package useroperationlog

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldID, id))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldRemark, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldUpdatedAt, v))
}

// Username applies equality check predicate on the "username" field. It's identical to UsernameEQ.
func Username(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldUsername, v))
}

// Method applies equality check predicate on the "method" field. It's identical to MethodEQ.
func Method(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldMethod, v))
}

// RequestID applies equality check predicate on the "request_id" field. It's identical to RequestIDEQ.
func RequestID(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldRequestID, v))
}

// URI applies equality check predicate on the "uri" field. It's identical to URIEQ.
func URI(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldURI, v))
}

// RequestBody applies equality check predicate on the "request_body" field. It's identical to RequestBodyEQ.
func RequestBody(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldRequestBody, v))
}

// Project applies equality check predicate on the "project" field. It's identical to ProjectEQ.
func Project(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldProject, v))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContainsFold(FieldRemark, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldUpdatedAt, v))
}

// UsernameEQ applies the EQ predicate on the "username" field.
func UsernameEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldUsername, v))
}

// UsernameNEQ applies the NEQ predicate on the "username" field.
func UsernameNEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldUsername, v))
}

// UsernameIn applies the In predicate on the "username" field.
func UsernameIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldUsername, vs...))
}

// UsernameNotIn applies the NotIn predicate on the "username" field.
func UsernameNotIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldUsername, vs...))
}

// UsernameGT applies the GT predicate on the "username" field.
func UsernameGT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldUsername, v))
}

// UsernameGTE applies the GTE predicate on the "username" field.
func UsernameGTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldUsername, v))
}

// UsernameLT applies the LT predicate on the "username" field.
func UsernameLT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldUsername, v))
}

// UsernameLTE applies the LTE predicate on the "username" field.
func UsernameLTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldUsername, v))
}

// UsernameContains applies the Contains predicate on the "username" field.
func UsernameContains(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContains(FieldUsername, v))
}

// UsernameHasPrefix applies the HasPrefix predicate on the "username" field.
func UsernameHasPrefix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasPrefix(FieldUsername, v))
}

// UsernameHasSuffix applies the HasSuffix predicate on the "username" field.
func UsernameHasSuffix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasSuffix(FieldUsername, v))
}

// UsernameEqualFold applies the EqualFold predicate on the "username" field.
func UsernameEqualFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEqualFold(FieldUsername, v))
}

// UsernameContainsFold applies the ContainsFold predicate on the "username" field.
func UsernameContainsFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContainsFold(FieldUsername, v))
}

// MethodEQ applies the EQ predicate on the "method" field.
func MethodEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldMethod, v))
}

// MethodNEQ applies the NEQ predicate on the "method" field.
func MethodNEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldMethod, v))
}

// MethodIn applies the In predicate on the "method" field.
func MethodIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldMethod, vs...))
}

// MethodNotIn applies the NotIn predicate on the "method" field.
func MethodNotIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldMethod, vs...))
}

// MethodGT applies the GT predicate on the "method" field.
func MethodGT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldMethod, v))
}

// MethodGTE applies the GTE predicate on the "method" field.
func MethodGTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldMethod, v))
}

// MethodLT applies the LT predicate on the "method" field.
func MethodLT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldMethod, v))
}

// MethodLTE applies the LTE predicate on the "method" field.
func MethodLTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldMethod, v))
}

// MethodContains applies the Contains predicate on the "method" field.
func MethodContains(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContains(FieldMethod, v))
}

// MethodHasPrefix applies the HasPrefix predicate on the "method" field.
func MethodHasPrefix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasPrefix(FieldMethod, v))
}

// MethodHasSuffix applies the HasSuffix predicate on the "method" field.
func MethodHasSuffix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasSuffix(FieldMethod, v))
}

// MethodEqualFold applies the EqualFold predicate on the "method" field.
func MethodEqualFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEqualFold(FieldMethod, v))
}

// MethodContainsFold applies the ContainsFold predicate on the "method" field.
func MethodContainsFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContainsFold(FieldMethod, v))
}

// RequestIDEQ applies the EQ predicate on the "request_id" field.
func RequestIDEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldRequestID, v))
}

// RequestIDNEQ applies the NEQ predicate on the "request_id" field.
func RequestIDNEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldRequestID, v))
}

// RequestIDIn applies the In predicate on the "request_id" field.
func RequestIDIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldRequestID, vs...))
}

// RequestIDNotIn applies the NotIn predicate on the "request_id" field.
func RequestIDNotIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldRequestID, vs...))
}

// RequestIDGT applies the GT predicate on the "request_id" field.
func RequestIDGT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldRequestID, v))
}

// RequestIDGTE applies the GTE predicate on the "request_id" field.
func RequestIDGTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldRequestID, v))
}

// RequestIDLT applies the LT predicate on the "request_id" field.
func RequestIDLT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldRequestID, v))
}

// RequestIDLTE applies the LTE predicate on the "request_id" field.
func RequestIDLTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldRequestID, v))
}

// RequestIDContains applies the Contains predicate on the "request_id" field.
func RequestIDContains(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContains(FieldRequestID, v))
}

// RequestIDHasPrefix applies the HasPrefix predicate on the "request_id" field.
func RequestIDHasPrefix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasPrefix(FieldRequestID, v))
}

// RequestIDHasSuffix applies the HasSuffix predicate on the "request_id" field.
func RequestIDHasSuffix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasSuffix(FieldRequestID, v))
}

// RequestIDEqualFold applies the EqualFold predicate on the "request_id" field.
func RequestIDEqualFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEqualFold(FieldRequestID, v))
}

// RequestIDContainsFold applies the ContainsFold predicate on the "request_id" field.
func RequestIDContainsFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContainsFold(FieldRequestID, v))
}

// URIEQ applies the EQ predicate on the "uri" field.
func URIEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldURI, v))
}

// URINEQ applies the NEQ predicate on the "uri" field.
func URINEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldURI, v))
}

// URIIn applies the In predicate on the "uri" field.
func URIIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldURI, vs...))
}

// URINotIn applies the NotIn predicate on the "uri" field.
func URINotIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldURI, vs...))
}

// URIGT applies the GT predicate on the "uri" field.
func URIGT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldURI, v))
}

// URIGTE applies the GTE predicate on the "uri" field.
func URIGTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldURI, v))
}

// URILT applies the LT predicate on the "uri" field.
func URILT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldURI, v))
}

// URILTE applies the LTE predicate on the "uri" field.
func URILTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldURI, v))
}

// URIContains applies the Contains predicate on the "uri" field.
func URIContains(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContains(FieldURI, v))
}

// URIHasPrefix applies the HasPrefix predicate on the "uri" field.
func URIHasPrefix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasPrefix(FieldURI, v))
}

// URIHasSuffix applies the HasSuffix predicate on the "uri" field.
func URIHasSuffix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasSuffix(FieldURI, v))
}

// URIEqualFold applies the EqualFold predicate on the "uri" field.
func URIEqualFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEqualFold(FieldURI, v))
}

// URIContainsFold applies the ContainsFold predicate on the "uri" field.
func URIContainsFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContainsFold(FieldURI, v))
}

// RequestBodyEQ applies the EQ predicate on the "request_body" field.
func RequestBodyEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldRequestBody, v))
}

// RequestBodyNEQ applies the NEQ predicate on the "request_body" field.
func RequestBodyNEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldRequestBody, v))
}

// RequestBodyIn applies the In predicate on the "request_body" field.
func RequestBodyIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldRequestBody, vs...))
}

// RequestBodyNotIn applies the NotIn predicate on the "request_body" field.
func RequestBodyNotIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldRequestBody, vs...))
}

// RequestBodyGT applies the GT predicate on the "request_body" field.
func RequestBodyGT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldRequestBody, v))
}

// RequestBodyGTE applies the GTE predicate on the "request_body" field.
func RequestBodyGTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldRequestBody, v))
}

// RequestBodyLT applies the LT predicate on the "request_body" field.
func RequestBodyLT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldRequestBody, v))
}

// RequestBodyLTE applies the LTE predicate on the "request_body" field.
func RequestBodyLTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldRequestBody, v))
}

// RequestBodyContains applies the Contains predicate on the "request_body" field.
func RequestBodyContains(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContains(FieldRequestBody, v))
}

// RequestBodyHasPrefix applies the HasPrefix predicate on the "request_body" field.
func RequestBodyHasPrefix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasPrefix(FieldRequestBody, v))
}

// RequestBodyHasSuffix applies the HasSuffix predicate on the "request_body" field.
func RequestBodyHasSuffix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasSuffix(FieldRequestBody, v))
}

// RequestBodyEqualFold applies the EqualFold predicate on the "request_body" field.
func RequestBodyEqualFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEqualFold(FieldRequestBody, v))
}

// RequestBodyContainsFold applies the ContainsFold predicate on the "request_body" field.
func RequestBodyContainsFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContainsFold(FieldRequestBody, v))
}

// ProjectEQ applies the EQ predicate on the "project" field.
func ProjectEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEQ(FieldProject, v))
}

// ProjectNEQ applies the NEQ predicate on the "project" field.
func ProjectNEQ(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNEQ(FieldProject, v))
}

// ProjectIn applies the In predicate on the "project" field.
func ProjectIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldIn(FieldProject, vs...))
}

// ProjectNotIn applies the NotIn predicate on the "project" field.
func ProjectNotIn(vs ...string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldNotIn(FieldProject, vs...))
}

// ProjectGT applies the GT predicate on the "project" field.
func ProjectGT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGT(FieldProject, v))
}

// ProjectGTE applies the GTE predicate on the "project" field.
func ProjectGTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldGTE(FieldProject, v))
}

// ProjectLT applies the LT predicate on the "project" field.
func ProjectLT(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLT(FieldProject, v))
}

// ProjectLTE applies the LTE predicate on the "project" field.
func ProjectLTE(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldLTE(FieldProject, v))
}

// ProjectContains applies the Contains predicate on the "project" field.
func ProjectContains(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContains(FieldProject, v))
}

// ProjectHasPrefix applies the HasPrefix predicate on the "project" field.
func ProjectHasPrefix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasPrefix(FieldProject, v))
}

// ProjectHasSuffix applies the HasSuffix predicate on the "project" field.
func ProjectHasSuffix(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldHasSuffix(FieldProject, v))
}

// ProjectEqualFold applies the EqualFold predicate on the "project" field.
func ProjectEqualFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldEqualFold(FieldProject, v))
}

// ProjectContainsFold applies the ContainsFold predicate on the "project" field.
func ProjectContainsFold(v string) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.FieldContainsFold(FieldProject, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.UserOperationLog) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.UserOperationLog) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.UserOperationLog) predicate.UserOperationLog {
	return predicate.UserOperationLog(sql.NotPredicates(p))
}
