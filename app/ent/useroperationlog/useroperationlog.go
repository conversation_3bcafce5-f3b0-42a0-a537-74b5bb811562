// Code generated by ent, DO NOT EDIT.

package useroperationlog

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the useroperationlog type in the database.
	Label = "user_operation_log"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldUsername holds the string denoting the username field in the database.
	FieldUsername = "username"
	// FieldMethod holds the string denoting the method field in the database.
	FieldMethod = "method"
	// FieldRequestID holds the string denoting the request_id field in the database.
	FieldRequestID = "request_id"
	// FieldURI holds the string denoting the uri field in the database.
	FieldURI = "uri"
	// FieldRequestBody holds the string denoting the request_body field in the database.
	FieldRequestBody = "request_body"
	// FieldProject holds the string denoting the project field in the database.
	FieldProject = "project"
	// Table holds the table name of the useroperationlog in the database.
	Table = "user_operation_logs"
)

// Columns holds all SQL columns for useroperationlog fields.
var Columns = []string{
	FieldID,
	FieldRemark,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldUsername,
	FieldMethod,
	FieldRequestID,
	FieldURI,
	FieldRequestBody,
	FieldProject,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// URIValidator is a validator for the "uri" field. It is called by the builders before save.
	URIValidator func(string) error
)

// OrderOption defines the ordering options for the UserOperationLog queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByUsername orders the results by the username field.
func ByUsername(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUsername, opts...).ToFunc()
}

// ByMethod orders the results by the method field.
func ByMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMethod, opts...).ToFunc()
}

// ByRequestID orders the results by the request_id field.
func ByRequestID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRequestID, opts...).ToFunc()
}

// ByURI orders the results by the uri field.
func ByURI(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldURI, opts...).ToFunc()
}

// ByRequestBody orders the results by the request_body field.
func ByRequestBody(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRequestBody, opts...).ToFunc()
}

// ByProject orders the results by the project field.
func ByProject(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProject, opts...).ToFunc()
}
