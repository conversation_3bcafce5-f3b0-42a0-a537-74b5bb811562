// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/predicate"
	"meta/app/ent/useroperationlog"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserOperationLogDelete is the builder for deleting a UserOperationLog entity.
type UserOperationLogDelete struct {
	config
	hooks    []Hook
	mutation *UserOperationLogMutation
}

// Where appends a list predicates to the UserOperationLogDelete builder.
func (uold *UserOperationLogDelete) Where(ps ...predicate.UserOperationLog) *UserOperationLogDelete {
	uold.mutation.Where(ps...)
	return uold
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (uold *UserOperationLogDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, uold.sqlExec, uold.mutation, uold.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (uold *UserOperationLogDelete) ExecX(ctx context.Context) int {
	n, err := uold.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (uold *UserOperationLogDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(useroperationlog.Table, sqlgraph.NewFieldSpec(useroperationlog.FieldID, field.TypeInt))
	if ps := uold.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, uold.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	uold.mutation.done = true
	return affected, err
}

// UserOperationLogDeleteOne is the builder for deleting a single UserOperationLog entity.
type UserOperationLogDeleteOne struct {
	uold *UserOperationLogDelete
}

// Where appends a list predicates to the UserOperationLogDelete builder.
func (uoldo *UserOperationLogDeleteOne) Where(ps ...predicate.UserOperationLog) *UserOperationLogDeleteOne {
	uoldo.uold.mutation.Where(ps...)
	return uoldo
}

// Exec executes the deletion query.
func (uoldo *UserOperationLogDeleteOne) Exec(ctx context.Context) error {
	n, err := uoldo.uold.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{useroperationlog.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (uoldo *UserOperationLogDeleteOne) ExecX(ctx context.Context) {
	if err := uoldo.Exec(ctx); err != nil {
		panic(err)
	}
}
