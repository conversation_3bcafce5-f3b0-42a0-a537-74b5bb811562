// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/cloudattackdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudAttackDataQuery is the builder for querying CloudAttackData entities.
type CloudAttackDataQuery struct {
	config
	ctx        *QueryContext
	order      []cloudattackdata.OrderOption
	inters     []Interceptor
	predicates []predicate.CloudAttackData
	withTenant *TenantQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the CloudAttackDataQuery builder.
func (cadq *CloudAttackDataQuery) Where(ps ...predicate.CloudAttackData) *CloudAttackDataQuery {
	cadq.predicates = append(cadq.predicates, ps...)
	return cadq
}

// Limit the number of records to be returned by this query.
func (cadq *CloudAttackDataQuery) Limit(limit int) *CloudAttackDataQuery {
	cadq.ctx.Limit = &limit
	return cadq
}

// Offset to start from.
func (cadq *CloudAttackDataQuery) Offset(offset int) *CloudAttackDataQuery {
	cadq.ctx.Offset = &offset
	return cadq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (cadq *CloudAttackDataQuery) Unique(unique bool) *CloudAttackDataQuery {
	cadq.ctx.Unique = &unique
	return cadq
}

// Order specifies how the records should be ordered.
func (cadq *CloudAttackDataQuery) Order(o ...cloudattackdata.OrderOption) *CloudAttackDataQuery {
	cadq.order = append(cadq.order, o...)
	return cadq
}

// QueryTenant chains the current query on the "tenant" edge.
func (cadq *CloudAttackDataQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: cadq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := cadq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := cadq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudattackdata.Table, cloudattackdata.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cloudattackdata.TenantTable, cloudattackdata.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(cadq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first CloudAttackData entity from the query.
// Returns a *NotFoundError when no CloudAttackData was found.
func (cadq *CloudAttackDataQuery) First(ctx context.Context) (*CloudAttackData, error) {
	nodes, err := cadq.Limit(1).All(setContextOp(ctx, cadq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{cloudattackdata.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (cadq *CloudAttackDataQuery) FirstX(ctx context.Context) *CloudAttackData {
	node, err := cadq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first CloudAttackData ID from the query.
// Returns a *NotFoundError when no CloudAttackData ID was found.
func (cadq *CloudAttackDataQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = cadq.Limit(1).IDs(setContextOp(ctx, cadq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{cloudattackdata.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (cadq *CloudAttackDataQuery) FirstIDX(ctx context.Context) int {
	id, err := cadq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single CloudAttackData entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one CloudAttackData entity is found.
// Returns a *NotFoundError when no CloudAttackData entities are found.
func (cadq *CloudAttackDataQuery) Only(ctx context.Context) (*CloudAttackData, error) {
	nodes, err := cadq.Limit(2).All(setContextOp(ctx, cadq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{cloudattackdata.Label}
	default:
		return nil, &NotSingularError{cloudattackdata.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (cadq *CloudAttackDataQuery) OnlyX(ctx context.Context) *CloudAttackData {
	node, err := cadq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only CloudAttackData ID in the query.
// Returns a *NotSingularError when more than one CloudAttackData ID is found.
// Returns a *NotFoundError when no entities are found.
func (cadq *CloudAttackDataQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = cadq.Limit(2).IDs(setContextOp(ctx, cadq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{cloudattackdata.Label}
	default:
		err = &NotSingularError{cloudattackdata.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (cadq *CloudAttackDataQuery) OnlyIDX(ctx context.Context) int {
	id, err := cadq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of CloudAttackDataSlice.
func (cadq *CloudAttackDataQuery) All(ctx context.Context) ([]*CloudAttackData, error) {
	ctx = setContextOp(ctx, cadq.ctx, "All")
	if err := cadq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*CloudAttackData, *CloudAttackDataQuery]()
	return withInterceptors[[]*CloudAttackData](ctx, cadq, qr, cadq.inters)
}

// AllX is like All, but panics if an error occurs.
func (cadq *CloudAttackDataQuery) AllX(ctx context.Context) []*CloudAttackData {
	nodes, err := cadq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of CloudAttackData IDs.
func (cadq *CloudAttackDataQuery) IDs(ctx context.Context) (ids []int, err error) {
	if cadq.ctx.Unique == nil && cadq.path != nil {
		cadq.Unique(true)
	}
	ctx = setContextOp(ctx, cadq.ctx, "IDs")
	if err = cadq.Select(cloudattackdata.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (cadq *CloudAttackDataQuery) IDsX(ctx context.Context) []int {
	ids, err := cadq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (cadq *CloudAttackDataQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, cadq.ctx, "Count")
	if err := cadq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, cadq, querierCount[*CloudAttackDataQuery](), cadq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (cadq *CloudAttackDataQuery) CountX(ctx context.Context) int {
	count, err := cadq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (cadq *CloudAttackDataQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, cadq.ctx, "Exist")
	switch _, err := cadq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (cadq *CloudAttackDataQuery) ExistX(ctx context.Context) bool {
	exist, err := cadq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the CloudAttackDataQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (cadq *CloudAttackDataQuery) Clone() *CloudAttackDataQuery {
	if cadq == nil {
		return nil
	}
	return &CloudAttackDataQuery{
		config:     cadq.config,
		ctx:        cadq.ctx.Clone(),
		order:      append([]cloudattackdata.OrderOption{}, cadq.order...),
		inters:     append([]Interceptor{}, cadq.inters...),
		predicates: append([]predicate.CloudAttackData{}, cadq.predicates...),
		withTenant: cadq.withTenant.Clone(),
		// clone intermediate query.
		sql:  cadq.sql.Clone(),
		path: cadq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (cadq *CloudAttackDataQuery) WithTenant(opts ...func(*TenantQuery)) *CloudAttackDataQuery {
	query := (&TenantClient{config: cadq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	cadq.withTenant = query
	return cadq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.CloudAttackData.Query().
//		GroupBy(cloudattackdata.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (cadq *CloudAttackDataQuery) GroupBy(field string, fields ...string) *CloudAttackDataGroupBy {
	cadq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &CloudAttackDataGroupBy{build: cadq}
	grbuild.flds = &cadq.ctx.Fields
	grbuild.label = cloudattackdata.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.CloudAttackData.Query().
//		Select(cloudattackdata.FieldTenantID).
//		Scan(ctx, &v)
func (cadq *CloudAttackDataQuery) Select(fields ...string) *CloudAttackDataSelect {
	cadq.ctx.Fields = append(cadq.ctx.Fields, fields...)
	sbuild := &CloudAttackDataSelect{CloudAttackDataQuery: cadq}
	sbuild.label = cloudattackdata.Label
	sbuild.flds, sbuild.scan = &cadq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a CloudAttackDataSelect configured with the given aggregations.
func (cadq *CloudAttackDataQuery) Aggregate(fns ...AggregateFunc) *CloudAttackDataSelect {
	return cadq.Select().Aggregate(fns...)
}

func (cadq *CloudAttackDataQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range cadq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, cadq); err != nil {
				return err
			}
		}
	}
	for _, f := range cadq.ctx.Fields {
		if !cloudattackdata.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if cadq.path != nil {
		prev, err := cadq.path(ctx)
		if err != nil {
			return err
		}
		cadq.sql = prev
	}
	if cloudattackdata.Policy == nil {
		return errors.New("ent: uninitialized cloudattackdata.Policy (forgotten import ent/runtime?)")
	}
	if err := cloudattackdata.Policy.EvalQuery(ctx, cadq); err != nil {
		return err
	}
	return nil
}

func (cadq *CloudAttackDataQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*CloudAttackData, error) {
	var (
		nodes       = []*CloudAttackData{}
		_spec       = cadq.querySpec()
		loadedTypes = [1]bool{
			cadq.withTenant != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*CloudAttackData).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &CloudAttackData{config: cadq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, cadq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := cadq.withTenant; query != nil {
		if err := cadq.loadTenant(ctx, query, nodes, nil,
			func(n *CloudAttackData, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (cadq *CloudAttackDataQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*CloudAttackData, init func(*CloudAttackData), assign func(*CloudAttackData, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*CloudAttackData)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (cadq *CloudAttackDataQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := cadq.querySpec()
	_spec.Node.Columns = cadq.ctx.Fields
	if len(cadq.ctx.Fields) > 0 {
		_spec.Unique = cadq.ctx.Unique != nil && *cadq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, cadq.driver, _spec)
}

func (cadq *CloudAttackDataQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(cloudattackdata.Table, cloudattackdata.Columns, sqlgraph.NewFieldSpec(cloudattackdata.FieldID, field.TypeInt))
	_spec.From = cadq.sql
	if unique := cadq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if cadq.path != nil {
		_spec.Unique = true
	}
	if fields := cadq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cloudattackdata.FieldID)
		for i := range fields {
			if fields[i] != cloudattackdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if cadq.withTenant != nil {
			_spec.Node.AddColumnOnce(cloudattackdata.FieldTenantID)
		}
	}
	if ps := cadq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := cadq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := cadq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := cadq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (cadq *CloudAttackDataQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(cadq.driver.Dialect())
	t1 := builder.Table(cloudattackdata.Table)
	columns := cadq.ctx.Fields
	if len(columns) == 0 {
		columns = cloudattackdata.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if cadq.sql != nil {
		selector = cadq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if cadq.ctx.Unique != nil && *cadq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range cadq.predicates {
		p(selector)
	}
	for _, p := range cadq.order {
		p(selector)
	}
	if offset := cadq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := cadq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// CloudAttackDataGroupBy is the group-by builder for CloudAttackData entities.
type CloudAttackDataGroupBy struct {
	selector
	build *CloudAttackDataQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (cadgb *CloudAttackDataGroupBy) Aggregate(fns ...AggregateFunc) *CloudAttackDataGroupBy {
	cadgb.fns = append(cadgb.fns, fns...)
	return cadgb
}

// Scan applies the selector query and scans the result into the given value.
func (cadgb *CloudAttackDataGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cadgb.build.ctx, "GroupBy")
	if err := cadgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CloudAttackDataQuery, *CloudAttackDataGroupBy](ctx, cadgb.build, cadgb, cadgb.build.inters, v)
}

func (cadgb *CloudAttackDataGroupBy) sqlScan(ctx context.Context, root *CloudAttackDataQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(cadgb.fns))
	for _, fn := range cadgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*cadgb.flds)+len(cadgb.fns))
		for _, f := range *cadgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*cadgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cadgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// CloudAttackDataSelect is the builder for selecting fields of CloudAttackData entities.
type CloudAttackDataSelect struct {
	*CloudAttackDataQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cads *CloudAttackDataSelect) Aggregate(fns ...AggregateFunc) *CloudAttackDataSelect {
	cads.fns = append(cads.fns, fns...)
	return cads
}

// Scan applies the selector query and scans the result into the given value.
func (cads *CloudAttackDataSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cads.ctx, "Select")
	if err := cads.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CloudAttackDataQuery, *CloudAttackDataSelect](ctx, cads.CloudAttackDataQuery, cads, cads.inters, v)
}

func (cads *CloudAttackDataSelect) sqlScan(ctx context.Context, root *CloudAttackDataQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cads.fns))
	for _, fn := range cads.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cads.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cads.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
