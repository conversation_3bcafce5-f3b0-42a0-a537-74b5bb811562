// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/skylinedos"
	"meta/app/ent/tenant"
	"meta/app/entity/netease/gamecloud"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SkylineDosUpdate is the builder for updating SkylineDos entities.
type SkylineDosUpdate struct {
	config
	hooks    []Hook
	mutation *SkylineDosMutation
}

// Where appends a list predicates to the SkylineDosUpdate builder.
func (sdu *SkylineDosUpdate) Where(ps ...predicate.SkylineDos) *SkylineDosUpdate {
	sdu.mutation.Where(ps...)
	return sdu
}

// SetTenantID sets the "tenant_id" field.
func (sdu *SkylineDosUpdate) SetTenantID(i int) *SkylineDosUpdate {
	sdu.mutation.SetTenantID(i)
	return sdu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableTenantID(i *int) *SkylineDosUpdate {
	if i != nil {
		sdu.SetTenantID(*i)
	}
	return sdu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (sdu *SkylineDosUpdate) ClearTenantID() *SkylineDosUpdate {
	sdu.mutation.ClearTenantID()
	return sdu
}

// SetUpdatedAt sets the "updated_at" field.
func (sdu *SkylineDosUpdate) SetUpdatedAt(t time.Time) *SkylineDosUpdate {
	sdu.mutation.SetUpdatedAt(t)
	return sdu
}

// SetRemark sets the "remark" field.
func (sdu *SkylineDosUpdate) SetRemark(s string) *SkylineDosUpdate {
	sdu.mutation.SetRemark(s)
	return sdu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableRemark(s *string) *SkylineDosUpdate {
	if s != nil {
		sdu.SetRemark(*s)
	}
	return sdu
}

// ClearRemark clears the value of the "remark" field.
func (sdu *SkylineDosUpdate) ClearRemark() *SkylineDosUpdate {
	sdu.mutation.ClearRemark()
	return sdu
}

// SetStartTime sets the "start_time" field.
func (sdu *SkylineDosUpdate) SetStartTime(t time.Time) *SkylineDosUpdate {
	sdu.mutation.SetStartTime(t)
	return sdu
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableStartTime(t *time.Time) *SkylineDosUpdate {
	if t != nil {
		sdu.SetStartTime(*t)
	}
	return sdu
}

// SetEndTime sets the "end_time" field.
func (sdu *SkylineDosUpdate) SetEndTime(t time.Time) *SkylineDosUpdate {
	sdu.mutation.SetEndTime(t)
	return sdu
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableEndTime(t *time.Time) *SkylineDosUpdate {
	if t != nil {
		sdu.SetEndTime(*t)
	}
	return sdu
}

// ClearEndTime clears the value of the "end_time" field.
func (sdu *SkylineDosUpdate) ClearEndTime() *SkylineDosUpdate {
	sdu.mutation.ClearEndTime()
	return sdu
}

// SetRegion sets the "region" field.
func (sdu *SkylineDosUpdate) SetRegion(s string) *SkylineDosUpdate {
	sdu.mutation.SetRegion(s)
	return sdu
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableRegion(s *string) *SkylineDosUpdate {
	if s != nil {
		sdu.SetRegion(*s)
	}
	return sdu
}

// SetResource sets the "resource" field.
func (sdu *SkylineDosUpdate) SetResource(s string) *SkylineDosUpdate {
	sdu.mutation.SetResource(s)
	return sdu
}

// SetNillableResource sets the "resource" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableResource(s *string) *SkylineDosUpdate {
	if s != nil {
		sdu.SetResource(*s)
	}
	return sdu
}

// SetResourceType sets the "resource_type" field.
func (sdu *SkylineDosUpdate) SetResourceType(s string) *SkylineDosUpdate {
	sdu.mutation.SetResourceType(s)
	return sdu
}

// SetNillableResourceType sets the "resource_type" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableResourceType(s *string) *SkylineDosUpdate {
	if s != nil {
		sdu.SetResourceType(*s)
	}
	return sdu
}

// SetVectorTypes sets the "vector_types" field.
func (sdu *SkylineDosUpdate) SetVectorTypes(s *[]string) *SkylineDosUpdate {
	sdu.mutation.SetVectorTypes(s)
	return sdu
}

// ClearVectorTypes clears the value of the "vector_types" field.
func (sdu *SkylineDosUpdate) ClearVectorTypes() *SkylineDosUpdate {
	sdu.mutation.ClearVectorTypes()
	return sdu
}

// SetStatus sets the "status" field.
func (sdu *SkylineDosUpdate) SetStatus(s string) *SkylineDosUpdate {
	sdu.mutation.SetStatus(s)
	return sdu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableStatus(s *string) *SkylineDosUpdate {
	if s != nil {
		sdu.SetStatus(*s)
	}
	return sdu
}

// SetAttackID sets the "attack_id" field.
func (sdu *SkylineDosUpdate) SetAttackID(s string) *SkylineDosUpdate {
	sdu.mutation.SetAttackID(s)
	return sdu
}

// SetNillableAttackID sets the "attack_id" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableAttackID(s *string) *SkylineDosUpdate {
	if s != nil {
		sdu.SetAttackID(*s)
	}
	return sdu
}

// SetAttackCounters sets the "attack_counters" field.
func (sdu *SkylineDosUpdate) SetAttackCounters(gc *[]gamecloud.AttackCounter) *SkylineDosUpdate {
	sdu.mutation.SetAttackCounters(gc)
	return sdu
}

// ClearAttackCounters clears the value of the "attack_counters" field.
func (sdu *SkylineDosUpdate) ClearAttackCounters() *SkylineDosUpdate {
	sdu.mutation.ClearAttackCounters()
	return sdu
}

// SetProject sets the "project" field.
func (sdu *SkylineDosUpdate) SetProject(s string) *SkylineDosUpdate {
	sdu.mutation.SetProject(s)
	return sdu
}

// SetNillableProject sets the "project" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableProject(s *string) *SkylineDosUpdate {
	if s != nil {
		sdu.SetProject(*s)
	}
	return sdu
}

// SetDurationTime sets the "duration_time" field.
func (sdu *SkylineDosUpdate) SetDurationTime(i int64) *SkylineDosUpdate {
	sdu.mutation.ResetDurationTime()
	sdu.mutation.SetDurationTime(i)
	return sdu
}

// SetNillableDurationTime sets the "duration_time" field if the given value is not nil.
func (sdu *SkylineDosUpdate) SetNillableDurationTime(i *int64) *SkylineDosUpdate {
	if i != nil {
		sdu.SetDurationTime(*i)
	}
	return sdu
}

// AddDurationTime adds i to the "duration_time" field.
func (sdu *SkylineDosUpdate) AddDurationTime(i int64) *SkylineDosUpdate {
	sdu.mutation.AddDurationTime(i)
	return sdu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sdu *SkylineDosUpdate) SetTenant(t *Tenant) *SkylineDosUpdate {
	return sdu.SetTenantID(t.ID)
}

// Mutation returns the SkylineDosMutation object of the builder.
func (sdu *SkylineDosUpdate) Mutation() *SkylineDosMutation {
	return sdu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (sdu *SkylineDosUpdate) ClearTenant() *SkylineDosUpdate {
	sdu.mutation.ClearTenant()
	return sdu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (sdu *SkylineDosUpdate) Save(ctx context.Context) (int, error) {
	if err := sdu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, sdu.sqlSave, sdu.mutation, sdu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sdu *SkylineDosUpdate) SaveX(ctx context.Context) int {
	affected, err := sdu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (sdu *SkylineDosUpdate) Exec(ctx context.Context) error {
	_, err := sdu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sdu *SkylineDosUpdate) ExecX(ctx context.Context) {
	if err := sdu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sdu *SkylineDosUpdate) defaults() error {
	if _, ok := sdu.mutation.UpdatedAt(); !ok {
		if skylinedos.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized skylinedos.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := skylinedos.UpdateDefaultUpdatedAt()
		sdu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sdu *SkylineDosUpdate) check() error {
	if v, ok := sdu.mutation.Remark(); ok {
		if err := skylinedos.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SkylineDos.remark": %w`, err)}
		}
	}
	return nil
}

func (sdu *SkylineDosUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := sdu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(skylinedos.Table, skylinedos.Columns, sqlgraph.NewFieldSpec(skylinedos.FieldID, field.TypeInt))
	if ps := sdu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sdu.mutation.UpdatedAt(); ok {
		_spec.SetField(skylinedos.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := sdu.mutation.Remark(); ok {
		_spec.SetField(skylinedos.FieldRemark, field.TypeString, value)
	}
	if sdu.mutation.RemarkCleared() {
		_spec.ClearField(skylinedos.FieldRemark, field.TypeString)
	}
	if value, ok := sdu.mutation.StartTime(); ok {
		_spec.SetField(skylinedos.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := sdu.mutation.EndTime(); ok {
		_spec.SetField(skylinedos.FieldEndTime, field.TypeTime, value)
	}
	if sdu.mutation.EndTimeCleared() {
		_spec.ClearField(skylinedos.FieldEndTime, field.TypeTime)
	}
	if value, ok := sdu.mutation.Region(); ok {
		_spec.SetField(skylinedos.FieldRegion, field.TypeString, value)
	}
	if value, ok := sdu.mutation.Resource(); ok {
		_spec.SetField(skylinedos.FieldResource, field.TypeString, value)
	}
	if value, ok := sdu.mutation.ResourceType(); ok {
		_spec.SetField(skylinedos.FieldResourceType, field.TypeString, value)
	}
	if value, ok := sdu.mutation.VectorTypes(); ok {
		_spec.SetField(skylinedos.FieldVectorTypes, field.TypeJSON, value)
	}
	if sdu.mutation.VectorTypesCleared() {
		_spec.ClearField(skylinedos.FieldVectorTypes, field.TypeJSON)
	}
	if value, ok := sdu.mutation.Status(); ok {
		_spec.SetField(skylinedos.FieldStatus, field.TypeString, value)
	}
	if value, ok := sdu.mutation.AttackID(); ok {
		_spec.SetField(skylinedos.FieldAttackID, field.TypeString, value)
	}
	if value, ok := sdu.mutation.AttackCounters(); ok {
		_spec.SetField(skylinedos.FieldAttackCounters, field.TypeJSON, value)
	}
	if sdu.mutation.AttackCountersCleared() {
		_spec.ClearField(skylinedos.FieldAttackCounters, field.TypeJSON)
	}
	if value, ok := sdu.mutation.Project(); ok {
		_spec.SetField(skylinedos.FieldProject, field.TypeString, value)
	}
	if value, ok := sdu.mutation.DurationTime(); ok {
		_spec.SetField(skylinedos.FieldDurationTime, field.TypeInt64, value)
	}
	if value, ok := sdu.mutation.AddedDurationTime(); ok {
		_spec.AddField(skylinedos.FieldDurationTime, field.TypeInt64, value)
	}
	if sdu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   skylinedos.TenantTable,
			Columns: []string{skylinedos.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sdu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   skylinedos.TenantTable,
			Columns: []string{skylinedos.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, sdu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{skylinedos.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	sdu.mutation.done = true
	return n, nil
}

// SkylineDosUpdateOne is the builder for updating a single SkylineDos entity.
type SkylineDosUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SkylineDosMutation
}

// SetTenantID sets the "tenant_id" field.
func (sduo *SkylineDosUpdateOne) SetTenantID(i int) *SkylineDosUpdateOne {
	sduo.mutation.SetTenantID(i)
	return sduo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableTenantID(i *int) *SkylineDosUpdateOne {
	if i != nil {
		sduo.SetTenantID(*i)
	}
	return sduo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (sduo *SkylineDosUpdateOne) ClearTenantID() *SkylineDosUpdateOne {
	sduo.mutation.ClearTenantID()
	return sduo
}

// SetUpdatedAt sets the "updated_at" field.
func (sduo *SkylineDosUpdateOne) SetUpdatedAt(t time.Time) *SkylineDosUpdateOne {
	sduo.mutation.SetUpdatedAt(t)
	return sduo
}

// SetRemark sets the "remark" field.
func (sduo *SkylineDosUpdateOne) SetRemark(s string) *SkylineDosUpdateOne {
	sduo.mutation.SetRemark(s)
	return sduo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableRemark(s *string) *SkylineDosUpdateOne {
	if s != nil {
		sduo.SetRemark(*s)
	}
	return sduo
}

// ClearRemark clears the value of the "remark" field.
func (sduo *SkylineDosUpdateOne) ClearRemark() *SkylineDosUpdateOne {
	sduo.mutation.ClearRemark()
	return sduo
}

// SetStartTime sets the "start_time" field.
func (sduo *SkylineDosUpdateOne) SetStartTime(t time.Time) *SkylineDosUpdateOne {
	sduo.mutation.SetStartTime(t)
	return sduo
}

// SetNillableStartTime sets the "start_time" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableStartTime(t *time.Time) *SkylineDosUpdateOne {
	if t != nil {
		sduo.SetStartTime(*t)
	}
	return sduo
}

// SetEndTime sets the "end_time" field.
func (sduo *SkylineDosUpdateOne) SetEndTime(t time.Time) *SkylineDosUpdateOne {
	sduo.mutation.SetEndTime(t)
	return sduo
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableEndTime(t *time.Time) *SkylineDosUpdateOne {
	if t != nil {
		sduo.SetEndTime(*t)
	}
	return sduo
}

// ClearEndTime clears the value of the "end_time" field.
func (sduo *SkylineDosUpdateOne) ClearEndTime() *SkylineDosUpdateOne {
	sduo.mutation.ClearEndTime()
	return sduo
}

// SetRegion sets the "region" field.
func (sduo *SkylineDosUpdateOne) SetRegion(s string) *SkylineDosUpdateOne {
	sduo.mutation.SetRegion(s)
	return sduo
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableRegion(s *string) *SkylineDosUpdateOne {
	if s != nil {
		sduo.SetRegion(*s)
	}
	return sduo
}

// SetResource sets the "resource" field.
func (sduo *SkylineDosUpdateOne) SetResource(s string) *SkylineDosUpdateOne {
	sduo.mutation.SetResource(s)
	return sduo
}

// SetNillableResource sets the "resource" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableResource(s *string) *SkylineDosUpdateOne {
	if s != nil {
		sduo.SetResource(*s)
	}
	return sduo
}

// SetResourceType sets the "resource_type" field.
func (sduo *SkylineDosUpdateOne) SetResourceType(s string) *SkylineDosUpdateOne {
	sduo.mutation.SetResourceType(s)
	return sduo
}

// SetNillableResourceType sets the "resource_type" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableResourceType(s *string) *SkylineDosUpdateOne {
	if s != nil {
		sduo.SetResourceType(*s)
	}
	return sduo
}

// SetVectorTypes sets the "vector_types" field.
func (sduo *SkylineDosUpdateOne) SetVectorTypes(s *[]string) *SkylineDosUpdateOne {
	sduo.mutation.SetVectorTypes(s)
	return sduo
}

// ClearVectorTypes clears the value of the "vector_types" field.
func (sduo *SkylineDosUpdateOne) ClearVectorTypes() *SkylineDosUpdateOne {
	sduo.mutation.ClearVectorTypes()
	return sduo
}

// SetStatus sets the "status" field.
func (sduo *SkylineDosUpdateOne) SetStatus(s string) *SkylineDosUpdateOne {
	sduo.mutation.SetStatus(s)
	return sduo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableStatus(s *string) *SkylineDosUpdateOne {
	if s != nil {
		sduo.SetStatus(*s)
	}
	return sduo
}

// SetAttackID sets the "attack_id" field.
func (sduo *SkylineDosUpdateOne) SetAttackID(s string) *SkylineDosUpdateOne {
	sduo.mutation.SetAttackID(s)
	return sduo
}

// SetNillableAttackID sets the "attack_id" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableAttackID(s *string) *SkylineDosUpdateOne {
	if s != nil {
		sduo.SetAttackID(*s)
	}
	return sduo
}

// SetAttackCounters sets the "attack_counters" field.
func (sduo *SkylineDosUpdateOne) SetAttackCounters(gc *[]gamecloud.AttackCounter) *SkylineDosUpdateOne {
	sduo.mutation.SetAttackCounters(gc)
	return sduo
}

// ClearAttackCounters clears the value of the "attack_counters" field.
func (sduo *SkylineDosUpdateOne) ClearAttackCounters() *SkylineDosUpdateOne {
	sduo.mutation.ClearAttackCounters()
	return sduo
}

// SetProject sets the "project" field.
func (sduo *SkylineDosUpdateOne) SetProject(s string) *SkylineDosUpdateOne {
	sduo.mutation.SetProject(s)
	return sduo
}

// SetNillableProject sets the "project" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableProject(s *string) *SkylineDosUpdateOne {
	if s != nil {
		sduo.SetProject(*s)
	}
	return sduo
}

// SetDurationTime sets the "duration_time" field.
func (sduo *SkylineDosUpdateOne) SetDurationTime(i int64) *SkylineDosUpdateOne {
	sduo.mutation.ResetDurationTime()
	sduo.mutation.SetDurationTime(i)
	return sduo
}

// SetNillableDurationTime sets the "duration_time" field if the given value is not nil.
func (sduo *SkylineDosUpdateOne) SetNillableDurationTime(i *int64) *SkylineDosUpdateOne {
	if i != nil {
		sduo.SetDurationTime(*i)
	}
	return sduo
}

// AddDurationTime adds i to the "duration_time" field.
func (sduo *SkylineDosUpdateOne) AddDurationTime(i int64) *SkylineDosUpdateOne {
	sduo.mutation.AddDurationTime(i)
	return sduo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sduo *SkylineDosUpdateOne) SetTenant(t *Tenant) *SkylineDosUpdateOne {
	return sduo.SetTenantID(t.ID)
}

// Mutation returns the SkylineDosMutation object of the builder.
func (sduo *SkylineDosUpdateOne) Mutation() *SkylineDosMutation {
	return sduo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (sduo *SkylineDosUpdateOne) ClearTenant() *SkylineDosUpdateOne {
	sduo.mutation.ClearTenant()
	return sduo
}

// Where appends a list predicates to the SkylineDosUpdate builder.
func (sduo *SkylineDosUpdateOne) Where(ps ...predicate.SkylineDos) *SkylineDosUpdateOne {
	sduo.mutation.Where(ps...)
	return sduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (sduo *SkylineDosUpdateOne) Select(field string, fields ...string) *SkylineDosUpdateOne {
	sduo.fields = append([]string{field}, fields...)
	return sduo
}

// Save executes the query and returns the updated SkylineDos entity.
func (sduo *SkylineDosUpdateOne) Save(ctx context.Context) (*SkylineDos, error) {
	if err := sduo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sduo.sqlSave, sduo.mutation, sduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sduo *SkylineDosUpdateOne) SaveX(ctx context.Context) *SkylineDos {
	node, err := sduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (sduo *SkylineDosUpdateOne) Exec(ctx context.Context) error {
	_, err := sduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sduo *SkylineDosUpdateOne) ExecX(ctx context.Context) {
	if err := sduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sduo *SkylineDosUpdateOne) defaults() error {
	if _, ok := sduo.mutation.UpdatedAt(); !ok {
		if skylinedos.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized skylinedos.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := skylinedos.UpdateDefaultUpdatedAt()
		sduo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sduo *SkylineDosUpdateOne) check() error {
	if v, ok := sduo.mutation.Remark(); ok {
		if err := skylinedos.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SkylineDos.remark": %w`, err)}
		}
	}
	return nil
}

func (sduo *SkylineDosUpdateOne) sqlSave(ctx context.Context) (_node *SkylineDos, err error) {
	if err := sduo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(skylinedos.Table, skylinedos.Columns, sqlgraph.NewFieldSpec(skylinedos.FieldID, field.TypeInt))
	id, ok := sduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SkylineDos.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := sduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, skylinedos.FieldID)
		for _, f := range fields {
			if !skylinedos.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != skylinedos.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := sduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sduo.mutation.UpdatedAt(); ok {
		_spec.SetField(skylinedos.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := sduo.mutation.Remark(); ok {
		_spec.SetField(skylinedos.FieldRemark, field.TypeString, value)
	}
	if sduo.mutation.RemarkCleared() {
		_spec.ClearField(skylinedos.FieldRemark, field.TypeString)
	}
	if value, ok := sduo.mutation.StartTime(); ok {
		_spec.SetField(skylinedos.FieldStartTime, field.TypeTime, value)
	}
	if value, ok := sduo.mutation.EndTime(); ok {
		_spec.SetField(skylinedos.FieldEndTime, field.TypeTime, value)
	}
	if sduo.mutation.EndTimeCleared() {
		_spec.ClearField(skylinedos.FieldEndTime, field.TypeTime)
	}
	if value, ok := sduo.mutation.Region(); ok {
		_spec.SetField(skylinedos.FieldRegion, field.TypeString, value)
	}
	if value, ok := sduo.mutation.Resource(); ok {
		_spec.SetField(skylinedos.FieldResource, field.TypeString, value)
	}
	if value, ok := sduo.mutation.ResourceType(); ok {
		_spec.SetField(skylinedos.FieldResourceType, field.TypeString, value)
	}
	if value, ok := sduo.mutation.VectorTypes(); ok {
		_spec.SetField(skylinedos.FieldVectorTypes, field.TypeJSON, value)
	}
	if sduo.mutation.VectorTypesCleared() {
		_spec.ClearField(skylinedos.FieldVectorTypes, field.TypeJSON)
	}
	if value, ok := sduo.mutation.Status(); ok {
		_spec.SetField(skylinedos.FieldStatus, field.TypeString, value)
	}
	if value, ok := sduo.mutation.AttackID(); ok {
		_spec.SetField(skylinedos.FieldAttackID, field.TypeString, value)
	}
	if value, ok := sduo.mutation.AttackCounters(); ok {
		_spec.SetField(skylinedos.FieldAttackCounters, field.TypeJSON, value)
	}
	if sduo.mutation.AttackCountersCleared() {
		_spec.ClearField(skylinedos.FieldAttackCounters, field.TypeJSON)
	}
	if value, ok := sduo.mutation.Project(); ok {
		_spec.SetField(skylinedos.FieldProject, field.TypeString, value)
	}
	if value, ok := sduo.mutation.DurationTime(); ok {
		_spec.SetField(skylinedos.FieldDurationTime, field.TypeInt64, value)
	}
	if value, ok := sduo.mutation.AddedDurationTime(); ok {
		_spec.AddField(skylinedos.FieldDurationTime, field.TypeInt64, value)
	}
	if sduo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   skylinedos.TenantTable,
			Columns: []string{skylinedos.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := sduo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   skylinedos.TenantTable,
			Columns: []string{skylinedos.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &SkylineDos{config: sduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, sduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{skylinedos.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	sduo.mutation.done = true
	return _node, nil
}
