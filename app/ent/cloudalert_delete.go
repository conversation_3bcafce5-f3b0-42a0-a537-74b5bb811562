// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"meta/app/ent/cloudalert"
	"meta/app/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudAlertDelete is the builder for deleting a CloudAlert entity.
type CloudAlertDelete struct {
	config
	hooks    []Hook
	mutation *CloudAlertMutation
}

// Where appends a list predicates to the CloudAlertDelete builder.
func (cad *CloudAlertDelete) Where(ps ...predicate.CloudAlert) *CloudAlertDelete {
	cad.mutation.Where(ps...)
	return cad
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (cad *CloudAlertDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, cad.sqlExec, cad.mutation, cad.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (cad *CloudAlertDelete) ExecX(ctx context.Context) int {
	n, err := cad.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (cad *CloudAlertDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(cloudalert.Table, sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt))
	if ps := cad.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, cad.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	cad.mutation.done = true
	return affected, err
}

// CloudAlertDeleteOne is the builder for deleting a single CloudAlert entity.
type CloudAlertDeleteOne struct {
	cad *CloudAlertDelete
}

// Where appends a list predicates to the CloudAlertDelete builder.
func (cado *CloudAlertDeleteOne) Where(ps ...predicate.CloudAlert) *CloudAlertDeleteOne {
	cado.cad.mutation.Where(ps...)
	return cado
}

// Exec executes the deletion query.
func (cado *CloudAlertDeleteOne) Exec(ctx context.Context) error {
	n, err := cado.cad.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{cloudalert.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (cado *CloudAlertDeleteOne) ExecX(ctx context.Context) {
	if err := cado.Exec(ctx); err != nil {
		panic(err)
	}
}
