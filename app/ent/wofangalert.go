// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/tenant"
	"meta/app/ent/wofangalert"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// WofangAlert is the model entity for the WofangAlert schema.
type WofangAlert struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 租户Id，可选
	TenantID *int `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// 攻击状态，1：攻击结束，2：攻击中
	AttackStatus int `json:"attack_status,omitempty" query:"attack_status,omitempty"`
	// 攻击类型
	AttackType *[]string `json:"attack_type,omitempty" query:"attack_type,omitempty"`
	// DeviceIP holds the value of the "device_ip" field.
	DeviceIP string `json:"device_ip,omitempty" query:"device_ip,omitempty"`
	// 防护ip
	ZoneIP string `json:"zone_ip,omitempty" query:"zone_ip,omitempty"`
	// AttackID holds the value of the "attack_id" field.
	AttackID int `json:"attack_id,omitempty" query:"attack_id,omitempty"`
	// 攻击开始时间
	StartTime time.Time `json:"start_time,omitempty"`
	// 攻击结束时间
	EndTime time.Time `json:"end_time,omitempty"`
	// MaxDropBps holds the value of the "max_drop_bps" field.
	MaxDropBps int64 `json:"max_drop_bps,omitempty" query:"max_drop_bps,omitempty"`
	// MaxInBps holds the value of the "max_in_bps" field.
	MaxInBps int64 `json:"max_in_bps,omitempty" query:"max_in_bps,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the WofangAlertQuery when eager-loading is set.
	Edges        WofangAlertEdges `json:"edges"`
	selectValues sql.SelectValues
}

// WofangAlertEdges holds the relations/edges for other nodes in the graph.
type WofangAlertEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e WofangAlertEdges) TenantOrErr() (*Tenant, error) {
	if e.loadedTypes[0] {
		if e.Tenant == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: tenant.Label}
		}
		return e.Tenant, nil
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*WofangAlert) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case wofangalert.FieldAttackType:
			values[i] = new([]byte)
		case wofangalert.FieldID, wofangalert.FieldTenantID, wofangalert.FieldAttackStatus, wofangalert.FieldAttackID, wofangalert.FieldMaxDropBps, wofangalert.FieldMaxInBps:
			values[i] = new(sql.NullInt64)
		case wofangalert.FieldRemark, wofangalert.FieldDeviceIP, wofangalert.FieldZoneIP:
			values[i] = new(sql.NullString)
		case wofangalert.FieldCreatedAt, wofangalert.FieldUpdatedAt, wofangalert.FieldStartTime, wofangalert.FieldEndTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the WofangAlert fields.
func (wa *WofangAlert) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case wofangalert.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			wa.ID = int(value.Int64)
		case wofangalert.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				wa.TenantID = new(int)
				*wa.TenantID = int(value.Int64)
			}
		case wofangalert.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				wa.CreatedAt = value.Time
			}
		case wofangalert.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				wa.UpdatedAt = value.Time
			}
		case wofangalert.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				wa.Remark = new(string)
				*wa.Remark = value.String
			}
		case wofangalert.FieldAttackStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field attack_status", values[i])
			} else if value.Valid {
				wa.AttackStatus = int(value.Int64)
			}
		case wofangalert.FieldAttackType:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field attack_type", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &wa.AttackType); err != nil {
					return fmt.Errorf("unmarshal field attack_type: %w", err)
				}
			}
		case wofangalert.FieldDeviceIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field device_ip", values[i])
			} else if value.Valid {
				wa.DeviceIP = value.String
			}
		case wofangalert.FieldZoneIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field zone_ip", values[i])
			} else if value.Valid {
				wa.ZoneIP = value.String
			}
		case wofangalert.FieldAttackID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field attack_id", values[i])
			} else if value.Valid {
				wa.AttackID = int(value.Int64)
			}
		case wofangalert.FieldStartTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field start_time", values[i])
			} else if value.Valid {
				wa.StartTime = value.Time
			}
		case wofangalert.FieldEndTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field end_time", values[i])
			} else if value.Valid {
				wa.EndTime = value.Time
			}
		case wofangalert.FieldMaxDropBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_drop_bps", values[i])
			} else if value.Valid {
				wa.MaxDropBps = value.Int64
			}
		case wofangalert.FieldMaxInBps:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_in_bps", values[i])
			} else if value.Valid {
				wa.MaxInBps = value.Int64
			}
		default:
			wa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the WofangAlert.
// This includes values selected through modifiers, order, etc.
func (wa *WofangAlert) Value(name string) (ent.Value, error) {
	return wa.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the WofangAlert entity.
func (wa *WofangAlert) QueryTenant() *TenantQuery {
	return NewWofangAlertClient(wa.config).QueryTenant(wa)
}

// Update returns a builder for updating this WofangAlert.
// Note that you need to call WofangAlert.Unwrap() before calling this method if this WofangAlert
// was returned from a transaction, and the transaction was committed or rolled back.
func (wa *WofangAlert) Update() *WofangAlertUpdateOne {
	return NewWofangAlertClient(wa.config).UpdateOne(wa)
}

// Unwrap unwraps the WofangAlert entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (wa *WofangAlert) Unwrap() *WofangAlert {
	_tx, ok := wa.config.driver.(*txDriver)
	if !ok {
		panic("ent: WofangAlert is not a transactional entity")
	}
	wa.config.driver = _tx.drv
	return wa
}

// String implements the fmt.Stringer.
func (wa *WofangAlert) String() string {
	var builder strings.Builder
	builder.WriteString("WofangAlert(")
	builder.WriteString(fmt.Sprintf("id=%v, ", wa.ID))
	if v := wa.TenantID; v != nil {
		builder.WriteString("tenant_id=")
		builder.WriteString(fmt.Sprintf("%v", *v))
	}
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(wa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(wa.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := wa.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("attack_status=")
	builder.WriteString(fmt.Sprintf("%v", wa.AttackStatus))
	builder.WriteString(", ")
	builder.WriteString("attack_type=")
	builder.WriteString(fmt.Sprintf("%v", wa.AttackType))
	builder.WriteString(", ")
	builder.WriteString("device_ip=")
	builder.WriteString(wa.DeviceIP)
	builder.WriteString(", ")
	builder.WriteString("zone_ip=")
	builder.WriteString(wa.ZoneIP)
	builder.WriteString(", ")
	builder.WriteString("attack_id=")
	builder.WriteString(fmt.Sprintf("%v", wa.AttackID))
	builder.WriteString(", ")
	builder.WriteString("start_time=")
	builder.WriteString(wa.StartTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("end_time=")
	builder.WriteString(wa.EndTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("max_drop_bps=")
	builder.WriteString(fmt.Sprintf("%v", wa.MaxDropBps))
	builder.WriteString(", ")
	builder.WriteString("max_in_bps=")
	builder.WriteString(fmt.Sprintf("%v", wa.MaxInBps))
	builder.WriteByte(')')
	return builder.String()
}

// WofangAlerts is a parsable slice of WofangAlert.
type WofangAlerts []*WofangAlert
