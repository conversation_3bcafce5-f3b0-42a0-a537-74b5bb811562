// Code generated by ent, DO NOT EDIT.

package socgroupticket

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the socgroupticket type in the database.
	Label = "soc_group_ticket"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldFollowList holds the string denoting the follow_list field in the database.
	FieldFollowList = "follow_list"
	// FieldDepartmentID holds the string denoting the department_id field in the database.
	FieldDepartmentID = "department_id"
	// FieldIPList holds the string denoting the ip_list field in the database.
	FieldIPList = "ip_list"
	// FieldMinBandwidth holds the string denoting the min_bandwidth field in the database.
	FieldMinBandwidth = "min_bandwidth"
	// FieldDivertType holds the string denoting the divert_type field in the database.
	FieldDivertType = "divert_type"
	// FieldOpType holds the string denoting the op_type field in the database.
	FieldOpType = "op_type"
	// FieldOpTime holds the string denoting the op_time field in the database.
	FieldOpTime = "op_time"
	// FieldConfigType holds the string denoting the config_type field in the database.
	FieldConfigType = "config_type"
	// FieldConfigArgs holds the string denoting the config_args field in the database.
	FieldConfigArgs = "config_args"
	// FieldProductName holds the string denoting the product_name field in the database.
	FieldProductName = "product_name"
	// FieldProductCode holds the string denoting the product_code field in the database.
	FieldProductCode = "product_code"
	// FieldContactList holds the string denoting the contact_list field in the database.
	FieldContactList = "contact_list"
	// FieldGroupTicketID holds the string denoting the group_ticket_id field in the database.
	FieldGroupTicketID = "group_ticket_id"
	// FieldErrorInfo holds the string denoting the error_info field in the database.
	FieldErrorInfo = "error_info"
	// FieldCreateUserID holds the string denoting the create_user_id field in the database.
	FieldCreateUserID = "create_user_id"
	// EdgeTenant holds the string denoting the tenant edge name in mutations.
	EdgeTenant = "tenant"
	// EdgeUser holds the string denoting the user edge name in mutations.
	EdgeUser = "user"
	// Table holds the table name of the socgroupticket in the database.
	Table = "soc_group_tickets"
	// TenantTable is the table that holds the tenant relation/edge.
	TenantTable = "soc_group_tickets"
	// TenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantInverseTable = "tenants"
	// TenantColumn is the table column denoting the tenant relation/edge.
	TenantColumn = "tenant_id"
	// UserTable is the table that holds the user relation/edge.
	UserTable = "soc_group_tickets"
	// UserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UserInverseTable = "users"
	// UserColumn is the table column denoting the user relation/edge.
	UserColumn = "create_user_id"
)

// Columns holds all SQL columns for socgroupticket fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldTenantID,
	FieldRemark,
	FieldName,
	FieldType,
	FieldDescription,
	FieldFollowList,
	FieldDepartmentID,
	FieldIPList,
	FieldMinBandwidth,
	FieldDivertType,
	FieldOpType,
	FieldOpTime,
	FieldConfigType,
	FieldConfigArgs,
	FieldProductName,
	FieldProductCode,
	FieldContactList,
	FieldGroupTicketID,
	FieldErrorInfo,
	FieldCreateUserID,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "meta/app/ent/runtime"
var (
	Hooks  [1]ent.Hook
	Policy ent.Policy
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// RemarkValidator is a validator for the "remark" field. It is called by the builders before save.
	RemarkValidator func(string) error
	// DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	DescriptionValidator func(string) error
	// ConfigArgsValidator is a validator for the "config_args" field. It is called by the builders before save.
	ConfigArgsValidator func(string) error
	// ErrorInfoValidator is a validator for the "error_info" field. It is called by the builders before save.
	ErrorInfoValidator func(string) error
)

// OrderOption defines the ordering options for the SocGroupTicket queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByDepartmentID orders the results by the department_id field.
func ByDepartmentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDepartmentID, opts...).ToFunc()
}

// ByMinBandwidth orders the results by the min_bandwidth field.
func ByMinBandwidth(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMinBandwidth, opts...).ToFunc()
}

// ByDivertType orders the results by the divert_type field.
func ByDivertType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDivertType, opts...).ToFunc()
}

// ByOpType orders the results by the op_type field.
func ByOpType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOpType, opts...).ToFunc()
}

// ByOpTime orders the results by the op_time field.
func ByOpTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOpTime, opts...).ToFunc()
}

// ByConfigType orders the results by the config_type field.
func ByConfigType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfigType, opts...).ToFunc()
}

// ByConfigArgs orders the results by the config_args field.
func ByConfigArgs(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConfigArgs, opts...).ToFunc()
}

// ByProductName orders the results by the product_name field.
func ByProductName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProductName, opts...).ToFunc()
}

// ByProductCode orders the results by the product_code field.
func ByProductCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldProductCode, opts...).ToFunc()
}

// ByGroupTicketID orders the results by the group_ticket_id field.
func ByGroupTicketID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGroupTicketID, opts...).ToFunc()
}

// ByErrorInfo orders the results by the error_info field.
func ByErrorInfo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldErrorInfo, opts...).ToFunc()
}

// ByCreateUserID orders the results by the create_user_id field.
func ByCreateUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreateUserID, opts...).ToFunc()
}

// ByTenantField orders the results by tenant field.
func ByTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantStep(), sql.OrderByField(field, opts...))
	}
}

// ByUserField orders the results by user field.
func ByUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUserStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
	)
}
func newUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
	)
}
