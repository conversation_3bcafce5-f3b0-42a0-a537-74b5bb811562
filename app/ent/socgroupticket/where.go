// Code generated by ent, DO NOT EDIT.

package socgroupticket

import (
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldUpdatedAt, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldTenantID, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldRemark, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldName, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldType, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldDescription, v))
}

// DepartmentID applies equality check predicate on the "department_id" field. It's identical to DepartmentIDEQ.
func DepartmentID(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldDepartmentID, v))
}

// MinBandwidth applies equality check predicate on the "min_bandwidth" field. It's identical to MinBandwidthEQ.
func MinBandwidth(v float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldMinBandwidth, v))
}

// DivertType applies equality check predicate on the "divert_type" field. It's identical to DivertTypeEQ.
func DivertType(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldDivertType, v))
}

// OpType applies equality check predicate on the "op_type" field. It's identical to OpTypeEQ.
func OpType(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldOpType, v))
}

// OpTime applies equality check predicate on the "op_time" field. It's identical to OpTimeEQ.
func OpTime(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldOpTime, v))
}

// ConfigType applies equality check predicate on the "config_type" field. It's identical to ConfigTypeEQ.
func ConfigType(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldConfigType, v))
}

// ConfigArgs applies equality check predicate on the "config_args" field. It's identical to ConfigArgsEQ.
func ConfigArgs(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldConfigArgs, v))
}

// ProductName applies equality check predicate on the "product_name" field. It's identical to ProductNameEQ.
func ProductName(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldProductName, v))
}

// ProductCode applies equality check predicate on the "product_code" field. It's identical to ProductCodeEQ.
func ProductCode(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldProductCode, v))
}

// GroupTicketID applies equality check predicate on the "group_ticket_id" field. It's identical to GroupTicketIDEQ.
func GroupTicketID(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldGroupTicketID, v))
}

// ErrorInfo applies equality check predicate on the "error_info" field. It's identical to ErrorInfoEQ.
func ErrorInfo(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldErrorInfo, v))
}

// CreateUserID applies equality check predicate on the "create_user_id" field. It's identical to CreateUserIDEQ.
func CreateUserID(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldCreateUserID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldUpdatedAt, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldTenantID))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContainsFold(FieldRemark, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContainsFold(FieldName, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldType, v))
}

// TypeContains applies the Contains predicate on the "type" field.
func TypeContains(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContains(FieldType, v))
}

// TypeHasPrefix applies the HasPrefix predicate on the "type" field.
func TypeHasPrefix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasPrefix(FieldType, v))
}

// TypeHasSuffix applies the HasSuffix predicate on the "type" field.
func TypeHasSuffix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasSuffix(FieldType, v))
}

// TypeEqualFold applies the EqualFold predicate on the "type" field.
func TypeEqualFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEqualFold(FieldType, v))
}

// TypeContainsFold applies the ContainsFold predicate on the "type" field.
func TypeContainsFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContainsFold(FieldType, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContainsFold(FieldDescription, v))
}

// FollowListIsNil applies the IsNil predicate on the "follow_list" field.
func FollowListIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldFollowList))
}

// FollowListNotNil applies the NotNil predicate on the "follow_list" field.
func FollowListNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldFollowList))
}

// DepartmentIDEQ applies the EQ predicate on the "department_id" field.
func DepartmentIDEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldDepartmentID, v))
}

// DepartmentIDNEQ applies the NEQ predicate on the "department_id" field.
func DepartmentIDNEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldDepartmentID, v))
}

// DepartmentIDIn applies the In predicate on the "department_id" field.
func DepartmentIDIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldDepartmentID, vs...))
}

// DepartmentIDNotIn applies the NotIn predicate on the "department_id" field.
func DepartmentIDNotIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldDepartmentID, vs...))
}

// DepartmentIDGT applies the GT predicate on the "department_id" field.
func DepartmentIDGT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldDepartmentID, v))
}

// DepartmentIDGTE applies the GTE predicate on the "department_id" field.
func DepartmentIDGTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldDepartmentID, v))
}

// DepartmentIDLT applies the LT predicate on the "department_id" field.
func DepartmentIDLT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldDepartmentID, v))
}

// DepartmentIDLTE applies the LTE predicate on the "department_id" field.
func DepartmentIDLTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldDepartmentID, v))
}

// IPListIsNil applies the IsNil predicate on the "ip_list" field.
func IPListIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldIPList))
}

// IPListNotNil applies the NotNil predicate on the "ip_list" field.
func IPListNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldIPList))
}

// MinBandwidthEQ applies the EQ predicate on the "min_bandwidth" field.
func MinBandwidthEQ(v float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldMinBandwidth, v))
}

// MinBandwidthNEQ applies the NEQ predicate on the "min_bandwidth" field.
func MinBandwidthNEQ(v float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldMinBandwidth, v))
}

// MinBandwidthIn applies the In predicate on the "min_bandwidth" field.
func MinBandwidthIn(vs ...float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldMinBandwidth, vs...))
}

// MinBandwidthNotIn applies the NotIn predicate on the "min_bandwidth" field.
func MinBandwidthNotIn(vs ...float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldMinBandwidth, vs...))
}

// MinBandwidthGT applies the GT predicate on the "min_bandwidth" field.
func MinBandwidthGT(v float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldMinBandwidth, v))
}

// MinBandwidthGTE applies the GTE predicate on the "min_bandwidth" field.
func MinBandwidthGTE(v float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldMinBandwidth, v))
}

// MinBandwidthLT applies the LT predicate on the "min_bandwidth" field.
func MinBandwidthLT(v float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldMinBandwidth, v))
}

// MinBandwidthLTE applies the LTE predicate on the "min_bandwidth" field.
func MinBandwidthLTE(v float32) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldMinBandwidth, v))
}

// DivertTypeEQ applies the EQ predicate on the "divert_type" field.
func DivertTypeEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldDivertType, v))
}

// DivertTypeNEQ applies the NEQ predicate on the "divert_type" field.
func DivertTypeNEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldDivertType, v))
}

// DivertTypeIn applies the In predicate on the "divert_type" field.
func DivertTypeIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldDivertType, vs...))
}

// DivertTypeNotIn applies the NotIn predicate on the "divert_type" field.
func DivertTypeNotIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldDivertType, vs...))
}

// DivertTypeGT applies the GT predicate on the "divert_type" field.
func DivertTypeGT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldDivertType, v))
}

// DivertTypeGTE applies the GTE predicate on the "divert_type" field.
func DivertTypeGTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldDivertType, v))
}

// DivertTypeLT applies the LT predicate on the "divert_type" field.
func DivertTypeLT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldDivertType, v))
}

// DivertTypeLTE applies the LTE predicate on the "divert_type" field.
func DivertTypeLTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldDivertType, v))
}

// OpTypeEQ applies the EQ predicate on the "op_type" field.
func OpTypeEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldOpType, v))
}

// OpTypeNEQ applies the NEQ predicate on the "op_type" field.
func OpTypeNEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldOpType, v))
}

// OpTypeIn applies the In predicate on the "op_type" field.
func OpTypeIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldOpType, vs...))
}

// OpTypeNotIn applies the NotIn predicate on the "op_type" field.
func OpTypeNotIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldOpType, vs...))
}

// OpTypeGT applies the GT predicate on the "op_type" field.
func OpTypeGT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldOpType, v))
}

// OpTypeGTE applies the GTE predicate on the "op_type" field.
func OpTypeGTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldOpType, v))
}

// OpTypeLT applies the LT predicate on the "op_type" field.
func OpTypeLT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldOpType, v))
}

// OpTypeLTE applies the LTE predicate on the "op_type" field.
func OpTypeLTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldOpType, v))
}

// OpTimeEQ applies the EQ predicate on the "op_time" field.
func OpTimeEQ(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldOpTime, v))
}

// OpTimeNEQ applies the NEQ predicate on the "op_time" field.
func OpTimeNEQ(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldOpTime, v))
}

// OpTimeIn applies the In predicate on the "op_time" field.
func OpTimeIn(vs ...time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldOpTime, vs...))
}

// OpTimeNotIn applies the NotIn predicate on the "op_time" field.
func OpTimeNotIn(vs ...time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldOpTime, vs...))
}

// OpTimeGT applies the GT predicate on the "op_time" field.
func OpTimeGT(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldOpTime, v))
}

// OpTimeGTE applies the GTE predicate on the "op_time" field.
func OpTimeGTE(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldOpTime, v))
}

// OpTimeLT applies the LT predicate on the "op_time" field.
func OpTimeLT(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldOpTime, v))
}

// OpTimeLTE applies the LTE predicate on the "op_time" field.
func OpTimeLTE(v time.Time) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldOpTime, v))
}

// OpTimeIsNil applies the IsNil predicate on the "op_time" field.
func OpTimeIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldOpTime))
}

// OpTimeNotNil applies the NotNil predicate on the "op_time" field.
func OpTimeNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldOpTime))
}

// ConfigTypeEQ applies the EQ predicate on the "config_type" field.
func ConfigTypeEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldConfigType, v))
}

// ConfigTypeNEQ applies the NEQ predicate on the "config_type" field.
func ConfigTypeNEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldConfigType, v))
}

// ConfigTypeIn applies the In predicate on the "config_type" field.
func ConfigTypeIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldConfigType, vs...))
}

// ConfigTypeNotIn applies the NotIn predicate on the "config_type" field.
func ConfigTypeNotIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldConfigType, vs...))
}

// ConfigTypeGT applies the GT predicate on the "config_type" field.
func ConfigTypeGT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldConfigType, v))
}

// ConfigTypeGTE applies the GTE predicate on the "config_type" field.
func ConfigTypeGTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldConfigType, v))
}

// ConfigTypeLT applies the LT predicate on the "config_type" field.
func ConfigTypeLT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldConfigType, v))
}

// ConfigTypeLTE applies the LTE predicate on the "config_type" field.
func ConfigTypeLTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldConfigType, v))
}

// ConfigArgsEQ applies the EQ predicate on the "config_args" field.
func ConfigArgsEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldConfigArgs, v))
}

// ConfigArgsNEQ applies the NEQ predicate on the "config_args" field.
func ConfigArgsNEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldConfigArgs, v))
}

// ConfigArgsIn applies the In predicate on the "config_args" field.
func ConfigArgsIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldConfigArgs, vs...))
}

// ConfigArgsNotIn applies the NotIn predicate on the "config_args" field.
func ConfigArgsNotIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldConfigArgs, vs...))
}

// ConfigArgsGT applies the GT predicate on the "config_args" field.
func ConfigArgsGT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldConfigArgs, v))
}

// ConfigArgsGTE applies the GTE predicate on the "config_args" field.
func ConfigArgsGTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldConfigArgs, v))
}

// ConfigArgsLT applies the LT predicate on the "config_args" field.
func ConfigArgsLT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldConfigArgs, v))
}

// ConfigArgsLTE applies the LTE predicate on the "config_args" field.
func ConfigArgsLTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldConfigArgs, v))
}

// ConfigArgsContains applies the Contains predicate on the "config_args" field.
func ConfigArgsContains(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContains(FieldConfigArgs, v))
}

// ConfigArgsHasPrefix applies the HasPrefix predicate on the "config_args" field.
func ConfigArgsHasPrefix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasPrefix(FieldConfigArgs, v))
}

// ConfigArgsHasSuffix applies the HasSuffix predicate on the "config_args" field.
func ConfigArgsHasSuffix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasSuffix(FieldConfigArgs, v))
}

// ConfigArgsEqualFold applies the EqualFold predicate on the "config_args" field.
func ConfigArgsEqualFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEqualFold(FieldConfigArgs, v))
}

// ConfigArgsContainsFold applies the ContainsFold predicate on the "config_args" field.
func ConfigArgsContainsFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContainsFold(FieldConfigArgs, v))
}

// ProductNameEQ applies the EQ predicate on the "product_name" field.
func ProductNameEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldProductName, v))
}

// ProductNameNEQ applies the NEQ predicate on the "product_name" field.
func ProductNameNEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldProductName, v))
}

// ProductNameIn applies the In predicate on the "product_name" field.
func ProductNameIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldProductName, vs...))
}

// ProductNameNotIn applies the NotIn predicate on the "product_name" field.
func ProductNameNotIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldProductName, vs...))
}

// ProductNameGT applies the GT predicate on the "product_name" field.
func ProductNameGT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldProductName, v))
}

// ProductNameGTE applies the GTE predicate on the "product_name" field.
func ProductNameGTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldProductName, v))
}

// ProductNameLT applies the LT predicate on the "product_name" field.
func ProductNameLT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldProductName, v))
}

// ProductNameLTE applies the LTE predicate on the "product_name" field.
func ProductNameLTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldProductName, v))
}

// ProductNameContains applies the Contains predicate on the "product_name" field.
func ProductNameContains(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContains(FieldProductName, v))
}

// ProductNameHasPrefix applies the HasPrefix predicate on the "product_name" field.
func ProductNameHasPrefix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasPrefix(FieldProductName, v))
}

// ProductNameHasSuffix applies the HasSuffix predicate on the "product_name" field.
func ProductNameHasSuffix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasSuffix(FieldProductName, v))
}

// ProductNameEqualFold applies the EqualFold predicate on the "product_name" field.
func ProductNameEqualFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEqualFold(FieldProductName, v))
}

// ProductNameContainsFold applies the ContainsFold predicate on the "product_name" field.
func ProductNameContainsFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContainsFold(FieldProductName, v))
}

// ProductCodeEQ applies the EQ predicate on the "product_code" field.
func ProductCodeEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldProductCode, v))
}

// ProductCodeNEQ applies the NEQ predicate on the "product_code" field.
func ProductCodeNEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldProductCode, v))
}

// ProductCodeIn applies the In predicate on the "product_code" field.
func ProductCodeIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldProductCode, vs...))
}

// ProductCodeNotIn applies the NotIn predicate on the "product_code" field.
func ProductCodeNotIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldProductCode, vs...))
}

// ProductCodeGT applies the GT predicate on the "product_code" field.
func ProductCodeGT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldProductCode, v))
}

// ProductCodeGTE applies the GTE predicate on the "product_code" field.
func ProductCodeGTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldProductCode, v))
}

// ProductCodeLT applies the LT predicate on the "product_code" field.
func ProductCodeLT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldProductCode, v))
}

// ProductCodeLTE applies the LTE predicate on the "product_code" field.
func ProductCodeLTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldProductCode, v))
}

// ProductCodeContains applies the Contains predicate on the "product_code" field.
func ProductCodeContains(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContains(FieldProductCode, v))
}

// ProductCodeHasPrefix applies the HasPrefix predicate on the "product_code" field.
func ProductCodeHasPrefix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasPrefix(FieldProductCode, v))
}

// ProductCodeHasSuffix applies the HasSuffix predicate on the "product_code" field.
func ProductCodeHasSuffix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasSuffix(FieldProductCode, v))
}

// ProductCodeIsNil applies the IsNil predicate on the "product_code" field.
func ProductCodeIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldProductCode))
}

// ProductCodeNotNil applies the NotNil predicate on the "product_code" field.
func ProductCodeNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldProductCode))
}

// ProductCodeEqualFold applies the EqualFold predicate on the "product_code" field.
func ProductCodeEqualFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEqualFold(FieldProductCode, v))
}

// ProductCodeContainsFold applies the ContainsFold predicate on the "product_code" field.
func ProductCodeContainsFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContainsFold(FieldProductCode, v))
}

// ContactListIsNil applies the IsNil predicate on the "contact_list" field.
func ContactListIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldContactList))
}

// ContactListNotNil applies the NotNil predicate on the "contact_list" field.
func ContactListNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldContactList))
}

// GroupTicketIDEQ applies the EQ predicate on the "group_ticket_id" field.
func GroupTicketIDEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldGroupTicketID, v))
}

// GroupTicketIDNEQ applies the NEQ predicate on the "group_ticket_id" field.
func GroupTicketIDNEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldGroupTicketID, v))
}

// GroupTicketIDIn applies the In predicate on the "group_ticket_id" field.
func GroupTicketIDIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldGroupTicketID, vs...))
}

// GroupTicketIDNotIn applies the NotIn predicate on the "group_ticket_id" field.
func GroupTicketIDNotIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldGroupTicketID, vs...))
}

// GroupTicketIDGT applies the GT predicate on the "group_ticket_id" field.
func GroupTicketIDGT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldGroupTicketID, v))
}

// GroupTicketIDGTE applies the GTE predicate on the "group_ticket_id" field.
func GroupTicketIDGTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldGroupTicketID, v))
}

// GroupTicketIDLT applies the LT predicate on the "group_ticket_id" field.
func GroupTicketIDLT(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldGroupTicketID, v))
}

// GroupTicketIDLTE applies the LTE predicate on the "group_ticket_id" field.
func GroupTicketIDLTE(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldGroupTicketID, v))
}

// GroupTicketIDIsNil applies the IsNil predicate on the "group_ticket_id" field.
func GroupTicketIDIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldGroupTicketID))
}

// GroupTicketIDNotNil applies the NotNil predicate on the "group_ticket_id" field.
func GroupTicketIDNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldGroupTicketID))
}

// ErrorInfoEQ applies the EQ predicate on the "error_info" field.
func ErrorInfoEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldErrorInfo, v))
}

// ErrorInfoNEQ applies the NEQ predicate on the "error_info" field.
func ErrorInfoNEQ(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldErrorInfo, v))
}

// ErrorInfoIn applies the In predicate on the "error_info" field.
func ErrorInfoIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldErrorInfo, vs...))
}

// ErrorInfoNotIn applies the NotIn predicate on the "error_info" field.
func ErrorInfoNotIn(vs ...string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldErrorInfo, vs...))
}

// ErrorInfoGT applies the GT predicate on the "error_info" field.
func ErrorInfoGT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGT(FieldErrorInfo, v))
}

// ErrorInfoGTE applies the GTE predicate on the "error_info" field.
func ErrorInfoGTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldGTE(FieldErrorInfo, v))
}

// ErrorInfoLT applies the LT predicate on the "error_info" field.
func ErrorInfoLT(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLT(FieldErrorInfo, v))
}

// ErrorInfoLTE applies the LTE predicate on the "error_info" field.
func ErrorInfoLTE(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldLTE(FieldErrorInfo, v))
}

// ErrorInfoContains applies the Contains predicate on the "error_info" field.
func ErrorInfoContains(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContains(FieldErrorInfo, v))
}

// ErrorInfoHasPrefix applies the HasPrefix predicate on the "error_info" field.
func ErrorInfoHasPrefix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasPrefix(FieldErrorInfo, v))
}

// ErrorInfoHasSuffix applies the HasSuffix predicate on the "error_info" field.
func ErrorInfoHasSuffix(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldHasSuffix(FieldErrorInfo, v))
}

// ErrorInfoIsNil applies the IsNil predicate on the "error_info" field.
func ErrorInfoIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldErrorInfo))
}

// ErrorInfoNotNil applies the NotNil predicate on the "error_info" field.
func ErrorInfoNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldErrorInfo))
}

// ErrorInfoEqualFold applies the EqualFold predicate on the "error_info" field.
func ErrorInfoEqualFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEqualFold(FieldErrorInfo, v))
}

// ErrorInfoContainsFold applies the ContainsFold predicate on the "error_info" field.
func ErrorInfoContainsFold(v string) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldContainsFold(FieldErrorInfo, v))
}

// CreateUserIDEQ applies the EQ predicate on the "create_user_id" field.
func CreateUserIDEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldEQ(FieldCreateUserID, v))
}

// CreateUserIDNEQ applies the NEQ predicate on the "create_user_id" field.
func CreateUserIDNEQ(v int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNEQ(FieldCreateUserID, v))
}

// CreateUserIDIn applies the In predicate on the "create_user_id" field.
func CreateUserIDIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIn(FieldCreateUserID, vs...))
}

// CreateUserIDNotIn applies the NotIn predicate on the "create_user_id" field.
func CreateUserIDNotIn(vs ...int) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotIn(FieldCreateUserID, vs...))
}

// CreateUserIDIsNil applies the IsNil predicate on the "create_user_id" field.
func CreateUserIDIsNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldIsNull(FieldCreateUserID))
}

// CreateUserIDNotNil applies the NotNil predicate on the "create_user_id" field.
func CreateUserIDNotNil() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.FieldNotNull(FieldCreateUserID))
}

// HasTenant applies the HasEdge predicate on the "tenant" edge.
func HasTenant() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantTable, TenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantWith applies the HasEdge predicate on the "tenant" edge with a given conditions (other predicates).
func HasTenantWith(preds ...predicate.Tenant) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(func(s *sql.Selector) {
		step := newTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.SocGroupTicket {
	return predicate.SocGroupTicket(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.SocGroupTicket) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.SocGroupTicket) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.SocGroupTicket) predicate.SocGroupTicket {
	return predicate.SocGroupTicket(sql.NotPredicates(p))
}
