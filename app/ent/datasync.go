// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"meta/app/ent/datasync"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// DataSync is the model entity for the DataSync schema.
type DataSync struct {
	config `json:"-" query:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 备注
	Remark *string `json:"remark,omitempty"`
	// PreDataList holds the value of the "pre_data_list" field.
	PreDataList *[]string `json:"pre_data_list,omitempty" query:"pre_data_list,omitempty"`
	// DataList holds the value of the "data_list" field.
	DataList *[]string `json:"data_list,omitempty" query:"data_list,omitempty"`
	// DataType holds the value of the "data_type" field.
	DataType string `json:"data_type,omitempty" query:"data_type,omitempty"`
	// Type holds the value of the "type" field.
	Type         string `json:"type,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DataSync) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case datasync.FieldPreDataList, datasync.FieldDataList:
			values[i] = new([]byte)
		case datasync.FieldID:
			values[i] = new(sql.NullInt64)
		case datasync.FieldRemark, datasync.FieldDataType, datasync.FieldType:
			values[i] = new(sql.NullString)
		case datasync.FieldCreatedAt, datasync.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DataSync fields.
func (ds *DataSync) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case datasync.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			ds.ID = int(value.Int64)
		case datasync.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ds.CreatedAt = value.Time
			}
		case datasync.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ds.UpdatedAt = value.Time
			}
		case datasync.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				ds.Remark = new(string)
				*ds.Remark = value.String
			}
		case datasync.FieldPreDataList:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field pre_data_list", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ds.PreDataList); err != nil {
					return fmt.Errorf("unmarshal field pre_data_list: %w", err)
				}
			}
		case datasync.FieldDataList:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field data_list", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ds.DataList); err != nil {
					return fmt.Errorf("unmarshal field data_list: %w", err)
				}
			}
		case datasync.FieldDataType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field data_type", values[i])
			} else if value.Valid {
				ds.DataType = value.String
			}
		case datasync.FieldType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				ds.Type = value.String
			}
		default:
			ds.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DataSync.
// This includes values selected through modifiers, order, etc.
func (ds *DataSync) Value(name string) (ent.Value, error) {
	return ds.selectValues.Get(name)
}

// Update returns a builder for updating this DataSync.
// Note that you need to call DataSync.Unwrap() before calling this method if this DataSync
// was returned from a transaction, and the transaction was committed or rolled back.
func (ds *DataSync) Update() *DataSyncUpdateOne {
	return NewDataSyncClient(ds.config).UpdateOne(ds)
}

// Unwrap unwraps the DataSync entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ds *DataSync) Unwrap() *DataSync {
	_tx, ok := ds.config.driver.(*txDriver)
	if !ok {
		panic("ent: DataSync is not a transactional entity")
	}
	ds.config.driver = _tx.drv
	return ds
}

// String implements the fmt.Stringer.
func (ds *DataSync) String() string {
	var builder strings.Builder
	builder.WriteString("DataSync(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ds.ID))
	builder.WriteString("created_at=")
	builder.WriteString(ds.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ds.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	if v := ds.Remark; v != nil {
		builder.WriteString("remark=")
		builder.WriteString(*v)
	}
	builder.WriteString(", ")
	builder.WriteString("pre_data_list=")
	builder.WriteString(fmt.Sprintf("%v", ds.PreDataList))
	builder.WriteString(", ")
	builder.WriteString("data_list=")
	builder.WriteString(fmt.Sprintf("%v", ds.DataList))
	builder.WriteString(", ")
	builder.WriteString("data_type=")
	builder.WriteString(ds.DataType)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(ds.Type)
	builder.WriteByte(')')
	return builder.String()
}

// DataSyncs is a parsable slice of DataSync.
type DataSyncs []*DataSync
