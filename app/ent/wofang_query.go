// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/predicate"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/tenant"
	"meta/app/ent/user"
	"meta/app/ent/wofang"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// WofangQuery is the builder for querying Wofang entities.
type WofangQuery struct {
	config
	ctx                      *QueryContext
	order                    []wofang.OrderOption
	inters                   []Interceptor
	predicates               []predicate.Wofang
	withTenant               *TenantQuery
	withUser                 *UserQuery
	withSpectrumAlerts       *SpectrumAlertQuery
	withMatrixSpectrumAlerts *MatrixSpectrumAlertQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the WofangQuery builder.
func (wq *WofangQuery) Where(ps ...predicate.Wofang) *WofangQuery {
	wq.predicates = append(wq.predicates, ps...)
	return wq
}

// Limit the number of records to be returned by this query.
func (wq *WofangQuery) Limit(limit int) *WofangQuery {
	wq.ctx.Limit = &limit
	return wq
}

// Offset to start from.
func (wq *WofangQuery) Offset(offset int) *WofangQuery {
	wq.ctx.Offset = &offset
	return wq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (wq *WofangQuery) Unique(unique bool) *WofangQuery {
	wq.ctx.Unique = &unique
	return wq
}

// Order specifies how the records should be ordered.
func (wq *WofangQuery) Order(o ...wofang.OrderOption) *WofangQuery {
	wq.order = append(wq.order, o...)
	return wq
}

// QueryTenant chains the current query on the "tenant" edge.
func (wq *WofangQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: wq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := wq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := wq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(wofang.Table, wofang.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, wofang.TenantTable, wofang.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(wq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUser chains the current query on the "user" edge.
func (wq *WofangQuery) QueryUser() *UserQuery {
	query := (&UserClient{config: wq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := wq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := wq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(wofang.Table, wofang.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, wofang.UserTable, wofang.UserColumn),
		)
		fromU = sqlgraph.SetNeighbors(wq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QuerySpectrumAlerts chains the current query on the "spectrum_alerts" edge.
func (wq *WofangQuery) QuerySpectrumAlerts() *SpectrumAlertQuery {
	query := (&SpectrumAlertClient{config: wq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := wq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := wq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(wofang.Table, wofang.FieldID, selector),
			sqlgraph.To(spectrumalert.Table, spectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, wofang.SpectrumAlertsTable, wofang.SpectrumAlertsColumn),
		)
		fromU = sqlgraph.SetNeighbors(wq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryMatrixSpectrumAlerts chains the current query on the "matrix_spectrum_alerts" edge.
func (wq *WofangQuery) QueryMatrixSpectrumAlerts() *MatrixSpectrumAlertQuery {
	query := (&MatrixSpectrumAlertClient{config: wq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := wq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := wq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(wofang.Table, wofang.FieldID, selector),
			sqlgraph.To(matrixspectrumalert.Table, matrixspectrumalert.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, wofang.MatrixSpectrumAlertsTable, wofang.MatrixSpectrumAlertsColumn),
		)
		fromU = sqlgraph.SetNeighbors(wq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Wofang entity from the query.
// Returns a *NotFoundError when no Wofang was found.
func (wq *WofangQuery) First(ctx context.Context) (*Wofang, error) {
	nodes, err := wq.Limit(1).All(setContextOp(ctx, wq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{wofang.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (wq *WofangQuery) FirstX(ctx context.Context) *Wofang {
	node, err := wq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Wofang ID from the query.
// Returns a *NotFoundError when no Wofang ID was found.
func (wq *WofangQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = wq.Limit(1).IDs(setContextOp(ctx, wq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{wofang.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (wq *WofangQuery) FirstIDX(ctx context.Context) int {
	id, err := wq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Wofang entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Wofang entity is found.
// Returns a *NotFoundError when no Wofang entities are found.
func (wq *WofangQuery) Only(ctx context.Context) (*Wofang, error) {
	nodes, err := wq.Limit(2).All(setContextOp(ctx, wq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{wofang.Label}
	default:
		return nil, &NotSingularError{wofang.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (wq *WofangQuery) OnlyX(ctx context.Context) *Wofang {
	node, err := wq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Wofang ID in the query.
// Returns a *NotSingularError when more than one Wofang ID is found.
// Returns a *NotFoundError when no entities are found.
func (wq *WofangQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = wq.Limit(2).IDs(setContextOp(ctx, wq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{wofang.Label}
	default:
		err = &NotSingularError{wofang.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (wq *WofangQuery) OnlyIDX(ctx context.Context) int {
	id, err := wq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Wofangs.
func (wq *WofangQuery) All(ctx context.Context) ([]*Wofang, error) {
	ctx = setContextOp(ctx, wq.ctx, "All")
	if err := wq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Wofang, *WofangQuery]()
	return withInterceptors[[]*Wofang](ctx, wq, qr, wq.inters)
}

// AllX is like All, but panics if an error occurs.
func (wq *WofangQuery) AllX(ctx context.Context) []*Wofang {
	nodes, err := wq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Wofang IDs.
func (wq *WofangQuery) IDs(ctx context.Context) (ids []int, err error) {
	if wq.ctx.Unique == nil && wq.path != nil {
		wq.Unique(true)
	}
	ctx = setContextOp(ctx, wq.ctx, "IDs")
	if err = wq.Select(wofang.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (wq *WofangQuery) IDsX(ctx context.Context) []int {
	ids, err := wq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (wq *WofangQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, wq.ctx, "Count")
	if err := wq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, wq, querierCount[*WofangQuery](), wq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (wq *WofangQuery) CountX(ctx context.Context) int {
	count, err := wq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (wq *WofangQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, wq.ctx, "Exist")
	switch _, err := wq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (wq *WofangQuery) ExistX(ctx context.Context) bool {
	exist, err := wq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the WofangQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (wq *WofangQuery) Clone() *WofangQuery {
	if wq == nil {
		return nil
	}
	return &WofangQuery{
		config:                   wq.config,
		ctx:                      wq.ctx.Clone(),
		order:                    append([]wofang.OrderOption{}, wq.order...),
		inters:                   append([]Interceptor{}, wq.inters...),
		predicates:               append([]predicate.Wofang{}, wq.predicates...),
		withTenant:               wq.withTenant.Clone(),
		withUser:                 wq.withUser.Clone(),
		withSpectrumAlerts:       wq.withSpectrumAlerts.Clone(),
		withMatrixSpectrumAlerts: wq.withMatrixSpectrumAlerts.Clone(),
		// clone intermediate query.
		sql:  wq.sql.Clone(),
		path: wq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (wq *WofangQuery) WithTenant(opts ...func(*TenantQuery)) *WofangQuery {
	query := (&TenantClient{config: wq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	wq.withTenant = query
	return wq
}

// WithUser tells the query-builder to eager-load the nodes that are connected to
// the "user" edge. The optional arguments are used to configure the query builder of the edge.
func (wq *WofangQuery) WithUser(opts ...func(*UserQuery)) *WofangQuery {
	query := (&UserClient{config: wq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	wq.withUser = query
	return wq
}

// WithSpectrumAlerts tells the query-builder to eager-load the nodes that are connected to
// the "spectrum_alerts" edge. The optional arguments are used to configure the query builder of the edge.
func (wq *WofangQuery) WithSpectrumAlerts(opts ...func(*SpectrumAlertQuery)) *WofangQuery {
	query := (&SpectrumAlertClient{config: wq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	wq.withSpectrumAlerts = query
	return wq
}

// WithMatrixSpectrumAlerts tells the query-builder to eager-load the nodes that are connected to
// the "matrix_spectrum_alerts" edge. The optional arguments are used to configure the query builder of the edge.
func (wq *WofangQuery) WithMatrixSpectrumAlerts(opts ...func(*MatrixSpectrumAlertQuery)) *WofangQuery {
	query := (&MatrixSpectrumAlertClient{config: wq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	wq.withMatrixSpectrumAlerts = query
	return wq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Wofang.Query().
//		GroupBy(wofang.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (wq *WofangQuery) GroupBy(field string, fields ...string) *WofangGroupBy {
	wq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &WofangGroupBy{build: wq}
	grbuild.flds = &wq.ctx.Fields
	grbuild.label = wofang.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.Wofang.Query().
//		Select(wofang.FieldCreatedAt).
//		Scan(ctx, &v)
func (wq *WofangQuery) Select(fields ...string) *WofangSelect {
	wq.ctx.Fields = append(wq.ctx.Fields, fields...)
	sbuild := &WofangSelect{WofangQuery: wq}
	sbuild.label = wofang.Label
	sbuild.flds, sbuild.scan = &wq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a WofangSelect configured with the given aggregations.
func (wq *WofangQuery) Aggregate(fns ...AggregateFunc) *WofangSelect {
	return wq.Select().Aggregate(fns...)
}

func (wq *WofangQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range wq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, wq); err != nil {
				return err
			}
		}
	}
	for _, f := range wq.ctx.Fields {
		if !wofang.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if wq.path != nil {
		prev, err := wq.path(ctx)
		if err != nil {
			return err
		}
		wq.sql = prev
	}
	if wofang.Policy == nil {
		return errors.New("ent: uninitialized wofang.Policy (forgotten import ent/runtime?)")
	}
	if err := wofang.Policy.EvalQuery(ctx, wq); err != nil {
		return err
	}
	return nil
}

func (wq *WofangQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Wofang, error) {
	var (
		nodes       = []*Wofang{}
		_spec       = wq.querySpec()
		loadedTypes = [4]bool{
			wq.withTenant != nil,
			wq.withUser != nil,
			wq.withSpectrumAlerts != nil,
			wq.withMatrixSpectrumAlerts != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Wofang).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Wofang{config: wq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, wq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := wq.withTenant; query != nil {
		if err := wq.loadTenant(ctx, query, nodes, nil,
			func(n *Wofang, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := wq.withUser; query != nil {
		if err := wq.loadUser(ctx, query, nodes, nil,
			func(n *Wofang, e *User) { n.Edges.User = e }); err != nil {
			return nil, err
		}
	}
	if query := wq.withSpectrumAlerts; query != nil {
		if err := wq.loadSpectrumAlerts(ctx, query, nodes,
			func(n *Wofang) { n.Edges.SpectrumAlerts = []*SpectrumAlert{} },
			func(n *Wofang, e *SpectrumAlert) { n.Edges.SpectrumAlerts = append(n.Edges.SpectrumAlerts, e) }); err != nil {
			return nil, err
		}
	}
	if query := wq.withMatrixSpectrumAlerts; query != nil {
		if err := wq.loadMatrixSpectrumAlerts(ctx, query, nodes,
			func(n *Wofang) { n.Edges.MatrixSpectrumAlerts = []*MatrixSpectrumAlert{} },
			func(n *Wofang, e *MatrixSpectrumAlert) {
				n.Edges.MatrixSpectrumAlerts = append(n.Edges.MatrixSpectrumAlerts, e)
			}); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (wq *WofangQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*Wofang, init func(*Wofang), assign func(*Wofang, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Wofang)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (wq *WofangQuery) loadUser(ctx context.Context, query *UserQuery, nodes []*Wofang, init func(*Wofang), assign func(*Wofang, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Wofang)
	for i := range nodes {
		if nodes[i].CreateUserID == nil {
			continue
		}
		fk := *nodes[i].CreateUserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "create_user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (wq *WofangQuery) loadSpectrumAlerts(ctx context.Context, query *SpectrumAlertQuery, nodes []*Wofang, init func(*Wofang), assign func(*Wofang, *SpectrumAlert)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Wofang)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(spectrumalert.FieldWofangID)
	}
	query.Where(predicate.SpectrumAlert(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(wofang.SpectrumAlertsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.WofangID
		if fk == nil {
			return fmt.Errorf(`foreign-key "wofang_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "wofang_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (wq *WofangQuery) loadMatrixSpectrumAlerts(ctx context.Context, query *MatrixSpectrumAlertQuery, nodes []*Wofang, init func(*Wofang), assign func(*Wofang, *MatrixSpectrumAlert)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*Wofang)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(matrixspectrumalert.FieldWofangID)
	}
	query.Where(predicate.MatrixSpectrumAlert(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(wofang.MatrixSpectrumAlertsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.WofangID
		if fk == nil {
			return fmt.Errorf(`foreign-key "wofang_id" is nil for node %v`, n.ID)
		}
		node, ok := nodeids[*fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "wofang_id" returned %v for node %v`, *fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (wq *WofangQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := wq.querySpec()
	_spec.Node.Columns = wq.ctx.Fields
	if len(wq.ctx.Fields) > 0 {
		_spec.Unique = wq.ctx.Unique != nil && *wq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, wq.driver, _spec)
}

func (wq *WofangQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(wofang.Table, wofang.Columns, sqlgraph.NewFieldSpec(wofang.FieldID, field.TypeInt))
	_spec.From = wq.sql
	if unique := wq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if wq.path != nil {
		_spec.Unique = true
	}
	if fields := wq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, wofang.FieldID)
		for i := range fields {
			if fields[i] != wofang.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if wq.withTenant != nil {
			_spec.Node.AddColumnOnce(wofang.FieldTenantID)
		}
		if wq.withUser != nil {
			_spec.Node.AddColumnOnce(wofang.FieldCreateUserID)
		}
	}
	if ps := wq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := wq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := wq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := wq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (wq *WofangQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(wq.driver.Dialect())
	t1 := builder.Table(wofang.Table)
	columns := wq.ctx.Fields
	if len(columns) == 0 {
		columns = wofang.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if wq.sql != nil {
		selector = wq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if wq.ctx.Unique != nil && *wq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range wq.predicates {
		p(selector)
	}
	for _, p := range wq.order {
		p(selector)
	}
	if offset := wq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := wq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WofangGroupBy is the group-by builder for Wofang entities.
type WofangGroupBy struct {
	selector
	build *WofangQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (wgb *WofangGroupBy) Aggregate(fns ...AggregateFunc) *WofangGroupBy {
	wgb.fns = append(wgb.fns, fns...)
	return wgb
}

// Scan applies the selector query and scans the result into the given value.
func (wgb *WofangGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, wgb.build.ctx, "GroupBy")
	if err := wgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WofangQuery, *WofangGroupBy](ctx, wgb.build, wgb, wgb.build.inters, v)
}

func (wgb *WofangGroupBy) sqlScan(ctx context.Context, root *WofangQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(wgb.fns))
	for _, fn := range wgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*wgb.flds)+len(wgb.fns))
		for _, f := range *wgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*wgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := wgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// WofangSelect is the builder for selecting fields of Wofang entities.
type WofangSelect struct {
	*WofangQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ws *WofangSelect) Aggregate(fns ...AggregateFunc) *WofangSelect {
	ws.fns = append(ws.fns, fns...)
	return ws
}

// Scan applies the selector query and scans the result into the given value.
func (ws *WofangSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ws.ctx, "Select")
	if err := ws.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*WofangQuery, *WofangSelect](ctx, ws.WofangQuery, ws, ws.inters, v)
}

func (ws *WofangSelect) sqlScan(ctx context.Context, root *WofangQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ws.fns))
	for _, fn := range ws.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ws.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ws.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
