// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudFlowDataQuery is the builder for querying CloudFlowData entities.
type CloudFlowDataQuery struct {
	config
	ctx            *QueryContext
	order          []cloudflowdata.OrderOption
	inters         []Interceptor
	predicates     []predicate.CloudFlowData
	withTenant     *TenantQuery
	withCloudAlert *CloudAlertQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the CloudFlowDataQuery builder.
func (cfdq *CloudFlowDataQuery) Where(ps ...predicate.CloudFlowData) *CloudFlowDataQuery {
	cfdq.predicates = append(cfdq.predicates, ps...)
	return cfdq
}

// Limit the number of records to be returned by this query.
func (cfdq *CloudFlowDataQuery) Limit(limit int) *CloudFlowDataQuery {
	cfdq.ctx.Limit = &limit
	return cfdq
}

// Offset to start from.
func (cfdq *CloudFlowDataQuery) Offset(offset int) *CloudFlowDataQuery {
	cfdq.ctx.Offset = &offset
	return cfdq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (cfdq *CloudFlowDataQuery) Unique(unique bool) *CloudFlowDataQuery {
	cfdq.ctx.Unique = &unique
	return cfdq
}

// Order specifies how the records should be ordered.
func (cfdq *CloudFlowDataQuery) Order(o ...cloudflowdata.OrderOption) *CloudFlowDataQuery {
	cfdq.order = append(cfdq.order, o...)
	return cfdq
}

// QueryTenant chains the current query on the "tenant" edge.
func (cfdq *CloudFlowDataQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: cfdq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := cfdq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := cfdq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudflowdata.Table, cloudflowdata.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, cloudflowdata.TenantTable, cloudflowdata.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(cfdq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryCloudAlert chains the current query on the "cloud_alert" edge.
func (cfdq *CloudFlowDataQuery) QueryCloudAlert() *CloudAlertQuery {
	query := (&CloudAlertClient{config: cfdq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := cfdq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := cfdq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(cloudflowdata.Table, cloudflowdata.FieldID, selector),
			sqlgraph.To(cloudalert.Table, cloudalert.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, cloudflowdata.CloudAlertTable, cloudflowdata.CloudAlertColumn),
		)
		fromU = sqlgraph.SetNeighbors(cfdq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first CloudFlowData entity from the query.
// Returns a *NotFoundError when no CloudFlowData was found.
func (cfdq *CloudFlowDataQuery) First(ctx context.Context) (*CloudFlowData, error) {
	nodes, err := cfdq.Limit(1).All(setContextOp(ctx, cfdq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{cloudflowdata.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (cfdq *CloudFlowDataQuery) FirstX(ctx context.Context) *CloudFlowData {
	node, err := cfdq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first CloudFlowData ID from the query.
// Returns a *NotFoundError when no CloudFlowData ID was found.
func (cfdq *CloudFlowDataQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = cfdq.Limit(1).IDs(setContextOp(ctx, cfdq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{cloudflowdata.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (cfdq *CloudFlowDataQuery) FirstIDX(ctx context.Context) int {
	id, err := cfdq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single CloudFlowData entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one CloudFlowData entity is found.
// Returns a *NotFoundError when no CloudFlowData entities are found.
func (cfdq *CloudFlowDataQuery) Only(ctx context.Context) (*CloudFlowData, error) {
	nodes, err := cfdq.Limit(2).All(setContextOp(ctx, cfdq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{cloudflowdata.Label}
	default:
		return nil, &NotSingularError{cloudflowdata.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (cfdq *CloudFlowDataQuery) OnlyX(ctx context.Context) *CloudFlowData {
	node, err := cfdq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only CloudFlowData ID in the query.
// Returns a *NotSingularError when more than one CloudFlowData ID is found.
// Returns a *NotFoundError when no entities are found.
func (cfdq *CloudFlowDataQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = cfdq.Limit(2).IDs(setContextOp(ctx, cfdq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{cloudflowdata.Label}
	default:
		err = &NotSingularError{cloudflowdata.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (cfdq *CloudFlowDataQuery) OnlyIDX(ctx context.Context) int {
	id, err := cfdq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of CloudFlowDataSlice.
func (cfdq *CloudFlowDataQuery) All(ctx context.Context) ([]*CloudFlowData, error) {
	ctx = setContextOp(ctx, cfdq.ctx, "All")
	if err := cfdq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*CloudFlowData, *CloudFlowDataQuery]()
	return withInterceptors[[]*CloudFlowData](ctx, cfdq, qr, cfdq.inters)
}

// AllX is like All, but panics if an error occurs.
func (cfdq *CloudFlowDataQuery) AllX(ctx context.Context) []*CloudFlowData {
	nodes, err := cfdq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of CloudFlowData IDs.
func (cfdq *CloudFlowDataQuery) IDs(ctx context.Context) (ids []int, err error) {
	if cfdq.ctx.Unique == nil && cfdq.path != nil {
		cfdq.Unique(true)
	}
	ctx = setContextOp(ctx, cfdq.ctx, "IDs")
	if err = cfdq.Select(cloudflowdata.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (cfdq *CloudFlowDataQuery) IDsX(ctx context.Context) []int {
	ids, err := cfdq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (cfdq *CloudFlowDataQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, cfdq.ctx, "Count")
	if err := cfdq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, cfdq, querierCount[*CloudFlowDataQuery](), cfdq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (cfdq *CloudFlowDataQuery) CountX(ctx context.Context) int {
	count, err := cfdq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (cfdq *CloudFlowDataQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, cfdq.ctx, "Exist")
	switch _, err := cfdq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (cfdq *CloudFlowDataQuery) ExistX(ctx context.Context) bool {
	exist, err := cfdq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the CloudFlowDataQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (cfdq *CloudFlowDataQuery) Clone() *CloudFlowDataQuery {
	if cfdq == nil {
		return nil
	}
	return &CloudFlowDataQuery{
		config:         cfdq.config,
		ctx:            cfdq.ctx.Clone(),
		order:          append([]cloudflowdata.OrderOption{}, cfdq.order...),
		inters:         append([]Interceptor{}, cfdq.inters...),
		predicates:     append([]predicate.CloudFlowData{}, cfdq.predicates...),
		withTenant:     cfdq.withTenant.Clone(),
		withCloudAlert: cfdq.withCloudAlert.Clone(),
		// clone intermediate query.
		sql:  cfdq.sql.Clone(),
		path: cfdq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (cfdq *CloudFlowDataQuery) WithTenant(opts ...func(*TenantQuery)) *CloudFlowDataQuery {
	query := (&TenantClient{config: cfdq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	cfdq.withTenant = query
	return cfdq
}

// WithCloudAlert tells the query-builder to eager-load the nodes that are connected to
// the "cloud_alert" edge. The optional arguments are used to configure the query builder of the edge.
func (cfdq *CloudFlowDataQuery) WithCloudAlert(opts ...func(*CloudAlertQuery)) *CloudFlowDataQuery {
	query := (&CloudAlertClient{config: cfdq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	cfdq.withCloudAlert = query
	return cfdq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.CloudFlowData.Query().
//		GroupBy(cloudflowdata.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (cfdq *CloudFlowDataQuery) GroupBy(field string, fields ...string) *CloudFlowDataGroupBy {
	cfdq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &CloudFlowDataGroupBy{build: cfdq}
	grbuild.flds = &cfdq.ctx.Fields
	grbuild.label = cloudflowdata.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int `json:"tenant_id,omitempty"`
//	}
//
//	client.CloudFlowData.Query().
//		Select(cloudflowdata.FieldTenantID).
//		Scan(ctx, &v)
func (cfdq *CloudFlowDataQuery) Select(fields ...string) *CloudFlowDataSelect {
	cfdq.ctx.Fields = append(cfdq.ctx.Fields, fields...)
	sbuild := &CloudFlowDataSelect{CloudFlowDataQuery: cfdq}
	sbuild.label = cloudflowdata.Label
	sbuild.flds, sbuild.scan = &cfdq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a CloudFlowDataSelect configured with the given aggregations.
func (cfdq *CloudFlowDataQuery) Aggregate(fns ...AggregateFunc) *CloudFlowDataSelect {
	return cfdq.Select().Aggregate(fns...)
}

func (cfdq *CloudFlowDataQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range cfdq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, cfdq); err != nil {
				return err
			}
		}
	}
	for _, f := range cfdq.ctx.Fields {
		if !cloudflowdata.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if cfdq.path != nil {
		prev, err := cfdq.path(ctx)
		if err != nil {
			return err
		}
		cfdq.sql = prev
	}
	if cloudflowdata.Policy == nil {
		return errors.New("ent: uninitialized cloudflowdata.Policy (forgotten import ent/runtime?)")
	}
	if err := cloudflowdata.Policy.EvalQuery(ctx, cfdq); err != nil {
		return err
	}
	return nil
}

func (cfdq *CloudFlowDataQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*CloudFlowData, error) {
	var (
		nodes       = []*CloudFlowData{}
		_spec       = cfdq.querySpec()
		loadedTypes = [2]bool{
			cfdq.withTenant != nil,
			cfdq.withCloudAlert != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*CloudFlowData).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &CloudFlowData{config: cfdq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, cfdq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := cfdq.withTenant; query != nil {
		if err := cfdq.loadTenant(ctx, query, nodes, nil,
			func(n *CloudFlowData, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := cfdq.withCloudAlert; query != nil {
		if err := cfdq.loadCloudAlert(ctx, query, nodes, nil,
			func(n *CloudFlowData, e *CloudAlert) { n.Edges.CloudAlert = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (cfdq *CloudFlowDataQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*CloudFlowData, init func(*CloudFlowData), assign func(*CloudFlowData, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*CloudFlowData)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (cfdq *CloudFlowDataQuery) loadCloudAlert(ctx context.Context, query *CloudAlertQuery, nodes []*CloudFlowData, init func(*CloudFlowData), assign func(*CloudFlowData, *CloudAlert)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*CloudFlowData)
	for i := range nodes {
		if nodes[i].CloudAlertID == nil {
			continue
		}
		fk := *nodes[i].CloudAlertID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(cloudalert.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "cloud_alert_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (cfdq *CloudFlowDataQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := cfdq.querySpec()
	_spec.Node.Columns = cfdq.ctx.Fields
	if len(cfdq.ctx.Fields) > 0 {
		_spec.Unique = cfdq.ctx.Unique != nil && *cfdq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, cfdq.driver, _spec)
}

func (cfdq *CloudFlowDataQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(cloudflowdata.Table, cloudflowdata.Columns, sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt))
	_spec.From = cfdq.sql
	if unique := cfdq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if cfdq.path != nil {
		_spec.Unique = true
	}
	if fields := cfdq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, cloudflowdata.FieldID)
		for i := range fields {
			if fields[i] != cloudflowdata.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if cfdq.withTenant != nil {
			_spec.Node.AddColumnOnce(cloudflowdata.FieldTenantID)
		}
		if cfdq.withCloudAlert != nil {
			_spec.Node.AddColumnOnce(cloudflowdata.FieldCloudAlertID)
		}
	}
	if ps := cfdq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := cfdq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := cfdq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := cfdq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (cfdq *CloudFlowDataQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(cfdq.driver.Dialect())
	t1 := builder.Table(cloudflowdata.Table)
	columns := cfdq.ctx.Fields
	if len(columns) == 0 {
		columns = cloudflowdata.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if cfdq.sql != nil {
		selector = cfdq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if cfdq.ctx.Unique != nil && *cfdq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range cfdq.predicates {
		p(selector)
	}
	for _, p := range cfdq.order {
		p(selector)
	}
	if offset := cfdq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := cfdq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// CloudFlowDataGroupBy is the group-by builder for CloudFlowData entities.
type CloudFlowDataGroupBy struct {
	selector
	build *CloudFlowDataQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (cfdgb *CloudFlowDataGroupBy) Aggregate(fns ...AggregateFunc) *CloudFlowDataGroupBy {
	cfdgb.fns = append(cfdgb.fns, fns...)
	return cfdgb
}

// Scan applies the selector query and scans the result into the given value.
func (cfdgb *CloudFlowDataGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cfdgb.build.ctx, "GroupBy")
	if err := cfdgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CloudFlowDataQuery, *CloudFlowDataGroupBy](ctx, cfdgb.build, cfdgb, cfdgb.build.inters, v)
}

func (cfdgb *CloudFlowDataGroupBy) sqlScan(ctx context.Context, root *CloudFlowDataQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(cfdgb.fns))
	for _, fn := range cfdgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*cfdgb.flds)+len(cfdgb.fns))
		for _, f := range *cfdgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*cfdgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cfdgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// CloudFlowDataSelect is the builder for selecting fields of CloudFlowData entities.
type CloudFlowDataSelect struct {
	*CloudFlowDataQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cfds *CloudFlowDataSelect) Aggregate(fns ...AggregateFunc) *CloudFlowDataSelect {
	cfds.fns = append(cfds.fns, fns...)
	return cfds
}

// Scan applies the selector query and scans the result into the given value.
func (cfds *CloudFlowDataSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cfds.ctx, "Select")
	if err := cfds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*CloudFlowDataQuery, *CloudFlowDataSelect](ctx, cfds.CloudFlowDataQuery, cfds, cfds.inters, v)
}

func (cfds *CloudFlowDataSelect) sqlScan(ctx context.Context, root *CloudFlowDataQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cfds.fns))
	for _, fn := range cfds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cfds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cfds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
