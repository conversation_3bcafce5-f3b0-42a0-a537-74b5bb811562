// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"math"
	"meta/app/ent/notify"
	"meta/app/ent/predicate"
	"meta/app/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// NotifyQuery is the builder for querying Notify entities.
type NotifyQuery struct {
	config
	ctx        *QueryContext
	order      []notify.OrderOption
	inters     []Interceptor
	predicates []predicate.Notify
	withTenant *TenantQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the NotifyQuery builder.
func (nq *NotifyQuery) Where(ps ...predicate.Notify) *NotifyQuery {
	nq.predicates = append(nq.predicates, ps...)
	return nq
}

// Limit the number of records to be returned by this query.
func (nq *NotifyQuery) Limit(limit int) *NotifyQuery {
	nq.ctx.Limit = &limit
	return nq
}

// Offset to start from.
func (nq *NotifyQuery) Offset(offset int) *NotifyQuery {
	nq.ctx.Offset = &offset
	return nq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (nq *NotifyQuery) Unique(unique bool) *NotifyQuery {
	nq.ctx.Unique = &unique
	return nq
}

// Order specifies how the records should be ordered.
func (nq *NotifyQuery) Order(o ...notify.OrderOption) *NotifyQuery {
	nq.order = append(nq.order, o...)
	return nq
}

// QueryTenant chains the current query on the "tenant" edge.
func (nq *NotifyQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: nq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := nq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := nq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(notify.Table, notify.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, notify.TenantTable, notify.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(nq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Notify entity from the query.
// Returns a *NotFoundError when no Notify was found.
func (nq *NotifyQuery) First(ctx context.Context) (*Notify, error) {
	nodes, err := nq.Limit(1).All(setContextOp(ctx, nq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{notify.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (nq *NotifyQuery) FirstX(ctx context.Context) *Notify {
	node, err := nq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Notify ID from the query.
// Returns a *NotFoundError when no Notify ID was found.
func (nq *NotifyQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = nq.Limit(1).IDs(setContextOp(ctx, nq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{notify.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (nq *NotifyQuery) FirstIDX(ctx context.Context) int {
	id, err := nq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Notify entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Notify entity is found.
// Returns a *NotFoundError when no Notify entities are found.
func (nq *NotifyQuery) Only(ctx context.Context) (*Notify, error) {
	nodes, err := nq.Limit(2).All(setContextOp(ctx, nq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{notify.Label}
	default:
		return nil, &NotSingularError{notify.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (nq *NotifyQuery) OnlyX(ctx context.Context) *Notify {
	node, err := nq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Notify ID in the query.
// Returns a *NotSingularError when more than one Notify ID is found.
// Returns a *NotFoundError when no entities are found.
func (nq *NotifyQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = nq.Limit(2).IDs(setContextOp(ctx, nq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{notify.Label}
	default:
		err = &NotSingularError{notify.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (nq *NotifyQuery) OnlyIDX(ctx context.Context) int {
	id, err := nq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Notifies.
func (nq *NotifyQuery) All(ctx context.Context) ([]*Notify, error) {
	ctx = setContextOp(ctx, nq.ctx, "All")
	if err := nq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Notify, *NotifyQuery]()
	return withInterceptors[[]*Notify](ctx, nq, qr, nq.inters)
}

// AllX is like All, but panics if an error occurs.
func (nq *NotifyQuery) AllX(ctx context.Context) []*Notify {
	nodes, err := nq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Notify IDs.
func (nq *NotifyQuery) IDs(ctx context.Context) (ids []int, err error) {
	if nq.ctx.Unique == nil && nq.path != nil {
		nq.Unique(true)
	}
	ctx = setContextOp(ctx, nq.ctx, "IDs")
	if err = nq.Select(notify.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (nq *NotifyQuery) IDsX(ctx context.Context) []int {
	ids, err := nq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (nq *NotifyQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, nq.ctx, "Count")
	if err := nq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, nq, querierCount[*NotifyQuery](), nq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (nq *NotifyQuery) CountX(ctx context.Context) int {
	count, err := nq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (nq *NotifyQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, nq.ctx, "Exist")
	switch _, err := nq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (nq *NotifyQuery) ExistX(ctx context.Context) bool {
	exist, err := nq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the NotifyQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (nq *NotifyQuery) Clone() *NotifyQuery {
	if nq == nil {
		return nil
	}
	return &NotifyQuery{
		config:     nq.config,
		ctx:        nq.ctx.Clone(),
		order:      append([]notify.OrderOption{}, nq.order...),
		inters:     append([]Interceptor{}, nq.inters...),
		predicates: append([]predicate.Notify{}, nq.predicates...),
		withTenant: nq.withTenant.Clone(),
		// clone intermediate query.
		sql:  nq.sql.Clone(),
		path: nq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (nq *NotifyQuery) WithTenant(opts ...func(*TenantQuery)) *NotifyQuery {
	query := (&TenantClient{config: nq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	nq.withTenant = query
	return nq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Notify.Query().
//		GroupBy(notify.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (nq *NotifyQuery) GroupBy(field string, fields ...string) *NotifyGroupBy {
	nq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &NotifyGroupBy{build: nq}
	grbuild.flds = &nq.ctx.Fields
	grbuild.label = notify.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.Notify.Query().
//		Select(notify.FieldCreatedAt).
//		Scan(ctx, &v)
func (nq *NotifyQuery) Select(fields ...string) *NotifySelect {
	nq.ctx.Fields = append(nq.ctx.Fields, fields...)
	sbuild := &NotifySelect{NotifyQuery: nq}
	sbuild.label = notify.Label
	sbuild.flds, sbuild.scan = &nq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a NotifySelect configured with the given aggregations.
func (nq *NotifyQuery) Aggregate(fns ...AggregateFunc) *NotifySelect {
	return nq.Select().Aggregate(fns...)
}

func (nq *NotifyQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range nq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, nq); err != nil {
				return err
			}
		}
	}
	for _, f := range nq.ctx.Fields {
		if !notify.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if nq.path != nil {
		prev, err := nq.path(ctx)
		if err != nil {
			return err
		}
		nq.sql = prev
	}
	if notify.Policy == nil {
		return errors.New("ent: uninitialized notify.Policy (forgotten import ent/runtime?)")
	}
	if err := notify.Policy.EvalQuery(ctx, nq); err != nil {
		return err
	}
	return nil
}

func (nq *NotifyQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Notify, error) {
	var (
		nodes       = []*Notify{}
		_spec       = nq.querySpec()
		loadedTypes = [1]bool{
			nq.withTenant != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Notify).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Notify{config: nq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, nq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := nq.withTenant; query != nil {
		if err := nq.loadTenant(ctx, query, nodes, nil,
			func(n *Notify, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (nq *NotifyQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*Notify, init func(*Notify), assign func(*Notify, *Tenant)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*Notify)
	for i := range nodes {
		if nodes[i].TenantID == nil {
			continue
		}
		fk := *nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (nq *NotifyQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := nq.querySpec()
	_spec.Node.Columns = nq.ctx.Fields
	if len(nq.ctx.Fields) > 0 {
		_spec.Unique = nq.ctx.Unique != nil && *nq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, nq.driver, _spec)
}

func (nq *NotifyQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(notify.Table, notify.Columns, sqlgraph.NewFieldSpec(notify.FieldID, field.TypeInt))
	_spec.From = nq.sql
	if unique := nq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if nq.path != nil {
		_spec.Unique = true
	}
	if fields := nq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, notify.FieldID)
		for i := range fields {
			if fields[i] != notify.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if nq.withTenant != nil {
			_spec.Node.AddColumnOnce(notify.FieldTenantID)
		}
	}
	if ps := nq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := nq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := nq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := nq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (nq *NotifyQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(nq.driver.Dialect())
	t1 := builder.Table(notify.Table)
	columns := nq.ctx.Fields
	if len(columns) == 0 {
		columns = notify.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if nq.sql != nil {
		selector = nq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if nq.ctx.Unique != nil && *nq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range nq.predicates {
		p(selector)
	}
	for _, p := range nq.order {
		p(selector)
	}
	if offset := nq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := nq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// NotifyGroupBy is the group-by builder for Notify entities.
type NotifyGroupBy struct {
	selector
	build *NotifyQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ngb *NotifyGroupBy) Aggregate(fns ...AggregateFunc) *NotifyGroupBy {
	ngb.fns = append(ngb.fns, fns...)
	return ngb
}

// Scan applies the selector query and scans the result into the given value.
func (ngb *NotifyGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ngb.build.ctx, "GroupBy")
	if err := ngb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotifyQuery, *NotifyGroupBy](ctx, ngb.build, ngb, ngb.build.inters, v)
}

func (ngb *NotifyGroupBy) sqlScan(ctx context.Context, root *NotifyQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ngb.fns))
	for _, fn := range ngb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ngb.flds)+len(ngb.fns))
		for _, f := range *ngb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ngb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ngb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// NotifySelect is the builder for selecting fields of Notify entities.
type NotifySelect struct {
	*NotifyQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ns *NotifySelect) Aggregate(fns ...AggregateFunc) *NotifySelect {
	ns.fns = append(ns.fns, fns...)
	return ns
}

// Scan applies the selector query and scans the result into the given value.
func (ns *NotifySelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ns.ctx, "Select")
	if err := ns.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*NotifyQuery, *NotifySelect](ctx, ns.NotifyQuery, ns, ns.inters, v)
}

func (ns *NotifySelect) sqlScan(ctx context.Context, root *NotifyQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ns.fns))
	for _, fn := range ns.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ns.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ns.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
