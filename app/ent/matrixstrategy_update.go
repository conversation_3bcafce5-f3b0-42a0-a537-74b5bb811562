// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/matrixspectrumalert"
	"meta/app/ent/matrixstrategy"
	"meta/app/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MatrixStrategyUpdate is the builder for updating MatrixStrategy entities.
type MatrixStrategyUpdate struct {
	config
	hooks    []Hook
	mutation *MatrixStrategyMutation
}

// Where appends a list predicates to the MatrixStrategyUpdate builder.
func (msu *MatrixStrategyUpdate) Where(ps ...predicate.MatrixStrategy) *MatrixStrategyUpdate {
	msu.mutation.Where(ps...)
	return msu
}

// SetUpdatedAt sets the "updated_at" field.
func (msu *MatrixStrategyUpdate) SetUpdatedAt(t time.Time) *MatrixStrategyUpdate {
	msu.mutation.SetUpdatedAt(t)
	return msu
}

// SetRemark sets the "remark" field.
func (msu *MatrixStrategyUpdate) SetRemark(s string) *MatrixStrategyUpdate {
	msu.mutation.SetRemark(s)
	return msu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (msu *MatrixStrategyUpdate) SetNillableRemark(s *string) *MatrixStrategyUpdate {
	if s != nil {
		msu.SetRemark(*s)
	}
	return msu
}

// ClearRemark clears the value of the "remark" field.
func (msu *MatrixStrategyUpdate) ClearRemark() *MatrixStrategyUpdate {
	msu.mutation.ClearRemark()
	return msu
}

// SetName sets the "name" field.
func (msu *MatrixStrategyUpdate) SetName(s string) *MatrixStrategyUpdate {
	msu.mutation.SetName(s)
	return msu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (msu *MatrixStrategyUpdate) SetNillableName(s *string) *MatrixStrategyUpdate {
	if s != nil {
		msu.SetName(*s)
	}
	return msu
}

// SetRegion sets the "region" field.
func (msu *MatrixStrategyUpdate) SetRegion(s string) *MatrixStrategyUpdate {
	msu.mutation.SetRegion(s)
	return msu
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (msu *MatrixStrategyUpdate) SetNillableRegion(s *string) *MatrixStrategyUpdate {
	if s != nil {
		msu.SetRegion(*s)
	}
	return msu
}

// SetNetType sets the "net_type" field.
func (msu *MatrixStrategyUpdate) SetNetType(s string) *MatrixStrategyUpdate {
	msu.mutation.SetNetType(s)
	return msu
}

// SetNillableNetType sets the "net_type" field if the given value is not nil.
func (msu *MatrixStrategyUpdate) SetNillableNetType(s *string) *MatrixStrategyUpdate {
	if s != nil {
		msu.SetNetType(*s)
	}
	return msu
}

// SetIsp sets the "isp" field.
func (msu *MatrixStrategyUpdate) SetIsp(s string) *MatrixStrategyUpdate {
	msu.mutation.SetIsp(s)
	return msu
}

// SetNillableIsp sets the "isp" field if the given value is not nil.
func (msu *MatrixStrategyUpdate) SetNillableIsp(s *string) *MatrixStrategyUpdate {
	if s != nil {
		msu.SetIsp(*s)
	}
	return msu
}

// SetMonitorBps sets the "monitor_bps" field.
func (msu *MatrixStrategyUpdate) SetMonitorBps(i int64) *MatrixStrategyUpdate {
	msu.mutation.ResetMonitorBps()
	msu.mutation.SetMonitorBps(i)
	return msu
}

// SetNillableMonitorBps sets the "monitor_bps" field if the given value is not nil.
func (msu *MatrixStrategyUpdate) SetNillableMonitorBps(i *int64) *MatrixStrategyUpdate {
	if i != nil {
		msu.SetMonitorBps(*i)
	}
	return msu
}

// AddMonitorBps adds i to the "monitor_bps" field.
func (msu *MatrixStrategyUpdate) AddMonitorBps(i int64) *MatrixStrategyUpdate {
	msu.mutation.AddMonitorBps(i)
	return msu
}

// SetDragBps sets the "drag_bps" field.
func (msu *MatrixStrategyUpdate) SetDragBps(i int64) *MatrixStrategyUpdate {
	msu.mutation.ResetDragBps()
	msu.mutation.SetDragBps(i)
	return msu
}

// SetNillableDragBps sets the "drag_bps" field if the given value is not nil.
func (msu *MatrixStrategyUpdate) SetNillableDragBps(i *int64) *MatrixStrategyUpdate {
	if i != nil {
		msu.SetDragBps(*i)
	}
	return msu
}

// AddDragBps adds i to the "drag_bps" field.
func (msu *MatrixStrategyUpdate) AddDragBps(i int64) *MatrixStrategyUpdate {
	msu.mutation.AddDragBps(i)
	return msu
}

// SetDragType sets the "drag_type" field.
func (msu *MatrixStrategyUpdate) SetDragType(i int) *MatrixStrategyUpdate {
	msu.mutation.ResetDragType()
	msu.mutation.SetDragType(i)
	return msu
}

// SetNillableDragType sets the "drag_type" field if the given value is not nil.
func (msu *MatrixStrategyUpdate) SetNillableDragType(i *int) *MatrixStrategyUpdate {
	if i != nil {
		msu.SetDragType(*i)
	}
	return msu
}

// AddDragType adds i to the "drag_type" field.
func (msu *MatrixStrategyUpdate) AddDragType(i int) *MatrixStrategyUpdate {
	msu.mutation.AddDragType(i)
	return msu
}

// AddMatrixStrategyAlertIDs adds the "matrix_strategy_alerts" edge to the MatrixSpectrumAlert entity by IDs.
func (msu *MatrixStrategyUpdate) AddMatrixStrategyAlertIDs(ids ...int) *MatrixStrategyUpdate {
	msu.mutation.AddMatrixStrategyAlertIDs(ids...)
	return msu
}

// AddMatrixStrategyAlerts adds the "matrix_strategy_alerts" edges to the MatrixSpectrumAlert entity.
func (msu *MatrixStrategyUpdate) AddMatrixStrategyAlerts(m ...*MatrixSpectrumAlert) *MatrixStrategyUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msu.AddMatrixStrategyAlertIDs(ids...)
}

// Mutation returns the MatrixStrategyMutation object of the builder.
func (msu *MatrixStrategyUpdate) Mutation() *MatrixStrategyMutation {
	return msu.mutation
}

// ClearMatrixStrategyAlerts clears all "matrix_strategy_alerts" edges to the MatrixSpectrumAlert entity.
func (msu *MatrixStrategyUpdate) ClearMatrixStrategyAlerts() *MatrixStrategyUpdate {
	msu.mutation.ClearMatrixStrategyAlerts()
	return msu
}

// RemoveMatrixStrategyAlertIDs removes the "matrix_strategy_alerts" edge to MatrixSpectrumAlert entities by IDs.
func (msu *MatrixStrategyUpdate) RemoveMatrixStrategyAlertIDs(ids ...int) *MatrixStrategyUpdate {
	msu.mutation.RemoveMatrixStrategyAlertIDs(ids...)
	return msu
}

// RemoveMatrixStrategyAlerts removes "matrix_strategy_alerts" edges to MatrixSpectrumAlert entities.
func (msu *MatrixStrategyUpdate) RemoveMatrixStrategyAlerts(m ...*MatrixSpectrumAlert) *MatrixStrategyUpdate {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msu.RemoveMatrixStrategyAlertIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (msu *MatrixStrategyUpdate) Save(ctx context.Context) (int, error) {
	if err := msu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, msu.sqlSave, msu.mutation, msu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (msu *MatrixStrategyUpdate) SaveX(ctx context.Context) int {
	affected, err := msu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (msu *MatrixStrategyUpdate) Exec(ctx context.Context) error {
	_, err := msu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msu *MatrixStrategyUpdate) ExecX(ctx context.Context) {
	if err := msu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msu *MatrixStrategyUpdate) defaults() error {
	if _, ok := msu.mutation.UpdatedAt(); !ok {
		if matrixstrategy.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixstrategy.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixstrategy.UpdateDefaultUpdatedAt()
		msu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (msu *MatrixStrategyUpdate) check() error {
	if v, ok := msu.mutation.Remark(); ok {
		if err := matrixstrategy.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "MatrixStrategy.remark": %w`, err)}
		}
	}
	if v, ok := msu.mutation.Name(); ok {
		if err := matrixstrategy.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "MatrixStrategy.name": %w`, err)}
		}
	}
	return nil
}

func (msu *MatrixStrategyUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := msu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(matrixstrategy.Table, matrixstrategy.Columns, sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt))
	if ps := msu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := msu.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixstrategy.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := msu.mutation.Remark(); ok {
		_spec.SetField(matrixstrategy.FieldRemark, field.TypeString, value)
	}
	if msu.mutation.RemarkCleared() {
		_spec.ClearField(matrixstrategy.FieldRemark, field.TypeString)
	}
	if value, ok := msu.mutation.Name(); ok {
		_spec.SetField(matrixstrategy.FieldName, field.TypeString, value)
	}
	if value, ok := msu.mutation.Region(); ok {
		_spec.SetField(matrixstrategy.FieldRegion, field.TypeString, value)
	}
	if value, ok := msu.mutation.NetType(); ok {
		_spec.SetField(matrixstrategy.FieldNetType, field.TypeString, value)
	}
	if value, ok := msu.mutation.Isp(); ok {
		_spec.SetField(matrixstrategy.FieldIsp, field.TypeString, value)
	}
	if value, ok := msu.mutation.MonitorBps(); ok {
		_spec.SetField(matrixstrategy.FieldMonitorBps, field.TypeInt64, value)
	}
	if value, ok := msu.mutation.AddedMonitorBps(); ok {
		_spec.AddField(matrixstrategy.FieldMonitorBps, field.TypeInt64, value)
	}
	if value, ok := msu.mutation.DragBps(); ok {
		_spec.SetField(matrixstrategy.FieldDragBps, field.TypeInt64, value)
	}
	if value, ok := msu.mutation.AddedDragBps(); ok {
		_spec.AddField(matrixstrategy.FieldDragBps, field.TypeInt64, value)
	}
	if value, ok := msu.mutation.DragType(); ok {
		_spec.SetField(matrixstrategy.FieldDragType, field.TypeInt, value)
	}
	if value, ok := msu.mutation.AddedDragType(); ok {
		_spec.AddField(matrixstrategy.FieldDragType, field.TypeInt, value)
	}
	if msu.mutation.MatrixStrategyAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixstrategy.MatrixStrategyAlertsTable,
			Columns: []string{matrixstrategy.MatrixStrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msu.mutation.RemovedMatrixStrategyAlertsIDs(); len(nodes) > 0 && !msu.mutation.MatrixStrategyAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixstrategy.MatrixStrategyAlertsTable,
			Columns: []string{matrixstrategy.MatrixStrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msu.mutation.MatrixStrategyAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixstrategy.MatrixStrategyAlertsTable,
			Columns: []string{matrixstrategy.MatrixStrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, msu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{matrixstrategy.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	msu.mutation.done = true
	return n, nil
}

// MatrixStrategyUpdateOne is the builder for updating a single MatrixStrategy entity.
type MatrixStrategyUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *MatrixStrategyMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (msuo *MatrixStrategyUpdateOne) SetUpdatedAt(t time.Time) *MatrixStrategyUpdateOne {
	msuo.mutation.SetUpdatedAt(t)
	return msuo
}

// SetRemark sets the "remark" field.
func (msuo *MatrixStrategyUpdateOne) SetRemark(s string) *MatrixStrategyUpdateOne {
	msuo.mutation.SetRemark(s)
	return msuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (msuo *MatrixStrategyUpdateOne) SetNillableRemark(s *string) *MatrixStrategyUpdateOne {
	if s != nil {
		msuo.SetRemark(*s)
	}
	return msuo
}

// ClearRemark clears the value of the "remark" field.
func (msuo *MatrixStrategyUpdateOne) ClearRemark() *MatrixStrategyUpdateOne {
	msuo.mutation.ClearRemark()
	return msuo
}

// SetName sets the "name" field.
func (msuo *MatrixStrategyUpdateOne) SetName(s string) *MatrixStrategyUpdateOne {
	msuo.mutation.SetName(s)
	return msuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (msuo *MatrixStrategyUpdateOne) SetNillableName(s *string) *MatrixStrategyUpdateOne {
	if s != nil {
		msuo.SetName(*s)
	}
	return msuo
}

// SetRegion sets the "region" field.
func (msuo *MatrixStrategyUpdateOne) SetRegion(s string) *MatrixStrategyUpdateOne {
	msuo.mutation.SetRegion(s)
	return msuo
}

// SetNillableRegion sets the "region" field if the given value is not nil.
func (msuo *MatrixStrategyUpdateOne) SetNillableRegion(s *string) *MatrixStrategyUpdateOne {
	if s != nil {
		msuo.SetRegion(*s)
	}
	return msuo
}

// SetNetType sets the "net_type" field.
func (msuo *MatrixStrategyUpdateOne) SetNetType(s string) *MatrixStrategyUpdateOne {
	msuo.mutation.SetNetType(s)
	return msuo
}

// SetNillableNetType sets the "net_type" field if the given value is not nil.
func (msuo *MatrixStrategyUpdateOne) SetNillableNetType(s *string) *MatrixStrategyUpdateOne {
	if s != nil {
		msuo.SetNetType(*s)
	}
	return msuo
}

// SetIsp sets the "isp" field.
func (msuo *MatrixStrategyUpdateOne) SetIsp(s string) *MatrixStrategyUpdateOne {
	msuo.mutation.SetIsp(s)
	return msuo
}

// SetNillableIsp sets the "isp" field if the given value is not nil.
func (msuo *MatrixStrategyUpdateOne) SetNillableIsp(s *string) *MatrixStrategyUpdateOne {
	if s != nil {
		msuo.SetIsp(*s)
	}
	return msuo
}

// SetMonitorBps sets the "monitor_bps" field.
func (msuo *MatrixStrategyUpdateOne) SetMonitorBps(i int64) *MatrixStrategyUpdateOne {
	msuo.mutation.ResetMonitorBps()
	msuo.mutation.SetMonitorBps(i)
	return msuo
}

// SetNillableMonitorBps sets the "monitor_bps" field if the given value is not nil.
func (msuo *MatrixStrategyUpdateOne) SetNillableMonitorBps(i *int64) *MatrixStrategyUpdateOne {
	if i != nil {
		msuo.SetMonitorBps(*i)
	}
	return msuo
}

// AddMonitorBps adds i to the "monitor_bps" field.
func (msuo *MatrixStrategyUpdateOne) AddMonitorBps(i int64) *MatrixStrategyUpdateOne {
	msuo.mutation.AddMonitorBps(i)
	return msuo
}

// SetDragBps sets the "drag_bps" field.
func (msuo *MatrixStrategyUpdateOne) SetDragBps(i int64) *MatrixStrategyUpdateOne {
	msuo.mutation.ResetDragBps()
	msuo.mutation.SetDragBps(i)
	return msuo
}

// SetNillableDragBps sets the "drag_bps" field if the given value is not nil.
func (msuo *MatrixStrategyUpdateOne) SetNillableDragBps(i *int64) *MatrixStrategyUpdateOne {
	if i != nil {
		msuo.SetDragBps(*i)
	}
	return msuo
}

// AddDragBps adds i to the "drag_bps" field.
func (msuo *MatrixStrategyUpdateOne) AddDragBps(i int64) *MatrixStrategyUpdateOne {
	msuo.mutation.AddDragBps(i)
	return msuo
}

// SetDragType sets the "drag_type" field.
func (msuo *MatrixStrategyUpdateOne) SetDragType(i int) *MatrixStrategyUpdateOne {
	msuo.mutation.ResetDragType()
	msuo.mutation.SetDragType(i)
	return msuo
}

// SetNillableDragType sets the "drag_type" field if the given value is not nil.
func (msuo *MatrixStrategyUpdateOne) SetNillableDragType(i *int) *MatrixStrategyUpdateOne {
	if i != nil {
		msuo.SetDragType(*i)
	}
	return msuo
}

// AddDragType adds i to the "drag_type" field.
func (msuo *MatrixStrategyUpdateOne) AddDragType(i int) *MatrixStrategyUpdateOne {
	msuo.mutation.AddDragType(i)
	return msuo
}

// AddMatrixStrategyAlertIDs adds the "matrix_strategy_alerts" edge to the MatrixSpectrumAlert entity by IDs.
func (msuo *MatrixStrategyUpdateOne) AddMatrixStrategyAlertIDs(ids ...int) *MatrixStrategyUpdateOne {
	msuo.mutation.AddMatrixStrategyAlertIDs(ids...)
	return msuo
}

// AddMatrixStrategyAlerts adds the "matrix_strategy_alerts" edges to the MatrixSpectrumAlert entity.
func (msuo *MatrixStrategyUpdateOne) AddMatrixStrategyAlerts(m ...*MatrixSpectrumAlert) *MatrixStrategyUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msuo.AddMatrixStrategyAlertIDs(ids...)
}

// Mutation returns the MatrixStrategyMutation object of the builder.
func (msuo *MatrixStrategyUpdateOne) Mutation() *MatrixStrategyMutation {
	return msuo.mutation
}

// ClearMatrixStrategyAlerts clears all "matrix_strategy_alerts" edges to the MatrixSpectrumAlert entity.
func (msuo *MatrixStrategyUpdateOne) ClearMatrixStrategyAlerts() *MatrixStrategyUpdateOne {
	msuo.mutation.ClearMatrixStrategyAlerts()
	return msuo
}

// RemoveMatrixStrategyAlertIDs removes the "matrix_strategy_alerts" edge to MatrixSpectrumAlert entities by IDs.
func (msuo *MatrixStrategyUpdateOne) RemoveMatrixStrategyAlertIDs(ids ...int) *MatrixStrategyUpdateOne {
	msuo.mutation.RemoveMatrixStrategyAlertIDs(ids...)
	return msuo
}

// RemoveMatrixStrategyAlerts removes "matrix_strategy_alerts" edges to MatrixSpectrumAlert entities.
func (msuo *MatrixStrategyUpdateOne) RemoveMatrixStrategyAlerts(m ...*MatrixSpectrumAlert) *MatrixStrategyUpdateOne {
	ids := make([]int, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return msuo.RemoveMatrixStrategyAlertIDs(ids...)
}

// Where appends a list predicates to the MatrixStrategyUpdate builder.
func (msuo *MatrixStrategyUpdateOne) Where(ps ...predicate.MatrixStrategy) *MatrixStrategyUpdateOne {
	msuo.mutation.Where(ps...)
	return msuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (msuo *MatrixStrategyUpdateOne) Select(field string, fields ...string) *MatrixStrategyUpdateOne {
	msuo.fields = append([]string{field}, fields...)
	return msuo
}

// Save executes the query and returns the updated MatrixStrategy entity.
func (msuo *MatrixStrategyUpdateOne) Save(ctx context.Context) (*MatrixStrategy, error) {
	if err := msuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, msuo.sqlSave, msuo.mutation, msuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (msuo *MatrixStrategyUpdateOne) SaveX(ctx context.Context) *MatrixStrategy {
	node, err := msuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (msuo *MatrixStrategyUpdateOne) Exec(ctx context.Context) error {
	_, err := msuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (msuo *MatrixStrategyUpdateOne) ExecX(ctx context.Context) {
	if err := msuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (msuo *MatrixStrategyUpdateOne) defaults() error {
	if _, ok := msuo.mutation.UpdatedAt(); !ok {
		if matrixstrategy.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized matrixstrategy.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := matrixstrategy.UpdateDefaultUpdatedAt()
		msuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (msuo *MatrixStrategyUpdateOne) check() error {
	if v, ok := msuo.mutation.Remark(); ok {
		if err := matrixstrategy.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "MatrixStrategy.remark": %w`, err)}
		}
	}
	if v, ok := msuo.mutation.Name(); ok {
		if err := matrixstrategy.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "MatrixStrategy.name": %w`, err)}
		}
	}
	return nil
}

func (msuo *MatrixStrategyUpdateOne) sqlSave(ctx context.Context) (_node *MatrixStrategy, err error) {
	if err := msuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(matrixstrategy.Table, matrixstrategy.Columns, sqlgraph.NewFieldSpec(matrixstrategy.FieldID, field.TypeInt))
	id, ok := msuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "MatrixStrategy.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := msuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, matrixstrategy.FieldID)
		for _, f := range fields {
			if !matrixstrategy.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != matrixstrategy.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := msuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := msuo.mutation.UpdatedAt(); ok {
		_spec.SetField(matrixstrategy.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := msuo.mutation.Remark(); ok {
		_spec.SetField(matrixstrategy.FieldRemark, field.TypeString, value)
	}
	if msuo.mutation.RemarkCleared() {
		_spec.ClearField(matrixstrategy.FieldRemark, field.TypeString)
	}
	if value, ok := msuo.mutation.Name(); ok {
		_spec.SetField(matrixstrategy.FieldName, field.TypeString, value)
	}
	if value, ok := msuo.mutation.Region(); ok {
		_spec.SetField(matrixstrategy.FieldRegion, field.TypeString, value)
	}
	if value, ok := msuo.mutation.NetType(); ok {
		_spec.SetField(matrixstrategy.FieldNetType, field.TypeString, value)
	}
	if value, ok := msuo.mutation.Isp(); ok {
		_spec.SetField(matrixstrategy.FieldIsp, field.TypeString, value)
	}
	if value, ok := msuo.mutation.MonitorBps(); ok {
		_spec.SetField(matrixstrategy.FieldMonitorBps, field.TypeInt64, value)
	}
	if value, ok := msuo.mutation.AddedMonitorBps(); ok {
		_spec.AddField(matrixstrategy.FieldMonitorBps, field.TypeInt64, value)
	}
	if value, ok := msuo.mutation.DragBps(); ok {
		_spec.SetField(matrixstrategy.FieldDragBps, field.TypeInt64, value)
	}
	if value, ok := msuo.mutation.AddedDragBps(); ok {
		_spec.AddField(matrixstrategy.FieldDragBps, field.TypeInt64, value)
	}
	if value, ok := msuo.mutation.DragType(); ok {
		_spec.SetField(matrixstrategy.FieldDragType, field.TypeInt, value)
	}
	if value, ok := msuo.mutation.AddedDragType(); ok {
		_spec.AddField(matrixstrategy.FieldDragType, field.TypeInt, value)
	}
	if msuo.mutation.MatrixStrategyAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixstrategy.MatrixStrategyAlertsTable,
			Columns: []string{matrixstrategy.MatrixStrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msuo.mutation.RemovedMatrixStrategyAlertsIDs(); len(nodes) > 0 && !msuo.mutation.MatrixStrategyAlertsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixstrategy.MatrixStrategyAlertsTable,
			Columns: []string{matrixstrategy.MatrixStrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := msuo.mutation.MatrixStrategyAlertsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   matrixstrategy.MatrixStrategyAlertsTable,
			Columns: []string{matrixstrategy.MatrixStrategyAlertsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(matrixspectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &MatrixStrategy{config: msuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, msuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{matrixstrategy.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	msuo.mutation.done = true
	return _node, nil
}
