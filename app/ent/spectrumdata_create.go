// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/spectrumalert"
	"meta/app/ent/spectrumdata"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SpectrumDataCreate is the builder for creating a SpectrumData entity.
type SpectrumDataCreate struct {
	config
	mutation *SpectrumDataMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (sdc *SpectrumDataCreate) SetTenantID(i int) *SpectrumDataCreate {
	sdc.mutation.SetTenantID(i)
	return sdc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (sdc *SpectrumDataCreate) SetNillableTenantID(i *int) *SpectrumDataCreate {
	if i != nil {
		sdc.SetTenantID(*i)
	}
	return sdc
}

// SetCreatedAt sets the "created_at" field.
func (sdc *SpectrumDataCreate) SetCreatedAt(t time.Time) *SpectrumDataCreate {
	sdc.mutation.SetCreatedAt(t)
	return sdc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sdc *SpectrumDataCreate) SetNillableCreatedAt(t *time.Time) *SpectrumDataCreate {
	if t != nil {
		sdc.SetCreatedAt(*t)
	}
	return sdc
}

// SetSpectrumAlertID sets the "spectrum_alert_id" field.
func (sdc *SpectrumDataCreate) SetSpectrumAlertID(i int) *SpectrumDataCreate {
	sdc.mutation.SetSpectrumAlertID(i)
	return sdc
}

// SetNillableSpectrumAlertID sets the "spectrum_alert_id" field if the given value is not nil.
func (sdc *SpectrumDataCreate) SetNillableSpectrumAlertID(i *int) *SpectrumDataCreate {
	if i != nil {
		sdc.SetSpectrumAlertID(*i)
	}
	return sdc
}

// SetIP sets the "ip" field.
func (sdc *SpectrumDataCreate) SetIP(s string) *SpectrumDataCreate {
	sdc.mutation.SetIP(s)
	return sdc
}

// SetTime sets the "time" field.
func (sdc *SpectrumDataCreate) SetTime(t time.Time) *SpectrumDataCreate {
	sdc.mutation.SetTime(t)
	return sdc
}

// SetMonitorID sets the "monitor_id" field.
func (sdc *SpectrumDataCreate) SetMonitorID(i int) *SpectrumDataCreate {
	sdc.mutation.SetMonitorID(i)
	return sdc
}

// SetDataType sets the "data_type" field.
func (sdc *SpectrumDataCreate) SetDataType(i int) *SpectrumDataCreate {
	sdc.mutation.SetDataType(i)
	return sdc
}

// SetBps sets the "bps" field.
func (sdc *SpectrumDataCreate) SetBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetBps(i)
	return sdc
}

// SetPps sets the "pps" field.
func (sdc *SpectrumDataCreate) SetPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetPps(i)
	return sdc
}

// SetSynBps sets the "syn_bps" field.
func (sdc *SpectrumDataCreate) SetSynBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetSynBps(i)
	return sdc
}

// SetSynPps sets the "syn_pps" field.
func (sdc *SpectrumDataCreate) SetSynPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetSynPps(i)
	return sdc
}

// SetAckBps sets the "ack_bps" field.
func (sdc *SpectrumDataCreate) SetAckBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetAckBps(i)
	return sdc
}

// SetAckPps sets the "ack_pps" field.
func (sdc *SpectrumDataCreate) SetAckPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetAckPps(i)
	return sdc
}

// SetSynAckBps sets the "syn_ack_bps" field.
func (sdc *SpectrumDataCreate) SetSynAckBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetSynAckBps(i)
	return sdc
}

// SetSynAckPps sets the "syn_ack_pps" field.
func (sdc *SpectrumDataCreate) SetSynAckPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetSynAckPps(i)
	return sdc
}

// SetIcmpBps sets the "icmp_bps" field.
func (sdc *SpectrumDataCreate) SetIcmpBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetIcmpBps(i)
	return sdc
}

// SetIcmpPps sets the "icmp_pps" field.
func (sdc *SpectrumDataCreate) SetIcmpPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetIcmpPps(i)
	return sdc
}

// SetSmallPps sets the "small_pps" field.
func (sdc *SpectrumDataCreate) SetSmallPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetSmallPps(i)
	return sdc
}

// SetNtpPps sets the "ntp_pps" field.
func (sdc *SpectrumDataCreate) SetNtpPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetNtpPps(i)
	return sdc
}

// SetNtpBps sets the "ntp_bps" field.
func (sdc *SpectrumDataCreate) SetNtpBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetNtpBps(i)
	return sdc
}

// SetDNSQueryPps sets the "dns_query_pps" field.
func (sdc *SpectrumDataCreate) SetDNSQueryPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetDNSQueryPps(i)
	return sdc
}

// SetDNSQueryBps sets the "dns_query_bps" field.
func (sdc *SpectrumDataCreate) SetDNSQueryBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetDNSQueryBps(i)
	return sdc
}

// SetDNSAnswerPps sets the "dns_answer_pps" field.
func (sdc *SpectrumDataCreate) SetDNSAnswerPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetDNSAnswerPps(i)
	return sdc
}

// SetDNSAnswerBps sets the "dns_answer_bps" field.
func (sdc *SpectrumDataCreate) SetDNSAnswerBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetDNSAnswerBps(i)
	return sdc
}

// SetSsdpBps sets the "ssdp_bps" field.
func (sdc *SpectrumDataCreate) SetSsdpBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetSsdpBps(i)
	return sdc
}

// SetSsdpPps sets the "ssdp_pps" field.
func (sdc *SpectrumDataCreate) SetSsdpPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetSsdpPps(i)
	return sdc
}

// SetUDPPps sets the "udp_pps" field.
func (sdc *SpectrumDataCreate) SetUDPPps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetUDPPps(i)
	return sdc
}

// SetUDPBps sets the "udp_bps" field.
func (sdc *SpectrumDataCreate) SetUDPBps(i int64) *SpectrumDataCreate {
	sdc.mutation.SetUDPBps(i)
	return sdc
}

// SetQPS sets the "qps" field.
func (sdc *SpectrumDataCreate) SetQPS(i int64) *SpectrumDataCreate {
	sdc.mutation.SetQPS(i)
	return sdc
}

// SetReceiveCount sets the "receive_count" field.
func (sdc *SpectrumDataCreate) SetReceiveCount(i int) *SpectrumDataCreate {
	sdc.mutation.SetReceiveCount(i)
	return sdc
}

// SetIPType sets the "ip_type" field.
func (sdc *SpectrumDataCreate) SetIPType(i int) *SpectrumDataCreate {
	sdc.mutation.SetIPType(i)
	return sdc
}

// SetMonitor sets the "monitor" field.
func (sdc *SpectrumDataCreate) SetMonitor(s string) *SpectrumDataCreate {
	sdc.mutation.SetMonitor(s)
	return sdc
}

// SetNillableMonitor sets the "monitor" field if the given value is not nil.
func (sdc *SpectrumDataCreate) SetNillableMonitor(s *string) *SpectrumDataCreate {
	if s != nil {
		sdc.SetMonitor(*s)
	}
	return sdc
}

// SetProduct sets the "product" field.
func (sdc *SpectrumDataCreate) SetProduct(s string) *SpectrumDataCreate {
	sdc.mutation.SetProduct(s)
	return sdc
}

// SetNillableProduct sets the "product" field if the given value is not nil.
func (sdc *SpectrumDataCreate) SetNillableProduct(s *string) *SpectrumDataCreate {
	if s != nil {
		sdc.SetProduct(*s)
	}
	return sdc
}

// SetHost sets the "host" field.
func (sdc *SpectrumDataCreate) SetHost(s string) *SpectrumDataCreate {
	sdc.mutation.SetHost(s)
	return sdc
}

// SetNillableHost sets the "host" field if the given value is not nil.
func (sdc *SpectrumDataCreate) SetNillableHost(s *string) *SpectrumDataCreate {
	if s != nil {
		sdc.SetHost(*s)
	}
	return sdc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (sdc *SpectrumDataCreate) SetTenant(t *Tenant) *SpectrumDataCreate {
	return sdc.SetTenantID(t.ID)
}

// SetSpectrumAlert sets the "spectrum_alert" edge to the SpectrumAlert entity.
func (sdc *SpectrumDataCreate) SetSpectrumAlert(s *SpectrumAlert) *SpectrumDataCreate {
	return sdc.SetSpectrumAlertID(s.ID)
}

// Mutation returns the SpectrumDataMutation object of the builder.
func (sdc *SpectrumDataCreate) Mutation() *SpectrumDataMutation {
	return sdc.mutation
}

// Save creates the SpectrumData in the database.
func (sdc *SpectrumDataCreate) Save(ctx context.Context) (*SpectrumData, error) {
	if err := sdc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sdc.sqlSave, sdc.mutation, sdc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sdc *SpectrumDataCreate) SaveX(ctx context.Context) *SpectrumData {
	v, err := sdc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sdc *SpectrumDataCreate) Exec(ctx context.Context) error {
	_, err := sdc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sdc *SpectrumDataCreate) ExecX(ctx context.Context) {
	if err := sdc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sdc *SpectrumDataCreate) defaults() error {
	if _, ok := sdc.mutation.CreatedAt(); !ok {
		if spectrumdata.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized spectrumdata.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := spectrumdata.DefaultCreatedAt()
		sdc.mutation.SetCreatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sdc *SpectrumDataCreate) check() error {
	if _, ok := sdc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "SpectrumData.created_at"`)}
	}
	if _, ok := sdc.mutation.IP(); !ok {
		return &ValidationError{Name: "ip", err: errors.New(`ent: missing required field "SpectrumData.ip"`)}
	}
	if _, ok := sdc.mutation.Time(); !ok {
		return &ValidationError{Name: "time", err: errors.New(`ent: missing required field "SpectrumData.time"`)}
	}
	if _, ok := sdc.mutation.MonitorID(); !ok {
		return &ValidationError{Name: "monitor_id", err: errors.New(`ent: missing required field "SpectrumData.monitor_id"`)}
	}
	if _, ok := sdc.mutation.DataType(); !ok {
		return &ValidationError{Name: "data_type", err: errors.New(`ent: missing required field "SpectrumData.data_type"`)}
	}
	if _, ok := sdc.mutation.Bps(); !ok {
		return &ValidationError{Name: "bps", err: errors.New(`ent: missing required field "SpectrumData.bps"`)}
	}
	if _, ok := sdc.mutation.Pps(); !ok {
		return &ValidationError{Name: "pps", err: errors.New(`ent: missing required field "SpectrumData.pps"`)}
	}
	if _, ok := sdc.mutation.SynBps(); !ok {
		return &ValidationError{Name: "syn_bps", err: errors.New(`ent: missing required field "SpectrumData.syn_bps"`)}
	}
	if _, ok := sdc.mutation.SynPps(); !ok {
		return &ValidationError{Name: "syn_pps", err: errors.New(`ent: missing required field "SpectrumData.syn_pps"`)}
	}
	if _, ok := sdc.mutation.AckBps(); !ok {
		return &ValidationError{Name: "ack_bps", err: errors.New(`ent: missing required field "SpectrumData.ack_bps"`)}
	}
	if _, ok := sdc.mutation.AckPps(); !ok {
		return &ValidationError{Name: "ack_pps", err: errors.New(`ent: missing required field "SpectrumData.ack_pps"`)}
	}
	if _, ok := sdc.mutation.SynAckBps(); !ok {
		return &ValidationError{Name: "syn_ack_bps", err: errors.New(`ent: missing required field "SpectrumData.syn_ack_bps"`)}
	}
	if _, ok := sdc.mutation.SynAckPps(); !ok {
		return &ValidationError{Name: "syn_ack_pps", err: errors.New(`ent: missing required field "SpectrumData.syn_ack_pps"`)}
	}
	if _, ok := sdc.mutation.IcmpBps(); !ok {
		return &ValidationError{Name: "icmp_bps", err: errors.New(`ent: missing required field "SpectrumData.icmp_bps"`)}
	}
	if _, ok := sdc.mutation.IcmpPps(); !ok {
		return &ValidationError{Name: "icmp_pps", err: errors.New(`ent: missing required field "SpectrumData.icmp_pps"`)}
	}
	if _, ok := sdc.mutation.SmallPps(); !ok {
		return &ValidationError{Name: "small_pps", err: errors.New(`ent: missing required field "SpectrumData.small_pps"`)}
	}
	if _, ok := sdc.mutation.NtpPps(); !ok {
		return &ValidationError{Name: "ntp_pps", err: errors.New(`ent: missing required field "SpectrumData.ntp_pps"`)}
	}
	if _, ok := sdc.mutation.NtpBps(); !ok {
		return &ValidationError{Name: "ntp_bps", err: errors.New(`ent: missing required field "SpectrumData.ntp_bps"`)}
	}
	if _, ok := sdc.mutation.DNSQueryPps(); !ok {
		return &ValidationError{Name: "dns_query_pps", err: errors.New(`ent: missing required field "SpectrumData.dns_query_pps"`)}
	}
	if _, ok := sdc.mutation.DNSQueryBps(); !ok {
		return &ValidationError{Name: "dns_query_bps", err: errors.New(`ent: missing required field "SpectrumData.dns_query_bps"`)}
	}
	if _, ok := sdc.mutation.DNSAnswerPps(); !ok {
		return &ValidationError{Name: "dns_answer_pps", err: errors.New(`ent: missing required field "SpectrumData.dns_answer_pps"`)}
	}
	if _, ok := sdc.mutation.DNSAnswerBps(); !ok {
		return &ValidationError{Name: "dns_answer_bps", err: errors.New(`ent: missing required field "SpectrumData.dns_answer_bps"`)}
	}
	if _, ok := sdc.mutation.SsdpBps(); !ok {
		return &ValidationError{Name: "ssdp_bps", err: errors.New(`ent: missing required field "SpectrumData.ssdp_bps"`)}
	}
	if _, ok := sdc.mutation.SsdpPps(); !ok {
		return &ValidationError{Name: "ssdp_pps", err: errors.New(`ent: missing required field "SpectrumData.ssdp_pps"`)}
	}
	if _, ok := sdc.mutation.UDPPps(); !ok {
		return &ValidationError{Name: "udp_pps", err: errors.New(`ent: missing required field "SpectrumData.udp_pps"`)}
	}
	if _, ok := sdc.mutation.UDPBps(); !ok {
		return &ValidationError{Name: "udp_bps", err: errors.New(`ent: missing required field "SpectrumData.udp_bps"`)}
	}
	if _, ok := sdc.mutation.QPS(); !ok {
		return &ValidationError{Name: "qps", err: errors.New(`ent: missing required field "SpectrumData.qps"`)}
	}
	if _, ok := sdc.mutation.ReceiveCount(); !ok {
		return &ValidationError{Name: "receive_count", err: errors.New(`ent: missing required field "SpectrumData.receive_count"`)}
	}
	if _, ok := sdc.mutation.IPType(); !ok {
		return &ValidationError{Name: "ip_type", err: errors.New(`ent: missing required field "SpectrumData.ip_type"`)}
	}
	return nil
}

func (sdc *SpectrumDataCreate) sqlSave(ctx context.Context) (*SpectrumData, error) {
	if err := sdc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sdc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sdc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sdc.mutation.id = &_node.ID
	sdc.mutation.done = true
	return _node, nil
}

func (sdc *SpectrumDataCreate) createSpec() (*SpectrumData, *sqlgraph.CreateSpec) {
	var (
		_node = &SpectrumData{config: sdc.config}
		_spec = sqlgraph.NewCreateSpec(spectrumdata.Table, sqlgraph.NewFieldSpec(spectrumdata.FieldID, field.TypeInt))
	)
	if value, ok := sdc.mutation.CreatedAt(); ok {
		_spec.SetField(spectrumdata.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sdc.mutation.IP(); ok {
		_spec.SetField(spectrumdata.FieldIP, field.TypeString, value)
		_node.IP = value
	}
	if value, ok := sdc.mutation.Time(); ok {
		_spec.SetField(spectrumdata.FieldTime, field.TypeTime, value)
		_node.Time = value
	}
	if value, ok := sdc.mutation.MonitorID(); ok {
		_spec.SetField(spectrumdata.FieldMonitorID, field.TypeInt, value)
		_node.MonitorID = value
	}
	if value, ok := sdc.mutation.DataType(); ok {
		_spec.SetField(spectrumdata.FieldDataType, field.TypeInt, value)
		_node.DataType = value
	}
	if value, ok := sdc.mutation.Bps(); ok {
		_spec.SetField(spectrumdata.FieldBps, field.TypeInt64, value)
		_node.Bps = value
	}
	if value, ok := sdc.mutation.Pps(); ok {
		_spec.SetField(spectrumdata.FieldPps, field.TypeInt64, value)
		_node.Pps = value
	}
	if value, ok := sdc.mutation.SynBps(); ok {
		_spec.SetField(spectrumdata.FieldSynBps, field.TypeInt64, value)
		_node.SynBps = value
	}
	if value, ok := sdc.mutation.SynPps(); ok {
		_spec.SetField(spectrumdata.FieldSynPps, field.TypeInt64, value)
		_node.SynPps = value
	}
	if value, ok := sdc.mutation.AckBps(); ok {
		_spec.SetField(spectrumdata.FieldAckBps, field.TypeInt64, value)
		_node.AckBps = value
	}
	if value, ok := sdc.mutation.AckPps(); ok {
		_spec.SetField(spectrumdata.FieldAckPps, field.TypeInt64, value)
		_node.AckPps = value
	}
	if value, ok := sdc.mutation.SynAckBps(); ok {
		_spec.SetField(spectrumdata.FieldSynAckBps, field.TypeInt64, value)
		_node.SynAckBps = value
	}
	if value, ok := sdc.mutation.SynAckPps(); ok {
		_spec.SetField(spectrumdata.FieldSynAckPps, field.TypeInt64, value)
		_node.SynAckPps = value
	}
	if value, ok := sdc.mutation.IcmpBps(); ok {
		_spec.SetField(spectrumdata.FieldIcmpBps, field.TypeInt64, value)
		_node.IcmpBps = value
	}
	if value, ok := sdc.mutation.IcmpPps(); ok {
		_spec.SetField(spectrumdata.FieldIcmpPps, field.TypeInt64, value)
		_node.IcmpPps = value
	}
	if value, ok := sdc.mutation.SmallPps(); ok {
		_spec.SetField(spectrumdata.FieldSmallPps, field.TypeInt64, value)
		_node.SmallPps = value
	}
	if value, ok := sdc.mutation.NtpPps(); ok {
		_spec.SetField(spectrumdata.FieldNtpPps, field.TypeInt64, value)
		_node.NtpPps = value
	}
	if value, ok := sdc.mutation.NtpBps(); ok {
		_spec.SetField(spectrumdata.FieldNtpBps, field.TypeInt64, value)
		_node.NtpBps = value
	}
	if value, ok := sdc.mutation.DNSQueryPps(); ok {
		_spec.SetField(spectrumdata.FieldDNSQueryPps, field.TypeInt64, value)
		_node.DNSQueryPps = value
	}
	if value, ok := sdc.mutation.DNSQueryBps(); ok {
		_spec.SetField(spectrumdata.FieldDNSQueryBps, field.TypeInt64, value)
		_node.DNSQueryBps = value
	}
	if value, ok := sdc.mutation.DNSAnswerPps(); ok {
		_spec.SetField(spectrumdata.FieldDNSAnswerPps, field.TypeInt64, value)
		_node.DNSAnswerPps = value
	}
	if value, ok := sdc.mutation.DNSAnswerBps(); ok {
		_spec.SetField(spectrumdata.FieldDNSAnswerBps, field.TypeInt64, value)
		_node.DNSAnswerBps = value
	}
	if value, ok := sdc.mutation.SsdpBps(); ok {
		_spec.SetField(spectrumdata.FieldSsdpBps, field.TypeInt64, value)
		_node.SsdpBps = value
	}
	if value, ok := sdc.mutation.SsdpPps(); ok {
		_spec.SetField(spectrumdata.FieldSsdpPps, field.TypeInt64, value)
		_node.SsdpPps = value
	}
	if value, ok := sdc.mutation.UDPPps(); ok {
		_spec.SetField(spectrumdata.FieldUDPPps, field.TypeInt64, value)
		_node.UDPPps = value
	}
	if value, ok := sdc.mutation.UDPBps(); ok {
		_spec.SetField(spectrumdata.FieldUDPBps, field.TypeInt64, value)
		_node.UDPBps = value
	}
	if value, ok := sdc.mutation.QPS(); ok {
		_spec.SetField(spectrumdata.FieldQPS, field.TypeInt64, value)
		_node.QPS = value
	}
	if value, ok := sdc.mutation.ReceiveCount(); ok {
		_spec.SetField(spectrumdata.FieldReceiveCount, field.TypeInt, value)
		_node.ReceiveCount = value
	}
	if value, ok := sdc.mutation.IPType(); ok {
		_spec.SetField(spectrumdata.FieldIPType, field.TypeInt, value)
		_node.IPType = value
	}
	if value, ok := sdc.mutation.Monitor(); ok {
		_spec.SetField(spectrumdata.FieldMonitor, field.TypeString, value)
		_node.Monitor = &value
	}
	if value, ok := sdc.mutation.Product(); ok {
		_spec.SetField(spectrumdata.FieldProduct, field.TypeString, value)
		_node.Product = &value
	}
	if value, ok := sdc.mutation.Host(); ok {
		_spec.SetField(spectrumdata.FieldHost, field.TypeString, value)
		_node.Host = &value
	}
	if nodes := sdc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   spectrumdata.TenantTable,
			Columns: []string{spectrumdata.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := sdc.mutation.SpectrumAlertIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   spectrumdata.SpectrumAlertTable,
			Columns: []string{spectrumdata.SpectrumAlertColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(spectrumalert.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.SpectrumAlertID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// SpectrumDataCreateBulk is the builder for creating many SpectrumData entities in bulk.
type SpectrumDataCreateBulk struct {
	config
	err      error
	builders []*SpectrumDataCreate
}

// Save creates the SpectrumData entities in the database.
func (sdcb *SpectrumDataCreateBulk) Save(ctx context.Context) ([]*SpectrumData, error) {
	if sdcb.err != nil {
		return nil, sdcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(sdcb.builders))
	nodes := make([]*SpectrumData, len(sdcb.builders))
	mutators := make([]Mutator, len(sdcb.builders))
	for i := range sdcb.builders {
		func(i int, root context.Context) {
			builder := sdcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*SpectrumDataMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, sdcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, sdcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, sdcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (sdcb *SpectrumDataCreateBulk) SaveX(ctx context.Context) []*SpectrumData {
	v, err := sdcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sdcb *SpectrumDataCreateBulk) Exec(ctx context.Context) error {
	_, err := sdcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sdcb *SpectrumDataCreateBulk) ExecX(ctx context.Context) {
	if err := sdcb.Exec(ctx); err != nil {
		panic(err)
	}
}
