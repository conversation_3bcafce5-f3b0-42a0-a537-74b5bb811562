// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/cloudalert"
	"meta/app/ent/cloudflowdata"
	"meta/app/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// CloudAlertCreate is the builder for creating a CloudAlert entity.
type CloudAlertCreate struct {
	config
	mutation *CloudAlertMutation
	hooks    []Hook
}

// SetTenantID sets the "tenant_id" field.
func (cac *CloudAlertCreate) SetTenantID(i int) *CloudAlertCreate {
	cac.mutation.SetTenantID(i)
	return cac
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (cac *CloudAlertCreate) SetNillableTenantID(i *int) *CloudAlertCreate {
	if i != nil {
		cac.SetTenantID(*i)
	}
	return cac
}

// SetCreatedAt sets the "created_at" field.
func (cac *CloudAlertCreate) SetCreatedAt(t time.Time) *CloudAlertCreate {
	cac.mutation.SetCreatedAt(t)
	return cac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (cac *CloudAlertCreate) SetNillableCreatedAt(t *time.Time) *CloudAlertCreate {
	if t != nil {
		cac.SetCreatedAt(*t)
	}
	return cac
}

// SetUpdatedAt sets the "updated_at" field.
func (cac *CloudAlertCreate) SetUpdatedAt(t time.Time) *CloudAlertCreate {
	cac.mutation.SetUpdatedAt(t)
	return cac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (cac *CloudAlertCreate) SetNillableUpdatedAt(t *time.Time) *CloudAlertCreate {
	if t != nil {
		cac.SetUpdatedAt(*t)
	}
	return cac
}

// SetRemark sets the "remark" field.
func (cac *CloudAlertCreate) SetRemark(s string) *CloudAlertCreate {
	cac.mutation.SetRemark(s)
	return cac
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (cac *CloudAlertCreate) SetNillableRemark(s *string) *CloudAlertCreate {
	if s != nil {
		cac.SetRemark(*s)
	}
	return cac
}

// SetSrcIP sets the "src_ip" field.
func (cac *CloudAlertCreate) SetSrcIP(s string) *CloudAlertCreate {
	cac.mutation.SetSrcIP(s)
	return cac
}

// SetSrcPort sets the "src_port" field.
func (cac *CloudAlertCreate) SetSrcPort(i int) *CloudAlertCreate {
	cac.mutation.SetSrcPort(i)
	return cac
}

// SetDstIP sets the "dst_ip" field.
func (cac *CloudAlertCreate) SetDstIP(s string) *CloudAlertCreate {
	cac.mutation.SetDstIP(s)
	return cac
}

// SetDstPort sets the "dst_port" field.
func (cac *CloudAlertCreate) SetDstPort(i int) *CloudAlertCreate {
	cac.mutation.SetDstPort(i)
	return cac
}

// SetDefenceMode sets the "defence_mode" field.
func (cac *CloudAlertCreate) SetDefenceMode(i int) *CloudAlertCreate {
	cac.mutation.SetDefenceMode(i)
	return cac
}

// SetFlowMode sets the "flow_mode" field.
func (cac *CloudAlertCreate) SetFlowMode(i int) *CloudAlertCreate {
	cac.mutation.SetFlowMode(i)
	return cac
}

// SetTCPAckNum sets the "tcp_ack_num" field.
func (cac *CloudAlertCreate) SetTCPAckNum(s string) *CloudAlertCreate {
	cac.mutation.SetTCPAckNum(s)
	return cac
}

// SetTCPSeqNum sets the "tcp_seq_num" field.
func (cac *CloudAlertCreate) SetTCPSeqNum(s string) *CloudAlertCreate {
	cac.mutation.SetTCPSeqNum(s)
	return cac
}

// SetProtocol sets the "protocol" field.
func (cac *CloudAlertCreate) SetProtocol(i int) *CloudAlertCreate {
	cac.mutation.SetProtocol(i)
	return cac
}

// SetDefenceLevel sets the "defence_level" field.
func (cac *CloudAlertCreate) SetDefenceLevel(i int) *CloudAlertCreate {
	cac.mutation.SetDefenceLevel(i)
	return cac
}

// SetMaxPps sets the "max_pps" field.
func (cac *CloudAlertCreate) SetMaxPps(i int64) *CloudAlertCreate {
	cac.mutation.SetMaxPps(i)
	return cac
}

// SetMaxAttackPps sets the "max_attack_pps" field.
func (cac *CloudAlertCreate) SetMaxAttackPps(i int64) *CloudAlertCreate {
	cac.mutation.SetMaxAttackPps(i)
	return cac
}

// SetOverlimitPktCount sets the "overlimit_pkt_count" field.
func (cac *CloudAlertCreate) SetOverlimitPktCount(i int) *CloudAlertCreate {
	cac.mutation.SetOverlimitPktCount(i)
	return cac
}

// SetStartTime sets the "start_time" field.
func (cac *CloudAlertCreate) SetStartTime(t time.Time) *CloudAlertCreate {
	cac.mutation.SetStartTime(t)
	return cac
}

// SetEndTime sets the "end_time" field.
func (cac *CloudAlertCreate) SetEndTime(t time.Time) *CloudAlertCreate {
	cac.mutation.SetEndTime(t)
	return cac
}

// SetNillableEndTime sets the "end_time" field if the given value is not nil.
func (cac *CloudAlertCreate) SetNillableEndTime(t *time.Time) *CloudAlertCreate {
	if t != nil {
		cac.SetEndTime(*t)
	}
	return cac
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (cac *CloudAlertCreate) SetTenant(t *Tenant) *CloudAlertCreate {
	return cac.SetTenantID(t.ID)
}

// AddCloudflowDataIDs adds the "cloudflow_datas" edge to the CloudFlowData entity by IDs.
func (cac *CloudAlertCreate) AddCloudflowDataIDs(ids ...int) *CloudAlertCreate {
	cac.mutation.AddCloudflowDataIDs(ids...)
	return cac
}

// AddCloudflowDatas adds the "cloudflow_datas" edges to the CloudFlowData entity.
func (cac *CloudAlertCreate) AddCloudflowDatas(c ...*CloudFlowData) *CloudAlertCreate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cac.AddCloudflowDataIDs(ids...)
}

// Mutation returns the CloudAlertMutation object of the builder.
func (cac *CloudAlertCreate) Mutation() *CloudAlertMutation {
	return cac.mutation
}

// Save creates the CloudAlert in the database.
func (cac *CloudAlertCreate) Save(ctx context.Context) (*CloudAlert, error) {
	if err := cac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, cac.sqlSave, cac.mutation, cac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cac *CloudAlertCreate) SaveX(ctx context.Context) *CloudAlert {
	v, err := cac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cac *CloudAlertCreate) Exec(ctx context.Context) error {
	_, err := cac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cac *CloudAlertCreate) ExecX(ctx context.Context) {
	if err := cac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cac *CloudAlertCreate) defaults() error {
	if _, ok := cac.mutation.CreatedAt(); !ok {
		if cloudalert.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudalert.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := cloudalert.DefaultCreatedAt()
		cac.mutation.SetCreatedAt(v)
	}
	if _, ok := cac.mutation.UpdatedAt(); !ok {
		if cloudalert.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized cloudalert.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := cloudalert.DefaultUpdatedAt()
		cac.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (cac *CloudAlertCreate) check() error {
	if _, ok := cac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "CloudAlert.created_at"`)}
	}
	if _, ok := cac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "CloudAlert.updated_at"`)}
	}
	if v, ok := cac.mutation.Remark(); ok {
		if err := cloudalert.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "CloudAlert.remark": %w`, err)}
		}
	}
	if _, ok := cac.mutation.SrcIP(); !ok {
		return &ValidationError{Name: "src_ip", err: errors.New(`ent: missing required field "CloudAlert.src_ip"`)}
	}
	if _, ok := cac.mutation.SrcPort(); !ok {
		return &ValidationError{Name: "src_port", err: errors.New(`ent: missing required field "CloudAlert.src_port"`)}
	}
	if _, ok := cac.mutation.DstIP(); !ok {
		return &ValidationError{Name: "dst_ip", err: errors.New(`ent: missing required field "CloudAlert.dst_ip"`)}
	}
	if _, ok := cac.mutation.DstPort(); !ok {
		return &ValidationError{Name: "dst_port", err: errors.New(`ent: missing required field "CloudAlert.dst_port"`)}
	}
	if _, ok := cac.mutation.DefenceMode(); !ok {
		return &ValidationError{Name: "defence_mode", err: errors.New(`ent: missing required field "CloudAlert.defence_mode"`)}
	}
	if _, ok := cac.mutation.FlowMode(); !ok {
		return &ValidationError{Name: "flow_mode", err: errors.New(`ent: missing required field "CloudAlert.flow_mode"`)}
	}
	if _, ok := cac.mutation.TCPAckNum(); !ok {
		return &ValidationError{Name: "tcp_ack_num", err: errors.New(`ent: missing required field "CloudAlert.tcp_ack_num"`)}
	}
	if _, ok := cac.mutation.TCPSeqNum(); !ok {
		return &ValidationError{Name: "tcp_seq_num", err: errors.New(`ent: missing required field "CloudAlert.tcp_seq_num"`)}
	}
	if _, ok := cac.mutation.Protocol(); !ok {
		return &ValidationError{Name: "protocol", err: errors.New(`ent: missing required field "CloudAlert.protocol"`)}
	}
	if _, ok := cac.mutation.DefenceLevel(); !ok {
		return &ValidationError{Name: "defence_level", err: errors.New(`ent: missing required field "CloudAlert.defence_level"`)}
	}
	if _, ok := cac.mutation.MaxPps(); !ok {
		return &ValidationError{Name: "max_pps", err: errors.New(`ent: missing required field "CloudAlert.max_pps"`)}
	}
	if _, ok := cac.mutation.MaxAttackPps(); !ok {
		return &ValidationError{Name: "max_attack_pps", err: errors.New(`ent: missing required field "CloudAlert.max_attack_pps"`)}
	}
	if _, ok := cac.mutation.OverlimitPktCount(); !ok {
		return &ValidationError{Name: "overlimit_pkt_count", err: errors.New(`ent: missing required field "CloudAlert.overlimit_pkt_count"`)}
	}
	if _, ok := cac.mutation.StartTime(); !ok {
		return &ValidationError{Name: "start_time", err: errors.New(`ent: missing required field "CloudAlert.start_time"`)}
	}
	return nil
}

func (cac *CloudAlertCreate) sqlSave(ctx context.Context) (*CloudAlert, error) {
	if err := cac.check(); err != nil {
		return nil, err
	}
	_node, _spec := cac.createSpec()
	if err := sqlgraph.CreateNode(ctx, cac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	cac.mutation.id = &_node.ID
	cac.mutation.done = true
	return _node, nil
}

func (cac *CloudAlertCreate) createSpec() (*CloudAlert, *sqlgraph.CreateSpec) {
	var (
		_node = &CloudAlert{config: cac.config}
		_spec = sqlgraph.NewCreateSpec(cloudalert.Table, sqlgraph.NewFieldSpec(cloudalert.FieldID, field.TypeInt))
	)
	if value, ok := cac.mutation.CreatedAt(); ok {
		_spec.SetField(cloudalert.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := cac.mutation.UpdatedAt(); ok {
		_spec.SetField(cloudalert.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := cac.mutation.Remark(); ok {
		_spec.SetField(cloudalert.FieldRemark, field.TypeString, value)
		_node.Remark = &value
	}
	if value, ok := cac.mutation.SrcIP(); ok {
		_spec.SetField(cloudalert.FieldSrcIP, field.TypeString, value)
		_node.SrcIP = value
	}
	if value, ok := cac.mutation.SrcPort(); ok {
		_spec.SetField(cloudalert.FieldSrcPort, field.TypeInt, value)
		_node.SrcPort = value
	}
	if value, ok := cac.mutation.DstIP(); ok {
		_spec.SetField(cloudalert.FieldDstIP, field.TypeString, value)
		_node.DstIP = value
	}
	if value, ok := cac.mutation.DstPort(); ok {
		_spec.SetField(cloudalert.FieldDstPort, field.TypeInt, value)
		_node.DstPort = value
	}
	if value, ok := cac.mutation.DefenceMode(); ok {
		_spec.SetField(cloudalert.FieldDefenceMode, field.TypeInt, value)
		_node.DefenceMode = value
	}
	if value, ok := cac.mutation.FlowMode(); ok {
		_spec.SetField(cloudalert.FieldFlowMode, field.TypeInt, value)
		_node.FlowMode = value
	}
	if value, ok := cac.mutation.TCPAckNum(); ok {
		_spec.SetField(cloudalert.FieldTCPAckNum, field.TypeString, value)
		_node.TCPAckNum = value
	}
	if value, ok := cac.mutation.TCPSeqNum(); ok {
		_spec.SetField(cloudalert.FieldTCPSeqNum, field.TypeString, value)
		_node.TCPSeqNum = value
	}
	if value, ok := cac.mutation.Protocol(); ok {
		_spec.SetField(cloudalert.FieldProtocol, field.TypeInt, value)
		_node.Protocol = value
	}
	if value, ok := cac.mutation.DefenceLevel(); ok {
		_spec.SetField(cloudalert.FieldDefenceLevel, field.TypeInt, value)
		_node.DefenceLevel = value
	}
	if value, ok := cac.mutation.MaxPps(); ok {
		_spec.SetField(cloudalert.FieldMaxPps, field.TypeInt64, value)
		_node.MaxPps = value
	}
	if value, ok := cac.mutation.MaxAttackPps(); ok {
		_spec.SetField(cloudalert.FieldMaxAttackPps, field.TypeInt64, value)
		_node.MaxAttackPps = value
	}
	if value, ok := cac.mutation.OverlimitPktCount(); ok {
		_spec.SetField(cloudalert.FieldOverlimitPktCount, field.TypeInt, value)
		_node.OverlimitPktCount = value
	}
	if value, ok := cac.mutation.StartTime(); ok {
		_spec.SetField(cloudalert.FieldStartTime, field.TypeTime, value)
		_node.StartTime = value
	}
	if value, ok := cac.mutation.EndTime(); ok {
		_spec.SetField(cloudalert.FieldEndTime, field.TypeTime, value)
		_node.EndTime = value
	}
	if nodes := cac.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   cloudalert.TenantTable,
			Columns: []string{cloudalert.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := cac.mutation.CloudflowDatasIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   cloudalert.CloudflowDatasTable,
			Columns: []string{cloudalert.CloudflowDatasColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(cloudflowdata.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// CloudAlertCreateBulk is the builder for creating many CloudAlert entities in bulk.
type CloudAlertCreateBulk struct {
	config
	err      error
	builders []*CloudAlertCreate
}

// Save creates the CloudAlert entities in the database.
func (cacb *CloudAlertCreateBulk) Save(ctx context.Context) ([]*CloudAlert, error) {
	if cacb.err != nil {
		return nil, cacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cacb.builders))
	nodes := make([]*CloudAlert, len(cacb.builders))
	mutators := make([]Mutator, len(cacb.builders))
	for i := range cacb.builders {
		func(i int, root context.Context) {
			builder := cacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*CloudAlertMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cacb *CloudAlertCreateBulk) SaveX(ctx context.Context) []*CloudAlert {
	v, err := cacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cacb *CloudAlertCreateBulk) Exec(ctx context.Context) error {
	_, err := cacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cacb *CloudAlertCreateBulk) ExecX(ctx context.Context) {
	if err := cacb.Exec(ctx); err != nil {
		panic(err)
	}
}
