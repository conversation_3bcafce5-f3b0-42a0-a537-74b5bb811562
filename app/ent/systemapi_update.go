// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"meta/app/ent/predicate"
	"meta/app/ent/systemapi"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// SystemApiUpdate is the builder for updating SystemApi entities.
type SystemApiUpdate struct {
	config
	hooks    []Hook
	mutation *SystemApiMutation
}

// Where appends a list predicates to the SystemApiUpdate builder.
func (sau *SystemApiUpdate) Where(ps ...predicate.SystemApi) *SystemApiUpdate {
	sau.mutation.Where(ps...)
	return sau
}

// SetUpdatedAt sets the "updated_at" field.
func (sau *SystemApiUpdate) SetUpdatedAt(t time.Time) *SystemApiUpdate {
	sau.mutation.SetUpdatedAt(t)
	return sau
}

// SetRemark sets the "remark" field.
func (sau *SystemApiUpdate) SetRemark(s string) *SystemApiUpdate {
	sau.mutation.SetRemark(s)
	return sau
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sau *SystemApiUpdate) SetNillableRemark(s *string) *SystemApiUpdate {
	if s != nil {
		sau.SetRemark(*s)
	}
	return sau
}

// ClearRemark clears the value of the "remark" field.
func (sau *SystemApiUpdate) ClearRemark() *SystemApiUpdate {
	sau.mutation.ClearRemark()
	return sau
}

// SetName sets the "name" field.
func (sau *SystemApiUpdate) SetName(s string) *SystemApiUpdate {
	sau.mutation.SetName(s)
	return sau
}

// SetNillableName sets the "name" field if the given value is not nil.
func (sau *SystemApiUpdate) SetNillableName(s *string) *SystemApiUpdate {
	if s != nil {
		sau.SetName(*s)
	}
	return sau
}

// SetPath sets the "path" field.
func (sau *SystemApiUpdate) SetPath(s string) *SystemApiUpdate {
	sau.mutation.SetPath(s)
	return sau
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (sau *SystemApiUpdate) SetNillablePath(s *string) *SystemApiUpdate {
	if s != nil {
		sau.SetPath(*s)
	}
	return sau
}

// SetHTTPMethod sets the "http_method" field.
func (sau *SystemApiUpdate) SetHTTPMethod(s string) *SystemApiUpdate {
	sau.mutation.SetHTTPMethod(s)
	return sau
}

// SetNillableHTTPMethod sets the "http_method" field if the given value is not nil.
func (sau *SystemApiUpdate) SetNillableHTTPMethod(s *string) *SystemApiUpdate {
	if s != nil {
		sau.SetHTTPMethod(*s)
	}
	return sau
}

// SetRoles sets the "roles" field.
func (sau *SystemApiUpdate) SetRoles(s *[]string) *SystemApiUpdate {
	sau.mutation.SetRoles(s)
	return sau
}

// SetPublic sets the "public" field.
func (sau *SystemApiUpdate) SetPublic(b bool) *SystemApiUpdate {
	sau.mutation.SetPublic(b)
	return sau
}

// SetNillablePublic sets the "public" field if the given value is not nil.
func (sau *SystemApiUpdate) SetNillablePublic(b *bool) *SystemApiUpdate {
	if b != nil {
		sau.SetPublic(*b)
	}
	return sau
}

// SetSa sets the "sa" field.
func (sau *SystemApiUpdate) SetSa(b bool) *SystemApiUpdate {
	sau.mutation.SetSa(b)
	return sau
}

// SetNillableSa sets the "sa" field if the given value is not nil.
func (sau *SystemApiUpdate) SetNillableSa(b *bool) *SystemApiUpdate {
	if b != nil {
		sau.SetSa(*b)
	}
	return sau
}

// Mutation returns the SystemApiMutation object of the builder.
func (sau *SystemApiUpdate) Mutation() *SystemApiMutation {
	return sau.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (sau *SystemApiUpdate) Save(ctx context.Context) (int, error) {
	if err := sau.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, sau.sqlSave, sau.mutation, sau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sau *SystemApiUpdate) SaveX(ctx context.Context) int {
	affected, err := sau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (sau *SystemApiUpdate) Exec(ctx context.Context) error {
	_, err := sau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sau *SystemApiUpdate) ExecX(ctx context.Context) {
	if err := sau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sau *SystemApiUpdate) defaults() error {
	if _, ok := sau.mutation.UpdatedAt(); !ok {
		if systemapi.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized systemapi.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := systemapi.UpdateDefaultUpdatedAt()
		sau.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sau *SystemApiUpdate) check() error {
	if v, ok := sau.mutation.Remark(); ok {
		if err := systemapi.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SystemApi.remark": %w`, err)}
		}
	}
	return nil
}

func (sau *SystemApiUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := sau.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(systemapi.Table, systemapi.Columns, sqlgraph.NewFieldSpec(systemapi.FieldID, field.TypeInt))
	if ps := sau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sau.mutation.UpdatedAt(); ok {
		_spec.SetField(systemapi.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := sau.mutation.Remark(); ok {
		_spec.SetField(systemapi.FieldRemark, field.TypeString, value)
	}
	if sau.mutation.RemarkCleared() {
		_spec.ClearField(systemapi.FieldRemark, field.TypeString)
	}
	if value, ok := sau.mutation.Name(); ok {
		_spec.SetField(systemapi.FieldName, field.TypeString, value)
	}
	if value, ok := sau.mutation.Path(); ok {
		_spec.SetField(systemapi.FieldPath, field.TypeString, value)
	}
	if value, ok := sau.mutation.HTTPMethod(); ok {
		_spec.SetField(systemapi.FieldHTTPMethod, field.TypeString, value)
	}
	if value, ok := sau.mutation.Roles(); ok {
		_spec.SetField(systemapi.FieldRoles, field.TypeJSON, value)
	}
	if value, ok := sau.mutation.Public(); ok {
		_spec.SetField(systemapi.FieldPublic, field.TypeBool, value)
	}
	if value, ok := sau.mutation.Sa(); ok {
		_spec.SetField(systemapi.FieldSa, field.TypeBool, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, sau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{systemapi.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	sau.mutation.done = true
	return n, nil
}

// SystemApiUpdateOne is the builder for updating a single SystemApi entity.
type SystemApiUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *SystemApiMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (sauo *SystemApiUpdateOne) SetUpdatedAt(t time.Time) *SystemApiUpdateOne {
	sauo.mutation.SetUpdatedAt(t)
	return sauo
}

// SetRemark sets the "remark" field.
func (sauo *SystemApiUpdateOne) SetRemark(s string) *SystemApiUpdateOne {
	sauo.mutation.SetRemark(s)
	return sauo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (sauo *SystemApiUpdateOne) SetNillableRemark(s *string) *SystemApiUpdateOne {
	if s != nil {
		sauo.SetRemark(*s)
	}
	return sauo
}

// ClearRemark clears the value of the "remark" field.
func (sauo *SystemApiUpdateOne) ClearRemark() *SystemApiUpdateOne {
	sauo.mutation.ClearRemark()
	return sauo
}

// SetName sets the "name" field.
func (sauo *SystemApiUpdateOne) SetName(s string) *SystemApiUpdateOne {
	sauo.mutation.SetName(s)
	return sauo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (sauo *SystemApiUpdateOne) SetNillableName(s *string) *SystemApiUpdateOne {
	if s != nil {
		sauo.SetName(*s)
	}
	return sauo
}

// SetPath sets the "path" field.
func (sauo *SystemApiUpdateOne) SetPath(s string) *SystemApiUpdateOne {
	sauo.mutation.SetPath(s)
	return sauo
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (sauo *SystemApiUpdateOne) SetNillablePath(s *string) *SystemApiUpdateOne {
	if s != nil {
		sauo.SetPath(*s)
	}
	return sauo
}

// SetHTTPMethod sets the "http_method" field.
func (sauo *SystemApiUpdateOne) SetHTTPMethod(s string) *SystemApiUpdateOne {
	sauo.mutation.SetHTTPMethod(s)
	return sauo
}

// SetNillableHTTPMethod sets the "http_method" field if the given value is not nil.
func (sauo *SystemApiUpdateOne) SetNillableHTTPMethod(s *string) *SystemApiUpdateOne {
	if s != nil {
		sauo.SetHTTPMethod(*s)
	}
	return sauo
}

// SetRoles sets the "roles" field.
func (sauo *SystemApiUpdateOne) SetRoles(s *[]string) *SystemApiUpdateOne {
	sauo.mutation.SetRoles(s)
	return sauo
}

// SetPublic sets the "public" field.
func (sauo *SystemApiUpdateOne) SetPublic(b bool) *SystemApiUpdateOne {
	sauo.mutation.SetPublic(b)
	return sauo
}

// SetNillablePublic sets the "public" field if the given value is not nil.
func (sauo *SystemApiUpdateOne) SetNillablePublic(b *bool) *SystemApiUpdateOne {
	if b != nil {
		sauo.SetPublic(*b)
	}
	return sauo
}

// SetSa sets the "sa" field.
func (sauo *SystemApiUpdateOne) SetSa(b bool) *SystemApiUpdateOne {
	sauo.mutation.SetSa(b)
	return sauo
}

// SetNillableSa sets the "sa" field if the given value is not nil.
func (sauo *SystemApiUpdateOne) SetNillableSa(b *bool) *SystemApiUpdateOne {
	if b != nil {
		sauo.SetSa(*b)
	}
	return sauo
}

// Mutation returns the SystemApiMutation object of the builder.
func (sauo *SystemApiUpdateOne) Mutation() *SystemApiMutation {
	return sauo.mutation
}

// Where appends a list predicates to the SystemApiUpdate builder.
func (sauo *SystemApiUpdateOne) Where(ps ...predicate.SystemApi) *SystemApiUpdateOne {
	sauo.mutation.Where(ps...)
	return sauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (sauo *SystemApiUpdateOne) Select(field string, fields ...string) *SystemApiUpdateOne {
	sauo.fields = append([]string{field}, fields...)
	return sauo
}

// Save executes the query and returns the updated SystemApi entity.
func (sauo *SystemApiUpdateOne) Save(ctx context.Context) (*SystemApi, error) {
	if err := sauo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, sauo.sqlSave, sauo.mutation, sauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (sauo *SystemApiUpdateOne) SaveX(ctx context.Context) *SystemApi {
	node, err := sauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (sauo *SystemApiUpdateOne) Exec(ctx context.Context) error {
	_, err := sauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sauo *SystemApiUpdateOne) ExecX(ctx context.Context) {
	if err := sauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sauo *SystemApiUpdateOne) defaults() error {
	if _, ok := sauo.mutation.UpdatedAt(); !ok {
		if systemapi.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized systemapi.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := systemapi.UpdateDefaultUpdatedAt()
		sauo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (sauo *SystemApiUpdateOne) check() error {
	if v, ok := sauo.mutation.Remark(); ok {
		if err := systemapi.RemarkValidator(v); err != nil {
			return &ValidationError{Name: "remark", err: fmt.Errorf(`ent: validator failed for field "SystemApi.remark": %w`, err)}
		}
	}
	return nil
}

func (sauo *SystemApiUpdateOne) sqlSave(ctx context.Context) (_node *SystemApi, err error) {
	if err := sauo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(systemapi.Table, systemapi.Columns, sqlgraph.NewFieldSpec(systemapi.FieldID, field.TypeInt))
	id, ok := sauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "SystemApi.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := sauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, systemapi.FieldID)
		for _, f := range fields {
			if !systemapi.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != systemapi.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := sauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := sauo.mutation.UpdatedAt(); ok {
		_spec.SetField(systemapi.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := sauo.mutation.Remark(); ok {
		_spec.SetField(systemapi.FieldRemark, field.TypeString, value)
	}
	if sauo.mutation.RemarkCleared() {
		_spec.ClearField(systemapi.FieldRemark, field.TypeString)
	}
	if value, ok := sauo.mutation.Name(); ok {
		_spec.SetField(systemapi.FieldName, field.TypeString, value)
	}
	if value, ok := sauo.mutation.Path(); ok {
		_spec.SetField(systemapi.FieldPath, field.TypeString, value)
	}
	if value, ok := sauo.mutation.HTTPMethod(); ok {
		_spec.SetField(systemapi.FieldHTTPMethod, field.TypeString, value)
	}
	if value, ok := sauo.mutation.Roles(); ok {
		_spec.SetField(systemapi.FieldRoles, field.TypeJSON, value)
	}
	if value, ok := sauo.mutation.Public(); ok {
		_spec.SetField(systemapi.FieldPublic, field.TypeBool, value)
	}
	if value, ok := sauo.mutation.Sa(); ok {
		_spec.SetField(systemapi.FieldSa, field.TypeBool, value)
	}
	_node = &SystemApi{config: sauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, sauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{systemapi.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	sauo.mutation.done = true
	return _node, nil
}
