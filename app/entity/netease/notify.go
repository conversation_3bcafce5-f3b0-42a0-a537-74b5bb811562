package netease

type ResultNotify struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	MessageId string `json:"message_id"`
}
type Sms struct {
	MessageType  string   `json:"message_type"`
	Content      string   `json:"content"`
	ReceiverList []string `json:"reciever_list"`
}

type Phone struct {
	MessageType  string   `json:"message_type"`
	Content      string   `json:"content"`
	ReceiverList []string `json:"reciever_list"`
}

type Popo struct {
	Sender       string   `json:"sender"`
	MessageType  string   `json:"message_type"`
	Content      string   `json:"content"`
	ReceiverList []string `json:"reciever_list"`
}

type Email struct {
	Sender       string   `json:"sender"`
	MessageType  string   `json:"message_type"`
	Content      string   `json:"content"`
	ReceiverList []string `json:"reciever_list"`
	Subject      string   `json:"subject"`
	Cc           []string `json:"cc"`
	IsHtml       bool     `json:"is_html"`
}

func NewPopo() *Popo {
	return &Popo{
		MessageType: "popo",
		Sender:      "<EMAIL>",
	}
}

func NewSms() *Sms {
	return &Sms{
		MessageType: "sms",
	}
}
func NewPhone() *Phone {
	return &Phone{
		MessageType: "phone",
	}
}

func NewEmail() *Email {
	return &Email{
		MessageType: "mail",
		Sender:      "<EMAIL>",
		Subject:     "DDoS通知",
		IsHtml:      true,
	}

}
