/**
* <AUTHOR>
* @date 2022-12-19 16:49
* @description
 */

package netease

import "time"

// PushAlert 集团nds推送的告警
type PushAlert struct {
	MaxPps      int64  `json:"pps"`
	Product     string `json:"product"`
	Ip          string `json:"ip"`
	AttackTypes string `json:"type"`
	MaxBps      int64  `json:"bps"`
	Status      string `json:"status"`
	Time        int64  `json:"time"`
	AlertType   int    `json:"alert_type"` //1：NDS告警IP；2：机房出口告警IP
}
type JsonAlert struct {
	MaxPps      int64  `json:"maxPps"`
	Product     string `json:"product"`
	Ip          string `json:"ip"`
	StartTime   int64  `json:"startTime"`
	AttackTypes string `json:"attackTypes"`
	EndTime     int64  `json:"endTime"`
	MaxBps      int64  `json:"maxBps"`
}
type AlertVO struct {
	MaxPps      int64     `json:"maxPps"`
	Product     string    `json:"product"`
	Ip          string    `json:"ip"`
	StartTime   time.Time `json:"startTime"`
	AttackTypes string    `json:"attackTypes"`
	EndTime     time.Time `json:"endTime"`
	MaxBps      int64     `json:"maxBps"`
}

type AttackTemplate struct {
	Title           string    `json:"title"`
	AlertId         int       `json:"alertId"`
	Ip              string    `json:"ip,omitempty"`
	Group           string    `json:"group,omitempty"`
	Project         string    `json:"project,omitempty"`
	StartTime       time.Time `json:"startTime"`
	EndTime         time.Time `json:"endTime,omitempty"`
	AttackType      string    `json:"attackType,omitempty"`
	AlertInfo       string    `json:"alertInfo,omitempty"`
	SpectrumInfo    string    `json:"spectrumInfo,omitempty"`
	CleanInfo       string    `json:"cleanInfo,omitempty"`
	ProtectStrategy string    `json:"protectStrategy,omitempty"`
	ProcessInfo     string    `json:"processInfo,omitempty"`
	DetailLink      string    `json:"detailLink,omitempty"`
}

type NdsAttackData struct {
	Prefix string `json:"prefix"`
	Action string `json:"action"`
	Target string `json:"target"`
	Status string `json:"status"`

	NdsGroup      string `json:"ndsGroup,omitempty"`
	NdsProtect    string `json:"ndsProtect"`
	WoFangProtect string `json:"woFangProtect"`

	Title      string `json:"title"`
	AlertID    int    `json:"alertID"`
	IP         string `json:"ip,omitempty"`
	Project    string `json:"project,omitempty"`
	StartTime  string `json:"startTime"`
	EndTime    string `json:"endTime,omitempty"`
	AttackType string `json:"attackType,omitempty"`

	AlertBps string `json:"alertBps,omitempty"`
	AlertPps string `json:"alertPps,omitempty"`

	MaxAttackBps string `json:"MaxAttackBps,omitempty"`
	MaxAttackPps string `json:"MaxAttackPps,omitempty"`

	MaxCleanBps string `json:"MaxCleanBps,omitempty"`
	MaxCleanPps string `json:"MaxCleanPps,omitempty"`

	ProcessSuggestion string `json:"processSuggestion"`

	Schema string `json:"schema"`
	Domain string `json:"domain"`

	MailSignData string `json:"mailSignData"`

	ProjectID   int    `json:"projectID"`
	ProjectCode string `json:"projectCode"`
}

type WofangDrainData struct {
	Prefix string `json:"prefix"`
	Action string `json:"action"`
	Target string `json:"target"`
	Status string `json:"status"`

	Title     string `json:"title"`
	WofangID  int    `json:"wofangID"`
	AlertID   int    `json:"alertID"`
	IP        string `json:"ip,omitempty"`
	Project   string `json:"project,omitempty"`
	StartTime string `json:"startTime"`
	Message   string `json:"message,omitempty"`

	ProcessSuggestion string `json:"processSuggestion"`
	Schema            string `json:"schema"`
	Domain            string `json:"domain"`

	MailSignData string `json:"mailSignData"`
	ProjectID    int    `json:"projectID"`
	ProjectCode  string `json:"projectCode"`
}
type MatrixAlertData struct {
	Prefix string `json:"prefix"`
	Target string `json:"target"`
	Status string `json:"status"`

	Title            string `json:"title"`
	WofangID         int    `json:"wofangID"`
	MatrixAlertID    int    `json:"matrixAlertID"`
	MatrixStrategyID int    `json:"matrixStrategyID"`
	StartTime        string `json:"startTime"`
	EndTime          string `json:"endTime"`

	Region  string `json:"region,omitempty"`
	NetType string `json:"netType,omitempty"`
	Isp     string `json:"isp,omitempty"`

	AlertBps     string `json:"alertBps,omitempty"`
	MaxAttackBps string `json:"maxAttackBps,omitempty"`

	ProcessSuggestion string `json:"processSuggestion"`
	Schema            string `json:"schema"`
	Domain            string `json:"domain"`

	MailSignData string `json:"mailSignData"`
}

type NotifyData struct {
	Prefix  string `json:"prefix"`
	Status  string `json:"status"`
	Title   string `json:"title"`
	Message string `json:"message,omitempty"`
}
