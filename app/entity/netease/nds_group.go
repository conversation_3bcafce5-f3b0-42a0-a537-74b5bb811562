/**
* <AUTHOR>
* @date 2022-12-01 14:49
* @description
 */

package netease

import "time"

type Result struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data,omitempty"`
}

// Group 群组
type Group struct {
	GroupId   int64  `json:"groupId"`
	GroupName string `json:"groupName"`
}
type GroupDTO struct {
	GroupId   int64  `json:"groupId"`
	GroupName string `json:"groupName"`
	GroupType int    `json:"groupType"`
}

type GroupResponse struct {
	Code   int     `json:"code"`
	Msg    string  `json:"msg"`
	Data   []Group `json:"data"`
	Total  int     `json:"total"`
	Offset int     `json:"offset"`
	Limit  int     `json:"limit"`
}

// GroupQuery 根据关键词或者ip查询群组和Pool
type GroupQuery struct {
	KeyWord  string `json:"keyWord,omitempty"`
	Ip       string `json:"ip,omitempty"`
	Type     int    `json:"type"`
	Page     int    `json:"page"`
	PageSize int    `json:"pageSize"`
}

type GroupQueryType struct {
	GroupId int64 `json:"groupId"`
	Type    int   `json:"type"`
}

type GroupUpdate struct {
	AlertId int      `json:"alertId,omitempty"`
	GroupId int64    `json:"groupId"`
	Type    int      `json:"type"`
	Ips     []string `json:"ips"`
}

type GroupStrategy struct {
	GroupId int64 `json:"groupId"`
}

type GroupEventDetail struct {
	IP        string    `json:"ip"`
	StartTime time.Time `json:"startTime"`
	EndTime   time.Time `json:"endTime"`
}
type GroupRequestEventDetail struct {
	IP        string `json:"ip"`
	StartTime int64  `json:"startTime"`
	EndTime   int64  `json:"endTime"`
}

type GroupResult struct {
	Name     string
	Type     int
	Ips      []string
	Strategy string
}

type GroupCleanData struct {
	Ip          string  `json:"ip" redis:"ip"`
	Time        int64   `json:"time" redis:"time"`
	InBps       int64   `json:"inBps" redis:"inBps"`
	OutBps      int64   `json:"outBps" redis:"outBps"`
	InPps       int64   `json:"inPps" redis:"inPps"`
	OutPps      int64   `json:"outPps" redis:"outPps"`
	InAckPps    int64   `json:"inAckPps" redis:"inAckPps"`
	OutAckPps   int64   `json:"outAckPps" redis:"outAckPps"`
	InAckBps    int64   `json:"inAckBps" redis:"inAckBps"`
	OutAckBps   int64   `json:"outAckBps" redis:"outAckBps"`
	InSynPps    int64   `json:"inSynPps" redis:"inSynPps"`
	OutSynPps   int64   `json:"outSynPps" redis:"outSynPps"`
	InUdpPps    int64   `json:"inUdpPps" redis:"inUdpPps"`
	OutUdpPps   int64   `json:"outUdpPps" redis:"outUdpPps"`
	InUdpBps    int64   `json:"inUdpBps" redis:"inUdpBps"`
	OutUdpBps   int64   `json:"outUdpBps" redis:"outUdpBps"`
	InIcmpPps   int64   `json:"inIcmpPps" redis:"inIcmpPps"`
	InIcmpBps   int64   `json:"inIcmpBps" redis:"inIcmpBps"`
	OutIcmpBps  int64   `json:"outIcmpBps" redis:"outIcmpBps"`
	InDnsPps    int64   `json:"inDnsPps" redis:"inDnsPps"`
	OutDnsPps   int64   `json:"outDnsPps" redis:"outDnsPps"`
	InDnsBps    int64   `json:"inDnsBps" redis:"inDnsBps"`
	OutDnsBps   int64   `json:"outDnsBps" redis:"outDnsBps"`
	FilterId    int     `json:"filterId" redis:"filterId"`
	Filter      *string `json:"filter" redis:"filter"`
	AttackFlags int     `json:"attackFlags" redis:"attackFlags"`
	Host        *string `json:"host" redis:"host"`
	Count       int     `json:"count" redis:"count"`
	IpType      int     `json:"ipType" redis:"ipType"`
	OutIcmpPps  int64   `json:"outIcmpPps" redis:"outIcmpPps"`
}

type GroupSpectrumData struct {
	Ip           string  `json:"ip" redis:"ip"`
	Time         int64   `json:"time" redis:"time"`
	MonitorId    int     `json:"monitorId" redis:"monitorId"`
	Monitor      *string `json:"monitor" redis:"monitor"`
	DataType     int     `json:"dataType" redis:"dataType"`
	Bps          int64   `json:"bps" redis:"bps"`
	Pps          int64   `json:"pps" redis:"pps"`
	SynBps       int64   `json:"synBps" redis:"synBps"`
	SynPps       int64   `json:"synPps" redis:"synPps"`
	AckBps       int64   `json:"ackBps" redis:"ackBps"`
	AckPps       int64   `json:"ackPps" redis:"ackPps"`
	SynAckBps    int64   `json:"synAckBps" redis:"synAckBps"`
	SynAckPps    int64   `json:"synAckPps" redis:"synAckPps"`
	IcmpPps      int64   `json:"icmpPps" redis:"icmpPps"`
	IcmpBps      int64   `json:"icmpBps" redis:"icmpBps"`
	SmallPps     int64   `json:"smallPps" redis:"smallPps"`
	NtpPps       int64   `json:"ntpPps" redis:"ntpPps"`
	NtpBps       int64   `json:"ntpBps" redis:"ntpBps"`
	DnsQueryPps  int64   `json:"dnsQueryPps" redis:"dnsQueryPps"`
	DnsQueryBps  int64   `json:"dnsQueryBps" redis:"dnsQueryBps"`
	DnsAnswerPps int64   `json:"dnsAnswerPps" redis:"dnsAnswerPps"`
	DnsAnswerBps int64   `json:"dnsAnswerBps" redis:"dnsAnswerBps"`
	SsdpBps      int64   `json:"ssdpBps" redis:"ssdpBps"`
	SsdpPps      int64   `json:"ssdpPps" redis:"ssdpPps"`
	UdpPps       int64   `json:"udpPps" redis:"udpPps"`
	UdpBps       int64   `json:"udpBps" redis:"udpBps"`
	Qps          int64   `json:"qps" redis:"qps"`
	ReceiveCount int     `json:"receviceCount" redis:"receiveCount"`
	Product      *string `json:"product" redis:"product"`
	Host         *string `json:"host" redis:"host"`
	IpType       int     `json:"ipType" redis:"ipType"`
}

type GroupSpectrumDataResponse struct {
	Code int                 `json:"code"`
	Msg  string              `json:"msg"`
	Data []GroupSpectrumData `json:"data"`
}

type GroupCleanDataResponse struct {
	Code int              `json:"code"`
	Msg  string           `json:"msg"`
	Data []GroupCleanData `json:"data"`
}

type GroupIpDataResponse struct {
	Code int      `json:"code"`
	Msg  string   `json:"msg"`
	Data []string `json:"data"`
}

type GroupStrategyDataResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		MonitorInfo MonitorInfo `json:"monitorInfo"`
		DragInfo    DragInfo    `json:"dragInfo"`
		Nds4Config  Nds4Config  `json:"nds4Config"`
		Nds6Config  Nds6Config  `json:"nds6Config"`
	} `json:"data"`
}

// MonitorInfo 监控阈值
type MonitorInfo struct {
	AutoML   bool  `json:"autoML"`   // 是否开启机器学习
	TotalBps int64 `json:"totalBps"` // 单位bit/s
	TotalPps int64 `json:"totalPps"`
	SynBps   int64 `json:"synBps"`
	SynPps   int64 `json:"synPps"`
	AckBps   int64 `json:"ackBps"`
	AckPps   int64 `json:"ackPps"`
	IcmpBps  int64 `json:"icmpBps"`
	IcmpPps  int64 `json:"icmpPps"`
	UdpBps   int64 `json:"udpBps"`
	UdpPps   int64 `json:"udpPps"`
}

// DragInfo 牵引策略
type DragInfo struct {
	AutoDrag   bool `json:"autoDrag"`   // 是否自动牵引
	AutoUnDrag bool `json:"autoUnDrag"` // 是否自动回迁
}

// Nds4Config ipv4 清洗策略
type Nds4Config struct {
	TNoCleanLimit    int64 `json:"tNoCleanLimit"`    // 不清洗直接转发阈值
	SWhitelist       bool  `json:"sWhitelist"`       // 白名单开关
	SBlacklist       bool  `json:"sBlacklist"`       // 黑名单开关
	SEliminatePkt    bool  `json:"sEliminatePkt"`    // 过滤特殊报文开关
	SMalformedPkt    bool  `json:"sMalformedPkt"`    // 畸形包报文检测开关
	SAcl             bool  `json:"sAcl"`             // ACL开关
	SContentMatch    bool  `json:"sContentMatch"`    // 特征匹配开关
	STcpReflection   int   `json:"sTcpReflection"`   // TCP反射配置：0-关 1-开 2-自动
	SUdpReflection   int   `json:"sUdpReflection"`   // UDP反射配置：0-关 1-开 2-自动
	SFirstPktDrop    int   `json:"sFirstPktDrop"`    // 首包丢弃配置 ：0-关 1-开 2-自动
	SSourceCheck     int   `json:"sSourceCheck"`     // 源认证配置 ：0-关 1-开 2-自动
	SAntiOtherTcp    int   `json:"sAntiOtherTcp"`    // 防护除SYN外的TCP flood攻击  0-关 1-开 2-自动
	SPositionIdc     int   `json:"sPositionIdc"`     // idc国家地理位置：0-关 1-开 2-自动
	SPositionForeign int   `json:"sPositionForeign"` // 国外地理位置：0-关 1-开 2-自动
	SSrcSpeedLimit   bool  `json:"sSrcSpeedLimit"`   // 源ip限速开关
	SDstSpeedLimit   bool  `json:"sDstSpeedLimit"`   // 目的ip限速开关
}

// Nds6Config ipv6清洗策略
type Nds6Config struct {
	TNoCleanLimit  int64 `json:"tNoCleanLimit"`  // 不清洗直接转发阈值
	SMalformedPkt  bool  `json:"sMalformedPkt"`  // 畸形包报文检测开关
	SUdpReflection int   `json:"sUdpReflection"` // UDP反射配置：0-关 1-开 2-自动
	SSrcSpeedLimit bool  `json:"sSrcSpeedLimit"` // 源ip限速开关
	SDstSpeedLimit bool  `json:"sDstSpeedLimit"` // 目的ip限速开关
}

type AttackInfo struct {
	MaxBps      int64 `json:"maxBps" redis:"maxBps"`           // 攻击最大bps
	MaxPps      int64 `json:"maxPps" redis:"maxPps"`           // 攻击最大pps
	MaxCleanBps int64 `json:"maxCleanBps" redis:"maxCleanBps"` // 清洗的最大bps
	MaxCleanPps int64 `json:"maxCleanPps" redis:"maxCleanPps"` // 清洗的最大pps
}

type MatrixAttackInfo struct {
	MaxBps               int64 `json:"maxBps" redis:"maxBps"`                             // 攻击最大bps
	LowerMonitorBpsCount int64 `json:"lowerMonitorBpsCount" redis:"lowerMonitorBpsCount"` // 小于监控bps阈值次数
	SpectrumAlertID      int   `json:"spectrumAlertID" redis:"spectrumAlertID"`
	ProjectID            int   `json:"projectID" redis:"projectID"`
}

type SpectrumQueryInfo struct {
	IP        string    `json:"ip"`
	StartTime time.Time `json:"startTime" query:"start_time"`
	EndTime   time.Time `json:"endTime" query:"end_time"`
}
