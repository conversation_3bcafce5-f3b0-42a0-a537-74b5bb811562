package socgroup

import "time"

type QueryRequestVO struct {
	StartTime time.Time `json:"startTime"` //起始时间戳
	Offset    int       `json:"offset"`    //偏移量，0开始
}

type QueryRequestData struct {
	StartTime int64 `json:"starttime"` //起始时间戳
	Offset    int   `json:"offset"`    //偏移量，0开始
}
type QueryResponseData struct {
	Code      int         `json:"code"`            //0表示成功，非0表示失败
	Msg       string      `json:"msg"`             //返回的消息内容
	Total     int         `json:"total,omitempty"` //满足条件总数
	DataItems []WorkOrder `json:"dataitems"`       //返回工单的内容
}

type AddRequestData struct {
	WorkOrderType    string `json:"workOrderType"`    //DOSDIVERT固定值，表示DOS清洗防护工单
	Name             string `json:"name"`             //标题
	Description      string `json:"description"`      //工单描述信息
	FollowUserIdList []int  `json:"followUserIdList"` //工单跟踪者id列表
	DepartmentId     int    `json:"departmentId"`     //部⻔对应的ID，必须是SOC中已有的部⻔信息
	//牵引IP列表
	//如:
	//*******-10
	//*******/32
	//******* 一行一个，前两个代表IP段，最后一个代表单个IP
	IpList     string  `json:"ipList"`
	Bandwidth  float32 `json:"bandwidth"`  //牵引IP列表中最小的物理带宽或压测带宽(非业务流量)，保留两 位小数，单位Gbps
	DivertType int     `json:"divertType"` //清洗方式, 0表示清洗上线，1表示清洗下线 2调优
	//操作方式，0表示自动，1表示手动
	//[清洗上线] 可以设置0，1
	//[清洗下线、调优]只能设置1
	OpType                 int    `json:"opType"`
	OpTime                 int64  `json:"opTime"`                 //操作时间，时间戳，毫秒
	ConfigType             int    `json:"configType"`             //参数配置类型，0表示默认，1表示自定义  仅[清洗上线] 有效
	ConfigArgs             string `json:"configArgs"`             // 参数配置  仅[清洗上线和清洗调优且configType=1(自定义防护参数)] 有效
	ProductName            string `json:"productName"`            //产品中文名
	ProductAlias           string `json:"productAlias,omitempty"` //产品代号，不能包含中文(非必填)
	EmergencyContacterList []User `json:"emergencyContacterList"` //工单应急接口人
}
type AddResponseData struct {
	Code      int       `json:"code"`
	Msg       string    `json:"msg"`
	DataItems WorkOrder `json:"dataitems"`
}

type User struct {
	Name  string `json:"name"`
	Email string `json:"email"`
	Phone string `json:"phone,omitempty"`
}

type WorkOrder struct {
	Id                   int         `json:"id"`          //【SOC上工单的ID】
	Name                 string      `json:"name"`        //【标题】
	Description          string      `json:"description"` //【描述】
	CreateTime           int64       `json:"createTime"`  //【创建时间】
	UpdateTime           int64       `json:"updateTime"`  //【更新时间】
	Remark               interface{} `json:"remark"`      //【备注】
	AssetsContacterGroup struct {    //【联系人组】
		Id                 int         `json:"id"`
		Name               interface{} `json:"name"`
		Description        interface{} `json:"description"`
		AssetsType         string      `json:"assetsType"`
		ContacterGroupList []struct {  //【处理人列表】
			Id                  int         `json:"id"`
			Name                string      `json:"name"`
			Description         interface{} `json:"description"`
			CreateTime          int64       `json:"createTime"`
			UpdateTime          int64       `json:"updateTime"`
			Remark              interface{} `json:"remark"`
			BaseContacterObject struct {
				DataCategory struct {
					Id          int         `json:"id"`
					Name        string      `json:"name"`
					Description interface{} `json:"description"`
				} `json:"dataCategory"`
				Leader     interface{} `json:"leader"`
				Contacters []struct {
					Id                   int           `json:"id"`
					Email                string        `json:"email"`
					Phone                interface{}   `json:"phone"`
					Username             string        `json:"username"`
					Fullname             string        `json:"fullname"`
					UserRole             interface{}   `json:"userRole"`
					UserGroupList        interface{}   `json:"userGroupList"`
					Department1          interface{}   `json:"department1"`
					Department2          interface{}   `json:"department2"`
					Department3          interface{}   `json:"department3"`
					Count                int           `json:"count"`
					CreateTime           interface{}   `json:"createTime"`
					LastLoginTimeDate    interface{}   `json:"lastLoginTimeDate"`
					UserAuthorityList    interface{}   `json:"userAuthorityList"`
					GrantedAuthorityList []interface{} `json:"grantedAuthorityList"`
					SupperUser           bool          `json:"supperUser"`
				} `json:"contacters"`
				Cclists []struct {
					Id                   int           `json:"id"`
					Email                string        `json:"email"`
					Phone                interface{}   `json:"phone"`
					Username             string        `json:"username"`
					Fullname             string        `json:"fullname"`
					UserRole             interface{}   `json:"userRole"`
					UserGroupList        interface{}   `json:"userGroupList"`
					Department1          interface{}   `json:"department1"`
					Department2          interface{}   `json:"department2"`
					Department3          interface{}   `json:"department3"`
					Count                int           `json:"count"`
					CreateTime           interface{}   `json:"createTime"`
					LastLoginTimeDate    interface{}   `json:"lastLoginTimeDate"`
					UserAuthorityList    interface{}   `json:"userAuthorityList"`
					GrantedAuthorityList []interface{} `json:"grantedAuthorityList"`
					SupperUser           bool          `json:"supperUser"`
				} `json:"cclists"`
				DepartmentName interface{} `json:"departmentName"`
				DepartId       interface{} `json:"departId"`
				EntityEmpty    bool        `json:"entityEmpty"`
			} `json:"baseContacterObject"`
			NotifyStrategy string `json:"notifyStrategy"`
			MapGroupId     int    `json:"mapGroupId"`
			EntityEmpty    bool   `json:"entityEmpty"`
		} `json:"contacterGroupList"`
		VulsContacterGroup    interface{} `json:"vulsContacterGroup"`
		DefaultContacterGroup struct {    //【忽略 忽略 忽略】
			Id                  int         `json:"id"`
			Name                string      `json:"name"`
			Description         interface{} `json:"description"`
			CreateTime          int64       `json:"createTime"`
			UpdateTime          int64       `json:"updateTime"`
			Remark              interface{} `json:"remark"`
			BaseContacterObject struct {
				DataCategory struct {
					Id          int         `json:"id"`
					Name        string      `json:"name"`
					Description interface{} `json:"description"`
				} `json:"dataCategory"`
				Leader     interface{} `json:"leader"`
				Contacters []struct {
					Id                   int           `json:"id"`
					Email                string        `json:"email"`
					Phone                interface{}   `json:"phone"`
					Username             string        `json:"username"`
					Fullname             string        `json:"fullname"`
					UserRole             interface{}   `json:"userRole"`
					UserGroupList        interface{}   `json:"userGroupList"`
					Department1          interface{}   `json:"department1"`
					Department2          interface{}   `json:"department2"`
					Department3          interface{}   `json:"department3"`
					Count                int           `json:"count"`
					CreateTime           interface{}   `json:"createTime"`
					LastLoginTimeDate    interface{}   `json:"lastLoginTimeDate"`
					UserAuthorityList    interface{}   `json:"userAuthorityList"`
					GrantedAuthorityList []interface{} `json:"grantedAuthorityList"`
					SupperUser           bool          `json:"supperUser"`
				} `json:"contacters"`
				Cclists []struct { //【跟踪人列表】
					Id                   int           `json:"id"`
					Email                string        `json:"email"`
					Phone                interface{}   `json:"phone"`
					Username             string        `json:"username"`
					Fullname             string        `json:"fullname"`
					UserRole             interface{}   `json:"userRole"`
					UserGroupList        interface{}   `json:"userGroupList"`
					Department1          interface{}   `json:"department1"`
					Department2          interface{}   `json:"department2"`
					Department3          interface{}   `json:"department3"`
					Count                int           `json:"count"`
					CreateTime           interface{}   `json:"createTime"`
					LastLoginTimeDate    interface{}   `json:"lastLoginTimeDate"`
					UserAuthorityList    interface{}   `json:"userAuthorityList"`
					GrantedAuthorityList []interface{} `json:"grantedAuthorityList"`
					SupperUser           bool          `json:"supperUser"`
				} `json:"cclists"`
				DepartmentName interface{} `json:"departmentName"`
				DepartId       interface{} `json:"departId"`
				EntityEmpty    bool        `json:"entityEmpty"`
			} `json:"baseContacterObject"`
			NotifyStrategy string `json:"notifyStrategy"`
			MapGroupId     int    `json:"mapGroupId"`
			EntityEmpty    bool   `json:"entityEmpty"`
		} `json:"defaultContacterGroup"`
	} `json:"assetsContacterGroup"`
	Leader     interface{} `json:"leader"`
	AssetsType string      `json:"assetsType"`
	Department struct {
		Id                   int         `json:"id"`
		Name                 string      `json:"name"`
		Description          string      `json:"description"`
		CreateTime           int64       `json:"createTime"`
		UpdateTime           int64       `json:"updateTime"`
		Remark               interface{} `json:"remark"`
		AssetsContacterGroup interface{} `json:"assetsContacterGroup"`
		Leader               interface{} `json:"leader"`
		AssetsType           string      `json:"assetsType"`
		Level                int         `json:"level"`
		Upperid              int         `json:"upperid"`
		UpperName            interface{} `json:"upperName"`
		OaId                 interface{} `json:"oaId"`
		ParentOaId           interface{} `json:"parentOaId"`
		State                interface{} `json:"state"`
		LevelOneId           int         `json:"levelOneId"`
	} `json:"department"`
	Product              interface{} `json:"product"`
	Dept1                interface{} `json:"dept1"`
	Dept2                interface{} `json:"dept2"`
	Dept3                interface{} `json:"dept3"`
	WorkOrderType        string      `json:"workOrderType"`
	Phase                int         `json:"phase"`
	Url                  interface{} `json:"url"`
	AttachGroupkey       interface{} `json:"attachGroupkey"`
	ResultAttachGroupkey string      `json:"resultAttachGroupkey"`
	AttachFileName       interface{} `json:"attachFileName"`
	ResultAttachFileName string      `json:"resultAttachFileName"`
	WorkOrderStatus      string      `json:"workOrderStatus"`
	ExpectTime           interface{} `json:"expectTime"`
	FinalDeadlineTime    interface{} `json:"finalDeadlineTime"`
	ApplyUser            struct {    //【申请人】
		Id                   int           `json:"id"`
		Email                string        `json:"email"`
		Phone                interface{}   `json:"phone"`
		Username             string        `json:"username"`
		Fullname             string        `json:"fullname"`
		UserRole             interface{}   `json:"userRole"`
		UserGroupList        interface{}   `json:"userGroupList"`
		Department1          interface{}   `json:"department1"`
		Department2          interface{}   `json:"department2"`
		Department3          interface{}   `json:"department3"`
		Count                int           `json:"count"`
		CreateTime           interface{}   `json:"createTime"`
		LastLoginTimeDate    interface{}   `json:"lastLoginTimeDate"`
		UserAuthorityList    interface{}   `json:"userAuthorityList"`
		GrantedAuthorityList []interface{} `json:"grantedAuthorityList"`
		SupperUser           bool          `json:"supperUser"`
	} `json:"applyUser"`
	CommentStatus int         `json:"commentStatus"`
	FlowId        interface{} `json:"flowId"`
	TestCycle     int         `json:"testCycle"`
	ParentId      int         `json:"parentId"`
	CycleStatus   int         `json:"cycleStatus"`
	WorkingDays   float64     `json:"workingDays"`
	AutoTestType  interface{} `json:"autoTestType"`
	ExtraConfig   struct {
		DepartmentName         string  `json:"departmentName"`
		DivertType             int     `json:"divertType"` //【清洗方式】
		Bandwidth              float64 `json:"bandwidth"`
		OpTime                 int64   `json:"opTime"`                 //【操作时间】
		OpType                 int     `json:"opType"`                 //【操作方式】
		DepartmentId           int     `json:"departmentId"`           //【SOC平台中的部⻔id】
		ConfigArgs             string  `json:"configArgs"`             //【自定义的参数】
		ConfigType             int     `json:"configType"`             //【参数配置类型】
		ProductName            string  `json:"productName"`            //【自己填写的产品中文名】
		IpList                 string  `json:"ipList"`                 //【IP列表】
		ProductAlias           string  `json:"productAlias"`           //【自己填写的产品代号，选填，不可包含中文】
		EmergencyContacterList []User  `json:"emergencyContacterList"` //【紧急联系人】
	} `json:"extraConfig"`
	ExtraConfigString string      `json:"extraConfigString"` //【忽略忽略忽略】
	DepartmentName    interface{} `json:"departmentName"`
	Department1StName interface{} `json:"department1stName"`
	Department2NdName interface{} `json:"department2ndName"`
	Department3RdName interface{} `json:"department3rdName"`
}
