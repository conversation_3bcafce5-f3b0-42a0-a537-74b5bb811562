/**
* <AUTHOR>
* @date 2023-02-03 12:22
* @description
 */

package netease

type External struct {
	Nds      Nds      `json:"nds"`
	WoFang   WoFang   `json:"woFang"`
	SocGroup SocGroup `json:"socGroup"`
	Service  Service  `json:"service"`
	Ka<PERSON><PERSON>ka    `json:"kafka"`
}

type Nds struct {
	Api       string `json:"api"`
	Apikey    string `json:"apikey"`
	OldApi    string `json:"oldApi"`
	OldApikey string `json:"oldApikey"`
}

type SocGroup struct {
	Api      string `json:"api"`
	ApiTest  string `json:"apiTest"`
	Username string `json:"username"`
	Password string `json:"password"`
}
type WoFang struct {
	Api              string `json:"api,omitempty"`
	AppId            int    `json:"appId,omitempty"`
	AppKey           string `json:"appKey,omitempty"`
	IdcCode          int    `json:"idcCode,omitempty"`
	AutoUnDrogSecond int    `json:"autoUnDrogSecond,omitempty"`

	Api2            string `json:"api2,omitempty"`
	Api2Id          int    `json:"api2Id,omitempty"`
	Api2Key         string `json:"api2Key,omitempty"`
	Version         string `json:"version"`
	SignatureMethod string `json:"signatureMethod"`
}
type Service struct {
	SocGroupApiKey  string `json:"socGroupApiKey"`
	QianliyanApiKey string `json:"qianliyanApiKey"`
}
type Kafka struct {
	NetCloud NetCloud `json:"netCloud"`
}
type NetCloud struct {
	GroupId      string
	Broker       string
	Topics       []string
	SaslEnable   bool
	SaslUser     string
	SaslPassword string
}
