/**
* <AUTHOR>
* @date 2023-04-11 16:04
* @description
 */

package netease

import "time"

type MatrixStrategy struct {
	Name       string
	Region     string
	NetType    string
	ISP        string
	MonitorBps int64
	DragBps    int64
	DragType   int
}

type MatrixIPDbResponse struct {
	Code int `json:"code"`
	Data []struct {
		Id         int    `json:"id"`
		SegmentId  int    `json:"segment_id"`
		Address    string `json:"address"`
		Status     int    `json:"status"`
		AssetId    string `json:"asset_id"`
		Project    string `json:"project"`
		Note       string `json:"note"`
		SysNote    string `json:"sys_note"`
		SyncSrc    int    `json:"sync_src"`
		RmsUrl     string `json:"rms_url"`
		Creator    string `json:"creator"`
		Editor     string `json:"editor"`
		CreateTime int    `json:"create_time"`
		UpdateTime int    `json:"update_time"`
	} `json:"data"`
	Message string `json:"message"`
	Success bool   `json:"success"`
	Total   int    `json:"total"`
}

type MatrixFlowResponse struct {
	Status string `json:"status"`
	Data   struct {
		ResultType string         `json:"resultType"`
		Result     []MetricResult `json:"result"`
	} `json:"data"`
}

type MetricResult struct {
	Metric Metric `json:"metric"`
	// "value": [
	// 1682240494,
	// "111153851"
	// ]
	Value []any `json:"value"`
}
type Metric struct {
	Name         string `json:"__name__"`
	ShareProject string `json:"_share_project_"`
	DeviceIp     string `json:"device_ip"`
	DeviceName   string `json:"device_name"`
	Interface    string `json:"interface"`
}
type MatrixFlowData struct {
	DeviceIp   string    `json:"device_ip"`
	DeviceName string    `json:"device_name"`
	Interface  string    `json:"interface"`
	Time       time.Time `json:"time"`
	Bps        int64     `json:"bps"`
}

type MatrixStrategyInfo struct {
	ID         string `json:"id" redis:"id"`
	Name       string `json:"name" redis:"name"`
	MonitorBps int64  `json:"monitorBps" redis:"monitorBps"`
	DragBps    int64  `json:"dragBps" redis:"dragBps"`
	DragType   int    `json:"dragType" redis:"dragType"`
}

type MatrixLineQuery struct {
	ProjectCode  string `json:"project_code"`
	Query        string `json:"query"`
	Source       string `json:"source"`
	Nocache      int    `json:"nocache"`
	ShareProject string `json:"share_project"`
}
type MatrixLineQueryRawResponse struct {
	Status string `json:"status"`
	Data   struct {
		ResultType string               `json:"resultType"`
		Result     []MatrixMetricResult `json:"result"`
	} `json:"data"`
}
type MatrixLineMetricResult struct {
	Name         string `json:"__name__"`
	ShareProject string `json:"_share_project_"`
	EntityType   string `json:"entity_type"`
	Isp          string `json:"isp"`
	Region       string `json:"region"`
	Type         string `json:"type"`
}
type MatrixMetricResult struct {
	Metric MatrixLineMetricResult `json:"metric"`
	Value  []any                  `json:"value"`
}

type MatrixLine struct {
	Isp      string `json:"isp"`
	Region   string `json:"region"`
	NetType  string `json:"type"`
	DataType string `json:"dataType"`
	Data     any    `json:"data,omitempty"`
}
type MatrixBps struct {
	QueryTime time.Time `json:"queryTime"`
	Bps       int64     `json:"bps"`
}
type MatrixPps struct {
	QueryTime time.Time `json:"queryTime"`
	Pps       int64     `json:"pps"`
}
type MatrixRatio struct {
	QueryTime time.Time `json:"queryTime"`
	Ratio     float64   `json:"ratio"`
}

type MatrixLineDataAll struct {
	Isp       string    `json:"isp"`
	Region    string    `json:"region"`
	NetType   string    `json:"type"`
	Bps       int64     `json:"bps"`
	Pps       int64     `json:"pps"`
	Ratio     float64   `json:"ratio"`
	QueryTime time.Time `json:"queryTime"`
}
