/**
* <AUTHOR>
* @date 2023-05-16 15:04
* @description
 */

package neteaseip

type ResponseData struct {
	Code int `json:"code"`
	Data struct {
		Ip        string `json:"ip"`
		Continent struct {
			Code  string `json:"code"`
			Names Names  `json:"names"`
		} `json:"continent"`
		Country struct {
			IsoCode string `json:"iso_code"`
			Names   Names  `json:"names"`
		} `json:"country"`
		Subdivisions struct {
			IsoCode string `json:"iso_code"`
			Names   Names  `json:"names"`
		} `json:"subdivisions"`
		City struct {
			Id    int   `json:"id"`
			Names Names `json:"names"`
		} `json:"city"`
		County struct {
			Id    int   `json:"id"`
			Names Names `json:"names"`
		} `json:"county"`
		Isp struct {
			Id    int `json:"id"`
			Names struct {
				ZhCN string `json:"zh_CN"`
			} `json:"names"`
		} `json:"isp"`
		Location struct {
			Org       string  `json:"org"`
			TimeZone  string  `json:"time_zone"`
			Latitude  float64 `json:"latitude"`
			Longitude float64 `json:"longitude"`
		} `json:"location"`
	} `json:"data"`
}

type Names struct {
	Ru   string `json:"ru"`
	Fr   string `json:"fr"`
	En   string `json:"en"`
	Pt   string `json:"pt"`
	ZhCN string `json:"zh_CN"`
	De   string `json:"de"`
	Jp   string `json:"jp"`
	Spa  string `json:"spa"`
}
