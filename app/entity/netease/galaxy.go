/**
* <AUTHOR>
* @date 2022-12-21 15:02
* @description
 */

package netease

type SpotlightResult struct {
	Id          string      `json:"id"`
	Ip          string      `json:"ip"`
	IpInternal  string      `json:"ip_internal"`
	Name        string      `json:"name"`
	Pid         string      `json:"pid"`
	IspId       string      `json:"isp_id"`
	MachineType string      `json:"machineType"`
	ParentId    string      `json:"parent_id"`
	Description string      `json:"description"`
	Dirty       string      `json:"dirty"`
	Asset       interface{} `json:"asset"`
	Ips         []struct {
		Id          string      `json:"id"`
		Ip          string      `json:"ip"`
		IpType      string      `json:"ipType"`
		MachineId   string      `json:"machine_id"`
		Description string      `json:"description"`
		RegionId    interface{} `json:"region_id"`
	} `json:"ips"`
	Groups []struct {
		GroupName  string `json:"groupName"`
		Pid        string `json:"pid"`
		Gid        string `json:"gid"`
		IspId      string `json:"isp_id"`
		Status     string `json:"status"`
		GroupCode  string `json:"groupCode"`
		Class      string `json:"class"`
		Type       string `json:"type"`
		ServicesId string `json:"services_id"`
		Id         string `json:"id"`
		Services   []struct {
			ServiceName   string `json:"serviceName"`
			AloneType     string `json:"aloneType"`
			ServiceCode   string `json:"serviceCode"`
			ShortCode     string `json:"shortCode"`
			Sla           string `json:"sla"`
			ServiceType   string `json:"serviceType"`
			ServiceClass  string `json:"serviceClass"`
			Description   string `json:"description"`
			Env           string `json:"env"`
			MachineId     string `json:"machine_id"`
			ServiceId     string `json:"service_id"`
			Rank          string `json:"rank"`
			TimeRank      string `json:"time_rank"`
			Num           string `json:"num"`
			ServiceStatus string `json:"serviceStatus"`
			Id            string `json:"id"`
		} `json:"services"`
	} `json:"groups"`
	Project struct {
		Id               string `json:"id"`
		ProjectCode      string `json:"projectCode"`
		ProjectName      string `json:"projectName"`
		ProjectSettingId string `json:"project_setting_id"`
	} `json:"project"`
	Sa []struct {
		DepartmentId interface{} `json:"department_id"`
		Email        string      `json:"email"`
		Fullname     string      `json:"fullname"`
		Gmail        string      `json:"gmail"`
		Id           int         `json:"id"`
		Name         string      `json:"name"`
		No           string      `json:"no"`
		Phone        string      `json:"phone"`
		Status       int         `json:"status"`
		Type         int         `json:"type"`
		Yixin        interface{} `json:"yixin"`
	} `json:"sa"`
	OnDuty struct {
		Oncall []interface{} `json:"oncall"`
		Leader []interface{} `json:"leader"`
		Master []interface{} `json:"master"`
	} `json:"on_duty"`
	Sla    any         `json:"sla"`
	Region interface{} `json:"region"`
}
