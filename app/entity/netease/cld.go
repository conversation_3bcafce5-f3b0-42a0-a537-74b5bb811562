/**
* <AUTHOR>
* @date 2023-04-10 17:26
* @description
 */

package netease

type CldProject struct {
	Ip      string `json:"ip"`
	Version string `json:"version"`
	Region  string `json:"region"`
	Type    string `json:"type"`
	Tenant  string `json:"tenant"`
}

type CldCIDR struct {
	CidrList []struct {
		Cidr   string `json:"cidr"`
		Type   string `json:"type"`
		Region string `json:"region"`
		Source string `json:"source"` // v2 | v3 | lbc
	} `json:"cidr_list"`
}
