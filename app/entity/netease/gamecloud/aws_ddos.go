/**
* <AUTHOR>
* @date 2023-04-13 15:53
* @description
 */

package gamecloud

import "time"

type AwsResponse struct {
	Items []AwsDosResult `json:"items"`
	Total int            `json:"total"`
	Links struct {
		PreviousPage interface{} `json:"_previous_page"`
		NextPage     string      `json:"_next_page"`
	} `json:"links"`
	Num  int `json:"_num"`
	Page int `json:"_page"`
}
type AwsDosResult struct {
	Id           int         `json:"id"`
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`
	CreatedBy    interface{} `json:"created_by"`
	AttackId     string      `json:"attack_id"`     // 攻击ID
	ResourceArn  string      `json:"resource_arn"`  // 资源Arn
	ResourceType string      `json:"resource_type"` // 资源类型
	Region       string      `json:"region"`        // 资源region，Example: region=[ap-east-1]HongKong
	Resource     string      `json:"resource"`      // 资源信息(EIP类型展示IP地址，LB/AGA展示资源ID)
	StartTime    time.Time   `json:"start_time"`
	EndTime      time.Time   `json:"end_time"`
	Status       string      `json:"status"`       // 状态
	VectorTypes  []string    `json:"vector_types"` // 攻击类型
	SubResources []struct {
		AttackVectors []struct {
			VectorCounters []AttackCounter `json:"VectorCounters"`
			VectorType     string          `json:"VectorType"`
		} `json:"AttackVectors"`
		Counters []AttackCounter `json:"Counters"`
		Id       string          `json:"Id"`
		Type     string          `json:"NetType"`
	} `json:"sub_resources"`
	AttackCounters   []AttackCounter `json:"attack_counters"`
	AttackProperties []interface{}   `json:"attack_properties"`
	Mitigations      []struct {
		MitigationName string `json:"MitigationName"`
	} `json:"mitigations"`
	Project      string `json:"project"`    // Auth项目代号
	AccountId    string `json:"account_id"` // AWS账号ID
	DurationTime int64  `json:"duration_time"`
}

type AttackCounter struct {
	Average int64  `json:"Average"`
	Max     int64  `json:"Max"`
	N       int    `json:"N"`
	Name    string `json:"Name"`
	Sum     int64  `json:"Sum"`
	Unit    string `json:"Unit"`
}
