/**
* <AUTHOR>
* @date 2023-03-14 15:45
* @description
 */

package gamecloud

// 通用数据
type Tags struct {
	StartTs string `json:"start_ts"`              // 触发攻击开始的时间戳
	SrcIp   string `json:"src_ip"`                // 攻击源IP
	SrcPort string `json:"src_port"`              // 攻击源端口
	DstIp   string `json:"dst_ip"`                // 受攻击的目的IP
	DstPort string `json:"dst_port"`              // 受攻击的目的端口
	Proto   string `json:"proto"`                 // 4层协议，6代表TCP协议，17代表UDP协议
	Tenant  string `json:"tenant_name,omitempty"` // EIP所关联的项目，如果查不到项目，默认就是UNKNOWN
}

// 攻击开始
type StartTags struct {
	Tags
	DefenceMode  string `json:"defence_mode,omitempty"`  // 清洗攻击流量的模式
	FlowMode     string `json:"flow_mode,omitempty"`     // 流匹配模式
	TcpAckNum    string ` json:"tcp_ack_num,omitempty"`  // TCP报文中的ACK值
	TcpSeqNum    string `json:"tcp_seq_num,omitempty"`   // TCP报文中的SEQ值
	DefenceLevel string `json:"defence_level,omitempty"` // 防护水平，目前有两种水平：0表示监控而不丢包，1表示丢包
}

// 攻击结束
type EndTags struct {
	StartTags
	FinalTs           string `json:"final_ts,omitempty"`       // 攻击结束（最后一个攻击报文到达）的时间戳
	Count             int    `json:"count,omitempty"`          // 从攻击开始到攻击结束清洗的报文数量
	MaxAttackPps      string `json:"max_attack_pps,omitempty"` // 从攻击开始到攻击结束，最大的pps
	OverLimitPktCount string `json:"overlimit_pkt_count"`      // 超出阈值的报文总数
}

// 流超限数据
type FlowTags struct {
	Tags
	FinalTs             string `json:"final_ts,omitempty"`                // 攻击结束（最后一个攻击报文到达）的时间戳
	Count               int    `json:"count,omitempty"`                   // 表示从触发攻击开始到上一个报文到达的时间内，总共的请求数量
	FlowOverMaxPpsCount string `json:"flow_over_max_pps_count,omitempty"` // 总共pps>1000的次数
	MaxAttackPps        string `json:"max_attack_pps,omitempty"`          // 从攻击开始到攻击结束，最大的pps
}

// 流的攻击pps数据
type FlowAttackPpsTag struct {
	Tags
	FinalTs          string `json:"final_ts"`           // pps统计结束的时间，开始到结束就是1秒
	CurrentAttackPps string `json:"current_attack_pps"` // 这一秒的pps
}

// 层级pps
type FlowLevelTags struct {
	FlowLevel1    string `json:"flow_level_1"`    // pps为[0,50]的流数量
	FlowLevel2    string `json:"flow_level_2"`    // pps为[0,50]的流数量
	FlowLevel3    string `json:"flow_level_3"`    // pps为[101,500]的流数量
	FlowLevel4    string `json:"flow_level_4"`    // pps为[501,1000]的流数量
	FlowLevel5    string `json:"flow_level_5"`    // pps为[1001,正无穷]的流数量
	PolicyGroupId string `json:"policy_group_id"` // 策略组的ID
}
