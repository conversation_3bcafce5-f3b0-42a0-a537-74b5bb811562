/**
* <AUTHOR>
* @date 2023-03-14 14:58
* @description
 */

package gamecloud

// First 用于首次反序列化数据，区分数据类型
type First struct {
	Data []any  `json:"data"`
	Type string `json:"type"`
}

type Data struct {
	Metric    string `json:"metric"`    //数据格式类型，攻击开始：attack_start_data，攻击结束：attack_end_data，流超限数据：attack_flow_data，流的攻击pps数据：flow_attack_pps_data
	Value     int    `json:"value"`     //暂时保留
	Timestamp int64  `json:"timestamp"` //上报的时间戳
}

// AttackStartData 攻击开始数据（attack_start_data)，表示某一条/某一组连接访问宿主的频率过高（超过了阈值），触发了清洗行为，简单描述即为攻击开始。
type AttackStartData struct {
	Data Start  `json:"data"`
	Type string `json:"type"` //消息来源，固定为：aegis
}
type Start struct {
	Data
	Tags StartTags `json:"tags"` //具体清洗数据字段
}

// AttackEndData 攻击结束数据（attack_end_data），在攻击开始的基础上，上述某一条/某一组连接访问宿主的频率恢复正常（低于阈值），并且在一定时间内维持该状态，则表示为攻击结束
type AttackEndData struct {
	Data End    `json:"data"`
	Type string `json:"type"` //消息来源，固定为：aegis
}
type End struct {
	Data
	Tags EndTags `json:"tags"` //具体清洗数据字段
}

// FlowData 流超限数据（flow_data），表示某一条连接的发包速率严重超出正常连接发包速率，比如正常用户每条流每秒钟最多请求100个请求，但是发现某一条连接请求每秒超过了1000次。
type FlowData struct {
	Data Flow   `json:"data"`
	Type string `json:"type"` //消息来源，固定为：aegis
}
type Flow struct {
	Data
	Tags FlowTags `json:"tags"` //具体清洗数据字段
}

// FlowAttackPpsData 流的攻击pps数据
type FlowAttackPpsData struct {
	Data FlowAttackPps `json:"data"`
	Type string        `json:"type"` //消息来源，固定为：aegis
}
type FlowAttackPps struct {
	Data
	Tags FlowAttackPpsTag `json:"tags"` //具体清洗数据字段
}

// FlowLevelData 层级pps
type FlowLevelData struct {
	Data FlowLevel `json:"data"`
	Type string    `json:"type"` //消息来源，固定为：aegis
}

type FlowLevel struct {
	Data
	Tags FlowLevelTags `json:"tags"`
}
