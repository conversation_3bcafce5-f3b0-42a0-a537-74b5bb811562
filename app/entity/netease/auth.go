/**
* <AUTHOR>
* @date 2022-12-28 12:28
* @description
 */

package netease

type ProjectResult struct {
	Business           string `json:"business"`
	Code               string `json:"code"`
	CodeAlias          string `json:"code_alias"`
	CodeType           string `json:"code_type"`
	CostCode           string `json:"cost_code"`
	CostName           string `json:"cost_name"`
	Description        string `json:"description"`
	DevelopPlace       string `json:"develop_place"`
	FinanceCode        string `json:"finance_code"`
	FinanceName        string `json:"finance_name"`
	Genre              string `json:"genre"`
	Id                 int    `json:"id"`
	MaintenanceGroup   any    `json:"maintenance_group"`
	MaintenanceTime    any    `json:"maintenance_time"`
	Name               string `json:"name"`
	Origin             string `json:"origin"`
	Product            string `json:"product"`
	PublishPlace       string `json:"publish_place"`
	Sla                any    `json:"sla"`
	Team               string `json:"team"`
	TertiaryDepartment string `json:"tertiary_department"`
	Timezone           string `json:"timezone"`
	Type               string `json:"type"`
	Workshop           string `json:"workshop"`
}

type SaResult struct {
	Items []struct {
		DepartmentId any    `json:"department_id"`
		Email        string `json:"email"`
		Fullname     string `json:"fullname"`
		Gmail        string `json:"gmail"`
		Id           int    `json:"id"`
		Name         string `json:"name"`
		No           string `json:"no"`
		Phone        string `json:"phone"`
		Status       int    `json:"status"`
		Type         int    `json:"type"`
		Yixin        any    `json:"yixin"`
	} `json:"items"`
	Total int `json:"total"`
}

type XAuth struct {
	NotifyToken string `json:"notifyToken"`
	V2          V2     `json:"v2"`
	Api         Api    `json:"api"`
}
type V2 struct {
	User    string `json:"user"`
	Project string `json:"project"`
	Key     string `json:"key"`
	Ttl     int    `json:"ttl"`
}
type V2Response struct {
	Expire  int    `json:"expire"`
	Token   string `json:"token"`
	TokenV1 string `json:"tokenV1"`
	Ttl     int    `json:"ttl"`
	User    string `json:"user"`
}
type Api struct {
	Galaxy    string `json:"galaxy"`
	Notify    string `json:"notify"`
	Auth      string `json:"auth"`
	AuthV2    string `json:"authV2"`
	Lbc       string `json:"lbc"`
	Cld       string `json:"cld"`
	Matrix    Matrix `json:"matrix"`
	Skyline   string `json:"skyline"`
	NeteaseIP string `json:"neteaseIP"`
}
type Matrix struct {
	IPDb string `json:"ipDb"`
	Flow string `json:"flow"`
}
type V2AuthResponse struct {
	Exp  int    `json:"exp"`
	Ist  int    `json:"ist"`
	User string `json:"user"`
}

type V1Group struct {
	Code        string  `json:"code"`
	Description *string `json:"description"`
	Fullname    string  `json:"fullname"`
	Id          int     `json:"id"`
	Name        string  `json:"name"`
	Parent      any     `json:"parent"`
	Project     string  `json:"project"`
	Type        any     `json:"type"`
}
type V1Project struct {
	Business           string  `json:"business"`
	Code               string  `json:"code"`
	CodeAlias          string  `json:"code_alias"`
	CodeType           string  `json:"code_type"`
	CostCode           string  `json:"cost_code"`
	CostName           string  `json:"cost_name"`
	Description        string  `json:"description"`
	DevelopPlace       string  `json:"develop_place"`
	FinanceCode        string  `json:"finance_code"`
	FinanceName        string  `json:"finance_name"`
	Genre              string  `json:"genre"`
	Id                 int     `json:"id"`
	MaintenanceGroup   *string `json:"maintenance_group"`
	MaintenanceTime    any     `json:"maintenance_time"`
	Name               string  `json:"name"`
	Origin             string  `json:"origin"`
	Product            string  `json:"product"`
	PublishPlace       string  `json:"publish_place"`
	Sla                any     `json:"sla"`
	Team               string  `json:"team"`
	TertiaryDepartment string  `json:"tertiary_department"`
	Timezone           string  `json:"timezone"`
	Type               string  `json:"type"`
	Workshop           string  `json:"workshop"`
}
type V1UserInfoResponse struct {
	DepartmentId any         `json:"department_id"`
	Email        string      `json:"email"`
	Fullname     string      `json:"fullname"`
	Gmail        string      `json:"gmail"`
	Groups       []V1Group   `json:"groups"`
	Id           int         `json:"id"`
	Name         string      `json:"name"`
	No           string      `json:"no"`
	Phone        string      `json:"phone"`
	Projects     []V1Project `json:"projects"`
	Status       int         `json:"status"`
	Type         int         `json:"type"`
	Yixin        any         `json:"yixin"`
}
type AllProject struct {
	Items []struct {
		BackupTeamId       any    `json:"backup_team_id"`
		Business           string `json:"business"`
		Code               string `json:"code"`
		CodeAlias          string `json:"code_alias"`
		CodeType           string `json:"code_type"`
		CostCode           string `json:"cost_code"`
		CostName           string `json:"cost_name"`
		Description        string `json:"description"`
		DevelopPlace       string `json:"develop_place"`
		FinanceCode        string `json:"finance_code"`
		FinanceName        string `json:"finance_name"`
		Genre              string `json:"genre"`
		Id                 int    `json:"id"`
		MaintenanceGroup   any    `json:"maintenance_group"`
		MaintenanceTime    any    `json:"maintenance_time"`
		Name               string `json:"name"`
		Origin             string `json:"origin"`
		Product            string `json:"product"`
		PublishPlace       string `json:"publish_place"`
		Sla                any    `json:"sla"`
		Team               any    `json:"team"`
		TeamId             any    `json:"team_id"`
		TertiaryDepartment string `json:"tertiary_department"`
		Timezone           string `json:"timezone"`
		Type               string `json:"type"`
		Workshop           string `json:"workshop"`
	} `json:"items"`
	Total int `json:"total"`
}

type OfflineProject struct {
	Items []V1Project `json:"items"`
	Total int         `json:"total"`
}
