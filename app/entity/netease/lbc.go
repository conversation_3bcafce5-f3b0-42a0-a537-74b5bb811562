/**
* <AUTHOR>
* @date 2022-12-28 12:33
* @description
 */

package netease

type LbcProjectResult struct {
	Alarmflag        bool   `json:"alarmflag"`
	Alarmgroup       string `json:"alarmgroup"`
	Autoreload       bool   `json:"autoreload"`
	Blocking         string `json:"blocking"`
	Configat         string `json:"configat"`
	Connections      any    `json:"connections"`
	Cpucount         int    `json:"cpucount"`
	Createat         string `json:"createat"`
	Creator          string `json:"creator"`
	DedicatedCluster bool   `json:"dedicated_cluster"`
	Dockercount      int    `json:"dockercount"`
	ForwardingMode   string `json:"forwarding_mode"`
	GameEnv          string `json:"game_env"`
	Hcport           any    `json:"hcport"`
	Hpps             any    `json:"hpps"`
	Http2Disabled    bool   `json:"http2_disabled"`
	Id               int    `json:"id"`
	InstanceType     string `json:"instance_type"`
	Keyless          any    `json:"keyless"`
	Kinginxversion   any    `json:"kinginxversion"`
	Layer            int    `json:"layer"`
	Location         string `json:"location"`
	LogEnabled       bool   `json:"log_enabled"`
	Networkopt       bool   `json:"networkopt"`
	Pdsesslimit      int    `json:"pdsesslimit"`
	Pdsratelimit     int    `json:"pdsratelimit"`
	Portcount        int    `json:"portcount"`
	Portlimit        int    `json:"portlimit"`
	Projectname      string `json:"projectname"`
	ProxyBuffering   bool   `json:"proxy_buffering"`
	Public           bool   `json:"public"`
	Readonlyshare    []any  `json:"readonlyshare"`
	Remark           string `json:"remark"`
	Rps              any    `json:"rps"`
	Script           string `json:"script"`
	Sesslimit        int    `json:"sesslimit"`
	Speedup          bool   `json:"speedup"`
	Sratelimit       int    `json:"sratelimit"`
	Sslopt           bool   `json:"sslopt"`
	Status           string `json:"status"`
	Synproxy         bool   `json:"synproxy"`
	Tag              struct {
	} `json:"tag"`
	Tenantid               int    `json:"tenantid"`
	Tenantname             string `json:"tenantname"`
	TracingSamplingPercent int    `json:"tracing_sampling_percent"`
	Uriforward             bool   `json:"uriforward"`
	Uuid                   string `json:"uuid"`
	Viptype                string `json:"viptype"`
	Virtualip              string `json:"virtualip"`
	Virtualip6             any    `json:"virtualip6"`
	Xffmode                string `json:"xffmode"`
}
