/**
* <AUTHOR>
* @date 2022-07-01 11:33
* @description
 */

package config

import "fmt"

type Cache struct {
	Redis Redis `json:"redis"`
}
type Redis struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	DbNum    int    `json:"dbNum"`
}

func (r *Redis) DSN() string {
	return fmt.Sprintf("redis://%s:%s@%s:%s/%d", r.<PERSON>rname, r.Password, r.<PERSON>, r.<PERSON>, r.DbNum)
}
