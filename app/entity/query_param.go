package entity

import (
	"meta/app/ent/extend"
)

type QueryParam struct {
	Search   []string `json:"search"`
	Current  int      `json:"current"`
	PageSize int      `json:"pageSize"`
	Order    string   `json:"order"`
	extend.TimeParam
}

func NewQueryParam() *QueryParam {
	return &QueryParam{Current: 0, PageSize: 20, Order: "-id"}
}

type AttackQuery struct {
	Attacking bool `json:"attacking,omitempty"  query:"attacking,omitempty"`
}
