// Deprecated: 旧沃防api，已失效
package wofang

type Response struct {
	Status int    `json:"status,omitempty"`
	Msg    string `json:"msg,omitempty"`
	Data   any    `json:"data,omitempty"`
}

type Add struct {
	DragType     string `json:"dragType"` // 类型，牵引: qy；黑洞：hd
	Ip           string `json:"ip"`
	UnDragSecond int    `json:"unDragSecond"` // 解封时长(秒)，表示多少秒后自动解封
}
type Query struct {
	DragType string `json:"dragType"`
	Ips      string `json:"ips"`
}

// NewDrag 牵引
func NewDrag(dragType, ip string, unDragSecond int) map[string]any {
	dataMap := make(map[string]any)
	// 牵引类型: qy
	dataMap["qynet"] = dragType
	dataMap["ip"] = ip
	dataMap["untime"] = unDragSecond
	return dataMap
}

// 牵引
func NewDragQy(ip string) map[string]any {
	dataMap := make(map[string]any)
	// 牵引类型: qy
	dataMap["qynet"] = "qy"
	dataMap["ip"] = ip
	dataMap["untime"] = 30
	return dataMap
}

// 黑洞
func NewDragHd(ip string) map[string]any {
	dataMap := make(map[string]any)
	// 牵引类型: hd
	dataMap["qynet"] = "hd"
	dataMap["ip"] = ip
	dataMap["untime"] = 30
	return dataMap
}

type NewQuery struct {
	Ip string `json:"ip"`
}
