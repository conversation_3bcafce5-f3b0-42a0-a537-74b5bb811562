package wofang

import "time"

type Response2 struct {
	Code    int    `json:"Code"`
	Message string `json:"Message"`
	Data    any    `json:"Data,omitempty"`
}
type AttackDataResponse struct {
	Code    int                `json:"Code"`
	Message string             `json:"Message"`
	Data    []WoFangAttackData `json:"Data"`
}

type WoFangAttackData struct {
	AttackStatus int    `json:"attackStatus"`
	AttackType   string `json:"attackType"`
	DeviceIp     string `json:"deviceIp"`
	EndTime      string `json:"endTime"`
	Id           int    `json:"id"`
	MaxDropKbps  int64  `json:"maxDropKbps"` //单位是 k
	MaxInKbps    int64  `json:"maxInKbps"`   //单位是 k
	StartTime    string `json:"startTime"`
	ZoneIp       string `json:"zoneIp"`
}

// NewDrag2 牵引，取消牵引
func NewDrag2(action, ip string, unDragSecond int, startTime time.Time) map[string]any {
	dataMap := make(map[string]any)
	dataMap["Action"] = action
	dataMap["DefendIp"] = ip
	if unDragSecond != 0 {
		dataMap["Duration"] = unDragSecond
	}
	if !startTime.IsZero() {
		dataMap["Duration"] = startTime.Unix()
	}
	return dataMap
}
