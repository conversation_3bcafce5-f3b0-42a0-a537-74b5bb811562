[stage]
#   - "dev", for start server without graceful shutdown
#   - "prod", for start server with graceful shutdown
status = "dev"
# 默认sa用户（前端登录）
user = "_ddos"
password = "6RKjYhCz2DpOlRdb"
httpSchema = "https"
#domain = "ddos.nie.netease.com"
#domain = "ddos-test.nie.netease.com"
domain = "localhost:8000"

[stage.api]
# 所有用户可读（GET）；完整路径
# publicGetPath = ["/api/v1/user/info", "/api/v1/tenant"]
publicGetPath = []
# 仅sa用户可以操作，如果在publicGetPathPrefix中，则所有可读
# 路径前缀
saPathPrefix = [
    "/api/v1/systemapi",
    "/api/v1/casbinrule",
    "/api/v1/tenant",
    "/api/v1/user",
    "/api/v1/matrixstrategy",
    "/api/v1/useroperationlog",
    "/api/v1/systemconfig",
    "/api/v1/datasync",
    "/api/v1/nds",
    "/swagger",
    "/api/v1/group",
    "/api/v1/external", # 外部接口，手动授权
]

[fiber]
#host = "127.0.0.1"
host = "0.0.0.0"
port = 9001
# 单位：秒
readTimeout = 60
# default ->  encoding/json
# sonic   ->  bytedance/sonic
# https://docs.gofiber.io/guide/faster-fiber
jsonCoder = "sonic"
# "UTC" => "UTC"
# "Local" => Local
timeLocation = "Local"

[auth]
# 启用认证和授权，仅stage.status=dev下对private路由生效
enable = true
# 认证
[auth.jwt]
# HS256, HS384, HS512
hmac = "HS256"
key = "k9KKHYwYYJNnhnf4A97hl6WuXkZeK4Ak"
# 过期时间，单位：小时
ttl = 168

[auth.casbin]
# casbin model.conf的路径
modelPath = "resource"

[ent]
# sqlite3不支持修改数据库资源 see: https://entgo.io/zh/docs/dialects#sqlite
# 自动迁移，只创建新表和索引，将列追加到表或扩展列类型。 例如，将int改为bigint。
autoMigrate = true
# 在调试模式，打印所有SQL查询
debugMode = false
# 删除资源，WithDropIndex 和 WithDropColumn 是用于删除表列和索引的两个选项。
withDropIndex = true
withDropColumn = true
# 默认情况下，每个表的SQL主键从1开始；这意味着不同类型的多个实体可以有相同的ID
# 如果想要全局唯一ID
# 注意：全局唯一ID无法跟Versioned Migrations同时生效
withGlobalUniqueID = false
# 支持的数据库后端：mysql、mariadb、postgres、sqlite3
# 需要在 [ent.db.xx] 配置启用的数据库信息
backend = "mysql"

#backend
[ent.db]
maxIdleConns = 10
maxOpenConns = 100
# Hour
connMaxLifetime = 1

[ent.db.postgres]
host = "127.0.0.1"
port = 5432
username = "postgres"
password = "ZrwBP6wb23NsEteL"
database = "meta"
param = "sslmode=disable"

#[ent.db.mysql]
#host = "127.0.0.1"
##host = "************"
##host = "************"
#port = 3306
#username = "root"
#password = "3z47d9HluLW1yVg2"
#database = "ddos"
#param = "parseTime=true&loc=Local"

[ent.db.mysql]
host = "sesa-ddostest1-100858-m.bj1.dumbo.nie.netease.com"
port = 3306
username = "projsa"
password = "kD7&gz8ZkL3ezEm1g8foxOac8pl25HqV"
database = "ddos"
param = "parseTime=true&loc=Local"

[ent.db.mariadb]
host = "127.0.0.1"
port = 55000
username = "root"
password = "mariadbpw"
database = "meta"
param = ""

[ent.db.sqlite3]
# memory，内存中
# rwc，文件中
mode = "rwc"
database = "meta.db"

##cache
#[cache.redis]
#host = "127.0.0.1"
##host = "************"
##host = "************"
#port = 6379
#username = ""
#password = "P46K1OzX0910k9kC"
#dbNum = 0

#cache
[cache.redis]
host = "jetis-14113-sesa-ddos-test-senior-m.redis.nie.netease.com"
port = 6379
username = ""
password = "8xQrA6BrCCdf0P_Ry1296qroS+"
dbNum = 0

#swagger api
[swagger]
enable = true

# log
# 日志
# web(fiber)日志
[log.web]
# https://docs.gofiber.io/api/middleware/logger
format = "[${time}] ${ip} ${pid} ${locals:requestid} ${status} - ${method} ${url} ${ua}\n"
# https://programming.guide/go/format-parse-string-time-date-example.html
timeFormat = "2006-01-02 15:04:05"
timeZone = "Local"
# 日志输出：stderr, lumberjack，开发可以stderr输出到终端，生产用lumberjack
output = "stderr"
# output = "lumberjack"
[log.web.lumberjack]
# 日志名称
logFile = "data/logs/web/meta.log"
# 日志大小限制，单位MB
maxSize = 100
# 历史日志文件保留天数，0:无限制
maxAge = 0
# 最大保留历史日志数量，0:无限制
maxBackup = 0
# 本地时区
localTime = true
# 压缩历史日志文件
compress = true

# 应用日志
[log.app]
# 1:debug，2:info，3:warn，4:error，5:dpanic，6:panic，7:fatal
# debug包括info,warn...以此类推
logLevel = 1
[log.app.lumberjack]
# 日志名称
logFile = "data/logs/app/meta.log"
# 日志大小限制，单位MB
maxSize = 100
# 历史日志文件保留天数，0:无限制
maxAge = 0
# 最大保留历史日志数量，0:无限制
maxBackup = 0
# 本地时区
localTime = true
# 压缩历史日志文件
compress = true
# log end

# x auth
[xAuth]
notifyToken = "afce5af7ecf9472585e0eaad749f315b"

[xAuth.v2]
user = "_sesa"
project = "sesa"
key = "7f51089c554147cd8545d100a239dea1"
ttl = 21000

[xAuth.api]
galaxy = "https://galaxy.nie.netease.com/cache/v1"
notify = "http://notify.nie.netease.com/api/v1"
auth = "http://auth.nie.netease.com/api/v1"
authV2 = "http://auth.nie.netease.com/api/v2"
lbc = "http://api-int-hz.lbc.nie.netease.com:8080"
cld = "http://api.cube.nie.netease.com"
skyline = "http://int.api-skyline.skyline.nie.netease.com"
neteaseIP = "https://api.ip.netease.com/v1"

[xAuth.api.matrix]
ipdb = "https://ipdb.nie.netease.com"
flow = "http://vmapi-external-int.monitor.nie.netease.com"
# x auth end

# 外部系统：集团nds、集团soc、沃防
[external]
# 集团NDS
[external.nds]
#api = "http://*************:8084/api/v2/outer/game"
api = "http://ddos-api.netease.com/api/v2/outer/game"
apikey = "663f7db930811a0c3ff7ce52d3607c31"
oldApi = "https://ddos.netease.com/api/game"
oldApikey = "67j5aTp9pagYD2Fw"

# 集团soc
[external.socGroup]
# status = "prod"会自动使用正式环境的api，其余使用测试环境apiTest
api = "http://api.soc.netease.com"
apiTest = "http://api.soc-test.netease.com"
username = "onlinegame"
password = "28541f48f2627c5713c300134985c405"

# 联通沃防
[external.wofang]
# 旧api，被忽悠的api
api = "http://api.wofangyun.com/apiv1"
appId = 243
appKey = "175c8e35dbe64885f44a64b03cded2d5"

#解封时长(秒)，表示多少秒后自动解封
autoUnDrogSecond = 60

# 新api
api2 = "http://api2.wofangyun.com/open_api/v3/"
api2Id = 100796
api2Key = "f2975ef3609235e3447321da8f6fe990"
version = "1.0"
signatureMethod = "HMAC-SHA1"

# 杭州联通机房
idcCode = 51

#对外提供服务
[external.service]
# 集团推送告警的apikey
socGroupApiKey = "oongasaeweiduuquupae9eech7ohjae7"
qianliyanApiKey = "a3f9d8c7b6e5f41289abcd1234ef5678"

[external.kafka]
[external.kafka.netCloud]
groupId = "ddos-test-group"
broker = "vmstoragecld.brokers.canal.netease.com:9093"
topics = ["cld_9603_ndcc_aegis"]
saslEnable = true
saslUser = "sre_cld_gon"
saslPassword = "3de3f569de4749ba9772"

# end

[[MatrixStrategies]]
name = "杭州Static联通"
region = "HangZhou"
netType = "Static"
isp = "ChinaUnicom"
monitorBps = 75
dragBps = 75
dragType = 10010

[[MatrixStrategies]]
name = "杭州BGP联通"
region = "HangZhou"
netType = "BGP"
isp = "ChinaUnicom"
monitorBps = 120
dragBps = 120
dragType = 10010